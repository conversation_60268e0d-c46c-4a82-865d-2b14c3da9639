<UserControl x:Class="CalculatorApp.OperatorsPanel"
             xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
             xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
             xmlns:d="http://schemas.microsoft.com/expression/blend/2008"
             xmlns:local="using:CalculatorApp"
             xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006"
             d:DesignHeight="315"
             d:DesignWidth="235"
             mc:Ignorable="d">
    <Grid>
        <local:CalculatorStandardOperators x:Name="StandardOperators"
                                           TabIndex="17"
                                           IsEnabled="{x:Bind Model.IsStandard, Mode=OneWay}"
                                           x:Load="{x:Bind Model.IsStandard, Mode=OneWay}"/>

        <local:CalculatorScientificOperators x:Name="ScientificOperators"
                                             TabIndex="16"
                                             Visibility="{x:Bind Model.IsScientific, Mode=OneWay}"
                                             IsEnabled="{x:Bind Model.IsScientific, Mode=OneWay}"
                                             x:Load="False"/>

        <local:CalculatorProgrammerBitFlipPanel x:Name="BitFlipPanel"
                                                Visibility="{x:Bind Model.IsBinaryBitFlippingEnabled, Mode=OneWay}"
                                                IsEnabled="{x:Bind Model.IsBinaryBitFlippingEnabled, Mode=OneWay}"
                                                x:Load="False"/>

        <local:CalculatorProgrammerRadixOperators x:Name="ProgrammerRadixOperators"
                                                  TabIndex="16"
                                                  Visibility="{x:Bind Model.AreProgrammerRadixOperatorsVisible, Mode=OneWay}"
                                                  IsEnabled="{x:Bind Model.IsProgrammer, Mode=OneWay}"
                                                  x:Load="False"/>

    </Grid>
</UserControl>
