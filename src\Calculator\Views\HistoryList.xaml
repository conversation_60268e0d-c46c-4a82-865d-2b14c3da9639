<UserControl x:Class="CalculatorApp.HistoryList"
             xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
             xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
             xmlns:automation="using:CalculatorApp.ViewModel.Common.Automation"
             xmlns:common="using:CalculatorApp.Common"
             xmlns:converters="using:CalculatorApp.Converters"
             xmlns:d="http://schemas.microsoft.com/expression/blend/2008"
             xmlns:local="using:CalculatorApp"
             xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006"
             xmlns:model="using:CalculatorApp.ViewModel"
             xmlns:muxc="using:Microsoft.UI.Xaml.Controls"
             xmlns:utils="using:CalculatorApp.Utils"
             AutomationProperties.AutomationId="HistoryList"
             FlowDirection="LeftToRight"
             mc:Ignorable="d">

    <UserControl.Resources>
        <ResourceDictionary>
            <ResourceDictionary.ThemeDictionaries>
                <ResourceDictionary x:Key="Default">
                    <Style x:Key="BodyTextBlockMediumStyle"
                           BasedOn="{StaticResource BodyTextBlockStyle}"
                           TargetType="TextBlock">
                        <Setter Property="Foreground" Value="{ThemeResource SystemControlForegroundBaseMediumBrush}"/>
                    </Style>
                </ResourceDictionary>
                <ResourceDictionary x:Key="Light">
                    <Style x:Key="BodyTextBlockMediumStyle"
                           BasedOn="{StaticResource BodyTextBlockStyle}"
                           TargetType="TextBlock">
                        <Setter Property="Foreground" Value="{ThemeResource SystemControlForegroundBaseMediumBrush}"/>
                    </Style>
                </ResourceDictionary>
                <ResourceDictionary x:Key="HighContrast">
                    <Style x:Key="BodyTextBlockMediumStyle"
                           BasedOn="{StaticResource BodyTextBlockStyle}"
                           TargetType="TextBlock"/>
                </ResourceDictionary>
            </ResourceDictionary.ThemeDictionaries>
            <converters:ItemSizeToVisibilityNegationConverter x:Key="ItemSizeToVisibilityNegationConverter"/>
            <converters:ItemSizeToVisibilityConverter x:Key="ItemSizeToVisibilityConverter"/>
            <automation:NarratorNotifier x:Name="NarratorNotifier" Announcement="{x:Bind Model.HistoryAnnouncement, Mode=OneWay}"/>

            <muxc:SymbolIconSource x:Key="DeleteSymbol" Symbol="Delete"/>

            <muxc:SwipeItems x:Key="HistorySwipeItems" Mode="Execute">
                <muxc:SwipeItem AutomationProperties.Name="{utils:ResourceString Name=DeleteHistorySwipeItem/[using:Windows.UI.Xaml.Automation]AutomationProperties/Name}"
                                IconSource="{StaticResource DeleteSymbol}"
                                Invoked="OnDeleteSwipeInvoked"
                                Text="{utils:ResourceString Name=DeleteHistorySwipeItem/Text}"/>
            </muxc:SwipeItems>

            <MenuFlyout x:Name="HistoryContextMenu">
                <MenuFlyoutItem Click="OnCopyMenuItemClicked"
                                Icon="Copy"
                                Text="{utils:ResourceString Name=CopyHistoryMenuItem/Text}"/>
                <MenuFlyoutItem AutomationProperties.Name="{utils:ResourceString Name=DeleteHistoryMenuItem/[using:Windows.UI.Xaml.Automation]AutomationProperties/Name}"
                                Click="OnDeleteMenuItemClicked"
                                Icon="Delete"
                                Text="{utils:ResourceString Name=DeleteHistoryMenuItem/Text}"/>
            </MenuFlyout>

            <DataTemplate x:Key="HistoryItemTemplate" x:DataType="model:HistoryItemViewModel">
                <muxc:SwipeControl AutomationProperties.Name="{x:Bind local:HistoryList.GetHistoryItemAutomationName(AccExpression, AccResult)}" RightItems="{StaticResource HistorySwipeItems}">
                    <StackPanel Margin="0,6,16,6" Background="Transparent">
                        <TextBlock x:Name="ExprTextBlock"
                                   Margin="0,0,0,4"
                                   HorizontalAlignment="Right"
                                   Style="{ThemeResource BodyTextBlockMediumStyle}"
                                   AutomationProperties.AccessibilityView="Raw"
                                   AutomationProperties.AutomationId="HistoryItemExpression"
                                   IsTextSelectionEnabled="True"
                                   Text="{x:Bind Expression}"
                                   TextAlignment="Right"
                                   TextWrapping="Wrap"/>
                        <TextBlock x:Name="ResultTextBlock"
                                   HorizontalAlignment="Right"
                                   Style="{ThemeResource SubtitleTextBlockStyle}"
                                   AutomationProperties.AccessibilityView="Raw"
                                   AutomationProperties.AutomationId="HistoryItemValue"
                                   IsTextSelectionEnabled="True"
                                   Text="{x:Bind Result}"
                                   TextAlignment="Right"
                                   TextWrapping="Wrap"/>
                    </StackPanel>
                </muxc:SwipeControl>
            </DataTemplate>
            <Style x:Key="HistoryItemContainerStyle"
                   BasedOn="{StaticResource ConditionalHistoryMemoryItemContainerStyle}"
                   TargetType="ListViewItem">
                <Setter Property="Margin" Value="0,0,0,20"/>
                <Setter Property="ContextFlyout" Value="{StaticResource HistoryContextMenu}"/>
            </Style>
        </ResourceDictionary>
    </UserControl.Resources>

    <Grid x:Name="LayoutGrid">
        <Grid.RowDefinitions>
            <RowDefinition Height="*"/>
            <RowDefinition Height="{x:Bind RowHeight, Mode=OneWay}"/>
        </Grid.RowDefinitions>

        <VisualStateManager.VisualStateGroups>
            <VisualStateGroup>
                <VisualState x:Name="DockedLayout">
                    <VisualState.StateTriggers>
                        <AdaptiveTrigger MinWindowWidth="560"/>
                    </VisualState.StateTriggers>
                    <VisualState.Setters>
                        <Setter Target="HistoryListRootGrid.(Grid.Row)" Value="0"/>
                        <Setter Target="HistoryListRootGrid.(Grid.RowSpan)" Value="2"/>
                        <Setter Target="HistoryListView.Padding" Value="0"/>
                        <Setter Target="BackgroundShade.Visibility" Value="Collapsed"/>
                    </VisualState.Setters>
                </VisualState>
                <VisualState x:Name="DefaultLayout">
                    <VisualState.StateTriggers>
                        <AdaptiveTrigger MinWindowWidth="0"/>
                    </VisualState.StateTriggers>
                </VisualState>
            </VisualStateGroup>
        </VisualStateManager.VisualStateGroups>
        <Border x:Name="BackgroundShade"
                Grid.Row="1"
                Margin="0,-1,0,0"
                Padding="0"
                HorizontalAlignment="Stretch"
                VerticalAlignment="Stretch"
                Background="{ThemeResource SolidBackgroundFillColorBase}"
                BorderBrush="{ThemeResource SystemControlForegroundChromeHighBrush}"
                BorderThickness="{ThemeResource HighContrastThicknessTop}"
                CornerRadius="{ThemeResource OverlayCornerRadius}"/>

        <Grid x:Name="HistoryListRootGrid" Grid.Row="1">
            <Grid.RowDefinitions>
                <RowDefinition Height="*"/>
                <RowDefinition Height="Auto"/>
            </Grid.RowDefinitions>
            <TextBlock x:Name="HistoryEmpty"
                       Margin="12,8,24,0"
                       Style="{ThemeResource BodyTextBlockStyle}"
                       Foreground="{ThemeResource TextFillColorPrimaryBrush}"
                       Text="{utils:ResourceString Name=HistoryEmpty/Text}"
                       TextWrapping="Wrap"
                       Visibility="{x:Bind Model.ItemsCount, Converter={StaticResource ItemSizeToVisibilityConverter}, Mode=OneWay}"/>
            <ListView x:Name="HistoryListView"
                      MinHeight="60"
                      Margin="4,0"
                      Padding="0,24,0,0"
                      HorizontalAlignment="Stretch"
                      AutomationProperties.AutomationId="HistoryListView"
                      IsItemClickEnabled="True"
                      ItemClick="ListView_ItemClick"
                      ItemContainerStyle="{StaticResource HistoryItemContainerStyle}"
                      ItemTemplate="{StaticResource HistoryItemTemplate}"
                      ItemsSource="{x:Bind Model.Items, Mode=OneWay}"
                      SelectionMode="None"
                      TabIndex="0"
                      Visibility="{x:Bind Model.ItemsCount, Mode=OneWay, Converter={StaticResource ItemSizeToVisibilityNegationConverter}}">
                <ListView.ItemContainerTransitions>
                    <TransitionCollection>
                        <AddDeleteThemeTransition/>
                        <ContentThemeTransition/>
                        <ReorderThemeTransition/>
                    </TransitionCollection>
                </ListView.ItemContainerTransitions>
            </ListView>
            <Button x:Name="ClearHistory"
                    Grid.Row="1"
                    Style="{StaticResource ClearAllHistoryMemoryButtonStyle}"
                    common:KeyboardShortcutManager.VirtualKeyControlShiftChord="{utils:ResourceVirtualKey Name=ClearHistory/[using:CalculatorApp.Common]KeyboardShortcutManager/VirtualKeyControlShiftChord}"
                    AccessKey="{utils:ResourceString Name=ClearHistory/AccessKey}"
                    AutomationProperties.AutomationId="ClearHistory"
                    AutomationProperties.Name="{utils:ResourceString Name=ClearHistory/[using:Windows.UI.Xaml.Automation]AutomationProperties/Name}"
                    Command="{x:Bind Model.ClearCommand, Mode=OneWay}"
                    Content=""
                    ToolTipService.ToolTip="{utils:ResourceString Name=ClearHistory/[using:Windows.UI.Xaml.Controls]ToolTipService/ToolTip}"
                    Visibility="{x:Bind Model.ItemsCount, Mode=OneWay, Converter={StaticResource ItemSizeToVisibilityNegationConverter}}"/>
        </Grid>
    </Grid>
</UserControl>
