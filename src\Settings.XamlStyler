{"AttributesTolerance": 2, "KeepFirstAttributeOnSameLine": true, "MaxAttributeCharatersPerLine": 0, "MaxAttributesPerLine": 1, "NewlineExemptionElements": "RadialGradientBrush, GradientStop, LinearGradientBrush, ScaleTransfom, SkewTransform, RotateTransform, TranslateTransform, Trigger, Condition, Setter", "SeparateByGroups": false, "EnableAttributeReordering": true, "AttributeOrderingRuleGroups": ["x:Class*", "xmlns, xmlns:x", "xmlns:*", "x:Key, Key, x:Name, Name, x:Uid, Uid, Title", "G<PERSON><PERSON>Row, <PERSON><PERSON>.<PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>.<PERSON><PERSON><PERSON>, Canvas.Left, Canvas.Top, Canvas.Right, Canvas.Bottom", "<PERSON><PERSON><PERSON>, <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>", "Margin, Padding, HorizontalAlignment, VerticalAlignment, HorizontalContentAlignment, VerticalContentAlignment, Panel.ZIndex", "Style, Background, <PERSON>eground, Fill, BorderBrush, BorderThickness, Stroke, StrokeThickness, Opacity", "FontFamily, FontSize, LineHeight, FontWeight, FontStyle, FontStretch", "*:*, *", "PageSource, PageIndex, Offset, Color, TargetName, Property, Value, StartPoint, EndPoint", "mc:I<PERSON>rable, d:IsDataSource, d:LayoutOverrides, d:IsStaticText", "Is<PERSON>nabled, x:Load, Load", "Value, Maximum, Minimum"], "OrderAttributesByName": true, "PutEndingBracketOnNewLine": false, "RemoveEndingTagOfEmptyElement": true, "SpaceBeforeClosingSlash": false, "RootElementLineBreakRule": 0, "ReorderVSM": 1, "ReorderGridChildren": false, "ReorderCanvasChildren": false, "ReorderSetters": 0, "FormatMarkupExtension": true, "NoNewLineMarkupExtensions": "x:Bind, Binding", "ThicknessSeparator": 2, "ThicknessAttributes": "<PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, ThumbnailClipMargin", "FormatOnSave": true, "CommentPadding": 1, "IndentSize": 4}