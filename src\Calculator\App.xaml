<Application x:Class="CalculatorApp.App"
             xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
             xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
             xmlns:Controls="using:CalculatorApp.Controls"
             xmlns:common="using:CalculatorApp.Common"
             xmlns:contract7NotPresent="http://schemas.microsoft.com/winfx/2006/xaml/presentation?IsApiContractNotPresent(Windows.Foundation.UniversalApiContract,7)"
             xmlns:contract7Present="http://schemas.microsoft.com/winfx/2006/xaml/presentation?IsApiContractPresent(Windows.Foundation.UniversalApiContract,7)"
             xmlns:converters="using:CalculatorApp.Converters"
             xmlns:local="using:CalculatorApp"
             xmlns:muxc="using:Microsoft.UI.Xaml.Controls"
             xmlns:primitives="using:Microsoft.UI.Xaml.Controls.Primitives"
             xmlns:utils="using:CalculatorApp.Utils">

    <Application.Resources>
        <ResourceDictionary>
            <ResourceDictionary.MergedDictionaries>
                <XamlControlsResources xmlns="using:Microsoft.UI.Xaml.Controls"/>

                <!-- Overwrite some resources of WinUI -->
                <ResourceDictionary>
                    <!-- overwrite the background of navigationview content to transparent to show Mica background -->
                    <StaticResource x:Key="NavigationViewContentBackground" ResourceKey="SystemControlTransparentBrush"/>
                    <!-- overwrite the NavigationView content margin -->
                    <Thickness x:Key="NavigationViewContentPresenterMargin">0</Thickness>
                    <!-- overwrite the NavigationView content border thickness -->
                    <Thickness x:Key="NavigationViewMinimalContentGridBorderThickness">0</Thickness>

                    <!-- overwrite the NavigationView pane background -->
                    <ResourceDictionary.ThemeDictionaries>
                        <ResourceDictionary x:Key="Default">
                            <StaticResource x:Key="DarkAcrylicInAppFillColorDefaultBrush" ResourceKey="AcrylicInAppFillColorDefaultBrush"/>
                            <StaticResource x:Key="NavigationViewDefaultPaneBackground" ResourceKey="DarkAcrylicInAppFillColorDefaultBrush"/>
                        </ResourceDictionary>
                        <ResourceDictionary x:Key="Light">
                            <StaticResource x:Key="LightAcrylicInAppFillColorDefaultBrush" ResourceKey="AcrylicInAppFillColorDefaultBrush"/>
                            <StaticResource x:Key="NavigationViewDefaultPaneBackground" ResourceKey="LightAcrylicInAppFillColorDefaultBrush"/>
                        </ResourceDictionary>
                        <ResourceDictionary x:Key="HighContrast">
                            <StaticResource x:Key="HCAcrylicInAppFillColorDefaultBrush" ResourceKey="AcrylicInAppFillColorDefaultBrush"/>
                            <StaticResource x:Key="NavigationViewDefaultPaneBackground" ResourceKey="HCAcrylicInAppFillColorDefaultBrush"/>
                        </ResourceDictionary>
                    </ResourceDictionary.ThemeDictionaries>
                </ResourceDictionary>
            </ResourceDictionary.MergedDictionaries>
            <ResourceDictionary.ThemeDictionaries>
                <ResourceDictionary x:Key="Default">
                    <Thickness x:Key="ControlBorderThemeThickness">0</Thickness>
                    <Thickness x:Key="HighContrastThicknessTop">0,0,0,0</Thickness>
                    <x:Double x:Key="HighContrastStrokeThickness">0</x:Double>
                    <Color x:Key="AltHighColor">#FF000000</Color>
                    <Color x:Key="ChromeMediumLowColor">#FF2B2B2B</Color>
                    <Color x:Key="OperatorPanelScrollButtonBackgroundColor">#FF858585</Color>
                    <SolidColorBrush x:Key="SystemControlBackgroundAltHighBrush" Color="{StaticResource AltHighColor}"/>
                    <SolidColorBrush x:Key="SystemControlBackgroundChromeMediumLowBrush" Color="{StaticResource ChromeMediumLowColor}"/>
                    <SolidColorBrush x:Key="SystemControlBackgroundTransparentBrush" Color="Transparent"/>
                    <SolidColorBrush x:Key="SystemControlHighlightTransparentBrush" Color="Transparent"/>
                    <SolidColorBrush x:Key="AppBackgroundAltMediumLowBrush" Color="{ThemeResource SystemAltMediumLowColor}"/>
                    <SolidColorBrush x:Key="AppOperatorPanelBackground"
                                     Opacity="0.4"
                                     Color="{ThemeResource SystemAltMediumLowColor}"/>
                    <SolidColorBrush x:Key="AppControlPageTextBaseMediumHighBrush" Color="{StaticResource SystemBaseMediumHighColor}"/>
                    <RevealBackgroundBrush x:Key="AppControlHoverButtonFaceBrush"
                                           FallbackColor="#18FFFFFF"
                                           Color="#18FFFFFF"/>
                    <RevealBackgroundBrush x:Key="AppControlPressedButtonFaceBrush"
                                           FallbackColor="#30FFFFFF"
                                           Color="#30FFFFFF"/>
                    <SolidColorBrush x:Key="AppControlTransparentAccentColorBrush" Color="{ThemeResource SystemAccentColor}"/>
                    <SolidColorBrush x:Key="AppControlPageTextBaseHighColorBrush" Color="{StaticResource SystemBaseHighColor}"/>
                    <SolidColorBrush x:Key="OperatorPanelScrollButtonBackgroundBrush" Color="{ThemeResource OperatorPanelScrollButtonBackgroundColor}"/>
                    <SolidColorBrush x:Key="AppControlHighlightCalcButtonBrush"
                                     Opacity="0.4"
                                     Color="{ThemeResource SystemAccentColor}"/>
                    <SolidColorBrush x:Key="AppControlHighlightCalcButtonToggledBrush"
                                     Opacity="0.4"
                                     Color="{ThemeResource SystemAccentColor}"/>

                    <SolidColorBrush x:Key="AppControlHighlightCalcButtonHoverBrush"
                                     Opacity="0.9"
                                     Color="{ThemeResource SystemAccentColor}"/>

                    <SolidColorBrush x:Key="AppControlForegroundAccentBrush" Color="{ThemeResource SystemAccentColor}"/>
                    <SolidColorBrush x:Key="AppControlPageTextRedColorBrush" Color="Red"/>
                    <RevealBorderBrush x:Key="AppControlForegroundTransparentRevealBorderBrush"
                                       FallbackColor="Transparent"
                                       TargetTheme="Dark"
                                       Color="Transparent"/>
                    <RevealBackgroundBrush x:Key="AppControlHighlightAccentRevealBackgroundBrush"
                                           FallbackColor="{ThemeResource SystemAccentColor}"
                                           TargetTheme="Dark"
                                           Color="{ThemeResource SystemAccentColor}"/>
                    <RevealBackgroundBrush x:Key="AppControlBackgroundListAccentHighRevealBackgroundBrush"
                                           FallbackColor="{ThemeResource SystemAccentColorDark3}"
                                           TargetTheme="Dark"
                                           Color="{ThemeResource SystemAccentColorDark3}"/>
                    <AcrylicBrush x:Key="AppChromeAcrylicHostBackdropMediumLowBrush"
                                  BackgroundSource="HostBackdrop"
                                  FallbackColor="{ThemeResource SystemChromeMediumColor}"
                                  TintColor="{ThemeResource SystemChromeLowColor}"
                                  TintOpacity="0.7"/>
                    <ApplicationTheme x:Key="CalcApplicationTheme">Dark</ApplicationTheme>
                    <SolidColorBrush x:Key="AppChromeAcrylicOperatorFlyoutBackgroundBrush" Color="#FF2F2F2F"/>
                    <SolidColorBrush x:Key="AppControlTransparentButtonBackgroundBrush" Color="Transparent"/>

                    <SolidColorBrush x:Key="EquationBrush1" Color="#4D92C8"/>
                    <SolidColorBrush x:Key="EquationBrush2" Color="#4DCDD5"/>
                    <SolidColorBrush x:Key="EquationBrush3" Color="#A366E0"/>
                    <SolidColorBrush x:Key="EquationBrush4" Color="#58A358"/>
                    <SolidColorBrush x:Key="EquationBrush5" Color="#4DDB97"/>
                    <SolidColorBrush x:Key="EquationBrush6" Color="#4DA688"/>
                    <SolidColorBrush x:Key="EquationBrush7" Color="#8A8B8C"/>
                    <SolidColorBrush x:Key="EquationBrush8" Color="#EF5865"/>
                    <SolidColorBrush x:Key="EquationBrush9" Color="#EB4DAF"/>
                    <SolidColorBrush x:Key="EquationBrush10" Color="#CA5B93"/>
                    <SolidColorBrush x:Key="EquationBrush11" Color="#FFCE4D"/>
                    <SolidColorBrush x:Key="EquationBrush12" Color="#F99255"/>
                    <SolidColorBrush x:Key="EquationBrush13" Color="#B0896D"/>
                    <SolidColorBrush x:Key="EquationBrush14" Color="#FFFFFF"/>

                    <StaticResource x:Key="CalcButtonTextFillColorDefaultBrush" ResourceKey="TextFillColorPrimaryBrush"/>
                    <StaticResource x:Key="CalcButtonTextFillColorHoverBrush" ResourceKey="TextFillColorPrimaryBrush"/>
                    <StaticResource x:Key="CalcButtonTextFillColorPressedBrush" ResourceKey="TextFillColorSecondaryBrush"/>
                    <StaticResource x:Key="CalcButtonTextFillColorDisabledBrush" ResourceKey="TextFillColorDisabledBrush"/>
                    <Color x:Key="CalcButtonBaseFillColor">#FFFFFF</Color>
                    <SolidColorBrush x:Key="CalcButtonFillColorDefaultBrush"
                                     Opacity="0.125"
                                     Color="{StaticResource CalcButtonBaseFillColor}"/>
                    <SolidColorBrush x:Key="CalcButtonFillColorHoverBrush"
                                     Opacity="0.0852"
                                     Color="{StaticResource CalcButtonBaseFillColor}"/>
                    <SolidColorBrush x:Key="CalcButtonFillColorPressedBrush"
                                     Opacity="0.0359"
                                     Color="{StaticResource CalcButtonBaseFillColor}"/>
                    <SolidColorBrush x:Key="CalcButtonFillColorDisabledBrush"
                                     Opacity="0.0359"
                                     Color="{StaticResource CalcButtonBaseFillColor}"/>
                    <SolidColorBrush x:Key="CalcButtonAltFillColorDefaultBrush"
                                     Opacity="0.0852"
                                     Color="{StaticResource CalcButtonBaseFillColor}"/>
                    <SolidColorBrush x:Key="CalcButtonAltFillColorHoverBrush"
                                     Opacity="0.1256"
                                     Color="{StaticResource CalcButtonBaseFillColor}"/>
                    <SolidColorBrush x:Key="CalcButtonAltFillColorPressedBrush"
                                     Opacity="0.0852"
                                     Color="{StaticResource CalcButtonBaseFillColor}"/>
                    <SolidColorBrush x:Key="CalcButtonAltFillColorDisabledBrush"
                                     Opacity="0.0359"
                                     Color="{StaticResource CalcButtonBaseFillColor}"/>

                    <Color x:Key="BackgroundSmokeFillColor">#000000</Color>
                    <SolidColorBrush x:Key="BackgroundSmokeFillColorBrush"
                                     Opacity="0.42"
                                     Color="{StaticResource BackgroundSmokeFillColor}"/>

                    <StaticResource x:Key="AppControlForegroundSecondaryBrush" ResourceKey="TextFillColorSecondaryBrush"/>
                    <StaticResource x:Key="AppControlBackgroundTertiaryBrush" ResourceKey="SubtleFillColorTertiaryBrush"/>
                    <StaticResource x:Key="AppControlBackgroundSecondaryBrush" ResourceKey="SubtleFillColorSecondaryBrush"/>
                </ResourceDictionary>
                <ResourceDictionary x:Key="Light">
                    <Thickness x:Key="ControlBorderThemeThickness">0</Thickness>
                    <Thickness x:Key="HighContrastThicknessTop">0,0,0,0</Thickness>
                    <x:Double x:Key="HighContrastStrokeThickness">0</x:Double>
                    <Color x:Key="AltHighColor">#FFF2F2F2</Color>
                    <Color x:Key="ChromeMediumLowColor">#FFE0E0E0</Color>
                    <Color x:Key="OperatorPanelScrollButtonBackgroundColor">#FF858585</Color>
                    <SolidColorBrush x:Key="SystemControlBackgroundAltHighBrush" Color="{StaticResource SystemAltHighColor}"/>
                    <SolidColorBrush x:Key="SystemControlBackgroundChromeMediumLowBrush" Color="{StaticResource ChromeMediumLowColor}"/>
                    <SolidColorBrush x:Key="SystemControlBackgroundTransparentBrush" Color="Transparent"/>
                    <SolidColorBrush x:Key="SystemControlHighlightTransparentBrush" Color="Transparent"/>
                    <SolidColorBrush x:Key="AppBackgroundAltMediumLowBrush" Color="{ThemeResource SystemAltMediumLowColor}"/>
                    <SolidColorBrush x:Key="AppOperatorPanelBackground"
                                     Opacity="0.4"
                                     Color="{ThemeResource SystemAltMediumLowColor}"/>
                    <SolidColorBrush x:Key="AppControlPageTextBaseMediumHighBrush" Color="{StaticResource SystemBaseMediumHighColor}"/>
                    <RevealBackgroundBrush x:Key="AppControlHoverButtonFaceBrush"
                                           FallbackColor="#17000000"
                                           Color="#17000000"/>
                    <RevealBackgroundBrush x:Key="AppControlPressedButtonFaceBrush"
                                           FallbackColor="#30000000"
                                           Color="#30000000"/>
                    <SolidColorBrush x:Key="AppControlTransparentAccentColorBrush" Color="{ThemeResource SystemAccentColor}"/>
                    <SolidColorBrush x:Key="AppControlPageTextBaseHighColorBrush" Color="{StaticResource SystemBaseHighColor}"/>
                    <SolidColorBrush x:Key="OperatorPanelScrollButtonBackgroundBrush" Color="{ThemeResource OperatorPanelScrollButtonBackgroundColor}"/>
                    <SolidColorBrush x:Key="AppControlForegroundAccentBrush" Color="{ThemeResource SystemAccentColor}"/>
                    <SolidColorBrush x:Key="AppControlPageTextRedColorBrush" Color="Red"/>
                    <SolidColorBrush x:Key="AppControlHighlightCalcButtonBrush"
                                     Opacity="0.4"
                                     Color="{ThemeResource SystemAccentColor}"/>
                    <SolidColorBrush x:Key="AppControlHighlightCalcButtonToggledBrush"
                                     Opacity="0.4"
                                     Color="{ThemeResource SystemAccentColor}"/>

                    <SolidColorBrush x:Key="AppControlHighlightCalcButtonHoverBrush"
                                     Opacity="0.7"
                                     Color="{ThemeResource SystemAccentColor}"/>

                    <RevealBorderBrush x:Key="AppControlForegroundTransparentRevealBorderBrush"
                                       FallbackColor="Transparent"
                                       TargetTheme="Light"
                                       Color="Transparent"/>
                    <RevealBackgroundBrush x:Key="AppControlHighlightAccentRevealBackgroundBrush"
                                           FallbackColor="{ThemeResource SystemAccentColor}"
                                           TargetTheme="Light"
                                           Color="{ThemeResource SystemAccentColor}"/>
                    <RevealBackgroundBrush x:Key="AppControlBackgroundListAccentHighRevealBackgroundBrush"
                                           FallbackColor="{ThemeResource SystemAccentColorLight3}"
                                           TargetTheme="Light"
                                           Color="{ThemeResource SystemAccentColorLight3}"/>
                    <AcrylicBrush x:Key="AppChromeAcrylicHostBackdropMediumLowBrush"
                                  BackgroundSource="HostBackdrop"
                                  FallbackColor="{ThemeResource SystemChromeMediumColor}"
                                  TintColor="{ThemeResource SystemChromeLowColor}"
                                  TintOpacity="0.7"/>
                    <AcrylicBrush x:Key="AppChromeAcrylicOperatorFlyoutBackgroundBrush"
                                  BackgroundSource="HostBackdrop"
                                  FallbackColor="{ThemeResource SystemChromeMediumColor}"
                                  TintColor="{ThemeResource SystemChromeLowColor}"
                                  TintOpacity="0.8"/>
                    <ApplicationTheme x:Key="CalcApplicationTheme">Light</ApplicationTheme>
                    <SolidColorBrush x:Key="AppControlTransparentButtonBackgroundBrush" Color="Transparent"/>

                    <SolidColorBrush x:Key="EquationBrush1" Color="#FF0063B1"/>
                    <SolidColorBrush x:Key="EquationBrush2" Color="#FF00B7C3"/>
                    <SolidColorBrush x:Key="EquationBrush3" Color="#FF6600CC"/>
                    <SolidColorBrush x:Key="EquationBrush4" Color="#FF107C10"/>
                    <SolidColorBrush x:Key="EquationBrush5" Color="#FF00CC6A"/>
                    <SolidColorBrush x:Key="EquationBrush6" Color="#FF008055"/>
                    <SolidColorBrush x:Key="EquationBrush7" Color="#FF58595B"/>
                    <SolidColorBrush x:Key="EquationBrush8" Color="#FFE81123"/>
                    <SolidColorBrush x:Key="EquationBrush9" Color="#FFE3008C"/>
                    <SolidColorBrush x:Key="EquationBrush10" Color="#FFB31564"/>
                    <SolidColorBrush x:Key="EquationBrush11" Color="#FFFFB900"/>
                    <SolidColorBrush x:Key="EquationBrush12" Color="#FFF7630C"/>
                    <SolidColorBrush x:Key="EquationBrush13" Color="#FF8E562E"/>
                    <SolidColorBrush x:Key="EquationBrush14" Color="#FF000000"/>

                    <StaticResource x:Key="CalcButtonTextFillColorDefaultBrush" ResourceKey="TextFillColorPrimaryBrush"/>
                    <StaticResource x:Key="CalcButtonTextFillColorHoverBrush" ResourceKey="TextFillColorPrimaryBrush"/>
                    <StaticResource x:Key="CalcButtonTextFillColorPressedBrush" ResourceKey="TextFillColorSecondaryBrush"/>
                    <StaticResource x:Key="CalcButtonTextFillColorDisabledBrush" ResourceKey="TextFillColorDisabledBrush"/>
                    <Color x:Key="CalcButtonBaseFillColor">#FFFFFF</Color>
                    <SolidColorBrush x:Key="CalcButtonFillColorDefaultBrush"
                                     Opacity="1"
                                     Color="{StaticResource CalcButtonBaseFillColor}"/>
                    <SolidColorBrush x:Key="CalcButtonFillColorHoverBrush"
                                     Opacity="0.75"
                                     Color="{StaticResource CalcButtonBaseFillColor}"/>
                    <SolidColorBrush x:Key="CalcButtonFillColorPressedBrush"
                                     Opacity="0.5"
                                     Color="{StaticResource CalcButtonBaseFillColor}"/>
                    <SolidColorBrush x:Key="CalcButtonFillColorDisabledBrush"
                                     Opacity="0.125"
                                     Color="{StaticResource CalcButtonBaseFillColor}"/>
                    <SolidColorBrush x:Key="CalcButtonAltFillColorDefaultBrush"
                                     Opacity="0.5"
                                     Color="{StaticResource CalcButtonBaseFillColor}"/>
                    <SolidColorBrush x:Key="CalcButtonAltFillColorHoverBrush"
                                     Opacity="0.25"
                                     Color="{StaticResource CalcButtonBaseFillColor}"/>
                    <SolidColorBrush x:Key="CalcButtonAltFillColorPressedBrush"
                                     Opacity="0.125"
                                     Color="{StaticResource CalcButtonBaseFillColor}"/>
                    <SolidColorBrush x:Key="CalcButtonAltFillColorDisabledBrush"
                                     Opacity="0.125"
                                     Color="{StaticResource CalcButtonBaseFillColor}"/>

                    <Color x:Key="BackgroundSmokeFillColor">#000000</Color>
                    <SolidColorBrush x:Key="BackgroundSmokeFillColorBrush"
                                     Opacity="0.3"
                                     Color="{StaticResource BackgroundSmokeFillColor}"/>

                    <StaticResource x:Key="AppControlForegroundSecondaryBrush" ResourceKey="TextFillColorSecondaryBrush"/>
                    <StaticResource x:Key="AppControlBackgroundTertiaryBrush" ResourceKey="SubtleFillColorTertiaryBrush"/>
                    <StaticResource x:Key="AppControlBackgroundSecondaryBrush" ResourceKey="SubtleFillColorSecondaryBrush"/>
                </ResourceDictionary>
                <ResourceDictionary x:Key="HighContrast">
                    <Thickness x:Key="ControlBorderThemeThickness">1</Thickness>
                    <Thickness x:Key="HighContrastThicknessTop">0,1,0,0</Thickness>
                    <x:Double x:Key="HighContrastStrokeThickness">2</x:Double>
                    <SolidColorBrush x:Key="SystemControlBackgroundAltHighBrush" Color="{ThemeResource SystemColorButtonFaceColor}"/>
                    <SolidColorBrush x:Key="SystemControlBackgroundChromeMediumLowBrush" Color="{ThemeResource SystemColorButtonFaceColor}"/>
                    <SolidColorBrush x:Key="SystemControlBackgroundTransparentBrush" Color="{ThemeResource SystemColorButtonFaceColor}"/>
                    <SolidColorBrush x:Key="SystemControlHighlightTransparentBrush" Color="{ThemeResource SystemColorHighlightColor}"/>
                    <SolidColorBrush x:Key="AppBackgroundAltMediumLowBrush" Color="{ThemeResource SystemColorButtonFaceColor}"/>
                    <SolidColorBrush x:Key="AppOperatorPanelBackground" Color="{ThemeResource SystemColorButtonFaceColor}"/>
                    <SolidColorBrush x:Key="AppControlPageTextBaseMediumHighBrush" Color="{ThemeResource SystemColorWindowTextColor}"/>
                    <SolidColorBrush x:Key="AppControlHoverButtonFaceBrush" Color="{ThemeResource SystemColorHighlightColor}"/>
                    <SolidColorBrush x:Key="AppControlPressedButtonFaceBrush" Color="{ThemeResource SystemColorHighlightColor}"/>
                    <SolidColorBrush x:Key="AppControlTransparentAccentColorBrush" Color="Transparent"/>
                    <SolidColorBrush x:Key="AppControlPageTextBaseHighColorBrush" Color="{ThemeResource SystemColorWindowTextColor}"/>
                    <SolidColorBrush x:Key="OperatorPanelScrollButtonBackgroundBrush" Color="{ThemeResource SystemColorButtonFaceColor}"/>
                    <SolidColorBrush x:Key="AppControlForegroundAccentBrush" Color="{ThemeResource SystemColorButtonTextColor}"/>
                    <SolidColorBrush x:Key="AppControlPageTextRedColorBrush" Color="{ThemeResource SystemColorWindowTextColor}"/>
                    <SolidColorBrush x:Key="AppControlForegroundTransparentRevealBorderBrush" Color="{ThemeResource SystemColorButtonTextColor}"/>
                    <SolidColorBrush x:Key="AppControlHighlightAccentRevealBackgroundBrush" Color="{ThemeResource SystemColorHighlightColor}"/>
                    <SolidColorBrush x:Key="AppControlBackgroundListAccentHighRevealBackgroundBrush" Color="{ThemeResource SystemColorButtonFaceColor}"/>
                    <SolidColorBrush x:Key="AppControlListLowRevealHighlightBrush" Color="{ThemeResource SystemColorHighlightColor}"/>
                    <SolidColorBrush x:Key="AppChromeAcrylicHostBackdropMediumLowBrush" Color="{ThemeResource SystemColorWindowColor}"/>
                    <SolidColorBrush x:Key="AppChromeAcrylicOperatorFlyoutBackgroundBrush" Color="{ThemeResource SystemColorWindowColor}"/>
                    <SolidColorBrush x:Key="AppControlHighlightCalcButtonBrush" Color="{ThemeResource SystemColorWindowColor}"/>
                    <SolidColorBrush x:Key="AppControlHighlightCalcButtonToggledBrush" Color="{ThemeResource SystemColorHighlightColor}"/>
                    <SolidColorBrush x:Key="AppControlHighlightCalcButtonHoverBrush" Color="{ThemeResource SystemColorHighlightColor}"/>
                    <SolidColorBrush x:Key="AppControlTransparentButtonBackgroundBrush" Color="{ThemeResource SystemColorButtonFaceColor}"/>
                    <ApplicationTheme x:Key="CalcApplicationTheme">Dark</ApplicationTheme>
                    <SolidColorBrush x:Key="EquationBrush1" Color="{ThemeResource SystemColorGrayTextColor}"/>
                    <SolidColorBrush x:Key="EquationBrush2" Color="{ThemeResource SystemColorHighlightColor}"/>
                    <SolidColorBrush x:Key="EquationBrush3" Color="{ThemeResource SystemColorHotlightColor}"/>
                    <SolidColorBrush x:Key="EquationBrush4" Color="{ThemeResource SystemColorWindowTextColor}"/>

                    <StaticResource x:Key="CalcButtonFillColorDefaultBrush" ResourceKey="SystemControlBackgroundBaseLowBrush"/>
                    <StaticResource x:Key="CalcButtonFillColorHoverBrush" ResourceKey="SystemColorHighlightTextColorBrush"/>
                    <StaticResource x:Key="CalcButtonFillColorPressedBrush" ResourceKey="SystemColorHighlightTextColorBrush"/>
                    <StaticResource x:Key="CalcButtonFillColorDisabledBrush" ResourceKey="SystemControlBackgroundBaseLowBrush"/>
                    <StaticResource x:Key="CalcButtonTextFillColorDefaultBrush" ResourceKey="SystemColorButtonTextColorBrush"/>
                    <StaticResource x:Key="CalcButtonTextFillColorHoverBrush" ResourceKey="SystemControlHighlightBaseHighBrush"/>
                    <StaticResource x:Key="CalcButtonTextFillColorPressedBrush" ResourceKey="SystemControlHighlightBaseHighBrush"/>
                    <StaticResource x:Key="CalcButtonTextFillColorDisabledBrush" ResourceKey="SystemControlDisabledBaseMediumLowBrush"/>
                    <StaticResource x:Key="CalcButtonAltFillColorDefaultBrush" ResourceKey="SystemControlBackgroundBaseLowBrush"/>
                    <StaticResource x:Key="CalcButtonAltFillColorHoverBrush" ResourceKey="SystemColorHighlightTextColorBrush"/>
                    <StaticResource x:Key="CalcButtonAltFillColorPressedBrush" ResourceKey="SystemColorHighlightTextColorBrush"/>
                    <StaticResource x:Key="CalcButtonAltFillColorDisabledBrush" ResourceKey="SystemControlBackgroundBaseLowBrush"/>

                    <StaticResource x:Key="BackgroundSmokeFillColorBrush" ResourceKey="SystemControlBackgroundTransparentBrush"/>

                    <StaticResource x:Key="AppControlForegroundSecondaryBrush" ResourceKey="SystemColorHighlightTextColorBrush"/>
                    <StaticResource x:Key="AppControlBackgroundTertiaryBrush" ResourceKey="SystemControlBackgroundTransparentBrush"/>
                    <StaticResource x:Key="AppControlBackgroundSecondaryBrush" ResourceKey="SystemControlHighlightTransparentBrush"/>
                </ResourceDictionary>
            </ResourceDictionary.ThemeDictionaries>

            <!-- Min Window Height/Width -->
            <x:Double x:Key="AppMinWindowHeight">500</x:Double>
            <x:Double x:Key="AppMinWindowWidth">320</x:Double>

            <FontFamily x:Key="CalculatorFontFamily">ms-appx:///Assets/CalculatorIcons.ttf#Calculator Fluent Icons</FontFamily>

            <x:Double x:Key="SplitViewOpenPaneLength">256</x:Double>
            <Thickness x:Key="PivotPortraitThemePadding">0,1,0,0</Thickness>
            <x:Double x:Key="PivotHeaderItemFontSize">14</x:Double>
            <FontWeight x:Key="PivotHeaderItemThemeFontWeight">Normal</FontWeight>

            <x:Double x:Key="OperatorPanelButtonRowSizeLarge">70</x:Double>
            <x:Double x:Key="OperatorPanelFontSizeLarge">24</x:Double>
            <x:Double x:Key="OperatorPanelGlyphFontSizeLarge">24</x:Double>
            <x:Double x:Key="OperatorPanelChevronFontSizeLarge">16</x:Double>

            <x:Double x:Key="OperatorPanelButtonRowSizeMedium">70</x:Double>
            <x:Double x:Key="OperatorPanelFontSizeMedium">16</x:Double>
            <x:Double x:Key="OperatorPanelGlyphFontSizeMedium">20</x:Double>
            <x:Double x:Key="OperatorPanelChevronFontSizeMedium">10</x:Double>

            <x:Double x:Key="OperatorPanelButtonRowSizeSmall">44</x:Double>
            <x:Double x:Key="OperatorPanelFontSizeSmall">12</x:Double>
            <x:Double x:Key="OperatorPanelGlyphFontSizeSmall">16</x:Double>
            <x:Double x:Key="OperatorPanelChevronFontSizeSmall">12</x:Double>

            <x:Double x:Key="CaptionFontSize">12</x:Double>
            <x:Double x:Key="BodyFontSize">14</x:Double>
            <x:Double x:Key="TitleFontSize">24</x:Double>

            <x:Double x:Key="BodyStrongFontSize">14</x:Double>
            <FontWeight x:Key="BodyStrongFontWeight">SemiBold</FontWeight>

            <!-- Hamburger button heights -->
            <x:Double x:Key="HamburgerHeight">48</x:Double>
            <GridLength x:Key="HamburgerHeightGridLength">48</GridLength>

            <x:Double x:Key="IconOnlySubtleButtonHeight">32</x:Double>
            <x:Double x:Key="CalcButtonCaptionSize">34</x:Double>
            <x:Double x:Key="CalcButtonTextIconCaptionSize">38</x:Double>
            <x:Double x:Key="CalcStandardOperatorCaptionSizeExtraLarge">48</x:Double>
            <x:Double x:Key="CalcStandardOperatorCaptionSizeLarge">24</x:Double>
            <!-- Numpad Standard/Scientific in Fill/Full -->
            <x:Double x:Key="CalcStandardOperatorCaptionSize">20</x:Double>
            <x:Double x:Key="CalcStandardOperatorTextIconCaptionSize">22</x:Double>
            <x:Double x:Key="CalcStandardOperatorCaptionSizeSmall">15</x:Double>
            <x:Double x:Key="CalcStandardOperatorCaptionSizeTiny">12</x:Double>
            <!-- Standard Operators Standard/Scientific in Fill/Full -->
            <x:Double x:Key="CalcOperatorCaptionSize">14</x:Double>
            <x:Double x:Key="CalcOperatorCaptionSizeSmall">12</x:Double>
            <x:Double x:Key="CalcOperatorTextIconCaptionSize">16</x:Double>

            <!-- White and black color brushes used for the Utils::GetContrastColor -->
            <SolidColorBrush x:Key="WhiteBrush" Color="White"/>
            <SolidColorBrush x:Key="BlackBrush" Color="Black"/>

            <primitives:CornerRadiusFilterConverter x:Key="CornerRadiusTopLeftToDoubleConverter" Filter="TopLeftValue"/>

            <!-- Base style for calc buttons -->
            <Style x:Key="CalcButtonStyle"
                   BasedOn="{StaticResource DefaultButtonStyle}"
                   TargetType="Controls:CalculatorButton">
                <Setter Property="MinWidth" Value="24"/>
                <Setter Property="Padding" Value="0"/>
                <Setter Property="MinHeight" Value="12"/>
                <Setter Property="Margin" Value="1"/>
                <Setter Property="Background" Value="{ThemeResource CalcButtonFillColorDefaultBrush}"/>
                <Setter Property="Foreground" Value="{ThemeResource CalcButtonTextFillColorDefaultBrush}"/>
                <Setter Property="HoverBackground" Value="{ThemeResource CalcButtonFillColorHoverBrush}"/>
                <Setter Property="HoverForeground" Value="{ThemeResource CalcButtonTextFillColorHoverBrush}"/>
                <Setter Property="PressBackground" Value="{ThemeResource CalcButtonFillColorPressedBrush}"/>
                <Setter Property="PressForeground" Value="{ThemeResource CalcButtonTextFillColorPressedBrush}"/>
                <Setter Property="DisabledBackground" Value="{ThemeResource CalcButtonFillColorDisabledBrush}"/>
                <Setter Property="DisabledForeground" Value="{ThemeResource CalcButtonTextFillColorDisabledBrush}"/>
                <Setter Property="HorizontalAlignment" Value="Stretch"/>
                <Setter Property="VerticalAlignment" Value="Stretch"/>
                <Setter Property="FontSize" Value="{StaticResource CalcButtonCaptionSize}"/>
                <Setter Property="FontWeight" Value="Normal"/>
                <Setter Property="HorizontalContentAlignment" Value="Center"/>
                <Setter Property="VerticalContentAlignment" Value="Center"/>
                <Setter Property="Template">
                    <Setter.Value>
                        <ControlTemplate TargetType="Button">
                            <ContentPresenter x:Name="ContentPresenter"
                                              Padding="{TemplateBinding Padding}"
                                              HorizontalContentAlignment="{TemplateBinding HorizontalContentAlignment}"
                                              VerticalContentAlignment="{TemplateBinding VerticalContentAlignment}"
                                              Background="{TemplateBinding Background}"
                                              BorderBrush="{TemplateBinding BorderBrush}"
                                              BorderThickness="{TemplateBinding BorderThickness}"
                                              contract7NotPresent:CornerRadius="{ThemeResource ControlCornerRadius}"
                                              contract7Present:BackgroundSizing="{TemplateBinding BackgroundSizing}"
                                              contract7Present:CornerRadius="{TemplateBinding CornerRadius}"
                                              muxc:AnimatedIcon.State="Normal"
                                              AutomationProperties.AccessibilityView="Raw"
                                              Content="{TemplateBinding Content}"
                                              ContentTemplate="{TemplateBinding ContentTemplate}"
                                              ContentTransitions="{TemplateBinding ContentTransitions}">

                                <contract7Present:ContentPresenter.BackgroundTransition>
                                    <contract7Present:BrushTransition Duration="0:0:0.083"/>
                                </contract7Present:ContentPresenter.BackgroundTransition>

                                <VisualStateManager.VisualStateGroups>
                                    <VisualStateGroup x:Name="CommonStates">
                                        <VisualState x:Name="Normal"/>

                                        <VisualState x:Name="PointerOver">
                                            <Storyboard>
                                                <ObjectAnimationUsingKeyFrames Storyboard.TargetName="ContentPresenter" Storyboard.TargetProperty="Background">
                                                    <DiscreteObjectKeyFrame KeyTime="0" Value="{Binding HoverBackground, RelativeSource={RelativeSource TemplatedParent}}"/>
                                                </ObjectAnimationUsingKeyFrames>
                                                <ObjectAnimationUsingKeyFrames Storyboard.TargetName="ContentPresenter" Storyboard.TargetProperty="BorderBrush">
                                                    <DiscreteObjectKeyFrame KeyTime="0" Value="{ThemeResource ButtonBorderBrushPointerOver}"/>
                                                </ObjectAnimationUsingKeyFrames>
                                                <ObjectAnimationUsingKeyFrames Storyboard.TargetName="ContentPresenter" Storyboard.TargetProperty="Foreground">
                                                    <DiscreteObjectKeyFrame KeyTime="0" Value="{Binding HoverForeground, RelativeSource={RelativeSource TemplatedParent}}"/>
                                                </ObjectAnimationUsingKeyFrames>
                                            </Storyboard>
                                            <VisualState.Setters>
                                                <Setter Target="ContentPresenter.(muxc:AnimatedIcon.State)" Value="PointerOver"/>
                                            </VisualState.Setters>
                                        </VisualState>

                                        <VisualState x:Name="Pressed">
                                            <Storyboard>
                                                <ObjectAnimationUsingKeyFrames Storyboard.TargetName="ContentPresenter" Storyboard.TargetProperty="Background">
                                                    <DiscreteObjectKeyFrame KeyTime="0" Value="{Binding PressBackground, RelativeSource={RelativeSource TemplatedParent}}"/>
                                                </ObjectAnimationUsingKeyFrames>
                                                <ObjectAnimationUsingKeyFrames Storyboard.TargetName="ContentPresenter" Storyboard.TargetProperty="BorderBrush">
                                                    <DiscreteObjectKeyFrame KeyTime="0" Value="{ThemeResource ButtonBorderBrushPressed}"/>
                                                </ObjectAnimationUsingKeyFrames>
                                                <ObjectAnimationUsingKeyFrames Storyboard.TargetName="ContentPresenter" Storyboard.TargetProperty="Foreground">
                                                    <DiscreteObjectKeyFrame KeyTime="0" Value="{Binding PressForeground, RelativeSource={RelativeSource TemplatedParent}}"/>
                                                </ObjectAnimationUsingKeyFrames>
                                            </Storyboard>
                                            <VisualState.Setters>
                                                <Setter Target="ContentPresenter.(muxc:AnimatedIcon.State)" Value="Pressed"/>
                                            </VisualState.Setters>
                                        </VisualState>

                                        <VisualState x:Name="Disabled">
                                            <Storyboard>
                                                <ObjectAnimationUsingKeyFrames Storyboard.TargetName="ContentPresenter" Storyboard.TargetProperty="Background">
                                                    <DiscreteObjectKeyFrame KeyTime="0" Value="{Binding DisabledBackground, RelativeSource={RelativeSource TemplatedParent}}"/>
                                                </ObjectAnimationUsingKeyFrames>
                                                <ObjectAnimationUsingKeyFrames Storyboard.TargetName="ContentPresenter" Storyboard.TargetProperty="BorderBrush">
                                                    <DiscreteObjectKeyFrame KeyTime="0" Value="{ThemeResource ButtonBorderBrushDisabled}"/>
                                                </ObjectAnimationUsingKeyFrames>
                                                <ObjectAnimationUsingKeyFrames Storyboard.TargetName="ContentPresenter" Storyboard.TargetProperty="Foreground">
                                                    <DiscreteObjectKeyFrame KeyTime="0" Value="{Binding DisabledForeground, RelativeSource={RelativeSource TemplatedParent}}"/>
                                                </ObjectAnimationUsingKeyFrames>
                                            </Storyboard>
                                            <VisualState.Setters>
                                                <!-- DisabledVisual Should be handled by the control, not the animated icon. -->
                                                <Setter Target="ContentPresenter.(muxc:AnimatedIcon.State)" Value="Normal"/>
                                            </VisualState.Setters>
                                        </VisualState>
                                    </VisualStateGroup>
                                </VisualStateManager.VisualStateGroups>
                            </ContentPresenter>
                        </ControlTemplate>
                    </Setter.Value>
                </Setter>
            </Style>

            <Style x:Key="NumericButtonStyle"
                   BasedOn="{StaticResource CalcButtonStyle}"
                   TargetType="Controls:CalculatorButton">
                <Setter Property="FontWeight" Value="Normal"/>
            </Style>
            <Style x:Key="NumericButtonStyle10"
                   BasedOn="{StaticResource NumericButtonStyle}"
                   TargetType="Controls:CalculatorButton">
                <Setter Property="FontSize" Value="10"/>
            </Style>
            <Style x:Key="NumericButtonStyle12"
                   BasedOn="{StaticResource NumericButtonStyle}"
                   TargetType="Controls:CalculatorButton">
                <Setter Property="FontSize" Value="12"/>
            </Style>
            <Style x:Key="NumericButtonStyle14"
                   BasedOn="{StaticResource NumericButtonStyle}"
                   TargetType="Controls:CalculatorButton">
                <Setter Property="FontSize" Value="14"/>
            </Style>
            <Style x:Key="NumericButtonStyle16"
                   BasedOn="{StaticResource NumericButtonStyle}"
                   TargetType="Controls:CalculatorButton">
                <Setter Property="FontSize" Value="16"/>
            </Style>
            <Style x:Key="NumericButtonStyle18"
                   BasedOn="{StaticResource NumericButtonStyle}"
                   TargetType="Controls:CalculatorButton">
                <Setter Property="FontSize" Value="18"/>
            </Style>
            <Style x:Key="NumericButtonStyle24"
                   BasedOn="{StaticResource NumericButtonStyle}"
                   TargetType="Controls:CalculatorButton">
                <Setter Property="FontSize" Value="24"/>
            </Style>
            <Style x:Key="NumericButtonStyle28"
                   BasedOn="{StaticResource NumericButtonStyle}"
                   TargetType="Controls:CalculatorButton">
                <Setter Property="FontSize" Value="28"/>
            </Style>
            <Style x:Key="NumericButtonStyle34"
                   BasedOn="{StaticResource NumericButtonStyle}"
                   TargetType="Controls:CalculatorButton">
                <Setter Property="FontSize" Value="34"/>
            </Style>
            <Style x:Key="NumericButtonStyle38"
                   BasedOn="{StaticResource NumericButtonStyle}"
                   TargetType="Controls:CalculatorButton">
                <Setter Property="FontSize" Value="38"/>
            </Style>
            <Style x:Key="NumericButtonStyle46"
                   BasedOn="{StaticResource NumericButtonStyle}"
                   TargetType="Controls:CalculatorButton">
                <Setter Property="FontSize" Value="46"/>
            </Style>
            <Style x:Key="NumericButtonStyle48"
                   BasedOn="{StaticResource NumericButtonStyle}"
                   TargetType="Controls:CalculatorButton">
                <Setter Property="FontSize" Value="48"/>
            </Style>

            <Style x:Key="SymbolOperatorKeypadButtonStyle"
                   BasedOn="{StaticResource NumericButtonStyle}"
                   TargetType="Controls:CalculatorButton">
                <Setter Property="FontFamily" Value="{StaticResource CalculatorFontFamily}"/>
                <Setter Property="FontSize" Value="{StaticResource CalcOperatorCaptionSize}"/>
            </Style>

            <Style x:Key="OperatorButtonStyle"
                   BasedOn="{StaticResource CalcButtonStyle}"
                   TargetType="Controls:CalculatorButton">
                <Setter Property="Background" Value="{ThemeResource CalcButtonAltFillColorDefaultBrush}"/>
                <Setter Property="HoverBackground" Value="{ThemeResource CalcButtonAltFillColorHoverBrush}"/>
                <Setter Property="PressBackground" Value="{ThemeResource CalcButtonAltFillColorPressedBrush}"/>
                <Setter Property="DisabledBackground" Value="{ThemeResource CalcButtonAltFillColorDisabledBrush}"/>
                <Setter Property="FontSize" Value="{StaticResource CalcOperatorCaptionSize}"/>
            </Style>

            <Style x:Key="SymbolOperatorButtonStyle"
                   BasedOn="{StaticResource OperatorButtonStyle}"
                   TargetType="Controls:CalculatorButton">
                <Setter Property="FontFamily" Value="{StaticResource CalculatorFontFamily}"/>
            </Style>

            <Style x:Key="ParenthesisCalcButtonStyle"
                   BasedOn="{StaticResource OperatorButtonStyle}"
                   TargetType="Controls:CalculatorButton">
                <Setter Property="Template">
                    <Setter.Value>
                        <ControlTemplate TargetType="Controls:CalculatorButton">
                            <Grid x:Name="RootGrid">
                                <VisualStateManager.VisualStateGroups>
                                    <VisualStateGroup x:Name="CommonStates">
                                        <VisualState x:Name="Normal"/>

                                        <VisualState x:Name="PointerOver">
                                            <Storyboard>
                                                <ObjectAnimationUsingKeyFrames Storyboard.TargetName="ContentPresenter" Storyboard.TargetProperty="Background">
                                                    <DiscreteObjectKeyFrame KeyTime="0" Value="{Binding HoverBackground, RelativeSource={RelativeSource TemplatedParent}}"/>
                                                </ObjectAnimationUsingKeyFrames>
                                                <ObjectAnimationUsingKeyFrames Storyboard.TargetName="ContentPresenter" Storyboard.TargetProperty="BorderBrush">
                                                    <DiscreteObjectKeyFrame KeyTime="0" Value="{ThemeResource ButtonBorderBrushPointerOver}"/>
                                                </ObjectAnimationUsingKeyFrames>
                                                <ObjectAnimationUsingKeyFrames Storyboard.TargetName="ContentPresenter" Storyboard.TargetProperty="Foreground">
                                                    <DiscreteObjectKeyFrame KeyTime="0" Value="{Binding HoverForeground, RelativeSource={RelativeSource TemplatedParent}}"/>
                                                </ObjectAnimationUsingKeyFrames>
                                                <ObjectAnimationUsingKeyFrames Storyboard.TargetName="ParenthesisCount" Storyboard.TargetProperty="Foreground">
                                                    <DiscreteObjectKeyFrame KeyTime="0" Value="{Binding HoverForeground, RelativeSource={RelativeSource TemplatedParent}}"/>
                                                </ObjectAnimationUsingKeyFrames>
                                            </Storyboard>
                                            <VisualState.Setters>
                                                <Setter Target="ContentPresenter.(muxc:AnimatedIcon.State)" Value="PointerOver"/>
                                            </VisualState.Setters>
                                        </VisualState>

                                        <VisualState x:Name="Pressed">
                                            <Storyboard>
                                                <ObjectAnimationUsingKeyFrames Storyboard.TargetName="ContentPresenter" Storyboard.TargetProperty="Background">
                                                    <DiscreteObjectKeyFrame KeyTime="0" Value="{Binding PressBackground, RelativeSource={RelativeSource TemplatedParent}}"/>
                                                </ObjectAnimationUsingKeyFrames>
                                                <ObjectAnimationUsingKeyFrames Storyboard.TargetName="ContentPresenter" Storyboard.TargetProperty="BorderBrush">
                                                    <DiscreteObjectKeyFrame KeyTime="0" Value="{ThemeResource ButtonBorderBrushPressed}"/>
                                                </ObjectAnimationUsingKeyFrames>
                                                <ObjectAnimationUsingKeyFrames Storyboard.TargetName="ContentPresenter" Storyboard.TargetProperty="Foreground">
                                                    <DiscreteObjectKeyFrame KeyTime="0" Value="{Binding PressForeground, RelativeSource={RelativeSource TemplatedParent}}"/>
                                                </ObjectAnimationUsingKeyFrames>
                                                <ObjectAnimationUsingKeyFrames Storyboard.TargetName="ParenthesisCount" Storyboard.TargetProperty="Foreground">
                                                    <DiscreteObjectKeyFrame KeyTime="0" Value="{Binding PressForeground, RelativeSource={RelativeSource TemplatedParent}}"/>
                                                </ObjectAnimationUsingKeyFrames>
                                            </Storyboard>
                                            <VisualState.Setters>
                                                <Setter Target="ContentPresenter.(muxc:AnimatedIcon.State)" Value="Pressed"/>
                                            </VisualState.Setters>
                                        </VisualState>

                                        <VisualState x:Name="Disabled">
                                            <Storyboard>
                                                <ObjectAnimationUsingKeyFrames Storyboard.TargetName="ContentPresenter" Storyboard.TargetProperty="Background">
                                                    <DiscreteObjectKeyFrame KeyTime="0" Value="{Binding DisabledBackground, RelativeSource={RelativeSource TemplatedParent}}"/>
                                                </ObjectAnimationUsingKeyFrames>
                                                <ObjectAnimationUsingKeyFrames Storyboard.TargetName="ContentPresenter" Storyboard.TargetProperty="BorderBrush">
                                                    <DiscreteObjectKeyFrame KeyTime="0" Value="{ThemeResource ButtonBorderBrushDisabled}"/>
                                                </ObjectAnimationUsingKeyFrames>
                                                <ObjectAnimationUsingKeyFrames Storyboard.TargetName="ContentPresenter" Storyboard.TargetProperty="Foreground">
                                                    <DiscreteObjectKeyFrame KeyTime="0" Value="{Binding DisabledForeground, RelativeSource={RelativeSource TemplatedParent}}"/>
                                                </ObjectAnimationUsingKeyFrames>
                                                <ObjectAnimationUsingKeyFrames Storyboard.TargetName="ParenthesisCount" Storyboard.TargetProperty="Foreground">
                                                    <DiscreteObjectKeyFrame KeyTime="0" Value="{Binding DisabledForeground, RelativeSource={RelativeSource TemplatedParent}}"/>
                                                </ObjectAnimationUsingKeyFrames>
                                            </Storyboard>
                                            <VisualState.Setters>
                                                <!-- DisabledVisual Should be handled by the control, not the animated icon. -->
                                                <Setter Target="ContentPresenter.(muxc:AnimatedIcon.State)" Value="Normal"/>
                                            </VisualState.Setters>
                                        </VisualState>
                                    </VisualStateGroup>
                                </VisualStateManager.VisualStateGroups>

                                <ContentPresenter x:Name="ContentPresenter"
                                                  Padding="{TemplateBinding Padding}"
                                                  HorizontalContentAlignment="{TemplateBinding HorizontalContentAlignment}"
                                                  VerticalContentAlignment="{TemplateBinding VerticalContentAlignment}"
                                                  Background="{TemplateBinding Background}"
                                                  BorderBrush="{TemplateBinding BorderBrush}"
                                                  BorderThickness="{TemplateBinding BorderThickness}"
                                                  contract7NotPresent:CornerRadius="{ThemeResource ControlCornerRadius}"
                                                  contract7Present:BackgroundSizing="{TemplateBinding BackgroundSizing}"
                                                  contract7Present:CornerRadius="{TemplateBinding CornerRadius}"
                                                  muxc:AnimatedIcon.State="Normal"
                                                  AutomationProperties.AccessibilityView="Raw"
                                                  Content="{TemplateBinding Content}"
                                                  ContentTemplate="{TemplateBinding ContentTemplate}"
                                                  ContentTransitions="{TemplateBinding ContentTransitions}">

                                    <contract7Present:ContentPresenter.BackgroundTransition>
                                        <contract7Present:BrushTransition Duration="0:0:0.083"/>
                                    </contract7Present:ContentPresenter.BackgroundTransition>
                                </ContentPresenter>
                                <TextBlock x:Name="ParenthesisCount"
                                           Margin="18,8,0,-8"
                                           HorizontalAlignment="Center"
                                           VerticalAlignment="Center"
                                           FontSize="{TemplateBinding MinHeight}"
                                           FontWeight="SemiBold"
                                           AutomationProperties.AccessibilityView="Raw"
                                           Text="{TemplateBinding Tag}"/>
                            </Grid>
                        </ControlTemplate>
                    </Setter.Value>
                </Setter>
            </Style>

            <Style x:Key="AccentEmphasizedCalcButtonStyle"
                   BasedOn="{StaticResource AccentButtonStyle}"
                   TargetType="Controls:CalculatorButton">
                <Setter Property="MinWidth" Value="24"/>
                <Setter Property="MinHeight" Value="12"/>
                <Setter Property="Padding" Value="0"/>
                <Setter Property="Margin" Value="1"/>
                <Setter Property="HorizontalAlignment" Value="Stretch"/>
                <Setter Property="VerticalAlignment" Value="Stretch"/>
                <Setter Property="HorizontalContentAlignment" Value="Center"/>
                <Setter Property="VerticalContentAlignment" Value="Center"/>
                <Setter Property="FontWeight" Value="Normal"/>
                <Setter Property="FontFamily" Value="{StaticResource CalculatorFontFamily}"/>
                <Setter Property="FontSize" Value="{StaticResource CalcOperatorCaptionSize}"/>
                <Setter Property="BorderBrush" Value="{ThemeResource SystemControlForegroundTransparentBrush}"/>
                <Setter Property="BorderThickness" Value="{ThemeResource ControlBorderThemeThickness}"/>
            </Style>

            <Style x:Key="EmphasizedCalcButtonStyle"
                   BasedOn="{StaticResource SymbolOperatorButtonStyle}"
                   TargetType="Controls:CalculatorButton">
                <Setter Property="HoverBackground" Value="{ThemeResource AppControlHighlightCalcButtonHoverBrush}"/>
                <Setter Property="HoverForeground" Value="{ThemeResource SystemControlForegroundBaseHighBrush}"/>
                <Setter Property="PressBackground" Value="{ThemeResource SystemControlHighlightAccentBrush}"/>
                <Setter Property="PressForeground" Value="{ThemeResource SystemControlForegroundBaseHighBrush}"/>
            </Style>

            <Style x:Key="AccentEmphasizedOperatorCalcButtonStyle"
                   BasedOn="{StaticResource OperatorButtonStyle}"
                   TargetType="Controls:CalculatorButton">
                <Setter Property="HoverBackground" Value="{ThemeResource AppControlHighlightCalcButtonHoverBrush}"/>
                <Setter Property="HoverForeground" Value="{ThemeResource SystemControlForegroundBaseHighBrush}"/>
                <Setter Property="PressBackground" Value="{ThemeResource SystemControlHighlightAccentBrush}"/>
                <Setter Property="PressForeground" Value="{ThemeResource SystemControlForegroundBaseHighBrush}"/>
                <Setter Property="Background" Value="{ThemeResource AppControlHighlightCalcButtonBrush}"/>
            </Style>

            <!-- RESULTS -->
            <Style x:Key="ResultsScroller" TargetType="ScrollViewer">
                <Setter Property="HorizontalScrollMode" Value="Disabled"/>
                <Setter Property="VerticalScrollMode" Value="Disabled"/>
                <Setter Property="VerticalScrollBarVisibility" Value="Disabled"/>
                <Setter Property="HorizontalScrollBarVisibility" Value="Disabled"/>
                <Setter Property="IsHitTestVisible" Value="True"/>
                <Setter Property="HorizontalAlignment" Value="Stretch"/>
                <Setter Property="HorizontalContentAlignment" Value="Right"/>
            </Style>
            <Style x:Key="ResultsScrollerSnapped"
                   BasedOn="{StaticResource ResultsScroller}"
                   TargetType="ScrollViewer">
                <Setter Property="HorizontalScrollMode" Value="Enabled"/>
                <Setter Property="HorizontalScrollBarVisibility" Value="Hidden"/>
                <Setter Property="IsHitTestVisible" Value="True"/>
                <Setter Property="ZoomMode" Value="Disabled"/>
            </Style>

            <Style x:Key="CalculationResultStyle" TargetType="Controls:CalculationResult">
                <Setter Property="Background" Value="Transparent"/>
                <Setter Property="Foreground" Value="{ThemeResource SystemControlPageTextBaseHighBrush}"/>
                <Setter Property="HorizontalAlignment" Value="Stretch"/>
                <Setter Property="VerticalAlignment" Value="Stretch"/>
                <Setter Property="HorizontalContentAlignment" Value="Right"/>
                <Setter Property="VerticalContentAlignment" Value="Top"/>
                <Setter Property="IsTextScaleFactorEnabled" Value="False"/>
                <Setter Property="UseSystemFocusVisuals" Value="True"/>
                <Setter Property="Template">
                    <Setter.Value>
                        <ControlTemplate TargetType="Controls:CalculationResult">
                            <Grid x:Name="Border"
                                  Background="{TemplateBinding Background}"
                                  FlowDirection="LeftToRight">
                                <Grid.ColumnDefinitions>
                                    <ColumnDefinition Width="12"/>
                                    <ColumnDefinition/>
                                    <ColumnDefinition Width="12"/>
                                </Grid.ColumnDefinitions>
                                <VisualStateManager.VisualStateGroups>
                                    <VisualStateGroup x:Name="ActiveStates">
                                        <VisualState x:Name="Active">
                                            <VisualState.Setters>
                                                <Setter Target="NormalOutput.FontWeight" Value="SemiBold"/>
                                                <Setter Target="NormalOutput.IsTextSelectionEnabled" Value="True"/>
                                            </VisualState.Setters>
                                        </VisualState>
                                        <VisualState x:Name="Normal"/>
                                    </VisualStateGroup>
                                </VisualStateManager.VisualStateGroups>
                                <ScrollViewer x:Name="TextContainer"
                                              Grid.Column="1"
                                              Padding="0,0,0,0"
                                              Style="{ThemeResource ResultsScrollerSnapped}"
                                              AutomationProperties.AccessibilityView="Raw">
                                    <TextBlock x:Name="NormalOutput"
                                               Margin="{TemplateBinding DisplayMargin}"
                                               HorizontalAlignment="{TemplateBinding HorizontalContentAlignment}"
                                               VerticalAlignment="{TemplateBinding VerticalContentAlignment}"
                                               Foreground="{TemplateBinding Foreground}"
                                               FontSize="{TemplateBinding FontSize}"
                                               FontWeight="Light"
                                               AutomationProperties.AccessibilityView="Raw"
                                               Text="{TemplateBinding DisplayValue}"
                                               TextAlignment="{TemplateBinding HorizontalContentAlignment}"
                                               TextWrapping="NoWrap"/>
                                </ScrollViewer>
                                <HyperlinkButton x:Name="ScrollLeft"
                                                 Grid.Column="0"
                                                 Width="20"
                                                 MinWidth="20"
                                                 MinHeight="24"
                                                 Margin="-4,0,-4,0"
                                                 Padding="0,-3,0,4"
                                                 VerticalAlignment="Top"
                                                 HorizontalContentAlignment="Center"
                                                 VerticalContentAlignment="Center"
                                                 Foreground="{ThemeResource SystemControlForegroundAccentBrush}"
                                                 BorderThickness="0"
                                                 AutomationProperties.AutomationId="CalculationResultScrollLeft"
                                                 AutomationProperties.Name="{utils:ResourceString Name=CalculationResultScrollLeft/[using:Windows.UI.Xaml.Automation]AutomationProperties/Name}"
                                                 Visibility="Collapsed">
                                    <FontIcon x:Name="ScrollLeftText"
                                              FontFamily="{ThemeResource CalculatorFontFamily}"
                                              FontSize="12"
                                              Glyph="&#xE96F;"/>
                                </HyperlinkButton>
                                <HyperlinkButton x:Name="ScrollRight"
                                                 Grid.Column="2"
                                                 Width="20"
                                                 MinWidth="20"
                                                 MinHeight="24"
                                                 Margin="-4,0,-4,0"
                                                 Padding="0,-3,0,4"
                                                 VerticalAlignment="Top"
                                                 HorizontalContentAlignment="Center"
                                                 VerticalContentAlignment="Center"
                                                 Foreground="{ThemeResource SystemControlForegroundAccentBrush}"
                                                 BorderThickness="0"
                                                 AutomationProperties.AutomationId="CalculationResultScrollRight"
                                                 AutomationProperties.Name="{utils:ResourceString Name=CalculationResultScrollRight/[using:Windows.UI.Xaml.Automation]AutomationProperties/Name}"
                                                 Visibility="Collapsed">
                                    <FontIcon x:Name="ScrollRightText"
                                              FontFamily="{ThemeResource CalculatorFontFamily}"
                                              FontSize="12"
                                              Glyph="&#xE970;"/>
                                </HyperlinkButton>
                            </Grid>
                        </ControlTemplate>
                    </Setter.Value>
                </Setter>
            </Style>
            <contract7NotPresent:Style x:Key="ConditionalCalculationResultStyle"
                                       BasedOn="{StaticResource CalculationResultStyle}"
                                       TargetType="Controls:CalculationResult"/>
            <contract7Present:Style x:Key="ConditionalCalculationResultStyle"
                                    BasedOn="{StaticResource CalculationResultStyle}"
                                    TargetType="Controls:CalculationResult">
                <Setter Property="CornerRadius" Value="{ThemeResource ControlCornerRadius}"/>
            </contract7Present:Style>

            <Style x:Key="OperatorPanelButtonSmallStyle"
                   BasedOn="{StaticResource OperatorPanelButtonStyle}"
                   TargetType="Controls:OperatorPanelButton">
                <Setter Property="FontSize" Value="{StaticResource OperatorPanelFontSizeSmall}"/>
                <Setter Property="GlyphFontSize" Value="{StaticResource OperatorPanelGlyphFontSizeSmall}"/>
                <Setter Property="ChevronFontSize" Value="{StaticResource OperatorPanelChevronFontSizeSmall}"/>
            </Style>

            <Style x:Key="OperatorPanelButtonMediumStyle"
                   BasedOn="{StaticResource OperatorPanelButtonStyle}"
                   TargetType="Controls:OperatorPanelButton">
                <Setter Property="FontSize" Value="{StaticResource OperatorPanelFontSizeMedium}"/>
                <Setter Property="GlyphFontSize" Value="{StaticResource OperatorPanelGlyphFontSizeMedium}"/>
                <Setter Property="ChevronFontSize" Value="{StaticResource OperatorPanelChevronFontSizeMedium}"/>
            </Style>

            <Style x:Key="OperatorPanelButtonLargeStyle"
                   BasedOn="{StaticResource OperatorPanelButtonStyle}"
                   TargetType="Controls:OperatorPanelButton">
                <Setter Property="FontSize" Value="{StaticResource OperatorPanelFontSizeLarge}"/>
                <Setter Property="GlyphFontSize" Value="{StaticResource OperatorPanelGlyphFontSizeLarge}"/>
                <Setter Property="ChevronFontSize" Value="{StaticResource OperatorPanelChevronFontSizeLarge}"/>
            </Style>

            <Style x:Key="OperatorPanelButtonStyle"
                   BasedOn="{StaticResource DefaultToggleButtonStyle}"
                   TargetType="Controls:OperatorPanelButton">
                <Setter Property="FontSize" Value="{StaticResource CaptionFontSize}"/>
                <Setter Property="GlyphFontSize" Value="{StaticResource CaptionFontSize}"/>
                <Setter Property="ChevronFontSize" Value="{StaticResource CaptionFontSize}"/>
                <Setter Property="Padding" Value="8,0,8,0"/>
                <Setter Property="MinWidth" Value="32"/>
                <Setter Property="HorizontalAlignment" Value="Left"/>
                <Setter Property="VerticalAlignment" Value="Stretch"/>
                <Setter Property="HorizontalContentAlignment" Value="Center"/>
                <Setter Property="VerticalContentAlignment" Value="Center"/>
                <Setter Property="Foreground" Value="{ThemeResource ToggleButtonForeground}"/>
                <Setter Property="Background" Value="{ThemeResource SubtleFillColorTransparentBrush}"/>
                <Setter Property="BorderThickness" Value="{ThemeResource ControlBorderThemeThickness}"/>
                <Setter Property="Margin" Value="1"/>
                <Setter Property="Template">
                    <Setter.Value>
                        <ControlTemplate TargetType="Controls:OperatorPanelButton">
                            <ContentPresenter x:Name="ContentPresenter"
                                              Padding="{TemplateBinding Padding}"
                                              HorizontalAlignment="{TemplateBinding HorizontalAlignment}"
                                              VerticalAlignment="{TemplateBinding VerticalAlignment}"
                                              HorizontalContentAlignment="{TemplateBinding HorizontalContentAlignment}"
                                              VerticalContentAlignment="{TemplateBinding VerticalContentAlignment}"
                                              Background="{TemplateBinding Background}"
                                              BorderBrush="{TemplateBinding BorderBrush}"
                                              BorderThickness="{TemplateBinding BorderThickness}"
                                              FontSize="{TemplateBinding FontSize}"
                                              FontWeight="Normal"
                                              contract7NotPresent:CornerRadius="{ThemeResource ControlCornerRadius}"
                                              contract7Present:BackgroundSizing="{TemplateBinding BackgroundSizing}"
                                              contract7Present:CornerRadius="{TemplateBinding CornerRadius}"
                                              AutomationProperties.AccessibilityView="Raw"
                                              ContentTemplate="{TemplateBinding ContentTemplate}"
                                              ContentTransitions="{TemplateBinding ContentTransitions}">

                                <contract7Present:ContentPresenter.BackgroundTransition>
                                    <contract7Present:BrushTransition Duration="0:0:0.083"/>
                                </contract7Present:ContentPresenter.BackgroundTransition>

                                <VisualStateManager.VisualStateGroups>
                                    <VisualStateGroup x:Name="CommonStates">
                                        <VisualState x:Name="Normal"/>

                                        <VisualState x:Name="PointerOver">
                                            <Storyboard>
                                                <ObjectAnimationUsingKeyFrames Storyboard.TargetName="ContentPresenter" Storyboard.TargetProperty="Background">
                                                    <DiscreteObjectKeyFrame KeyTime="0" Value="{ThemeResource SubtleFillColorSecondaryBrush}"/>
                                                </ObjectAnimationUsingKeyFrames>
                                                <ObjectAnimationUsingKeyFrames Storyboard.TargetName="ContentPresenter" Storyboard.TargetProperty="BorderBrush">
                                                    <DiscreteObjectKeyFrame KeyTime="0" Value="{ThemeResource ToggleButtonBorderBrushPointerOver}"/>
                                                </ObjectAnimationUsingKeyFrames>
                                                <ObjectAnimationUsingKeyFrames Storyboard.TargetName="ContentPresenter" Storyboard.TargetProperty="Foreground">
                                                    <DiscreteObjectKeyFrame KeyTime="0" Value="{ThemeResource TextFillColorPrimaryBrush}"/>
                                                </ObjectAnimationUsingKeyFrames>
                                            </Storyboard>
                                        </VisualState>
                                        <VisualState x:Name="Pressed">
                                            <Storyboard>
                                                <ObjectAnimationUsingKeyFrames Storyboard.TargetName="ContentPresenter" Storyboard.TargetProperty="Background">
                                                    <DiscreteObjectKeyFrame KeyTime="0" Value="{ThemeResource SubtleFillColorTertiaryBrush}"/>
                                                </ObjectAnimationUsingKeyFrames>
                                                <ObjectAnimationUsingKeyFrames Storyboard.TargetName="ContentPresenter" Storyboard.TargetProperty="BorderBrush">
                                                    <DiscreteObjectKeyFrame KeyTime="0" Value="{ThemeResource ToggleButtonBorderBrushPressed}"/>
                                                </ObjectAnimationUsingKeyFrames>
                                                <ObjectAnimationUsingKeyFrames Storyboard.TargetName="ContentPresenter" Storyboard.TargetProperty="Foreground">
                                                    <DiscreteObjectKeyFrame KeyTime="0" Value="{ThemeResource TextFillColorSecondaryBrush}"/>
                                                </ObjectAnimationUsingKeyFrames>
                                            </Storyboard>
                                        </VisualState>
                                        <VisualState x:Name="Disabled">
                                            <Storyboard>
                                                <ObjectAnimationUsingKeyFrames Storyboard.TargetName="ContentPresenter" Storyboard.TargetProperty="Background">
                                                    <DiscreteObjectKeyFrame KeyTime="0" Value="{ThemeResource SubtleFillColorDisabledBrush}"/>
                                                </ObjectAnimationUsingKeyFrames>
                                                <ObjectAnimationUsingKeyFrames Storyboard.TargetName="ContentPresenter" Storyboard.TargetProperty="Foreground">
                                                    <DiscreteObjectKeyFrame KeyTime="0" Value="{ThemeResource TextFillColorDisabledBrush}"/>
                                                </ObjectAnimationUsingKeyFrames>
                                                <ObjectAnimationUsingKeyFrames Storyboard.TargetName="ContentPresenter" Storyboard.TargetProperty="BorderBrush">
                                                    <DiscreteObjectKeyFrame KeyTime="0" Value="{ThemeResource ToggleButtonBorderBrushDisabled}"/>
                                                </ObjectAnimationUsingKeyFrames>
                                            </Storyboard>
                                        </VisualState>
                                        <VisualState x:Name="Checked">
                                            <Storyboard>
                                                <ObjectAnimationUsingKeyFrames Storyboard.TargetName="ContentPresenter" Storyboard.TargetProperty="Background">
                                                    <DiscreteObjectKeyFrame KeyTime="0" Value="{ThemeResource AppControlBackgroundTertiaryBrush}"/>
                                                </ObjectAnimationUsingKeyFrames>
                                                <ObjectAnimationUsingKeyFrames Storyboard.TargetName="ContentPresenter" Storyboard.TargetProperty="Foreground">
                                                    <DiscreteObjectKeyFrame KeyTime="0" Value="{ThemeResource TextFillColorSecondaryBrush}"/>
                                                </ObjectAnimationUsingKeyFrames>
                                                <ObjectAnimationUsingKeyFrames Storyboard.TargetName="ContentPresenter" Storyboard.TargetProperty="BorderBrush">
                                                    <DiscreteObjectKeyFrame KeyTime="0" Value="{ThemeResource ToggleButtonBorderBrush}"/>
                                                </ObjectAnimationUsingKeyFrames>
                                                <contract7Present:ObjectAnimationUsingKeyFrames Storyboard.TargetName="ContentPresenter" Storyboard.TargetProperty="BackgroundSizing">
                                                    <DiscreteObjectKeyFrame KeyTime="0" Value="{ThemeResource ToggleButtonCheckedStateBackgroundSizing}"/>
                                                </contract7Present:ObjectAnimationUsingKeyFrames>
                                            </Storyboard>
                                        </VisualState>
                                        <VisualState x:Name="CheckedPointerOver">
                                            <Storyboard>
                                                <ObjectAnimationUsingKeyFrames Storyboard.TargetName="ContentPresenter" Storyboard.TargetProperty="Background">
                                                    <DiscreteObjectKeyFrame KeyTime="0" Value="{ThemeResource SubtleFillColorTertiaryBrush}"/>
                                                </ObjectAnimationUsingKeyFrames>
                                                <ObjectAnimationUsingKeyFrames Storyboard.TargetName="ContentPresenter" Storyboard.TargetProperty="BorderBrush">
                                                    <DiscreteObjectKeyFrame KeyTime="0" Value="{ThemeResource ToggleButtonBorderBrushCheckedPointerOver}"/>
                                                </ObjectAnimationUsingKeyFrames>
                                                <ObjectAnimationUsingKeyFrames Storyboard.TargetName="ContentPresenter" Storyboard.TargetProperty="Foreground">
                                                    <DiscreteObjectKeyFrame KeyTime="0" Value="{ThemeResource TextFillColorSecondaryBrush}"/>
                                                </ObjectAnimationUsingKeyFrames>
                                                <contract7Present:ObjectAnimationUsingKeyFrames Storyboard.TargetName="ContentPresenter" Storyboard.TargetProperty="BackgroundSizing">
                                                    <DiscreteObjectKeyFrame KeyTime="0" Value="{ThemeResource ToggleButtonCheckedStateBackgroundSizing}"/>
                                                </contract7Present:ObjectAnimationUsingKeyFrames>
                                            </Storyboard>
                                        </VisualState>
                                        <VisualState x:Name="CheckedPressed">
                                            <Storyboard>
                                                <ObjectAnimationUsingKeyFrames Storyboard.TargetName="ContentPresenter" Storyboard.TargetProperty="Background">
                                                    <DiscreteObjectKeyFrame KeyTime="0" Value="{ThemeResource SubtleFillColorTertiaryBrush}"/>
                                                </ObjectAnimationUsingKeyFrames>
                                                <ObjectAnimationUsingKeyFrames Storyboard.TargetName="ContentPresenter" Storyboard.TargetProperty="Foreground">
                                                    <DiscreteObjectKeyFrame KeyTime="0" Value="{ThemeResource TextFillColorSecondaryBrush}"/>
                                                </ObjectAnimationUsingKeyFrames>
                                                <ObjectAnimationUsingKeyFrames Storyboard.TargetName="ContentPresenter" Storyboard.TargetProperty="BorderBrush">
                                                    <DiscreteObjectKeyFrame KeyTime="0" Value="{ThemeResource ToggleButtonBorderBrushCheckedPressed}"/>
                                                </ObjectAnimationUsingKeyFrames>
                                                <contract7Present:ObjectAnimationUsingKeyFrames Storyboard.TargetName="ContentPresenter" Storyboard.TargetProperty="BackgroundSizing">
                                                    <DiscreteObjectKeyFrame KeyTime="0" Value="{ThemeResource ToggleButtonCheckedStateBackgroundSizing}"/>
                                                </contract7Present:ObjectAnimationUsingKeyFrames>
                                            </Storyboard>
                                        </VisualState>
                                        <VisualState x:Name="CheckedDisabled">
                                            <Storyboard>
                                                <ObjectAnimationUsingKeyFrames Storyboard.TargetName="ContentPresenter" Storyboard.TargetProperty="Background">
                                                    <DiscreteObjectKeyFrame KeyTime="0" Value="{ThemeResource SubtleFillColorDisabledBrush}"/>
                                                </ObjectAnimationUsingKeyFrames>
                                                <ObjectAnimationUsingKeyFrames Storyboard.TargetName="ContentPresenter" Storyboard.TargetProperty="Foreground">
                                                    <DiscreteObjectKeyFrame KeyTime="0" Value="{ThemeResource TextFillColorDisabledBrush}"/>
                                                </ObjectAnimationUsingKeyFrames>
                                                <ObjectAnimationUsingKeyFrames Storyboard.TargetName="ContentPresenter" Storyboard.TargetProperty="BorderBrush">
                                                    <DiscreteObjectKeyFrame KeyTime="0" Value="{ThemeResource ToggleButtonBorderBrushCheckedDisabled}"/>
                                                </ObjectAnimationUsingKeyFrames>
                                            </Storyboard>
                                        </VisualState>
                                        <VisualState x:Name="Indeterminate">
                                            <Storyboard>
                                                <ObjectAnimationUsingKeyFrames Storyboard.TargetName="ContentPresenter" Storyboard.TargetProperty="Background">
                                                    <DiscreteObjectKeyFrame KeyTime="0" Value="{ThemeResource SubtleFillColorTertiaryBrush}"/>
                                                </ObjectAnimationUsingKeyFrames>
                                                <ObjectAnimationUsingKeyFrames Storyboard.TargetName="ContentPresenter" Storyboard.TargetProperty="Foreground">
                                                    <DiscreteObjectKeyFrame KeyTime="0" Value="{ThemeResource TextFillColorSecondaryBrush}"/>
                                                </ObjectAnimationUsingKeyFrames>
                                                <ObjectAnimationUsingKeyFrames Storyboard.TargetName="ContentPresenter" Storyboard.TargetProperty="BorderBrush">
                                                    <DiscreteObjectKeyFrame KeyTime="0" Value="{ThemeResource ToggleButtonBorderBrushIndeterminate}"/>
                                                </ObjectAnimationUsingKeyFrames>
                                            </Storyboard>
                                        </VisualState>
                                        <VisualState x:Name="IndeterminatePointerOver">
                                            <Storyboard>
                                                <ObjectAnimationUsingKeyFrames Storyboard.TargetName="ContentPresenter" Storyboard.TargetProperty="Background">
                                                    <DiscreteObjectKeyFrame KeyTime="0" Value="{ThemeResource SubtleFillColorTertiaryBrush}"/>
                                                </ObjectAnimationUsingKeyFrames>
                                                <ObjectAnimationUsingKeyFrames Storyboard.TargetName="ContentPresenter" Storyboard.TargetProperty="BorderBrush">
                                                    <DiscreteObjectKeyFrame KeyTime="0" Value="{ThemeResource ToggleButtonBorderBrushIndeterminatePointerOver}"/>
                                                </ObjectAnimationUsingKeyFrames>
                                                <ObjectAnimationUsingKeyFrames Storyboard.TargetName="ContentPresenter" Storyboard.TargetProperty="Foreground">
                                                    <DiscreteObjectKeyFrame KeyTime="0" Value="{ThemeResource TextFillColorSecondaryBrush}"/>
                                                </ObjectAnimationUsingKeyFrames>
                                            </Storyboard>
                                        </VisualState>
                                        <VisualState x:Name="IndeterminatePressed">
                                            <Storyboard>
                                                <ObjectAnimationUsingKeyFrames Storyboard.TargetName="ContentPresenter" Storyboard.TargetProperty="Background">
                                                    <DiscreteObjectKeyFrame KeyTime="0" Value="{ThemeResource SubtleFillColorTertiaryBrush}"/>
                                                </ObjectAnimationUsingKeyFrames>
                                                <ObjectAnimationUsingKeyFrames Storyboard.TargetName="ContentPresenter" Storyboard.TargetProperty="BorderBrush">
                                                    <DiscreteObjectKeyFrame KeyTime="0" Value="{ThemeResource ToggleButtonBorderBrushIndeterminatePressed}"/>
                                                </ObjectAnimationUsingKeyFrames>
                                                <ObjectAnimationUsingKeyFrames Storyboard.TargetName="ContentPresenter" Storyboard.TargetProperty="Foreground">
                                                    <DiscreteObjectKeyFrame KeyTime="0" Value="{ThemeResource TextFillColorSecondaryBrush}"/>
                                                </ObjectAnimationUsingKeyFrames>
                                            </Storyboard>
                                        </VisualState>
                                        <VisualState x:Name="IndeterminateDisabled">
                                            <Storyboard>
                                                <ObjectAnimationUsingKeyFrames Storyboard.TargetName="ContentPresenter" Storyboard.TargetProperty="Background">
                                                    <DiscreteObjectKeyFrame KeyTime="0" Value="{ThemeResource SubtleFillColorDisabledBrush}"/>
                                                </ObjectAnimationUsingKeyFrames>
                                                <ObjectAnimationUsingKeyFrames Storyboard.TargetName="ContentPresenter" Storyboard.TargetProperty="Foreground">
                                                    <DiscreteObjectKeyFrame KeyTime="0" Value="{ThemeResource TextFillColorDisabledBrush}"/>
                                                </ObjectAnimationUsingKeyFrames>
                                                <ObjectAnimationUsingKeyFrames Storyboard.TargetName="ContentPresenter" Storyboard.TargetProperty="BorderBrush">
                                                    <DiscreteObjectKeyFrame KeyTime="0" Value="{ThemeResource ToggleButtonBorderBrushIndeterminateDisabled}"/>
                                                </ObjectAnimationUsingKeyFrames>
                                            </Storyboard>
                                        </VisualState>
                                    </VisualStateGroup>
                                </VisualStateManager.VisualStateGroups>

                                <StackPanel Orientation="Horizontal">
                                    <FontIcon Margin="0,2,8,0"
                                              VerticalAlignment="Center"
                                              FontFamily="{StaticResource CalculatorFontFamily}"
                                              FontSize="{TemplateBinding GlyphFontSize}"
                                              Glyph="{TemplateBinding Glyph}"/>
                                    <TextBlock VerticalAlignment="Center"
                                               FontSize="{TemplateBinding FontSize}"
                                               Text="{TemplateBinding Text}"/>
                                    <FontIcon Margin="8,4,0,0"
                                              VerticalAlignment="Center"
                                              FontFamily="{StaticResource CalculatorFontFamily}"
                                              FontSize="{TemplateBinding ChevronFontSize}"
                                              Glyph="&#xE70D;"/>
                                </StackPanel>
                            </ContentPresenter>
                        </ControlTemplate>
                    </Setter.Value>
                </Setter>
            </Style>

            <Style x:Key="SubtleButtonStyle"
                   BasedOn="{StaticResource DefaultButtonStyle}"
                   TargetType="Button">
                <Setter Property="Background" Value="{ThemeResource SubtleFillColorTransparentBrush}"/>
                <Setter Property="BorderThickness" Value="{ThemeResource ControlBorderThemeThickness}"/>
                <Setter Property="Margin" Value="1"/>
                <Setter Property="Padding" Value="0"/>
                <Setter Property="HorizontalAlignment" Value="Stretch"/>
                <Setter Property="VerticalAlignment" Value="Stretch"/>
                <Setter Property="HorizontalContentAlignment" Value="Center"/>
                <Setter Property="VerticalContentAlignment" Value="Center"/>
                <Setter Property="FontSize" Value="16"/>
                <Setter Property="FontFamily" Value="{StaticResource CalculatorFontFamily}"/>
                <Setter Property="FontWeight" Value="Normal"/>
                <Setter Property="Template">
                    <Setter.Value>
                        <ControlTemplate TargetType="Button">
                            <ContentPresenter x:Name="ContentPresenter"
                                              Padding="{TemplateBinding Padding}"
                                              HorizontalContentAlignment="{TemplateBinding HorizontalContentAlignment}"
                                              VerticalContentAlignment="{TemplateBinding VerticalContentAlignment}"
                                              Background="{TemplateBinding Background}"
                                              BorderBrush="{TemplateBinding BorderBrush}"
                                              BorderThickness="{TemplateBinding BorderThickness}"
                                              contract7NotPresent:CornerRadius="{ThemeResource ControlCornerRadius}"
                                              contract7Present:BackgroundSizing="{TemplateBinding BackgroundSizing}"
                                              contract7Present:CornerRadius="{TemplateBinding CornerRadius}"
                                              muxc:AnimatedIcon.State="Normal"
                                              AutomationProperties.AccessibilityView="Raw"
                                              Content="{TemplateBinding Content}"
                                              ContentTemplate="{TemplateBinding ContentTemplate}"
                                              ContentTransitions="{TemplateBinding ContentTransitions}">

                                <contract7Present:ContentPresenter.BackgroundTransition>
                                    <contract7Present:BrushTransition Duration="0:0:0.083"/>
                                </contract7Present:ContentPresenter.BackgroundTransition>

                                <VisualStateManager.VisualStateGroups>
                                    <VisualStateGroup x:Name="CommonStates">
                                        <VisualState x:Name="Normal"/>

                                        <VisualState x:Name="PointerOver">
                                            <Storyboard>
                                                <ObjectAnimationUsingKeyFrames Storyboard.TargetName="ContentPresenter" Storyboard.TargetProperty="Background">
                                                    <DiscreteObjectKeyFrame KeyTime="0" Value="{ThemeResource SubtleFillColorSecondaryBrush}"/>
                                                </ObjectAnimationUsingKeyFrames>
                                                <ObjectAnimationUsingKeyFrames Storyboard.TargetName="ContentPresenter" Storyboard.TargetProperty="BorderBrush">
                                                    <DiscreteObjectKeyFrame KeyTime="0" Value="{ThemeResource ButtonBorderBrushPointerOver}"/>
                                                </ObjectAnimationUsingKeyFrames>
                                                <ObjectAnimationUsingKeyFrames Storyboard.TargetName="ContentPresenter" Storyboard.TargetProperty="Foreground">
                                                    <DiscreteObjectKeyFrame KeyTime="0" Value="{ThemeResource ButtonForegroundPointerOver}"/>
                                                </ObjectAnimationUsingKeyFrames>
                                            </Storyboard>
                                            <VisualState.Setters>
                                                <Setter Target="ContentPresenter.(muxc:AnimatedIcon.State)" Value="PointerOver"/>
                                            </VisualState.Setters>
                                        </VisualState>

                                        <VisualState x:Name="Pressed">
                                            <Storyboard>
                                                <ObjectAnimationUsingKeyFrames Storyboard.TargetName="ContentPresenter" Storyboard.TargetProperty="Background">
                                                    <DiscreteObjectKeyFrame KeyTime="0" Value="{ThemeResource SubtleFillColorTertiaryBrush}"/>
                                                </ObjectAnimationUsingKeyFrames>
                                                <ObjectAnimationUsingKeyFrames Storyboard.TargetName="ContentPresenter" Storyboard.TargetProperty="BorderBrush">
                                                    <DiscreteObjectKeyFrame KeyTime="0" Value="{ThemeResource ButtonBorderBrushPressed}"/>
                                                </ObjectAnimationUsingKeyFrames>
                                                <ObjectAnimationUsingKeyFrames Storyboard.TargetName="ContentPresenter" Storyboard.TargetProperty="Foreground">
                                                    <DiscreteObjectKeyFrame KeyTime="0" Value="{ThemeResource ButtonForegroundPressed}"/>
                                                </ObjectAnimationUsingKeyFrames>
                                            </Storyboard>
                                            <VisualState.Setters>
                                                <Setter Target="ContentPresenter.(muxc:AnimatedIcon.State)" Value="Pressed"/>
                                            </VisualState.Setters>
                                        </VisualState>

                                        <VisualState x:Name="Disabled">
                                            <Storyboard>
                                                <ObjectAnimationUsingKeyFrames Storyboard.TargetName="ContentPresenter" Storyboard.TargetProperty="Background">
                                                    <DiscreteObjectKeyFrame KeyTime="0" Value="{ThemeResource SubtleFillColorDisabledBrush}"/>
                                                </ObjectAnimationUsingKeyFrames>
                                                <ObjectAnimationUsingKeyFrames Storyboard.TargetName="ContentPresenter" Storyboard.TargetProperty="BorderBrush">
                                                    <DiscreteObjectKeyFrame KeyTime="0" Value="{ThemeResource ButtonBorderBrushDisabled}"/>
                                                </ObjectAnimationUsingKeyFrames>
                                                <ObjectAnimationUsingKeyFrames Storyboard.TargetName="ContentPresenter" Storyboard.TargetProperty="Foreground">
                                                    <DiscreteObjectKeyFrame KeyTime="0" Value="{ThemeResource ButtonForegroundDisabled}"/>
                                                </ObjectAnimationUsingKeyFrames>
                                            </Storyboard>
                                            <VisualState.Setters>
                                                <!-- DisabledVisual Should be handled by the control, not the animated icon. -->
                                                <Setter Target="ContentPresenter.(muxc:AnimatedIcon.State)" Value="Normal"/>
                                            </VisualState.Setters>
                                        </VisualState>
                                    </VisualStateGroup>
                                </VisualStateManager.VisualStateGroups>
                            </ContentPresenter>
                        </ControlTemplate>
                    </Setter.Value>
                </Setter>
            </Style>

            <Style x:Key="CaptionButtonStyle"
                   BasedOn="{StaticResource SubtleButtonStyle}"
                   TargetType="Button">
                <Setter Property="MinWidth" Value="32"/>
            </Style>

            <Style x:Key="MemoryHoverButtonStyle"
                   BasedOn="{StaticResource DefaultButtonStyle}"
                   TargetType="Button">
                <Setter Property="Height" Value="28"/>
                <Setter Property="Width" Value="36"/>
                <Setter Property="IsTabStop" Value="False"/>
                <Setter Property="AutomationProperties.AccessibilityView" Value="Raw"/>
                <Setter Property="Background" Value="{ThemeResource ControlFillColorDefaultBrush}"/>
                <Setter Property="Margin" Value="0"/>
                <Setter Property="Padding" Value="0"/>
                <Setter Property="HorizontalAlignment" Value="Stretch"/>
                <Setter Property="VerticalAlignment" Value="Stretch"/>
                <Setter Property="HorizontalContentAlignment" Value="Center"/>
                <Setter Property="VerticalContentAlignment" Value="Center"/>
                <Setter Property="FontSize" Value="12"/>
                <Setter Property="FontWeight" Value="Normal"/>
                <Setter Property="Template">
                    <Setter.Value>
                        <ControlTemplate TargetType="Button">
                            <ContentPresenter x:Name="ContentPresenter"
                                              Padding="{TemplateBinding Padding}"
                                              HorizontalContentAlignment="{TemplateBinding HorizontalContentAlignment}"
                                              VerticalContentAlignment="{TemplateBinding VerticalContentAlignment}"
                                              Background="{TemplateBinding Background}"
                                              BorderBrush="{TemplateBinding BorderBrush}"
                                              BorderThickness="{TemplateBinding BorderThickness}"
                                              contract7NotPresent:CornerRadius="{ThemeResource ControlCornerRadius}"
                                              contract7Present:BackgroundSizing="{TemplateBinding BackgroundSizing}"
                                              contract7Present:CornerRadius="{TemplateBinding CornerRadius}"
                                              muxc:AnimatedIcon.State="Normal"
                                              AutomationProperties.AccessibilityView="Raw"
                                              Content="{TemplateBinding Content}"
                                              ContentTemplate="{TemplateBinding ContentTemplate}"
                                              ContentTransitions="{TemplateBinding ContentTransitions}">

                                <contract7Present:ContentPresenter.BackgroundTransition>
                                    <contract7Present:BrushTransition Duration="0:0:0.083"/>
                                </contract7Present:ContentPresenter.BackgroundTransition>

                                <VisualStateManager.VisualStateGroups>
                                    <VisualStateGroup x:Name="CommonStates">
                                        <VisualState x:Name="Normal"/>

                                        <VisualState x:Name="PointerOver">
                                            <Storyboard>
                                                <ObjectAnimationUsingKeyFrames Storyboard.TargetName="ContentPresenter" Storyboard.TargetProperty="Background">
                                                    <DiscreteObjectKeyFrame KeyTime="0" Value="{ThemeResource ControlFillColorSecondaryBrush}"/>
                                                </ObjectAnimationUsingKeyFrames>
                                                <ObjectAnimationUsingKeyFrames Storyboard.TargetName="ContentPresenter" Storyboard.TargetProperty="BorderBrush">
                                                    <DiscreteObjectKeyFrame KeyTime="0" Value="{ThemeResource ButtonBorderBrushPointerOver}"/>
                                                </ObjectAnimationUsingKeyFrames>
                                                <ObjectAnimationUsingKeyFrames Storyboard.TargetName="ContentPresenter" Storyboard.TargetProperty="Foreground">
                                                    <DiscreteObjectKeyFrame KeyTime="0" Value="{ThemeResource ButtonForegroundPointerOver}"/>
                                                </ObjectAnimationUsingKeyFrames>
                                            </Storyboard>
                                            <VisualState.Setters>
                                                <Setter Target="ContentPresenter.(muxc:AnimatedIcon.State)" Value="PointerOver"/>
                                            </VisualState.Setters>
                                        </VisualState>

                                        <VisualState x:Name="Pressed">
                                            <Storyboard>
                                                <ObjectAnimationUsingKeyFrames Storyboard.TargetName="ContentPresenter" Storyboard.TargetProperty="Background">
                                                    <DiscreteObjectKeyFrame KeyTime="0" Value="{ThemeResource ControlFillColorTertiaryBrush}"/>
                                                </ObjectAnimationUsingKeyFrames>
                                                <ObjectAnimationUsingKeyFrames Storyboard.TargetName="ContentPresenter" Storyboard.TargetProperty="BorderBrush">
                                                    <DiscreteObjectKeyFrame KeyTime="0" Value="{ThemeResource ButtonBorderBrushPressed}"/>
                                                </ObjectAnimationUsingKeyFrames>
                                                <ObjectAnimationUsingKeyFrames Storyboard.TargetName="ContentPresenter" Storyboard.TargetProperty="Foreground">
                                                    <DiscreteObjectKeyFrame KeyTime="0" Value="{ThemeResource ButtonForegroundPressed}"/>
                                                </ObjectAnimationUsingKeyFrames>
                                            </Storyboard>
                                            <VisualState.Setters>
                                                <Setter Target="ContentPresenter.(muxc:AnimatedIcon.State)" Value="Pressed"/>
                                            </VisualState.Setters>
                                        </VisualState>

                                        <VisualState x:Name="Disabled">
                                            <Storyboard>
                                                <ObjectAnimationUsingKeyFrames Storyboard.TargetName="ContentPresenter" Storyboard.TargetProperty="Background">
                                                    <DiscreteObjectKeyFrame KeyTime="0" Value="{ThemeResource ControlFillColorDisabledBrush}"/>
                                                </ObjectAnimationUsingKeyFrames>
                                                <ObjectAnimationUsingKeyFrames Storyboard.TargetName="ContentPresenter" Storyboard.TargetProperty="BorderBrush">
                                                    <DiscreteObjectKeyFrame KeyTime="0" Value="{ThemeResource ButtonBorderBrushDisabled}"/>
                                                </ObjectAnimationUsingKeyFrames>
                                                <ObjectAnimationUsingKeyFrames Storyboard.TargetName="ContentPresenter" Storyboard.TargetProperty="Foreground">
                                                    <DiscreteObjectKeyFrame KeyTime="0" Value="{ThemeResource ButtonForegroundDisabled}"/>
                                                </ObjectAnimationUsingKeyFrames>
                                            </Storyboard>
                                            <VisualState.Setters>
                                                <!-- DisabledVisual Should be handled by the control, not the animated icon. -->
                                                <Setter Target="ContentPresenter.(muxc:AnimatedIcon.State)" Value="Normal"/>
                                            </VisualState.Setters>
                                        </VisualState>
                                    </VisualStateGroup>
                                </VisualStateManager.VisualStateGroups>
                            </ContentPresenter>
                        </ControlTemplate>
                    </Setter.Value>
                </Setter>
            </Style>

            <Style x:Key="CaptionToggleButtonStyle"
                   BasedOn="{StaticResource DefaultToggleButtonStyle}"
                   TargetType="ToggleButton">
                <Setter Property="BorderThickness" Value="1"/>
                <Setter Property="Padding" Value="0"/>
                <Setter Property="Margin" Value="1"/>
                <Setter Property="MinWidth" Value="32"/>
                <Setter Property="MinHeight" Value="24"/>
                <Setter Property="HorizontalAlignment" Value="Stretch"/>
                <Setter Property="VerticalAlignment" Value="Stretch"/>
                <Setter Property="HorizontalContentAlignment" Value="Center"/>
                <Setter Property="VerticalContentAlignment" Value="Center"/>
                <Setter Property="FontSize" Value="{StaticResource CalcOperatorCaptionSizeSmall}"/>
            </Style>

            <Style x:Key="CaptionToggleButtonWithIndicatorSmallStyle"
                   BasedOn="{StaticResource CaptionToggleButtonWithIndicatorStyle}"
                   TargetType="ToggleButton">
                <Setter Property="MinWidth" Value="0"/>
                <Setter Property="MinHeight" Value="0"/>
            </Style>

            <!-- Override normal/hover/pressed background -->
            <Style x:Key="CaptionToggleEmphasizedButtonStyle"
                   BasedOn="{StaticResource CaptionToggleButtonStyle}"
                   TargetType="ToggleButton">
                <Setter Property="Background" Value="{ThemeResource CalcButtonAltFillColorDefaultBrush}"/>
                <Setter Property="Template">
                    <Setter.Value>
                        <ControlTemplate TargetType="ToggleButton">
                            <ContentPresenter x:Name="ContentPresenter"
                                              Padding="{TemplateBinding Padding}"
                                              HorizontalContentAlignment="{TemplateBinding HorizontalContentAlignment}"
                                              VerticalContentAlignment="{TemplateBinding VerticalContentAlignment}"
                                              Background="{TemplateBinding Background}"
                                              BorderBrush="{TemplateBinding BorderBrush}"
                                              BorderThickness="{TemplateBinding BorderThickness}"
                                              contract7NotPresent:CornerRadius="{ThemeResource ControlCornerRadius}"
                                              contract7Present:BackgroundSizing="{TemplateBinding BackgroundSizing}"
                                              contract7Present:CornerRadius="{TemplateBinding CornerRadius}"
                                              AutomationProperties.AccessibilityView="Raw"
                                              Content="{TemplateBinding Content}"
                                              ContentTemplate="{TemplateBinding ContentTemplate}"
                                              ContentTransitions="{TemplateBinding ContentTransitions}">

                                <contract7Present:ContentPresenter.BackgroundTransition>
                                    <contract7Present:BrushTransition Duration="0:0:0.083"/>
                                </contract7Present:ContentPresenter.BackgroundTransition>

                                <VisualStateManager.VisualStateGroups>
                                    <VisualStateGroup x:Name="CommonStates">
                                        <VisualState x:Name="Normal"/>

                                        <VisualState x:Name="PointerOver">
                                            <Storyboard>
                                                <ObjectAnimationUsingKeyFrames Storyboard.TargetName="ContentPresenter" Storyboard.TargetProperty="Background">
                                                    <DiscreteObjectKeyFrame KeyTime="0" Value="{ThemeResource CalcButtonAltFillColorHoverBrush}"/>
                                                </ObjectAnimationUsingKeyFrames>
                                                <ObjectAnimationUsingKeyFrames Storyboard.TargetName="ContentPresenter" Storyboard.TargetProperty="BorderBrush">
                                                    <DiscreteObjectKeyFrame KeyTime="0" Value="{ThemeResource ToggleButtonBorderBrushPointerOver}"/>
                                                </ObjectAnimationUsingKeyFrames>
                                                <ObjectAnimationUsingKeyFrames Storyboard.TargetName="ContentPresenter" Storyboard.TargetProperty="Foreground">
                                                    <DiscreteObjectKeyFrame KeyTime="0" Value="{ThemeResource ToggleButtonForegroundPointerOver}"/>
                                                </ObjectAnimationUsingKeyFrames>
                                            </Storyboard>
                                        </VisualState>
                                        <VisualState x:Name="Pressed">
                                            <Storyboard>
                                                <ObjectAnimationUsingKeyFrames Storyboard.TargetName="ContentPresenter" Storyboard.TargetProperty="Background">
                                                    <DiscreteObjectKeyFrame KeyTime="0" Value="{ThemeResource CalcButtonAltFillColorPressedBrush}"/>
                                                </ObjectAnimationUsingKeyFrames>
                                                <ObjectAnimationUsingKeyFrames Storyboard.TargetName="ContentPresenter" Storyboard.TargetProperty="BorderBrush">
                                                    <DiscreteObjectKeyFrame KeyTime="0" Value="{ThemeResource ToggleButtonBorderBrushPressed}"/>
                                                </ObjectAnimationUsingKeyFrames>
                                                <ObjectAnimationUsingKeyFrames Storyboard.TargetName="ContentPresenter" Storyboard.TargetProperty="Foreground">
                                                    <DiscreteObjectKeyFrame KeyTime="0" Value="{ThemeResource ToggleButtonForegroundPressed}"/>
                                                </ObjectAnimationUsingKeyFrames>
                                            </Storyboard>
                                        </VisualState>
                                        <VisualState x:Name="Disabled">
                                            <Storyboard>
                                                <ObjectAnimationUsingKeyFrames Storyboard.TargetName="ContentPresenter" Storyboard.TargetProperty="Background">
                                                    <DiscreteObjectKeyFrame KeyTime="0" Value="{ThemeResource CalcButtonAltFillColorDisabledBrush}"/>
                                                </ObjectAnimationUsingKeyFrames>
                                                <ObjectAnimationUsingKeyFrames Storyboard.TargetName="ContentPresenter" Storyboard.TargetProperty="Foreground">
                                                    <DiscreteObjectKeyFrame KeyTime="0" Value="{ThemeResource ToggleButtonForegroundDisabled}"/>
                                                </ObjectAnimationUsingKeyFrames>
                                                <ObjectAnimationUsingKeyFrames Storyboard.TargetName="ContentPresenter" Storyboard.TargetProperty="BorderBrush">
                                                    <DiscreteObjectKeyFrame KeyTime="0" Value="{ThemeResource ToggleButtonBorderBrushDisabled}"/>
                                                </ObjectAnimationUsingKeyFrames>
                                            </Storyboard>
                                        </VisualState>
                                        <VisualState x:Name="Checked">
                                            <Storyboard>
                                                <ObjectAnimationUsingKeyFrames Storyboard.TargetName="ContentPresenter" Storyboard.TargetProperty="Background">
                                                    <DiscreteObjectKeyFrame KeyTime="0" Value="{ThemeResource ToggleButtonBackgroundChecked}"/>
                                                </ObjectAnimationUsingKeyFrames>
                                                <ObjectAnimationUsingKeyFrames Storyboard.TargetName="ContentPresenter" Storyboard.TargetProperty="Foreground">
                                                    <DiscreteObjectKeyFrame KeyTime="0" Value="{ThemeResource ToggleButtonForegroundChecked}"/>
                                                </ObjectAnimationUsingKeyFrames>
                                                <ObjectAnimationUsingKeyFrames Storyboard.TargetName="ContentPresenter" Storyboard.TargetProperty="BorderBrush">
                                                    <DiscreteObjectKeyFrame KeyTime="0" Value="{ThemeResource ToggleButtonBorderBrushChecked}"/>
                                                </ObjectAnimationUsingKeyFrames>
                                                <contract7Present:ObjectAnimationUsingKeyFrames Storyboard.TargetName="ContentPresenter" Storyboard.TargetProperty="BackgroundSizing">
                                                    <DiscreteObjectKeyFrame KeyTime="0" Value="{ThemeResource ToggleButtonCheckedStateBackgroundSizing}"/>
                                                </contract7Present:ObjectAnimationUsingKeyFrames>
                                            </Storyboard>
                                        </VisualState>
                                        <VisualState x:Name="CheckedPointerOver">
                                            <Storyboard>
                                                <ObjectAnimationUsingKeyFrames Storyboard.TargetName="ContentPresenter" Storyboard.TargetProperty="Background">
                                                    <DiscreteObjectKeyFrame KeyTime="0" Value="{ThemeResource ToggleButtonBackgroundCheckedPointerOver}"/>
                                                </ObjectAnimationUsingKeyFrames>
                                                <ObjectAnimationUsingKeyFrames Storyboard.TargetName="ContentPresenter" Storyboard.TargetProperty="BorderBrush">
                                                    <DiscreteObjectKeyFrame KeyTime="0" Value="{ThemeResource ToggleButtonBorderBrushCheckedPointerOver}"/>
                                                </ObjectAnimationUsingKeyFrames>
                                                <ObjectAnimationUsingKeyFrames Storyboard.TargetName="ContentPresenter" Storyboard.TargetProperty="Foreground">
                                                    <DiscreteObjectKeyFrame KeyTime="0" Value="{ThemeResource ToggleButtonForegroundCheckedPointerOver}"/>
                                                </ObjectAnimationUsingKeyFrames>
                                                <contract7Present:ObjectAnimationUsingKeyFrames Storyboard.TargetName="ContentPresenter" Storyboard.TargetProperty="BackgroundSizing">
                                                    <DiscreteObjectKeyFrame KeyTime="0" Value="{ThemeResource ToggleButtonCheckedStateBackgroundSizing}"/>
                                                </contract7Present:ObjectAnimationUsingKeyFrames>
                                            </Storyboard>
                                        </VisualState>
                                        <VisualState x:Name="CheckedPressed">
                                            <Storyboard>
                                                <ObjectAnimationUsingKeyFrames Storyboard.TargetName="ContentPresenter" Storyboard.TargetProperty="Background">
                                                    <DiscreteObjectKeyFrame KeyTime="0" Value="{ThemeResource ToggleButtonBackgroundCheckedPressed}"/>
                                                </ObjectAnimationUsingKeyFrames>
                                                <ObjectAnimationUsingKeyFrames Storyboard.TargetName="ContentPresenter" Storyboard.TargetProperty="Foreground">
                                                    <DiscreteObjectKeyFrame KeyTime="0" Value="{ThemeResource ToggleButtonForegroundCheckedPressed}"/>
                                                </ObjectAnimationUsingKeyFrames>
                                                <ObjectAnimationUsingKeyFrames Storyboard.TargetName="ContentPresenter" Storyboard.TargetProperty="BorderBrush">
                                                    <DiscreteObjectKeyFrame KeyTime="0" Value="{ThemeResource ToggleButtonBorderBrushCheckedPressed}"/>
                                                </ObjectAnimationUsingKeyFrames>
                                                <contract7Present:ObjectAnimationUsingKeyFrames Storyboard.TargetName="ContentPresenter" Storyboard.TargetProperty="BackgroundSizing">
                                                    <DiscreteObjectKeyFrame KeyTime="0" Value="{ThemeResource ToggleButtonCheckedStateBackgroundSizing}"/>
                                                </contract7Present:ObjectAnimationUsingKeyFrames>
                                            </Storyboard>
                                        </VisualState>
                                        <VisualState x:Name="CheckedDisabled">
                                            <Storyboard>
                                                <ObjectAnimationUsingKeyFrames Storyboard.TargetName="ContentPresenter" Storyboard.TargetProperty="Background">
                                                    <DiscreteObjectKeyFrame KeyTime="0" Value="{ThemeResource ToggleButtonBackgroundCheckedDisabled}"/>
                                                </ObjectAnimationUsingKeyFrames>
                                                <ObjectAnimationUsingKeyFrames Storyboard.TargetName="ContentPresenter" Storyboard.TargetProperty="Foreground">
                                                    <DiscreteObjectKeyFrame KeyTime="0" Value="{ThemeResource ToggleButtonForegroundCheckedDisabled}"/>
                                                </ObjectAnimationUsingKeyFrames>
                                                <ObjectAnimationUsingKeyFrames Storyboard.TargetName="ContentPresenter" Storyboard.TargetProperty="BorderBrush">
                                                    <DiscreteObjectKeyFrame KeyTime="0" Value="{ThemeResource ToggleButtonBorderBrushCheckedDisabled}"/>
                                                </ObjectAnimationUsingKeyFrames>
                                            </Storyboard>
                                        </VisualState>
                                        <VisualState x:Name="Indeterminate">
                                            <Storyboard>
                                                <ObjectAnimationUsingKeyFrames Storyboard.TargetName="ContentPresenter" Storyboard.TargetProperty="Background">
                                                    <DiscreteObjectKeyFrame KeyTime="0" Value="{ThemeResource ToggleButtonBackgroundIndeterminate}"/>
                                                </ObjectAnimationUsingKeyFrames>
                                                <ObjectAnimationUsingKeyFrames Storyboard.TargetName="ContentPresenter" Storyboard.TargetProperty="Foreground">
                                                    <DiscreteObjectKeyFrame KeyTime="0" Value="{ThemeResource ToggleButtonForegroundIndeterminate}"/>
                                                </ObjectAnimationUsingKeyFrames>
                                                <ObjectAnimationUsingKeyFrames Storyboard.TargetName="ContentPresenter" Storyboard.TargetProperty="BorderBrush">
                                                    <DiscreteObjectKeyFrame KeyTime="0" Value="{ThemeResource ToggleButtonBorderBrushIndeterminate}"/>
                                                </ObjectAnimationUsingKeyFrames>
                                            </Storyboard>
                                        </VisualState>
                                        <VisualState x:Name="IndeterminatePointerOver">
                                            <Storyboard>
                                                <ObjectAnimationUsingKeyFrames Storyboard.TargetName="ContentPresenter" Storyboard.TargetProperty="Background">
                                                    <DiscreteObjectKeyFrame KeyTime="0" Value="{ThemeResource ToggleButtonBackgroundIndeterminatePointerOver}"/>
                                                </ObjectAnimationUsingKeyFrames>
                                                <ObjectAnimationUsingKeyFrames Storyboard.TargetName="ContentPresenter" Storyboard.TargetProperty="BorderBrush">
                                                    <DiscreteObjectKeyFrame KeyTime="0" Value="{ThemeResource ToggleButtonBorderBrushIndeterminatePointerOver}"/>
                                                </ObjectAnimationUsingKeyFrames>
                                                <ObjectAnimationUsingKeyFrames Storyboard.TargetName="ContentPresenter" Storyboard.TargetProperty="Foreground">
                                                    <DiscreteObjectKeyFrame KeyTime="0" Value="{ThemeResource ToggleButtonForegroundIndeterminatePointerOver}"/>
                                                </ObjectAnimationUsingKeyFrames>
                                            </Storyboard>
                                        </VisualState>
                                        <VisualState x:Name="IndeterminatePressed">
                                            <Storyboard>
                                                <ObjectAnimationUsingKeyFrames Storyboard.TargetName="ContentPresenter" Storyboard.TargetProperty="Background">
                                                    <DiscreteObjectKeyFrame KeyTime="0" Value="{ThemeResource ToggleButtonBackgroundIndeterminatePressed}"/>
                                                </ObjectAnimationUsingKeyFrames>
                                                <ObjectAnimationUsingKeyFrames Storyboard.TargetName="ContentPresenter" Storyboard.TargetProperty="BorderBrush">
                                                    <DiscreteObjectKeyFrame KeyTime="0" Value="{ThemeResource ToggleButtonBorderBrushIndeterminatePressed}"/>
                                                </ObjectAnimationUsingKeyFrames>
                                                <ObjectAnimationUsingKeyFrames Storyboard.TargetName="ContentPresenter" Storyboard.TargetProperty="Foreground">
                                                    <DiscreteObjectKeyFrame KeyTime="0" Value="{ThemeResource ToggleButtonForegroundIndeterminatePressed}"/>
                                                </ObjectAnimationUsingKeyFrames>
                                            </Storyboard>
                                        </VisualState>
                                        <VisualState x:Name="IndeterminateDisabled">
                                            <Storyboard>
                                                <ObjectAnimationUsingKeyFrames Storyboard.TargetName="ContentPresenter" Storyboard.TargetProperty="Background">
                                                    <DiscreteObjectKeyFrame KeyTime="0" Value="{ThemeResource ToggleButtonBackgroundIndeterminateDisabled}"/>
                                                </ObjectAnimationUsingKeyFrames>
                                                <ObjectAnimationUsingKeyFrames Storyboard.TargetName="ContentPresenter" Storyboard.TargetProperty="Foreground">
                                                    <DiscreteObjectKeyFrame KeyTime="0" Value="{ThemeResource ToggleButtonForegroundIndeterminateDisabled}"/>
                                                </ObjectAnimationUsingKeyFrames>
                                                <ObjectAnimationUsingKeyFrames Storyboard.TargetName="ContentPresenter" Storyboard.TargetProperty="BorderBrush">
                                                    <DiscreteObjectKeyFrame KeyTime="0" Value="{ThemeResource ToggleButtonBorderBrushIndeterminateDisabled}"/>
                                                </ObjectAnimationUsingKeyFrames>
                                            </Storyboard>
                                        </VisualState>
                                    </VisualStateGroup>
                                </VisualStateManager.VisualStateGroups>
                            </ContentPresenter>
                        </ControlTemplate>
                    </Setter.Value>
                </Setter>
            </Style>

            <Style x:Key="CaptionToggleButtonWithIndicatorStyle"
                   BasedOn="{StaticResource CaptionToggleButtonStyle}"
                   TargetType="ToggleButton">
                <Setter Property="BorderThickness" Value="{ThemeResource ControlBorderThemeThickness}"/>
                <Setter Property="Padding" Value="0"/>
                <Setter Property="Margin" Value="1"/>
                <Setter Property="MinWidth" Value="32"/>
                <Setter Property="MinHeight" Value="24"/>
                <Setter Property="HorizontalAlignment" Value="Stretch"/>
                <Setter Property="VerticalAlignment" Value="Stretch"/>
                <Setter Property="HorizontalContentAlignment" Value="Center"/>
                <Setter Property="VerticalContentAlignment" Value="Center"/>
                <Setter Property="FontSize" Value="13"/>
                <Setter Property="Background" Value="{ThemeResource SubtleFillColorTransparentBrush}"/>
                <Setter Property="Template">
                    <Setter.Value>
                        <ControlTemplate TargetType="ToggleButton">
                            <Grid>
                                <VisualStateManager.VisualStateGroups>
                                    <VisualStateGroup x:Name="CommonStates">
                                        <VisualState x:Name="Normal"/>

                                        <VisualState x:Name="PointerOver">
                                            <Storyboard>
                                                <ObjectAnimationUsingKeyFrames Storyboard.TargetName="ContentPresenter" Storyboard.TargetProperty="Background">
                                                    <DiscreteObjectKeyFrame KeyTime="0" Value="{ThemeResource SubtleFillColorSecondaryBrush}"/>
                                                </ObjectAnimationUsingKeyFrames>
                                                <ObjectAnimationUsingKeyFrames Storyboard.TargetName="ContentPresenter" Storyboard.TargetProperty="BorderBrush">
                                                    <DiscreteObjectKeyFrame KeyTime="0" Value="{ThemeResource ToggleButtonBorderBrushPointerOver}"/>
                                                </ObjectAnimationUsingKeyFrames>
                                                <ObjectAnimationUsingKeyFrames Storyboard.TargetName="ContentPresenter" Storyboard.TargetProperty="Foreground">
                                                    <DiscreteObjectKeyFrame KeyTime="0" Value="{ThemeResource TextFillColorPrimaryBrush}"/>
                                                </ObjectAnimationUsingKeyFrames>
                                            </Storyboard>
                                        </VisualState>
                                        <VisualState x:Name="Pressed">
                                            <Storyboard>
                                                <ObjectAnimationUsingKeyFrames Storyboard.TargetName="ContentPresenter" Storyboard.TargetProperty="Background">
                                                    <DiscreteObjectKeyFrame KeyTime="0" Value="{ThemeResource SubtleFillColorTertiaryBrush}"/>
                                                </ObjectAnimationUsingKeyFrames>
                                                <ObjectAnimationUsingKeyFrames Storyboard.TargetName="ContentPresenter" Storyboard.TargetProperty="BorderBrush">
                                                    <DiscreteObjectKeyFrame KeyTime="0" Value="{ThemeResource ToggleButtonBorderBrushPressed}"/>
                                                </ObjectAnimationUsingKeyFrames>
                                                <ObjectAnimationUsingKeyFrames Storyboard.TargetName="ContentPresenter" Storyboard.TargetProperty="Foreground">
                                                    <DiscreteObjectKeyFrame KeyTime="0" Value="{ThemeResource TextFillColorSecondaryBrush}"/>
                                                </ObjectAnimationUsingKeyFrames>
                                            </Storyboard>
                                        </VisualState>
                                        <VisualState x:Name="Disabled">
                                            <Storyboard>
                                                <ObjectAnimationUsingKeyFrames Storyboard.TargetName="ContentPresenter" Storyboard.TargetProperty="Background">
                                                    <DiscreteObjectKeyFrame KeyTime="0" Value="{ThemeResource SubtleFillColorDisabledBrush}"/>
                                                </ObjectAnimationUsingKeyFrames>
                                                <ObjectAnimationUsingKeyFrames Storyboard.TargetName="ContentPresenter" Storyboard.TargetProperty="Foreground">
                                                    <DiscreteObjectKeyFrame KeyTime="0" Value="{ThemeResource TextFillColorDisabledBrush}"/>
                                                </ObjectAnimationUsingKeyFrames>
                                                <ObjectAnimationUsingKeyFrames Storyboard.TargetName="ContentPresenter" Storyboard.TargetProperty="BorderBrush">
                                                    <DiscreteObjectKeyFrame KeyTime="0" Value="{ThemeResource ToggleButtonBorderBrushDisabled}"/>
                                                </ObjectAnimationUsingKeyFrames>
                                            </Storyboard>
                                        </VisualState>
                                        <VisualState x:Name="Checked">
                                            <Storyboard>
                                                <ObjectAnimationUsingKeyFrames Storyboard.TargetName="ContentPresenter" Storyboard.TargetProperty="Background">
                                                    <DiscreteObjectKeyFrame KeyTime="0" Value="{ThemeResource SubtleFillColorTransparentBrush}"/>
                                                </ObjectAnimationUsingKeyFrames>
                                                <ObjectAnimationUsingKeyFrames Storyboard.TargetName="ContentPresenter" Storyboard.TargetProperty="Foreground">
                                                    <DiscreteObjectKeyFrame KeyTime="0" Value="{ThemeResource TextFillColorPrimaryBrush}"/>
                                                </ObjectAnimationUsingKeyFrames>
                                                <ObjectAnimationUsingKeyFrames Storyboard.TargetName="ContentPresenter" Storyboard.TargetProperty="BorderBrush">
                                                    <DiscreteObjectKeyFrame KeyTime="0" Value="{ThemeResource ToggleButtonBorderBrushChecked}"/>
                                                </ObjectAnimationUsingKeyFrames>
                                                <contract7Present:ObjectAnimationUsingKeyFrames Storyboard.TargetName="ContentPresenter" Storyboard.TargetProperty="BackgroundSizing">
                                                    <DiscreteObjectKeyFrame KeyTime="0" Value="{ThemeResource ToggleButtonCheckedStateBackgroundSizing}"/>
                                                </contract7Present:ObjectAnimationUsingKeyFrames>
                                                <ObjectAnimationUsingKeyFrames Storyboard.TargetName="SelectedPipe" Storyboard.TargetProperty="Background">
                                                    <DiscreteObjectKeyFrame KeyTime="0" Value="{ThemeResource ToggleButtonBackgroundChecked}"/>
                                                </ObjectAnimationUsingKeyFrames>
                                            </Storyboard>
                                        </VisualState>
                                        <VisualState x:Name="CheckedPointerOver">
                                            <Storyboard>
                                                <ObjectAnimationUsingKeyFrames Storyboard.TargetName="ContentPresenter" Storyboard.TargetProperty="Background">
                                                    <DiscreteObjectKeyFrame KeyTime="0" Value="{ThemeResource SubtleFillColorSecondaryBrush}"/>
                                                </ObjectAnimationUsingKeyFrames>
                                                <ObjectAnimationUsingKeyFrames Storyboard.TargetName="ContentPresenter" Storyboard.TargetProperty="BorderBrush">
                                                    <DiscreteObjectKeyFrame KeyTime="0" Value="{ThemeResource ToggleButtonBorderBrushCheckedPointerOver}"/>
                                                </ObjectAnimationUsingKeyFrames>
                                                <ObjectAnimationUsingKeyFrames Storyboard.TargetName="ContentPresenter" Storyboard.TargetProperty="Foreground">
                                                    <DiscreteObjectKeyFrame KeyTime="0" Value="{ThemeResource TextFillColorPrimaryBrush}"/>
                                                </ObjectAnimationUsingKeyFrames>
                                                <contract7Present:ObjectAnimationUsingKeyFrames Storyboard.TargetName="ContentPresenter" Storyboard.TargetProperty="BackgroundSizing">
                                                    <DiscreteObjectKeyFrame KeyTime="0" Value="{ThemeResource ToggleButtonCheckedStateBackgroundSizing}"/>
                                                </contract7Present:ObjectAnimationUsingKeyFrames>
                                                <ObjectAnimationUsingKeyFrames Storyboard.TargetName="SelectedPipe" Storyboard.TargetProperty="Background">
                                                    <DiscreteObjectKeyFrame KeyTime="0" Value="{ThemeResource ToggleButtonBackgroundCheckedPointerOver}"/>
                                                </ObjectAnimationUsingKeyFrames>
                                            </Storyboard>
                                        </VisualState>
                                        <VisualState x:Name="CheckedPressed">
                                            <Storyboard>
                                                <ObjectAnimationUsingKeyFrames Storyboard.TargetName="ContentPresenter" Storyboard.TargetProperty="Background">
                                                    <DiscreteObjectKeyFrame KeyTime="0" Value="{ThemeResource SubtleFillColorTertiaryBrush}"/>
                                                </ObjectAnimationUsingKeyFrames>
                                                <ObjectAnimationUsingKeyFrames Storyboard.TargetName="ContentPresenter" Storyboard.TargetProperty="Foreground">
                                                    <DiscreteObjectKeyFrame KeyTime="0" Value="{ThemeResource TextFillColorSecondaryBrush}"/>
                                                </ObjectAnimationUsingKeyFrames>
                                                <ObjectAnimationUsingKeyFrames Storyboard.TargetName="ContentPresenter" Storyboard.TargetProperty="BorderBrush">
                                                    <DiscreteObjectKeyFrame KeyTime="0" Value="{ThemeResource ToggleButtonBorderBrushCheckedPressed}"/>
                                                </ObjectAnimationUsingKeyFrames>
                                                <contract7Present:ObjectAnimationUsingKeyFrames Storyboard.TargetName="ContentPresenter" Storyboard.TargetProperty="BackgroundSizing">
                                                    <DiscreteObjectKeyFrame KeyTime="0" Value="{ThemeResource ToggleButtonCheckedStateBackgroundSizing}"/>
                                                </contract7Present:ObjectAnimationUsingKeyFrames>
                                                <ObjectAnimationUsingKeyFrames Storyboard.TargetName="SelectedPipe" Storyboard.TargetProperty="Background">
                                                    <DiscreteObjectKeyFrame KeyTime="0" Value="{ThemeResource ToggleButtonBackgroundCheckedPressed}"/>
                                                </ObjectAnimationUsingKeyFrames>
                                            </Storyboard>
                                        </VisualState>
                                        <VisualState x:Name="CheckedDisabled">
                                            <Storyboard>
                                                <ObjectAnimationUsingKeyFrames Storyboard.TargetName="ContentPresenter" Storyboard.TargetProperty="Background">
                                                    <DiscreteObjectKeyFrame KeyTime="0" Value="{ThemeResource SubtleFillColorDisabledBrush}"/>
                                                </ObjectAnimationUsingKeyFrames>
                                                <ObjectAnimationUsingKeyFrames Storyboard.TargetName="ContentPresenter" Storyboard.TargetProperty="Foreground">
                                                    <DiscreteObjectKeyFrame KeyTime="0" Value="{ThemeResource TextFillColorDisabledBrush}"/>
                                                </ObjectAnimationUsingKeyFrames>
                                                <ObjectAnimationUsingKeyFrames Storyboard.TargetName="ContentPresenter" Storyboard.TargetProperty="BorderBrush">
                                                    <DiscreteObjectKeyFrame KeyTime="0" Value="{ThemeResource ToggleButtonBorderBrushCheckedDisabled}"/>
                                                </ObjectAnimationUsingKeyFrames>
                                                <ObjectAnimationUsingKeyFrames Storyboard.TargetName="SelectedPipe" Storyboard.TargetProperty="Background">
                                                    <DiscreteObjectKeyFrame KeyTime="0" Value="{ThemeResource ToggleButtonBackgroundCheckedDisabled}"/>
                                                </ObjectAnimationUsingKeyFrames>
                                            </Storyboard>
                                        </VisualState>
                                        <VisualState x:Name="Indeterminate">
                                            <Storyboard>
                                                <ObjectAnimationUsingKeyFrames Storyboard.TargetName="ContentPresenter" Storyboard.TargetProperty="Background">
                                                    <DiscreteObjectKeyFrame KeyTime="0" Value="{ThemeResource SubtleFillColorTransparentBrush}"/>
                                                </ObjectAnimationUsingKeyFrames>
                                                <ObjectAnimationUsingKeyFrames Storyboard.TargetName="ContentPresenter" Storyboard.TargetProperty="Foreground">
                                                    <DiscreteObjectKeyFrame KeyTime="0" Value="{ThemeResource TextFillColorPrimaryBrush}"/>
                                                </ObjectAnimationUsingKeyFrames>
                                                <ObjectAnimationUsingKeyFrames Storyboard.TargetName="ContentPresenter" Storyboard.TargetProperty="BorderBrush">
                                                    <DiscreteObjectKeyFrame KeyTime="0" Value="{ThemeResource ToggleButtonBorderBrushIndeterminate}"/>
                                                </ObjectAnimationUsingKeyFrames>
                                            </Storyboard>
                                        </VisualState>
                                        <VisualState x:Name="IndeterminatePointerOver">
                                            <Storyboard>
                                                <ObjectAnimationUsingKeyFrames Storyboard.TargetName="ContentPresenter" Storyboard.TargetProperty="Background">
                                                    <DiscreteObjectKeyFrame KeyTime="0" Value="{ThemeResource SubtleFillColorSecondaryBrush}"/>
                                                </ObjectAnimationUsingKeyFrames>
                                                <ObjectAnimationUsingKeyFrames Storyboard.TargetName="ContentPresenter" Storyboard.TargetProperty="BorderBrush">
                                                    <DiscreteObjectKeyFrame KeyTime="0" Value="{ThemeResource ToggleButtonBorderBrushIndeterminatePointerOver}"/>
                                                </ObjectAnimationUsingKeyFrames>
                                                <ObjectAnimationUsingKeyFrames Storyboard.TargetName="ContentPresenter" Storyboard.TargetProperty="Foreground">
                                                    <DiscreteObjectKeyFrame KeyTime="0" Value="{ThemeResource TextFillColorPrimaryBrush}"/>
                                                </ObjectAnimationUsingKeyFrames>
                                            </Storyboard>
                                        </VisualState>
                                        <VisualState x:Name="IndeterminatePressed">
                                            <Storyboard>
                                                <ObjectAnimationUsingKeyFrames Storyboard.TargetName="ContentPresenter" Storyboard.TargetProperty="Background">
                                                    <DiscreteObjectKeyFrame KeyTime="0" Value="{ThemeResource SubtleFillColorTertiaryBrush}"/>
                                                </ObjectAnimationUsingKeyFrames>
                                                <ObjectAnimationUsingKeyFrames Storyboard.TargetName="ContentPresenter" Storyboard.TargetProperty="BorderBrush">
                                                    <DiscreteObjectKeyFrame KeyTime="0" Value="{ThemeResource ToggleButtonBorderBrushIndeterminatePressed}"/>
                                                </ObjectAnimationUsingKeyFrames>
                                                <ObjectAnimationUsingKeyFrames Storyboard.TargetName="ContentPresenter" Storyboard.TargetProperty="Foreground">
                                                    <DiscreteObjectKeyFrame KeyTime="0" Value="{ThemeResource TextFillColorSecondaryBrush}"/>
                                                </ObjectAnimationUsingKeyFrames>
                                            </Storyboard>
                                        </VisualState>
                                        <VisualState x:Name="IndeterminateDisabled">
                                            <Storyboard>
                                                <ObjectAnimationUsingKeyFrames Storyboard.TargetName="ContentPresenter" Storyboard.TargetProperty="Background">
                                                    <DiscreteObjectKeyFrame KeyTime="0" Value="{ThemeResource SubtleFillColorDisabledBrush}"/>
                                                </ObjectAnimationUsingKeyFrames>
                                                <ObjectAnimationUsingKeyFrames Storyboard.TargetName="ContentPresenter" Storyboard.TargetProperty="Foreground">
                                                    <DiscreteObjectKeyFrame KeyTime="0" Value="{ThemeResource TextFillColorDisabledBrush}"/>
                                                </ObjectAnimationUsingKeyFrames>
                                                <ObjectAnimationUsingKeyFrames Storyboard.TargetName="ContentPresenter" Storyboard.TargetProperty="BorderBrush">
                                                    <DiscreteObjectKeyFrame KeyTime="0" Value="{ThemeResource ToggleButtonBorderBrushIndeterminateDisabled}"/>
                                                </ObjectAnimationUsingKeyFrames>
                                            </Storyboard>
                                        </VisualState>
                                    </VisualStateGroup>
                                </VisualStateManager.VisualStateGroups>

                                <ContentPresenter x:Name="ContentPresenter"
                                                  Padding="{TemplateBinding Padding}"
                                                  HorizontalContentAlignment="{TemplateBinding HorizontalContentAlignment}"
                                                  VerticalContentAlignment="{TemplateBinding VerticalContentAlignment}"
                                                  Background="{TemplateBinding Background}"
                                                  BorderBrush="{TemplateBinding BorderBrush}"
                                                  BorderThickness="{TemplateBinding BorderThickness}"
                                                  contract7NotPresent:CornerRadius="{ThemeResource ControlCornerRadius}"
                                                  contract7Present:BackgroundSizing="{TemplateBinding BackgroundSizing}"
                                                  contract7Present:CornerRadius="{TemplateBinding CornerRadius}"
                                                  AutomationProperties.AccessibilityView="Raw"
                                                  Content="{TemplateBinding Content}"
                                                  ContentTemplate="{TemplateBinding ContentTemplate}"
                                                  ContentTransitions="{TemplateBinding ContentTransitions}">

                                    <contract7Present:ContentPresenter.BackgroundTransition>
                                        <contract7Present:BrushTransition Duration="0:0:0.083"/>
                                    </contract7Present:ContentPresenter.BackgroundTransition>
                                </ContentPresenter>

                                <Border x:Name="SelectedPipe"
                                        Width="16"
                                        Height="3"
                                        Margin="0,0,0,1"
                                        HorizontalAlignment="Center"
                                        VerticalAlignment="Bottom"
                                        Background="{TemplateBinding Background}"
                                        BorderThickness="0"
                                        CornerRadius="1.5">
                                    <contract7Present:Border.BackgroundTransition>
                                        <contract7Present:BrushTransition/>
                                    </contract7Present:Border.BackgroundTransition>
                                </Border>
                            </Grid>
                        </ControlTemplate>
                    </Setter.Value>
                </Setter>
            </Style>

            <Style x:Key="ProgWordSizeButtonStyle"
                   BasedOn="{StaticResource CaptionButtonStyle}"
                   TargetType="Button">
                <Setter Property="BorderBrush" Value="{ThemeResource ButtonBorderBrush}"/>
                <Setter Property="BorderThickness" Value="0"/>
                <Setter Property="HorizontalAlignment" Value="Center"/>
                <Setter Property="MinWidth" Value="80"/>
                <Setter Property="FontFamily" Value="{ThemeResource ContentControlThemeFontFamily}"/>
                <Setter Property="FontSize" Value="12"/>
                <Setter Property="Template">
                    <Setter.Value>
                        <ControlTemplate TargetType="Button">
                            <Grid x:Name="RootGrid"
                                  Background="{TemplateBinding Background}"
                                  BorderBrush="{TemplateBinding BorderBrush}"
                                  BorderThickness="{ThemeResource ControlBorderThemeThickness}"
                                  contract7Present:CornerRadius="{ThemeResource ControlCornerRadius}">
                                <VisualStateManager.VisualStateGroups>
                                    <VisualStateGroup x:Name="CommonStates">
                                        <VisualState x:Name="Normal"/>
                                        <VisualState x:Name="PointerOver">
                                            <VisualState.Setters>
                                                <Setter Target="RootGrid.Background" Value="{ThemeResource SubtleFillColorSecondaryBrush}"/>
                                            </VisualState.Setters>
                                        </VisualState>
                                        <VisualState x:Name="Pressed">
                                            <VisualState.Setters>
                                                <Setter Target="RootGrid.Background" Value="{ThemeResource SubtleFillColorTertiaryBrush}"/>
                                                <Setter Target="ContentPresenter.Foreground" Value="{ThemeResource TextFillColorSecondaryBrush}"/>
                                            </VisualState.Setters>
                                        </VisualState>
                                        <VisualState x:Name="Disabled">
                                            <VisualState.Setters>
                                                <Setter Target="ContentPresenter.Foreground" Value="{ThemeResource TextFillColorDisabled}"/>
                                                <Setter Target="ContentPresenter.BorderThickness" Value="0"/>
                                            </VisualState.Setters>
                                        </VisualState>
                                    </VisualStateGroup>
                                </VisualStateManager.VisualStateGroups>
                                <ContentPresenter x:Name="ContentPresenter"
                                                  Margin="{TemplateBinding Padding}"
                                                  HorizontalContentAlignment="{TemplateBinding HorizontalContentAlignment}"
                                                  VerticalContentAlignment="{TemplateBinding VerticalContentAlignment}"
                                                  BorderBrush="{ThemeResource AppControlForegroundTransparentRevealBorderBrush}"
                                                  BorderThickness="{TemplateBinding BorderThickness}"
                                                  FontSize="{TemplateBinding FontSize}"
                                                  FontWeight="Normal"
                                                  AutomationProperties.AccessibilityView="Raw"
                                                  Content="{TemplateBinding Content}"
                                                  ContentTemplate="{TemplateBinding ContentTemplate}"
                                                  ContentTransitions="{TemplateBinding ContentTransitions}"/>
                            </Grid>
                        </ControlTemplate>
                    </Setter.Value>
                </Setter>
            </Style>

            <Style x:Key="ProgKeypadRadioButtonStyle" TargetType="RadioButton">
                <Setter Property="Foreground" Value="{ThemeResource RadioButtonForeground}"/>
                <Setter Property="Background" Value="{ThemeResource SubtleFillColorTransparentBrush}"/>
                <Setter Property="BorderBrush" Value="{ThemeResource ButtonBorderBrush}"/>
                <Setter Property="Padding" Value="0,0,0,-2"/>
                <Setter Property="Margin" Value="1"/>
                <Setter Property="MinWidth" Value="32"/>
                <Setter Property="HorizontalAlignment" Value="Stretch"/>
                <Setter Property="VerticalAlignment" Value="Stretch"/>
                <Setter Property="HorizontalContentAlignment" Value="Center"/>
                <Setter Property="VerticalContentAlignment" Value="Center"/>
                <Setter Property="FontFamily" Value="{StaticResource CalculatorFontFamily}"/>
                <Setter Property="FontSize" Value="16"/>
                <Setter Property="FocusVisualMargin" Value="-3"/>
                <Setter Property="Template">
                    <Setter.Value>
                        <ControlTemplate TargetType="RadioButton">
                            <Grid x:Name="RootGrid"
                                  Background="{TemplateBinding Background}"
                                  BorderBrush="{TemplateBinding BorderBrush}"
                                  BorderThickness="{ThemeResource ControlBorderThemeThickness}"
                                  contract7Present:CornerRadius="{TemplateBinding CornerRadius}">
                                <Grid.RowDefinitions>
                                    <RowDefinition/>
                                    <RowDefinition Height="Auto"/>
                                </Grid.RowDefinitions>
                                <VisualStateManager.VisualStateGroups>
                                    <VisualStateGroup x:Name="Layout">
                                        <VisualState x:Name="MinSizeLayout">
                                            <VisualState.StateTriggers>
                                                <AdaptiveTrigger MinWindowHeight="{StaticResource AppMinWindowHeight}" MinWindowWidth="{StaticResource AppMinWindowWidth}"/>
                                            </VisualState.StateTriggers>
                                        </VisualState>
                                        <VisualState x:Name="DefaultLayout">
                                            <VisualState.StateTriggers>
                                                <AdaptiveTrigger MinWindowHeight="0" MinWindowWidth="0"/>
                                            </VisualState.StateTriggers>
                                        </VisualState>
                                    </VisualStateGroup>
                                    <VisualStateGroup x:Name="CommonStates">
                                        <VisualState x:Name="Normal"/>
                                        <VisualState x:Name="PointerOver">
                                            <VisualState.Setters>
                                                <Setter Target="RootGrid.Background" Value="{ThemeResource SubtleFillColorSecondaryBrush}"/>
                                            </VisualState.Setters>
                                        </VisualState>
                                        <VisualState x:Name="Pressed">
                                            <VisualState.Setters>
                                                <Setter Target="RootGrid.Background" Value="{ThemeResource SubtleFillColorTertiaryBrush}"/>
                                                <Setter Target="ContentPresenter.Foreground" Value="{ThemeResource TextFillColorSecondaryBrush}"/>
                                            </VisualState.Setters>
                                        </VisualState>
                                        <VisualState x:Name="Disabled">
                                            <VisualState.Setters>
                                                <Setter Target="ContentPresenter.Foreground" Value="{ThemeResource SystemControlDisabledBaseLowBrush}"/>
                                                <Setter Target="ContentPresenter.BorderThickness" Value="0"/>
                                            </VisualState.Setters>
                                        </VisualState>
                                        <VisualState x:Name="Checked">
                                            <VisualState.Setters>
                                                <Setter Target="RootGrid.Background" Value="{ThemeResource SystemControlHighlightTransparentBrush}"/>
                                                <Setter Target="ContentPresenter.Foreground" Value="{ThemeResource SystemControlHighlightAltBaseHighBrush}"/>
                                                <Setter Target="Carrot.Opacity" Value="1"/>
                                            </VisualState.Setters>
                                        </VisualState>
                                        <VisualState x:Name="CheckedPointerOver">
                                            <VisualState.Setters>
                                                <Setter Target="RootGrid.Background" Value="{ThemeResource AppControlHoverButtonFaceBrush}"/>
                                                <Setter Target="ContentPresenter.Foreground" Value="{ThemeResource SystemControlHighlightAltBaseMediumBrush}"/>
                                                <Setter Target="Carrot.Opacity" Value="1"/>
                                            </VisualState.Setters>
                                        </VisualState>
                                        <VisualState x:Name="CheckedPressed">
                                            <VisualState.Setters>
                                                <Setter Target="RootGrid.Background" Value="Red"/>
                                                <Setter Target="ContentPresenter.Foreground" Value="{ThemeResource SystemControlForegroundBaseMediumLowBrush}"/>
                                                <Setter Target="Carrot.Opacity" Value="1"/>
                                            </VisualState.Setters>
                                        </VisualState>
                                        <VisualState x:Name="CheckedDisabled">
                                            <VisualState.Setters>
                                                <Setter Target="RootGrid.Background" Value="{ThemeResource SystemControlHighlightTransparentBrush}"/>
                                                <Setter Target="ContentPresenter.Foreground" Value="{ThemeResource SystemControlHighlightAccentBrush}"/>
                                                <Setter Target="Carrot.Visibility" Value="Visible"/>
                                                <Setter Target="ContentPresenter.BorderThickness" Value="0"/>
                                            </VisualState.Setters>
                                        </VisualState>
                                    </VisualStateGroup>
                                </VisualStateManager.VisualStateGroups>

                                <Rectangle x:Name="Carrot"
                                           Grid.Row="1"
                                           Height="3"
                                           MaxWidth="16"
                                           Margin="0,1,0,0"
                                           VerticalAlignment="Bottom"
                                           Fill="{ThemeResource AccentFillColorDefaultBrush}"
                                           StrokeThickness="0"
                                           Opacity="0"
                                           RadiusX="1.5"
                                           RadiusY="1.5"/>

                                <ContentPresenter x:Name="ContentPresenter"
                                                  Margin="{TemplateBinding Padding}"
                                                  HorizontalAlignment="{TemplateBinding HorizontalAlignment}"
                                                  VerticalAlignment="{TemplateBinding VerticalAlignment}"
                                                  HorizontalContentAlignment="{TemplateBinding HorizontalContentAlignment}"
                                                  VerticalContentAlignment="{TemplateBinding VerticalContentAlignment}"
                                                  Foreground="{TemplateBinding Foreground}"
                                                  BorderBrush="{ThemeResource AppControlForegroundTransparentRevealBorderBrush}"
                                                  BorderThickness="{TemplateBinding BorderThickness}"
                                                  AutomationProperties.AccessibilityView="Raw"
                                                  Content="{TemplateBinding Content}"
                                                  ContentTemplate="{TemplateBinding ContentTemplate}"
                                                  ContentTransitions="{TemplateBinding ContentTransitions}"/>
                            </Grid>
                        </ControlTemplate>
                    </Setter.Value>
                </Setter>
            </Style>
            <contract7NotPresent:Style x:Key="ConditionalProgKeypadRadioButtonStyle"
                                       BasedOn="{StaticResource ProgKeypadRadioButtonStyle}"
                                       TargetType="RadioButton"/>
            <contract7Present:Style x:Key="ConditionalProgKeypadRadioButtonStyle"
                                    BasedOn="{StaticResource ProgKeypadRadioButtonStyle}"
                                    TargetType="RadioButton">
                <Setter Property="CornerRadius" Value="{ThemeResource ControlCornerRadius}"/>
            </contract7Present:Style>


            <Style x:Key="SquareIconButtonStyle"
                   BasedOn="{StaticResource SubtleButtonStyle}"
                   TargetType="Button">
                <Setter Property="Width" Value="{StaticResource IconOnlySubtleButtonHeight}"/>
                <Setter Property="Height" Value="{StaticResource IconOnlySubtleButtonHeight}"/>
                <Setter Property="Margin" Value="10,0,0,0"/>
                <Setter Property="MinHeight" Value="16"/>
                <Setter Property="MinWidth" Value="16"/>
                <Setter Property="VerticalAlignment" Value="Center"/>
                <Setter Property="HorizontalAlignment" Value="Left"/>
            </Style>

            <Style x:Key="HistoryButtonStyle"
                   BasedOn="{StaticResource SquareIconButtonStyle}"
                   TargetType="Button">
                <Setter Property="HorizontalAlignment" Value="Right"/>
                <Setter Property="Margin" Value="0,0,8,0"/>
            </Style>

            <Style x:Key="ClearAllHistoryMemoryButtonStyle"
                   BasedOn="{StaticResource SquareIconButtonStyle}"
                   TargetType="Button">
                <Setter Property="VerticalAlignment" Value="Bottom"/>
                <Setter Property="HorizontalAlignment" Value="Right"/>
                <Setter Property="Margin" Value="0,8,8,8"/>
                <Setter Property="Padding" Value="0"/>
            </Style>

            <Style x:Key="PathFakeButtonStyle" TargetType="Button">
                <Setter Property="Foreground" Value="{ThemeResource SystemControlForegroundBaseHighBrush}"/>
                <Setter Property="Background" Value="{ThemeResource SystemControlBackgroundTransparentBrush}"/>
                <Setter Property="BorderThickness" Value="1"/>
                <Setter Property="Margin" Value="1"/>
                <Setter Property="Padding" Value="0"/>
                <Setter Property="MinWidth" Value="32"/>
                <Setter Property="MinHeight" Value="24"/>
                <Setter Property="HorizontalAlignment" Value="Stretch"/>
                <Setter Property="VerticalAlignment" Value="Stretch"/>
                <Setter Property="HorizontalContentAlignment" Value="Center"/>
                <Setter Property="VerticalContentAlignment" Value="Center"/>
                <Setter Property="FontSize" Value="12"/>
                <Setter Property="Template">
                    <Setter.Value>
                        <ControlTemplate TargetType="Button">
                            <Grid x:Name="RootGrid" Background="{TemplateBinding Background}">
                                <VisualStateManager.VisualStateGroups>
                                    <VisualStateGroup x:Name="CommonStates">
                                        <VisualState x:Name="Normal"/>
                                        <VisualState x:Name="PointerOver">
                                            <VisualState.Setters>
                                                <Setter Target="RootGrid.(RevealBrush.State)" Value="PointerOver"/>
                                                <Setter Target="RootGrid.Background" Value="{ThemeResource SystemControlHighlightListLowBrush}"/>
                                                <Setter Target="ContentPresenter.Foreground" Value="{ThemeResource SystemControlHighlightAltBaseHighBrush}"/>
                                            </VisualState.Setters>
                                        </VisualState>
                                        <VisualState x:Name="Pressed">
                                            <VisualState.Setters>
                                                <Setter Target="RootGrid.(RevealBrush.State)" Value="Pressed"/>
                                                <Setter Target="RootGrid.Background" Value="{ThemeResource SystemControlHighlightListMediumBrush}"/>
                                                <Setter Target="ContentPresenter.Foreground" Value="{ThemeResource SystemControlHighlightAltBaseHighBrush}"/>
                                            </VisualState.Setters>
                                        </VisualState>
                                        <VisualState x:Name="Disabled">
                                            <VisualState.Setters>
                                                <Setter Target="ContentPresenter.Foreground" Value="{ThemeResource SystemControlDisabledBaseLowBrush}"/>
                                                <Setter Target="ContentPresenter.BorderThickness" Value="0"/>
                                            </VisualState.Setters>
                                        </VisualState>
                                    </VisualStateGroup>
                                </VisualStateManager.VisualStateGroups>
                                <ContentPresenter x:Name="ContentPresenter"
                                                  Padding="{TemplateBinding Padding}"
                                                  HorizontalAlignment="{TemplateBinding HorizontalAlignment}"
                                                  VerticalAlignment="{TemplateBinding VerticalAlignment}"
                                                  HorizontalContentAlignment="{TemplateBinding HorizontalContentAlignment}"
                                                  VerticalContentAlignment="{TemplateBinding VerticalContentAlignment}"
                                                  BorderBrush="{ThemeResource AppControlForegroundTransparentRevealBorderBrush}"
                                                  BorderThickness="{TemplateBinding BorderThickness}"
                                                  AutomationProperties.AccessibilityView="Raw"
                                                  ContentTemplate="{TemplateBinding ContentTemplate}"
                                                  ContentTransitions="{TemplateBinding ContentTransitions}">
                                    <ContentPresenter.Content>
                                        <PathIcon AutomationProperties.AccessibilityView="Raw" Data="{TemplateBinding Content}"/>
                                    </ContentPresenter.Content>
                                </ContentPresenter>
                            </Grid>
                        </ControlTemplate>
                    </Setter.Value>
                </Setter>
            </Style>

            <Style x:Key="HistoryMemoryItemContainerStyle" TargetType="ListViewItem">
                <Setter Property="Background" Value="{ThemeResource SystemControlBackgroundTransparentBrush}"/>
                <Setter Property="Foreground" Value="{ThemeResource SystemControlForegroundBaseHighBrush}"/>
                <Setter Property="TabNavigation" Value="Local"/>
                <Setter Property="Padding" Value="0"/>
                <Setter Property="HorizontalContentAlignment" Value="Stretch"/>
                <Setter Property="VerticalContentAlignment" Value="Center"/>
                <Setter Property="MinWidth" Value="{ThemeResource ListViewItemMinWidth}"/>
                <Setter Property="MinHeight" Value="{ThemeResource ListViewItemMinHeight}"/>
                <Setter Property="UseSystemFocusVisuals" Value="True"/>
                <Setter Property="Template">
                    <Setter.Value>
                        <ControlTemplate TargetType="ListViewItem">
                            <Grid x:Name="ContentBorder"
                                  Background="{TemplateBinding Background}"
                                  BorderBrush="{TemplateBinding BorderBrush}"
                                  BorderThickness="{TemplateBinding BorderThickness}">
                                <VisualStateManager.VisualStateGroups>
                                    <VisualStateGroup x:Name="CommonStates">
                                        <VisualState x:Name="Normal"/>
                                        <VisualState x:Name="PointerOver">
                                            <VisualState.Setters>
                                                <Setter Target="ContentPresenter.Foreground" Value="{ThemeResource TextFillColorPrimaryBrush}"/>
                                                <Setter Target="BorderBackground.Opacity" Value="1"/>
                                                <Setter Target="BorderBackground.Fill" Value="{ThemeResource AppControlBackgroundSecondaryBrush}"/>
                                                <Setter Target="BorderBackground.Stroke" Value="{ThemeResource AppControlBackgroundSecondaryBrush}"/>
                                            </VisualState.Setters>
                                        </VisualState>
                                        <VisualState x:Name="Pressed">
                                            <VisualState.Setters>
                                                <Setter Target="ContentPresenter.Foreground" Value="{ThemeResource TextFillColorSecondaryBrush}"/>
                                                <Setter Target="BorderBackground.Opacity" Value="1"/>
                                                <Setter Target="BorderBackground.Fill" Value="{ThemeResource AppControlBackgroundSecondaryBrush}"/>
                                                <Setter Target="BorderBackground.Stroke" Value="{ThemeResource AppControlBackgroundSecondaryBrush}"/>
                                            </VisualState.Setters>
                                            <Storyboard>
                                                <PointerDownThemeAnimation TargetName="ContentPresenter"/>
                                            </Storyboard>
                                        </VisualState>
                                        <VisualState x:Name="Selected"/>
                                        <VisualState x:Name="PointerOverSelected"/>
                                        <VisualState x:Name="PressedSelected"/>
                                        <VisualStateGroup.Transitions>
                                            <VisualTransition From="Pressed" To="Normal">
                                                <Storyboard>
                                                    <PointerUpThemeAnimation Storyboard.TargetName="ContentPresenter"/>
                                                </Storyboard>
                                            </VisualTransition>
                                            <VisualTransition From="Pressed" To="PointerOverSelected">
                                                <Storyboard>
                                                    <PointerUpThemeAnimation Storyboard.TargetName="ContentPresenter"/>
                                                </Storyboard>
                                            </VisualTransition>
                                        </VisualStateGroup.Transitions>
                                    </VisualStateGroup>
                                </VisualStateManager.VisualStateGroups>
                                <Rectangle x:Name="BorderBackground"
                                           Fill="{ThemeResource SubtleFillColorTransparentBrush}"
                                           StrokeThickness="{ThemeResource HighContrastStrokeThickness}"
                                           Opacity="0"
                                           IsHitTestVisible="False"
                                           RadiusX="{Binding Source={ThemeResource ControlCornerRadius}, Converter={StaticResource CornerRadiusTopLeftToDoubleConverter}, Mode=OneWay}"
                                           RadiusY="{Binding Source={ThemeResource ControlCornerRadius}, Converter={StaticResource CornerRadiusTopLeftToDoubleConverter}, Mode=OneWay}"/>
                                <Grid x:Name="ContentPresenterGrid"
                                      Margin="0,0,0,0"
                                      Background="Transparent">
                                    <Grid.RenderTransform>
                                        <TranslateTransform x:Name="ContentPresenterTranslateTransform"/>
                                    </Grid.RenderTransform>
                                    <ContentPresenter x:Name="ContentPresenter"
                                                      Margin="{TemplateBinding Padding}"
                                                      HorizontalAlignment="{TemplateBinding HorizontalContentAlignment}"
                                                      VerticalAlignment="{TemplateBinding VerticalContentAlignment}"
                                                      Content="{TemplateBinding Content}"
                                                      ContentTemplate="{TemplateBinding ContentTemplate}"
                                                      ContentTransitions="{TemplateBinding ContentTransitions}"/>
                                </Grid>
                            </Grid>
                        </ControlTemplate>
                    </Setter.Value>
                </Setter>
            </Style>

            <contract7NotPresent:Style x:Key="ConditionalHistoryMemoryItemContainerStyle"
                                       BasedOn="{StaticResource HistoryMemoryItemContainerStyle}"
                                       TargetType="ListViewItem"/>
            <contract7Present:Style x:Key="ConditionalHistoryMemoryItemContainerStyle"
                                    BasedOn="{StaticResource HistoryMemoryItemContainerStyle}"
                                    TargetType="ListViewItem">
                <Setter Property="CornerRadius" Value="{ThemeResource ControlCornerRadius}"/>
            </contract7Present:Style>

            <Style x:Key="OperatorPanelFlyoutStyle"
                   BasedOn="{StaticResource DefaultFlyoutPresenterStyle}"
                   TargetType="FlyoutPresenter">
                <Setter Property="Padding" Value="2"/>
                <Setter Property="Margin" Value="0,-3,0,0"/>
                <Setter Property="MaxWidth" Value="Infinity"/>
                <Setter Property="ScrollViewer.HorizontalScrollMode" Value="Disabled"/>
                <Setter Property="ScrollViewer.HorizontalScrollBarVisibility" Value="Hidden"/>
                <Setter Property="ScrollViewer.VerticalScrollMode" Value="Disabled"/>
                <Setter Property="ScrollViewer.VerticalScrollBarVisibility" Value="Hidden"/>
            </Style>

            <Style TargetType="Controls:OperatorPanelListView">
                <Setter Property="IsTabStop" Value="False"/>
                <Setter Property="TabNavigation" Value="Once"/>
                <Setter Property="IsSwipeEnabled" Value="True"/>
                <Setter Property="Height" Value="Auto"/>
                <Setter Property="HorizontalAlignment" Value="Stretch"/>
                <Setter Property="VerticalAlignment" Value="Stretch"/>
                <Setter Property="SelectionMode" Value="None"/>
                <Setter Property="ScrollViewer.HorizontalScrollBarVisibility" Value="Hidden"/>
                <Setter Property="ScrollViewer.VerticalScrollBarVisibility" Value="Disabled"/>
                <Setter Property="ScrollViewer.HorizontalScrollMode" Value="Enabled"/>
                <Setter Property="ScrollViewer.IsHorizontalRailEnabled" Value="False"/>
                <Setter Property="ScrollViewer.VerticalScrollMode" Value="Disabled"/>
                <Setter Property="ScrollViewer.IsVerticalRailEnabled" Value="False"/>
                <Setter Property="ScrollViewer.ZoomMode" Value="Disabled"/>
                <Setter Property="ScrollViewer.IsDeferredScrollingEnabled" Value="False"/>
                <Setter Property="ScrollViewer.BringIntoViewOnFocusChange" Value="True"/>
                <Setter Property="ItemContainerTransitions">
                    <Setter.Value>
                        <TransitionCollection>
                            <AddDeleteThemeTransition/>
                            <ContentThemeTransition/>
                            <ReorderThemeTransition/>
                        </TransitionCollection>
                    </Setter.Value>
                </Setter>
                <Setter Property="ItemsPanel">
                    <Setter.Value>
                        <ItemsPanelTemplate>
                            <StackPanel Height="Auto"
                                        VerticalAlignment="Stretch"
                                        Orientation="Horizontal"/>
                        </ItemsPanelTemplate>
                    </Setter.Value>
                </Setter>
                <Setter Property="ItemContainerStyle">
                    <Setter.Value>
                        <Style TargetType="ListViewItem">
                            <Setter Property="MinHeight" Value="0"/>
                            <Setter Property="MinWidth" Value="0"/>
                            <Setter Property="Padding" Value="0"/>
                            <Setter Property="Margin" Value="0"/>
                            <Setter Property="IsTabStop" Value="False"/>
                            <Setter Property="HorizontalContentAlignment" Value="Left"/>
                            <Setter Property="VerticalContentAlignment" Value="Stretch"/>
                        </Style>
                    </Setter.Value>
                </Setter>
                <Setter Property="Template">
                    <Setter.Value>
                        <ControlTemplate TargetType="Controls:OperatorPanelListView">
                            <Grid x:Name="Grid">
                                <Grid.Resources>
                                    <SolidColorBrush x:Key="ButtonBackgroundPointerOver" Color="{ThemeResource OperatorPanelScrollButtonBackgroundColor}"/>
                                    <SolidColorBrush x:Key="ButtonBackgroundPressed" Color="{ThemeResource OperatorPanelScrollButtonBackgroundColor}"/>
                                    <SolidColorBrush x:Key="ButtonForegroundPointerOver" Color="{ThemeResource SystemAltMediumHighColor}"/>
                                    <SolidColorBrush x:Key="ButtonBorderBrushPointerOver" Color="{ThemeResource SystemBaseMediumLowColor}"/>
                                    <SolidColorBrush x:Key="ButtonForegroundPressed" Color="{ThemeResource SystemAltMediumHighColor}"/>
                                </Grid.Resources>
                                <Grid.RowDefinitions>
                                    <RowDefinition Height="Auto"/>
                                    <RowDefinition Height="*"/>
                                </Grid.RowDefinitions>

                                <!-- Horizontal divider -->
                                <Rectangle Height="1"
                                           Margin="-3,4,-3,1"
                                           HorizontalAlignment="Stretch"
                                           VerticalAlignment="Top"
                                           Fill="{ThemeResource DividerStrokeColorDefaultBrush}"
                                           RadiusX="0.5"
                                           RadiusY="0.5"/>

                                <ScrollViewer x:Name="ScrollViewer"
                                              Grid.Row="1"
                                              AutomationProperties.AccessibilityView="Raw"
                                              BringIntoViewOnFocusChange="{TemplateBinding ScrollViewer.BringIntoViewOnFocusChange}"
                                              HorizontalScrollBarVisibility="{TemplateBinding ScrollViewer.HorizontalScrollBarVisibility}"
                                              HorizontalScrollMode="{TemplateBinding ScrollViewer.HorizontalScrollMode}"
                                              IsDeferredScrollingEnabled="{TemplateBinding ScrollViewer.IsDeferredScrollingEnabled}"
                                              IsHorizontalRailEnabled="{TemplateBinding ScrollViewer.IsHorizontalRailEnabled}"
                                              IsHorizontalScrollChainingEnabled="{TemplateBinding ScrollViewer.IsHorizontalScrollChainingEnabled}"
                                              IsVerticalRailEnabled="{TemplateBinding ScrollViewer.IsVerticalRailEnabled}"
                                              IsVerticalScrollChainingEnabled="{TemplateBinding ScrollViewer.IsVerticalScrollChainingEnabled}"
                                              TabNavigation="{TemplateBinding TabNavigation}"
                                              VerticalScrollBarVisibility="{TemplateBinding ScrollViewer.VerticalScrollBarVisibility}"
                                              VerticalScrollMode="{TemplateBinding ScrollViewer.VerticalScrollMode}"
                                              ZoomMode="{TemplateBinding ScrollViewer.ZoomMode}">
                                    <ItemsPresenter x:Name="Content"
                                                    Padding="{TemplateBinding Padding}"
                                                    Footer="{TemplateBinding Footer}"
                                                    FooterTemplate="{TemplateBinding FooterTemplate}"
                                                    FooterTransitions="{TemplateBinding FooterTransitions}"
                                                    Header="{TemplateBinding Header}"
                                                    HeaderTemplate="{TemplateBinding HeaderTemplate}"
                                                    HeaderTransitions="{TemplateBinding HeaderTransitions}"/>
                                </ScrollViewer>
                                <Button x:Name="ScrollLeft"
                                        Grid.Row="1"
                                        MinWidth="20"
                                        Padding="0"
                                        HorizontalAlignment="Left"
                                        VerticalAlignment="Stretch"
                                        Background="{ThemeResource OperatorPanelScrollButtonBackgroundBrush}"
                                        Foreground="{ThemeResource SystemAltMediumHighColor}"
                                        FontFamily="{ThemeResource CalculatorFontFamily}"
                                        FontSize="12"
                                        Content="&#xE96F;"
                                        IsTabStop="False"
                                        Visibility="Collapsed"/>
                                <Button x:Name="ScrollRight"
                                        Grid.Row="1"
                                        MinWidth="20"
                                        Padding="0"
                                        HorizontalAlignment="Right"
                                        VerticalAlignment="Stretch"
                                        Background="{ThemeResource OperatorPanelScrollButtonBackgroundBrush}"
                                        Foreground="{ThemeResource SystemAltMediumHighColor}"
                                        FontFamily="{ThemeResource CalculatorFontFamily}"
                                        FontSize="12"
                                        Content="&#xE970;"
                                        IsTabStop="False"
                                        Visibility="Collapsed"/>
                            </Grid>
                        </ControlTemplate>
                    </Setter.Value>
                </Setter>
            </Style>

            <Style x:Key="MathRichEditBoxStyle" TargetType="Controls:MathRichEditBox">
                <Setter Property="Foreground" Value="{ThemeResource TextControlForeground}"/>
                <Setter Property="Background" Value="Transparent"/>
                <Setter Property="ContentLinkForegroundColor" Value="{ThemeResource ContentLinkForegroundColor}"/>
                <Setter Property="ContentLinkBackgroundColor" Value="{ThemeResource ContentLinkBackgroundColor}"/>
                <Setter Property="SelectionHighlightColor" Value="{ThemeResource TextControlSelectionHighlightColor}"/>
                <Setter Property="BorderBrush" Value="{ThemeResource TextControlBorderBrush}"/>
                <Setter Property="BorderThickness" Value="{ThemeResource TextControlBorderThemeThickness}"/>
                <Setter Property="FontFamily" Value="{ThemeResource ContentControlThemeFontFamily}"/>
                <Setter Property="FontSize" Value="{ThemeResource ControlContentThemeFontSize}"/>
                <Setter Property="ScrollViewer.HorizontalScrollMode" Value="Auto"/>
                <Setter Property="ScrollViewer.VerticalScrollMode" Value="Auto"/>
                <Setter Property="ScrollViewer.HorizontalScrollBarVisibility" Value="Hidden"/>
                <Setter Property="ScrollViewer.VerticalScrollBarVisibility" Value="Hidden"/>
                <Setter Property="ScrollViewer.IsDeferredScrollingEnabled" Value="False"/>
                <Setter Property="Padding" Value="{ThemeResource TextControlThemePadding}"/>
                <Setter Property="UseSystemFocusVisuals" Value="{ThemeResource IsApplicationFocusVisualKindReveal}"/>
                <Setter Property="ContextFlyout" Value="{StaticResource TextControlCommandBarContextFlyout}"/>
                <Setter Property="Template">
                    <Setter.Value>
                        <ControlTemplate TargetType="Controls:MathRichEditBox">
                            <Grid>
                                <Grid.RowDefinitions>
                                    <RowDefinition Height="Auto"/>
                                    <RowDefinition Height="*"/>
                                    <RowDefinition Height="Auto"/>
                                </Grid.RowDefinitions>
                                <Grid.ColumnDefinitions>
                                    <ColumnDefinition Width="*"/>
                                    <ColumnDefinition Width="Auto"/>
                                    <ColumnDefinition Width="Auto"/>
                                    <ColumnDefinition Width="Auto"/>
                                </Grid.ColumnDefinitions>
                                <VisualStateManager.VisualStateGroups>
                                    <VisualStateGroup x:Name="CommonStates">
                                        <VisualState x:Name="Disabled">
                                            <Storyboard>
                                                <ObjectAnimationUsingKeyFrames Storyboard.TargetName="HeaderContentPresenter" Storyboard.TargetProperty="Foreground">
                                                    <DiscreteObjectKeyFrame KeyTime="0" Value="{ThemeResource TextControlHeaderForegroundDisabled}"/>
                                                </ObjectAnimationUsingKeyFrames>
                                                <ObjectAnimationUsingKeyFrames Storyboard.TargetName="BorderElement" Storyboard.TargetProperty="Background">
                                                    <DiscreteObjectKeyFrame KeyTime="0" Value="{ThemeResource TextControlBackgroundDisabled}"/>
                                                </ObjectAnimationUsingKeyFrames>
                                                <ObjectAnimationUsingKeyFrames Storyboard.TargetName="BorderElement" Storyboard.TargetProperty="BorderBrush">
                                                    <DiscreteObjectKeyFrame KeyTime="0" Value="{ThemeResource TextControlBorderBrushDisabled}"/>
                                                </ObjectAnimationUsingKeyFrames>
                                                <ObjectAnimationUsingKeyFrames Storyboard.TargetName="ContentElement" Storyboard.TargetProperty="Foreground">
                                                    <DiscreteObjectKeyFrame KeyTime="0" Value="{ThemeResource TextControlForegroundDisabled}"/>
                                                </ObjectAnimationUsingKeyFrames>
                                                <ObjectAnimationUsingKeyFrames Storyboard.TargetName="PlaceholderTextContentPresenter" Storyboard.TargetProperty="Foreground">
                                                    <DiscreteObjectKeyFrame KeyTime="0" Value="{ThemeResource TextControlPlaceholderForegroundDisabled}"/>
                                                </ObjectAnimationUsingKeyFrames>
                                            </Storyboard>
                                        </VisualState>
                                        <VisualState x:Name="Normal"/>
                                        <VisualState x:Name="PointerOver">
                                            <Storyboard>
                                                <ObjectAnimationUsingKeyFrames Storyboard.TargetName="BorderElement" Storyboard.TargetProperty="BorderBrush">
                                                    <DiscreteObjectKeyFrame KeyTime="0" Value="{ThemeResource TextControlBorderBrushPointerOver}"/>
                                                </ObjectAnimationUsingKeyFrames>
                                                <ObjectAnimationUsingKeyFrames Storyboard.TargetName="BorderElement" Storyboard.TargetProperty="Background">
                                                    <DiscreteObjectKeyFrame KeyTime="0" Value="Transparent"/>
                                                </ObjectAnimationUsingKeyFrames>
                                                <ObjectAnimationUsingKeyFrames Storyboard.TargetName="PlaceholderTextContentPresenter" Storyboard.TargetProperty="Foreground">
                                                    <DiscreteObjectKeyFrame KeyTime="0" Value="{ThemeResource TextControlPlaceholderForegroundPointerOver}"/>
                                                </ObjectAnimationUsingKeyFrames>
                                                <ObjectAnimationUsingKeyFrames Storyboard.TargetName="ContentElement" Storyboard.TargetProperty="Foreground">
                                                    <DiscreteObjectKeyFrame KeyTime="0" Value="{ThemeResource TextControlForegroundPointerOver}"/>
                                                </ObjectAnimationUsingKeyFrames>
                                            </Storyboard>
                                        </VisualState>
                                        <VisualState x:Name="Focused">
                                            <Storyboard>
                                                <ObjectAnimationUsingKeyFrames Storyboard.TargetName="PlaceholderTextContentPresenter" Storyboard.TargetProperty="Foreground">
                                                    <DiscreteObjectKeyFrame KeyTime="0" Value="{ThemeResource TextControlPlaceholderForeground}"/>
                                                </ObjectAnimationUsingKeyFrames>
                                                <ObjectAnimationUsingKeyFrames Storyboard.TargetName="BorderElement" Storyboard.TargetProperty="Background">
                                                    <DiscreteObjectKeyFrame KeyTime="0" Value="Transparent"/>
                                                </ObjectAnimationUsingKeyFrames>
                                                <ObjectAnimationUsingKeyFrames Storyboard.TargetName="BorderElement" Storyboard.TargetProperty="BorderBrush">
                                                    <DiscreteObjectKeyFrame KeyTime="0" Value="{ThemeResource TextControlBorderBrushFocused}"/>
                                                </ObjectAnimationUsingKeyFrames>
                                                <ObjectAnimationUsingKeyFrames Storyboard.TargetName="ContentElement" Storyboard.TargetProperty="Foreground">
                                                    <DiscreteObjectKeyFrame KeyTime="0" Value="{ThemeResource TextControlForegroundFocused}"/>
                                                </ObjectAnimationUsingKeyFrames>
                                            </Storyboard>
                                        </VisualState>
                                    </VisualStateGroup>
                                </VisualStateManager.VisualStateGroups>
                                <ContentPresenter x:Name="HeaderContentPresenter"
                                                  Grid.Row="0"
                                                  Grid.Column="0"
                                                  Grid.ColumnSpan="4"
                                                  Margin="{ThemeResource RichEditBoxTopHeaderMargin}"
                                                  VerticalAlignment="Top"
                                                  Foreground="{ThemeResource TextControlHeaderForeground}"
                                                  FontWeight="Normal"
                                                  x:DeferLoadStrategy="Lazy"
                                                  Content="{TemplateBinding Header}"
                                                  ContentTemplate="{TemplateBinding HeaderTemplate}"
                                                  TextWrapping="Wrap"
                                                  Visibility="Collapsed"/>
                                <Border x:Name="BorderElement"
                                        Grid.Row="1"
                                        Grid.RowSpan="1"
                                        Grid.Column="0"
                                        Grid.ColumnSpan="4"
                                        MinWidth="32"
                                        MinHeight="16"
                                        Background="{TemplateBinding Background}"
                                        BorderBrush="{TemplateBinding BorderBrush}"
                                        BorderThickness="{TemplateBinding BorderThickness}"
                                        Control.IsTemplateFocusTarget="True"
                                        CornerRadius="{TemplateBinding CornerRadius}"/>
                                <ScrollViewer x:Name="ContentElement"
                                              Grid.Row="1"
                                              Grid.Column="0"
                                              Margin="{TemplateBinding BorderThickness}"
                                              Padding="{TemplateBinding Padding}"
                                              VerticalAlignment="Center"
                                              AutomationProperties.AccessibilityView="Raw"
                                              HorizontalScrollBarVisibility="{TemplateBinding ScrollViewer.HorizontalScrollBarVisibility}"
                                              HorizontalScrollMode="{TemplateBinding ScrollViewer.HorizontalScrollMode}"
                                              IsDeferredScrollingEnabled="{TemplateBinding ScrollViewer.IsDeferredScrollingEnabled}"
                                              IsHorizontalRailEnabled="{TemplateBinding ScrollViewer.IsHorizontalRailEnabled}"
                                              IsTabStop="False"
                                              IsVerticalRailEnabled="{TemplateBinding ScrollViewer.IsVerticalRailEnabled}"
                                              VerticalScrollBarVisibility="{TemplateBinding ScrollViewer.VerticalScrollBarVisibility}"
                                              VerticalScrollMode="{TemplateBinding ScrollViewer.VerticalScrollMode}"
                                              ZoomMode="Disabled"/>
                                <TextBlock x:Name="PlaceholderTextContentPresenter"
                                           Grid.Row="1"
                                           Grid.Column="0"
                                           Grid.ColumnSpan="4"
                                           Margin="0,-1,0,0"
                                           Padding="{TemplateBinding Padding}"
                                           VerticalAlignment="Center"
                                           Foreground="{ThemeResource TextControlPlaceholderForeground}"
                                           IsHitTestVisible="False"
                                           Text="{TemplateBinding PlaceholderText}"
                                           TextAlignment="{TemplateBinding TextAlignment}"
                                           TextWrapping="{TemplateBinding TextWrapping}"/>
                                <ContentPresenter x:Name="DescriptionPresenter"
                                                  Grid.Row="2"
                                                  Grid.Column="0"
                                                  Grid.ColumnSpan="4"
                                                  Foreground="{ThemeResource SystemControlDescriptionTextForegroundBrush}"
                                                  AutomationProperties.AccessibilityView="Raw"
                                                  Content="{TemplateBinding Description}"
                                                  x:Load="False"/>
                            </Grid>
                        </ControlTemplate>
                    </Setter.Value>
                </Setter>
            </Style>

            <Style x:Key="CategoryNameTextBlockStyle"
                   BasedOn="{StaticResource SubtitleTextBlockStyle}"
                   TargetType="TextBlock">
                <Setter Property="Margin" Value="0,-3,0,0"/>
                <Setter Property="HorizontalAlignment" Value="Left"/>
                <Setter Property="VerticalAlignment" Value="Center"/>
            </Style>

            <Style x:Key="BodyStrongTextBlockStyle"
                   BasedOn="{StaticResource BodyTextBlockStyle}"
                   TargetType="TextBlock">
                <Setter Property="FontWeight" Value="SemiBold"/>
            </Style>

            <Style TargetType="local:TitleBar">
                <Setter Property="ButtonBackground" Value="Transparent"/>
                <Setter Property="ButtonForeground" Value="{ThemeResource SystemControlPageTextBaseHighBrush}"/>
                <Setter Property="ButtonInactiveBackground" Value="Transparent"/>
                <Setter Property="ButtonInactiveForeground" Value="{ThemeResource SystemControlForegroundChromeDisabledLowBrush}"/>
                <Setter Property="ButtonHoverBackground" Value="{ThemeResource SystemControlBackgroundListLowBrush}"/>
                <Setter Property="ButtonHoverForeground" Value="{ThemeResource SystemControlForegroundBaseHighBrush}"/>
                <Setter Property="ButtonPressedBackground" Value="{ThemeResource SystemControlBackgroundListMediumBrush}"/>
                <Setter Property="ButtonPressedForeground" Value="{ThemeResource SystemControlForegroundBaseHighBrush}"/>
            </Style>
        </ResourceDictionary>
    </Application.Resources>
</Application>
