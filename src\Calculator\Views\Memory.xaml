<UserControl x:Class="CalculatorApp.Memory"
             xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
             xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
             xmlns:common="using:CalculatorApp.Common"
             xmlns:controls="using:CalculatorApp.Controls"
             xmlns:converters="using:CalculatorApp.Converters"
             xmlns:d="http://schemas.microsoft.com/expression/blend/2008"
             xmlns:local="using:CalculatorApp"
             xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006"
             xmlns:model="using:CalculatorApp.ViewModel"
             xmlns:utils="using:CalculatorApp.Utils"
             x:Name="MemoryList"
             FlowDirection="LeftToRight"
             mc:Ignorable="d">

    <UserControl.Resources>
        <converters:BooleanToVisibilityConverter x:Key="BooleanToVisibilityConverter"/>
        <converters:BooleanToVisibilityNegationConverter x:Key="BooleanToVisibilityNegationConverter"/>
        <converters:BooleanNegationConverter x:Key="BooleanNegationConverter"/>

        <MenuFlyout x:Name="MemoryContextMenu">
            <MenuFlyoutItem Click="OnClearMenuItemClicked" Text="{utils:ResourceString Name=ClearMemoryMenuItem/Text}">
                <MenuFlyoutItem.Icon>
                    <FontIcon FontFamily="{StaticResource CalculatorFontFamily}" Glyph="&#xf754;"/>
                </MenuFlyoutItem.Icon>
            </MenuFlyoutItem>
            <MenuFlyoutItem Click="OnMemoryAddMenuItemClicked" Text="{utils:ResourceString Name=MemPlusMenuItem/Text}">
                <MenuFlyoutItem.Icon>
                    <FontIcon FontFamily="{StaticResource CalculatorFontFamily}" Glyph="&#xf757;"/>
                </MenuFlyoutItem.Icon>
            </MenuFlyoutItem>
            <MenuFlyoutItem Click="OnMemorySubtractMenuItemClicked" Text="{utils:ResourceString Name=MemMinusMenuItem/Text}">
                <MenuFlyoutItem.Icon>
                    <FontIcon FontFamily="{StaticResource CalculatorFontFamily}" Glyph="&#xf758;"/>
                </MenuFlyoutItem.Icon>
            </MenuFlyoutItem>
        </MenuFlyout>

        <DataTemplate x:Key="MemoryItemTemplate" x:DataType="model:MemoryItemViewModel">
            <local:MemoryListItem AutomationProperties.Name="{x:Bind Value, Mode=OneWay}" Model="{x:Bind Mode=OneWay}"/>
        </DataTemplate>
        <Style x:Key="MemoryItemContainerStyle"
               BasedOn="{StaticResource ConditionalHistoryMemoryItemContainerStyle}"
               TargetType="ListViewItem">
            <Setter Property="Margin" Value="0,0,0,8"/>
            <Setter Property="ContextFlyout" Value="{StaticResource MemoryContextMenu}"/>
        </Style>
    </UserControl.Resources>

    <Grid x:Name="LayoutGrid">
        <Grid.RowDefinitions>
            <RowDefinition Height="*"/>
            <RowDefinition Height="{Binding RowHeight, ElementName=MemoryList, Mode=OneWay}"/>
        </Grid.RowDefinitions>

        <VisualStateManager.VisualStateGroups>
            <VisualStateGroup x:Name="ErrorVisualStates">
                <VisualState x:Name="NoErrorLayout"/>
                <VisualState x:Name="ErrorLayout">
                    <VisualState.Setters>
                        <Setter Target="MemoryListView.IsEnabled" Value="False"/>
                    </VisualState.Setters>
                </VisualState>
            </VisualStateGroup>
            <VisualStateGroup>
                <VisualState x:Name="DockedLayout">
                    <VisualState.StateTriggers>
                        <AdaptiveTrigger MinWindowWidth="560"/>
                    </VisualState.StateTriggers>
                    <VisualState.Setters>
                        <Setter Target="MemoryPanel.(Grid.Row)" Value="0"/>
                        <Setter Target="MemoryPanel.(Grid.RowSpan)" Value="2"/>
                        <Setter Target="MemoryListView.Padding" Value="0"/>
                        <Setter Target="BackgroundShade.Visibility" Value="Collapsed"/>
                    </VisualState.Setters>
                </VisualState>
                <VisualState x:Name="DefaultLayout">
                    <VisualState.StateTriggers>
                        <AdaptiveTrigger MinWindowWidth="0"/>
                    </VisualState.StateTriggers>
                </VisualState>
            </VisualStateGroup>
        </VisualStateManager.VisualStateGroups>
        <Border x:Name="BackgroundShade"
                Grid.Row="1"
                Margin="0,-1,0,0"
                Padding="0"
                HorizontalAlignment="Stretch"
                VerticalAlignment="Stretch"
                Background="{ThemeResource SolidBackgroundFillColorBase}"
                BorderBrush="{ThemeResource SystemControlForegroundChromeHighBrush}"
                BorderThickness="{ThemeResource HighContrastThicknessTop}"
                CornerRadius="{ThemeResource OverlayCornerRadius}"/>
        <Grid x:Name="MemoryPanel" Grid.Row="1">
            <Grid.RowDefinitions>
                <RowDefinition Height="*"/>
                <RowDefinition Height="Auto"/>
            </Grid.RowDefinitions>
            <TextBlock x:Name="MemoryPaneEmpty"
                       Margin="12,8,24,0"
                       Style="{ThemeResource BodyTextBlockStyle}"
                       Foreground="{ThemeResource TextFillColorPrimaryBrush}"
                       Text="{utils:ResourceString Name=MemoryPaneEmpty/Text}"
                       TextWrapping="Wrap"
                       Visibility="{Binding IsMemoryEmpty, Converter={StaticResource BooleanToVisibilityConverter}}"/>
            <ListView x:Name="MemoryListView"
                      Margin="4,0"
                      Padding="0,24,0,0"
                      HorizontalAlignment="Stretch"
                      IsItemClickEnabled="true"
                      ItemClick="MemoryListItemClick"
                      ItemContainerStyle="{ThemeResource MemoryItemContainerStyle}"
                      ItemTemplate="{ThemeResource MemoryItemTemplate}"
                      ItemsSource="{x:Bind Model.MemorizedNumbers, Mode=OneWay}"
                      SelectionMode="None"
                      TabIndex="0"
                      Visibility="{x:Bind Model.IsMemoryEmpty, Mode=OneWay, Converter={StaticResource BooleanToVisibilityNegationConverter}}">
                <ListView.ItemContainerTransitions>
                    <TransitionCollection>
                        <AddDeleteThemeTransition/>
                        <ContentThemeTransition/>
                        <ReorderThemeTransition/>
                    </TransitionCollection>
                </ListView.ItemContainerTransitions>
            </ListView>
            <Button x:Name="ClearMemory"
                    Grid.Row="1"
                    Style="{ThemeResource ClearAllHistoryMemoryButtonStyle}"
                    AccessKey="{utils:ResourceString Name=ClearMemory/AccessKey}"
                    AutomationProperties.AutomationId="ClearMemory"
                    AutomationProperties.Name="{utils:ResourceString Name=ClearMemory/[using:Windows.UI.Xaml.Automation]AutomationProperties/Name}"
                    Command="{x:Bind Model.ClearMemoryCommand, Mode=OneWay}"
                    Content="&#xE74D;"
                    ToolTipService.ToolTip="{utils:ResourceString Name=ClearMemory/[using:Windows.UI.Xaml.Controls]ToolTipService/ToolTip}"
                    Visibility="{x:Bind Model.IsMemoryEmpty, Mode=OneWay, Converter={StaticResource BooleanToVisibilityNegationConverter}}"/>
        </Grid>

    </Grid>
</UserControl>
