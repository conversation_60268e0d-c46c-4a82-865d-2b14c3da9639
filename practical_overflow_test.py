#!/usr/bin/env python3
"""
Practical Windows Calculator Overflow Testing Script
This script tests for integer overflow vulnerabilities by sending
large inputs to the Windows Calculator application.
"""

import time
import subprocess
import pyautogui
import psutil
import sys
from typing import List, Tuple

class CalculatorOverflowTester:
    def __init__(self):
        self.calc_process = None
        self.initial_memory = 0
        
    def start_calculator(self) -> bool:
        """Start Windows Calculator application"""
        try:
            print("Starting Windows Calculator...")
            subprocess.Popen("calc.exe")
            time.sleep(2)  # Wait for calculator to start
            
            # Find calculator process
            for proc in psutil.process_iter(['pid', 'name']):
                if 'calculator' in proc.info['name'].lower() or 'calc' in proc.info['name'].lower():
                    self.calc_process = proc
                    self.initial_memory = proc.memory_info().rss
                    print(f"Calculator started (PID: {proc.info['pid']})")
                    print(f"Initial memory usage: {self.initial_memory / 1024 / 1024:.2f} MB")
                    return True
            
            print("Could not find calculator process")
            return False
            
        except Exception as e:
            print(f"Error starting calculator: {e}")
            return False
    
    def get_memory_usage(self) -> float:
        """Get current memory usage of calculator process"""
        if self.calc_process and self.calc_process.is_running():
            try:
                return self.calc_process.memory_info().rss / 1024 / 1024  # MB
            except:
                return 0
        return 0
    
    def send_input(self, input_string: str, delay: float = 0.1) -> bool:
        """Send input to calculator"""
        try:
            # Focus on calculator window
            pyautogui.getWindowsWithTitle("Calculator")[0].activate()
            time.sleep(0.5)
            
            # Clear calculator first
            pyautogui.press('escape')
            time.sleep(0.2)
            
            # Send the input string
            for char in input_string:
                pyautogui.press(char)
                time.sleep(delay)
                
            return True
        except Exception as e:
            print(f"Error sending input: {e}")
            return False
    
    def test_large_number_input(self) -> None:
        """Test 1: Large number input to trigger memory allocation"""
        print("\n=== Test 1: Large Number Input ===")
        
        test_cases = [
            ("Small", "9" * 50),
            ("Medium", "9" * 500),
            ("Large", "9" * 1000),
            ("Very Large", "9" * 5000),
        ]
        
        for name, number in test_cases:
            print(f"\nTesting {name} number ({len(number)} digits)...")
            
            initial_mem = self.get_memory_usage()
            print(f"Memory before: {initial_mem:.2f} MB")
            
            if self.send_input(number):
                time.sleep(2)  # Wait for processing
                
                final_mem = self.get_memory_usage()
                print(f"Memory after: {final_mem:.2f} MB")
                print(f"Memory increase: {final_mem - initial_mem:.2f} MB")
                
                if final_mem - initial_mem > 100:  # More than 100MB increase
                    print("🚨 POTENTIAL MEMORY ISSUE: Large memory increase detected!")
                
                # Check if calculator is still responsive
                if not self.calc_process.is_running():
                    print("🚨 CRASH DETECTED: Calculator process terminated!")
                    return
            else:
                print("❌ Failed to send input")
    
    def test_multiplication_overflow(self) -> None:
        """Test 2: Multiplication operations that could cause overflow"""
        print("\n=== Test 2: Multiplication Overflow ===")
        
        test_cases = [
            ("Large * Large", "999999999", "*", "999999999"),
            ("Max 32-bit * Max 32-bit", "4294967295", "*", "4294967295"),
            ("Scientific notation", "9.999e+308", "*", "9.999e+308"),
        ]
        
        for name, num1, op, num2 in test_cases:
            print(f"\nTesting {name}: {num1} {op} {num2}")
            
            initial_mem = self.get_memory_usage()
            
            # Input first number
            if self.send_input(num1):
                time.sleep(0.5)
                
                # Input operator
                pyautogui.press(op)
                time.sleep(0.5)
                
                # Input second number
                if self.send_input(num2):
                    time.sleep(0.5)
                    
                    # Press equals
                    pyautogui.press('enter')
                    time.sleep(2)
                    
                    final_mem = self.get_memory_usage()
                    print(f"Memory change: {final_mem - initial_mem:.2f} MB")
                    
                    if not self.calc_process.is_running():
                        print("🚨 CRASH DETECTED: Calculator crashed during operation!")
                        return
    
    def test_factorial_overflow(self) -> None:
        """Test 3: Factorial operations that could cause overflow"""
        print("\n=== Test 3: Factorial Overflow ===")
        
        # Note: Windows Calculator might not have factorial, but we can test large powers
        test_cases = [100, 200, 500, 1000]
        
        for num in test_cases:
            print(f"\nTesting large number operations with {num}...")
            
            initial_mem = self.get_memory_usage()
            
            # Try to input large number and perform operations
            if self.send_input(str(num)):
                time.sleep(0.5)
                
                # Try squaring it (x^2)
                pyautogui.hotkey('ctrl', 'shift', '2')  # x^2 shortcut if available
                time.sleep(2)
                
                final_mem = self.get_memory_usage()
                print(f"Memory change: {final_mem - initial_mem:.2f} MB")
                
                if not self.calc_process.is_running():
                    print("🚨 CRASH DETECTED!")
                    return
    
    def test_copy_paste_attack(self) -> None:
        """Test 4: Copy-paste large numbers to bypass input validation"""
        print("\n=== Test 4: Copy-Paste Attack ===")
        
        # Create extremely large number
        large_number = "9" * 10000
        
        print(f"Testing copy-paste of {len(large_number)} digit number...")
        
        try:
            # Copy to clipboard
            pyautogui.copy(large_number)
            time.sleep(0.5)
            
            # Focus calculator and paste
            pyautogui.getWindowsWithTitle("Calculator")[0].activate()
            time.sleep(0.5)
            
            initial_mem = self.get_memory_usage()
            
            # Paste the large number
            pyautogui.hotkey('ctrl', 'v')
            time.sleep(3)  # Wait for processing
            
            final_mem = self.get_memory_usage()
            print(f"Memory change: {final_mem - initial_mem:.2f} MB")
            
            if final_mem - initial_mem > 50:
                print("🚨 LARGE MEMORY ALLOCATION DETECTED!")
            
            if not self.calc_process.is_running():
                print("🚨 CRASH DETECTED!")
                
        except Exception as e:
            print(f"Error in copy-paste test: {e}")
    
    def run_all_tests(self) -> None:
        """Run all overflow tests"""
        print("Windows Calculator Overflow Vulnerability Tests")
        print("=" * 50)
        
        if not self.start_calculator():
            print("Failed to start calculator. Exiting.")
            return
        
        try:
            self.test_large_number_input()
            self.test_multiplication_overflow()
            self.test_factorial_overflow()
            self.test_copy_paste_attack()
            
        except KeyboardInterrupt:
            print("\nTests interrupted by user")
        except Exception as e:
            print(f"\nUnexpected error: {e}")
        finally:
            print("\nTest completed. Check for any crashes or memory issues.")
            if self.calc_process and self.calc_process.is_running():
                final_memory = self.get_memory_usage()
                print(f"Final memory usage: {final_memory:.2f} MB")
                print(f"Total memory increase: {final_memory - (self.initial_memory/1024/1024):.2f} MB")

def main():
    """Main function"""
    print("Installing required packages...")
    try:
        import pyautogui
        import psutil
    except ImportError:
        print("Installing required packages...")
        subprocess.check_call([sys.executable, "-m", "pip", "install", "pyautogui", "psutil"])
        import pyautogui
        import psutil
    
    # Set up pyautogui
    pyautogui.FAILSAFE = True
    pyautogui.PAUSE = 0.1
    
    tester = CalculatorOverflowTester()
    tester.run_all_tests()

if __name__ == "__main__":
    main()
