﻿
Microsoft Visual Studio Solution File, Format Version 12.00
# Visual Studio Version 17
VisualStudioVersion = 17.0.0.0
MinimumVisualStudioVersion = 17.0.0.0
Project("{2150E333-8FDC-42A3-9474-1A3956D46DE8}") = "Solution Items", "Solution Items", "{3A5DF651-B8A1-45CA-9135-964A6FC7F5D1}"
	ProjectSection(SolutionItems) = preProject
		..\.clang-format = ..\.clang-format
		nuget.config = nuget.config
	EndProjectSection
EndProject
Project("{FAE04EC0-301F-11D3-BF4B-00C04F79EFBC}") = "Calculator", "Calculator\Calculator.csproj", "{3B773403-B0D6-4F9A-948E-512A7A5FB315}"
EndProject
Project("{8BC9CEB8-8B4A-11D0-8D11-00A0C91BC942}") = "CalcManager", "CalcManager\CalcManager.vcxproj", "{311E866D-8B93-4609-A691-265941FEE101}"
EndProject
Project("{8BC9CEB8-8B4A-11D0-8D11-00A0C91BC942}") = "CalcViewModel", "CalcViewModel\CalcViewModel.vcxproj", "{812D1A7B-B8AC-49E4-8E6D-AF5D59500D56}"
EndProject
Project("{8BC9CEB8-8B4A-11D0-8D11-00A0C91BC942}") = "CalculatorUnitTests", "CalculatorUnitTests\CalculatorUnitTests.vcxproj", "{D3BAED2C-4B07-4E1D-8807-9D6499450349}"
EndProject
Project("{9A19103F-16F7-4668-BE54-9A1E7A4F7556}") = "CalculatorUITests", "CalculatorUITests\CalculatorUITests.csproj", "{B2C5ADFF-D6B5-48C1-BB8C-571BFD583D7F}"
EndProject
Project("{9A19103F-16F7-4668-BE54-9A1E7A4F7556}") = "CalculatorUITestFramework", "CalculatorUITestFramework\CalculatorUITestFramework.csproj", "{96454213-94AF-457D-9DF9-B14F80E7770F}"
EndProject
Project("{8BC9CEB8-8B4A-11D0-8D11-00A0C91BC942}") = "GraphingImpl", "GraphingImpl\GraphingImpl.vcxproj", "{52E03A58-B378-4F50-8BFB-F659FB85E790}"
EndProject
Project("{8BC9CEB8-8B4A-11D0-8D11-00A0C91BC942}") = "GraphControl", "GraphControl\GraphControl.vcxproj", "{E727A92B-F149-492C-8117-C039A298719B}"
EndProject
Project("{8BC9CEB8-8B4A-11D0-8D11-00A0C91BC942}") = "TraceLogging", "TraceLogging\TraceLogging.vcxproj", "{FC81FF41-02CD-4CD9-9BC5-45A1E39AC6ED}"
EndProject
Project("{8BC9CEB8-8B4A-11D0-8D11-00A0C91BC942}") = "CalcViewModelCopyForUT", "CalcViewModelCopyForUT\CalcViewModelCopyForUT.vcxproj", "{CC9B4FA7-D746-4F52-9401-0AD1B4D6B16D}"
EndProject
Project("{FAE04EC0-301F-11D3-BF4B-00C04F79EFBC}") = "Calculator.ManagedViewModels", "Calculator.ManagedViewModels\Calculator.ManagedViewModels.csproj", "{2179CFDE-CDED-4DF0-8C24-A0EF6B425771}"
EndProject
Global
	GlobalSection(SolutionConfigurationPlatforms) = preSolution
		Debug|ARM = Debug|ARM
		Debug|ARM64 = Debug|ARM64
		Debug|x64 = Debug|x64
		Debug|x86 = Debug|x86
		Release|ARM = Release|ARM
		Release|ARM64 = Release|ARM64
		Release|x64 = Release|x64
		Release|x86 = Release|x86
	EndGlobalSection
	GlobalSection(ProjectConfigurationPlatforms) = postSolution
		{3B773403-B0D6-4F9A-948E-512A7A5FB315}.Debug|ARM.ActiveCfg = Debug|ARM
		{3B773403-B0D6-4F9A-948E-512A7A5FB315}.Debug|ARM.Build.0 = Debug|ARM
		{3B773403-B0D6-4F9A-948E-512A7A5FB315}.Debug|ARM.Deploy.0 = Debug|ARM
		{3B773403-B0D6-4F9A-948E-512A7A5FB315}.Debug|ARM64.ActiveCfg = Debug|ARM64
		{3B773403-B0D6-4F9A-948E-512A7A5FB315}.Debug|ARM64.Build.0 = Debug|ARM64
		{3B773403-B0D6-4F9A-948E-512A7A5FB315}.Debug|ARM64.Deploy.0 = Debug|ARM64
		{3B773403-B0D6-4F9A-948E-512A7A5FB315}.Debug|x64.ActiveCfg = Debug|x64
		{3B773403-B0D6-4F9A-948E-512A7A5FB315}.Debug|x64.Build.0 = Debug|x64
		{3B773403-B0D6-4F9A-948E-512A7A5FB315}.Debug|x64.Deploy.0 = Debug|x64
		{3B773403-B0D6-4F9A-948E-512A7A5FB315}.Debug|x86.ActiveCfg = Debug|x86
		{3B773403-B0D6-4F9A-948E-512A7A5FB315}.Debug|x86.Build.0 = Debug|x86
		{3B773403-B0D6-4F9A-948E-512A7A5FB315}.Debug|x86.Deploy.0 = Debug|x86
		{3B773403-B0D6-4F9A-948E-512A7A5FB315}.Release|ARM.ActiveCfg = Release|ARM
		{3B773403-B0D6-4F9A-948E-512A7A5FB315}.Release|ARM.Build.0 = Release|ARM
		{3B773403-B0D6-4F9A-948E-512A7A5FB315}.Release|ARM.Deploy.0 = Release|ARM
		{3B773403-B0D6-4F9A-948E-512A7A5FB315}.Release|ARM64.ActiveCfg = Release|ARM64
		{3B773403-B0D6-4F9A-948E-512A7A5FB315}.Release|ARM64.Build.0 = Release|ARM64
		{3B773403-B0D6-4F9A-948E-512A7A5FB315}.Release|ARM64.Deploy.0 = Release|ARM64
		{3B773403-B0D6-4F9A-948E-512A7A5FB315}.Release|x64.ActiveCfg = Release|x64
		{3B773403-B0D6-4F9A-948E-512A7A5FB315}.Release|x64.Build.0 = Release|x64
		{3B773403-B0D6-4F9A-948E-512A7A5FB315}.Release|x64.Deploy.0 = Release|x64
		{3B773403-B0D6-4F9A-948E-512A7A5FB315}.Release|x86.ActiveCfg = Release|x86
		{3B773403-B0D6-4F9A-948E-512A7A5FB315}.Release|x86.Build.0 = Release|x86
		{3B773403-B0D6-4F9A-948E-512A7A5FB315}.Release|x86.Deploy.0 = Release|x86
		{311E866D-8B93-4609-A691-265941FEE101}.Debug|ARM.ActiveCfg = Debug|ARM
		{311E866D-8B93-4609-A691-265941FEE101}.Debug|ARM.Build.0 = Debug|ARM
		{311E866D-8B93-4609-A691-265941FEE101}.Debug|ARM64.ActiveCfg = Debug|ARM64
		{311E866D-8B93-4609-A691-265941FEE101}.Debug|ARM64.Build.0 = Debug|ARM64
		{311E866D-8B93-4609-A691-265941FEE101}.Debug|x64.ActiveCfg = Debug|x64
		{311E866D-8B93-4609-A691-265941FEE101}.Debug|x64.Build.0 = Debug|x64
		{311E866D-8B93-4609-A691-265941FEE101}.Debug|x86.ActiveCfg = Debug|Win32
		{311E866D-8B93-4609-A691-265941FEE101}.Debug|x86.Build.0 = Debug|Win32
		{311E866D-8B93-4609-A691-265941FEE101}.Release|ARM.ActiveCfg = Release|ARM
		{311E866D-8B93-4609-A691-265941FEE101}.Release|ARM.Build.0 = Release|ARM
		{311E866D-8B93-4609-A691-265941FEE101}.Release|ARM64.ActiveCfg = Release|ARM64
		{311E866D-8B93-4609-A691-265941FEE101}.Release|ARM64.Build.0 = Release|ARM64
		{311E866D-8B93-4609-A691-265941FEE101}.Release|x64.ActiveCfg = Release|x64
		{311E866D-8B93-4609-A691-265941FEE101}.Release|x64.Build.0 = Release|x64
		{311E866D-8B93-4609-A691-265941FEE101}.Release|x86.ActiveCfg = Release|Win32
		{311E866D-8B93-4609-A691-265941FEE101}.Release|x86.Build.0 = Release|Win32
		{812D1A7B-B8AC-49E4-8E6D-AF5D59500D56}.Debug|ARM.ActiveCfg = Debug|ARM
		{812D1A7B-B8AC-49E4-8E6D-AF5D59500D56}.Debug|ARM.Build.0 = Debug|ARM
		{812D1A7B-B8AC-49E4-8E6D-AF5D59500D56}.Debug|ARM64.ActiveCfg = Debug|ARM64
		{812D1A7B-B8AC-49E4-8E6D-AF5D59500D56}.Debug|ARM64.Build.0 = Debug|ARM64
		{812D1A7B-B8AC-49E4-8E6D-AF5D59500D56}.Debug|x64.ActiveCfg = Debug|x64
		{812D1A7B-B8AC-49E4-8E6D-AF5D59500D56}.Debug|x64.Build.0 = Debug|x64
		{812D1A7B-B8AC-49E4-8E6D-AF5D59500D56}.Debug|x86.ActiveCfg = Debug|Win32
		{812D1A7B-B8AC-49E4-8E6D-AF5D59500D56}.Debug|x86.Build.0 = Debug|Win32
		{812D1A7B-B8AC-49E4-8E6D-AF5D59500D56}.Release|ARM.ActiveCfg = Release|ARM
		{812D1A7B-B8AC-49E4-8E6D-AF5D59500D56}.Release|ARM.Build.0 = Release|ARM
		{812D1A7B-B8AC-49E4-8E6D-AF5D59500D56}.Release|ARM64.ActiveCfg = Release|ARM64
		{812D1A7B-B8AC-49E4-8E6D-AF5D59500D56}.Release|ARM64.Build.0 = Release|ARM64
		{812D1A7B-B8AC-49E4-8E6D-AF5D59500D56}.Release|x64.ActiveCfg = Release|x64
		{812D1A7B-B8AC-49E4-8E6D-AF5D59500D56}.Release|x64.Build.0 = Release|x64
		{812D1A7B-B8AC-49E4-8E6D-AF5D59500D56}.Release|x86.ActiveCfg = Release|Win32
		{812D1A7B-B8AC-49E4-8E6D-AF5D59500D56}.Release|x86.Build.0 = Release|Win32
		{D3BAED2C-4B07-4E1D-8807-9D6499450349}.Debug|ARM.ActiveCfg = Debug|ARM
		{D3BAED2C-4B07-4E1D-8807-9D6499450349}.Debug|ARM64.ActiveCfg = Debug|ARM64
		{D3BAED2C-4B07-4E1D-8807-9D6499450349}.Debug|x64.ActiveCfg = Debug|x64
		{D3BAED2C-4B07-4E1D-8807-9D6499450349}.Debug|x64.Build.0 = Debug|x64
		{D3BAED2C-4B07-4E1D-8807-9D6499450349}.Debug|x64.Deploy.0 = Debug|x64
		{D3BAED2C-4B07-4E1D-8807-9D6499450349}.Debug|x86.ActiveCfg = Debug|Win32
		{D3BAED2C-4B07-4E1D-8807-9D6499450349}.Debug|x86.Build.0 = Debug|Win32
		{D3BAED2C-4B07-4E1D-8807-9D6499450349}.Debug|x86.Deploy.0 = Debug|Win32
		{D3BAED2C-4B07-4E1D-8807-9D6499450349}.Release|ARM.ActiveCfg = Release|ARM
		{D3BAED2C-4B07-4E1D-8807-9D6499450349}.Release|ARM64.ActiveCfg = Release|ARM64
		{D3BAED2C-4B07-4E1D-8807-9D6499450349}.Release|x64.ActiveCfg = Release|x64
		{D3BAED2C-4B07-4E1D-8807-9D6499450349}.Release|x64.Build.0 = Release|x64
		{D3BAED2C-4B07-4E1D-8807-9D6499450349}.Release|x64.Deploy.0 = Release|x64
		{D3BAED2C-4B07-4E1D-8807-9D6499450349}.Release|x86.ActiveCfg = Release|Win32
		{D3BAED2C-4B07-4E1D-8807-9D6499450349}.Release|x86.Build.0 = Release|Win32
		{D3BAED2C-4B07-4E1D-8807-9D6499450349}.Release|x86.Deploy.0 = Release|Win32
		{B2C5ADFF-D6B5-48C1-BB8C-571BFD583D7F}.Debug|ARM.ActiveCfg = Debug|Any CPU
		{B2C5ADFF-D6B5-48C1-BB8C-571BFD583D7F}.Debug|ARM.Build.0 = Debug|Any CPU
		{B2C5ADFF-D6B5-48C1-BB8C-571BFD583D7F}.Debug|ARM64.ActiveCfg = Debug|Any CPU
		{B2C5ADFF-D6B5-48C1-BB8C-571BFD583D7F}.Debug|ARM64.Build.0 = Debug|Any CPU
		{B2C5ADFF-D6B5-48C1-BB8C-571BFD583D7F}.Debug|x64.ActiveCfg = Debug|Any CPU
		{B2C5ADFF-D6B5-48C1-BB8C-571BFD583D7F}.Debug|x64.Build.0 = Debug|Any CPU
		{B2C5ADFF-D6B5-48C1-BB8C-571BFD583D7F}.Debug|x86.ActiveCfg = Debug|Any CPU
		{B2C5ADFF-D6B5-48C1-BB8C-571BFD583D7F}.Debug|x86.Build.0 = Debug|Any CPU
		{B2C5ADFF-D6B5-48C1-BB8C-571BFD583D7F}.Release|ARM.ActiveCfg = Release|Any CPU
		{B2C5ADFF-D6B5-48C1-BB8C-571BFD583D7F}.Release|ARM.Build.0 = Release|Any CPU
		{B2C5ADFF-D6B5-48C1-BB8C-571BFD583D7F}.Release|ARM64.ActiveCfg = Release|Any CPU
		{B2C5ADFF-D6B5-48C1-BB8C-571BFD583D7F}.Release|ARM64.Build.0 = Release|Any CPU
		{B2C5ADFF-D6B5-48C1-BB8C-571BFD583D7F}.Release|x64.ActiveCfg = Release|Any CPU
		{B2C5ADFF-D6B5-48C1-BB8C-571BFD583D7F}.Release|x64.Build.0 = Release|Any CPU
		{B2C5ADFF-D6B5-48C1-BB8C-571BFD583D7F}.Release|x86.ActiveCfg = Release|Any CPU
		{B2C5ADFF-D6B5-48C1-BB8C-571BFD583D7F}.Release|x86.Build.0 = Release|Any CPU
		{96454213-94AF-457D-9DF9-B14F80E7770F}.Debug|ARM.ActiveCfg = Debug|Any CPU
		{96454213-94AF-457D-9DF9-B14F80E7770F}.Debug|ARM.Build.0 = Debug|Any CPU
		{96454213-94AF-457D-9DF9-B14F80E7770F}.Debug|ARM64.ActiveCfg = Debug|Any CPU
		{96454213-94AF-457D-9DF9-B14F80E7770F}.Debug|ARM64.Build.0 = Debug|Any CPU
		{96454213-94AF-457D-9DF9-B14F80E7770F}.Debug|x64.ActiveCfg = Debug|Any CPU
		{96454213-94AF-457D-9DF9-B14F80E7770F}.Debug|x64.Build.0 = Debug|Any CPU
		{96454213-94AF-457D-9DF9-B14F80E7770F}.Debug|x86.ActiveCfg = Debug|Any CPU
		{96454213-94AF-457D-9DF9-B14F80E7770F}.Debug|x86.Build.0 = Debug|Any CPU
		{96454213-94AF-457D-9DF9-B14F80E7770F}.Release|ARM.ActiveCfg = Release|Any CPU
		{96454213-94AF-457D-9DF9-B14F80E7770F}.Release|ARM.Build.0 = Release|Any CPU
		{96454213-94AF-457D-9DF9-B14F80E7770F}.Release|ARM64.ActiveCfg = Release|Any CPU
		{96454213-94AF-457D-9DF9-B14F80E7770F}.Release|ARM64.Build.0 = Release|Any CPU
		{96454213-94AF-457D-9DF9-B14F80E7770F}.Release|x64.ActiveCfg = Release|Any CPU
		{96454213-94AF-457D-9DF9-B14F80E7770F}.Release|x64.Build.0 = Release|Any CPU
		{96454213-94AF-457D-9DF9-B14F80E7770F}.Release|x86.ActiveCfg = Release|Any CPU
		{96454213-94AF-457D-9DF9-B14F80E7770F}.Release|x86.Build.0 = Release|Any CPU
		{52E03A58-B378-4F50-8BFB-F659FB85E790}.Debug|ARM.ActiveCfg = Debug|ARM
		{52E03A58-B378-4F50-8BFB-F659FB85E790}.Debug|ARM.Build.0 = Debug|ARM
		{52E03A58-B378-4F50-8BFB-F659FB85E790}.Debug|ARM64.ActiveCfg = Debug|ARM64
		{52E03A58-B378-4F50-8BFB-F659FB85E790}.Debug|ARM64.Build.0 = Debug|ARM64
		{52E03A58-B378-4F50-8BFB-F659FB85E790}.Debug|x64.ActiveCfg = Debug|x64
		{52E03A58-B378-4F50-8BFB-F659FB85E790}.Debug|x64.Build.0 = Debug|x64
		{52E03A58-B378-4F50-8BFB-F659FB85E790}.Debug|x86.ActiveCfg = Debug|Win32
		{52E03A58-B378-4F50-8BFB-F659FB85E790}.Debug|x86.Build.0 = Debug|Win32
		{52E03A58-B378-4F50-8BFB-F659FB85E790}.Release|ARM.ActiveCfg = Release|ARM
		{52E03A58-B378-4F50-8BFB-F659FB85E790}.Release|ARM.Build.0 = Release|ARM
		{52E03A58-B378-4F50-8BFB-F659FB85E790}.Release|ARM64.ActiveCfg = Release|ARM64
		{52E03A58-B378-4F50-8BFB-F659FB85E790}.Release|ARM64.Build.0 = Release|ARM64
		{52E03A58-B378-4F50-8BFB-F659FB85E790}.Release|x64.ActiveCfg = Release|x64
		{52E03A58-B378-4F50-8BFB-F659FB85E790}.Release|x64.Build.0 = Release|x64
		{52E03A58-B378-4F50-8BFB-F659FB85E790}.Release|x86.ActiveCfg = Release|Win32
		{52E03A58-B378-4F50-8BFB-F659FB85E790}.Release|x86.Build.0 = Release|Win32
		{E727A92B-F149-492C-8117-C039A298719B}.Debug|ARM.ActiveCfg = Debug|ARM
		{E727A92B-F149-492C-8117-C039A298719B}.Debug|ARM.Build.0 = Debug|ARM
		{E727A92B-F149-492C-8117-C039A298719B}.Debug|ARM64.ActiveCfg = Debug|ARM64
		{E727A92B-F149-492C-8117-C039A298719B}.Debug|ARM64.Build.0 = Debug|ARM64
		{E727A92B-F149-492C-8117-C039A298719B}.Debug|x64.ActiveCfg = Debug|x64
		{E727A92B-F149-492C-8117-C039A298719B}.Debug|x64.Build.0 = Debug|x64
		{E727A92B-F149-492C-8117-C039A298719B}.Debug|x86.ActiveCfg = Debug|Win32
		{E727A92B-F149-492C-8117-C039A298719B}.Debug|x86.Build.0 = Debug|Win32
		{E727A92B-F149-492C-8117-C039A298719B}.Release|ARM.ActiveCfg = Release|ARM
		{E727A92B-F149-492C-8117-C039A298719B}.Release|ARM.Build.0 = Release|ARM
		{E727A92B-F149-492C-8117-C039A298719B}.Release|ARM64.ActiveCfg = Release|ARM64
		{E727A92B-F149-492C-8117-C039A298719B}.Release|ARM64.Build.0 = Release|ARM64
		{E727A92B-F149-492C-8117-C039A298719B}.Release|x64.ActiveCfg = Release|x64
		{E727A92B-F149-492C-8117-C039A298719B}.Release|x64.Build.0 = Release|x64
		{E727A92B-F149-492C-8117-C039A298719B}.Release|x86.ActiveCfg = Release|Win32
		{E727A92B-F149-492C-8117-C039A298719B}.Release|x86.Build.0 = Release|Win32
		{FC81FF41-02CD-4CD9-9BC5-45A1E39AC6ED}.Debug|ARM.ActiveCfg = Debug|ARM
		{FC81FF41-02CD-4CD9-9BC5-45A1E39AC6ED}.Debug|ARM.Build.0 = Debug|ARM
		{FC81FF41-02CD-4CD9-9BC5-45A1E39AC6ED}.Debug|ARM64.ActiveCfg = Debug|ARM64
		{FC81FF41-02CD-4CD9-9BC5-45A1E39AC6ED}.Debug|ARM64.Build.0 = Debug|ARM64
		{FC81FF41-02CD-4CD9-9BC5-45A1E39AC6ED}.Debug|x64.ActiveCfg = Debug|x64
		{FC81FF41-02CD-4CD9-9BC5-45A1E39AC6ED}.Debug|x64.Build.0 = Debug|x64
		{FC81FF41-02CD-4CD9-9BC5-45A1E39AC6ED}.Debug|x86.ActiveCfg = Debug|Win32
		{FC81FF41-02CD-4CD9-9BC5-45A1E39AC6ED}.Debug|x86.Build.0 = Debug|Win32
		{FC81FF41-02CD-4CD9-9BC5-45A1E39AC6ED}.Release|ARM.ActiveCfg = Release|ARM
		{FC81FF41-02CD-4CD9-9BC5-45A1E39AC6ED}.Release|ARM.Build.0 = Release|ARM
		{FC81FF41-02CD-4CD9-9BC5-45A1E39AC6ED}.Release|ARM64.ActiveCfg = Release|ARM64
		{FC81FF41-02CD-4CD9-9BC5-45A1E39AC6ED}.Release|ARM64.Build.0 = Release|ARM64
		{FC81FF41-02CD-4CD9-9BC5-45A1E39AC6ED}.Release|x64.ActiveCfg = Release|x64
		{FC81FF41-02CD-4CD9-9BC5-45A1E39AC6ED}.Release|x64.Build.0 = Release|x64
		{FC81FF41-02CD-4CD9-9BC5-45A1E39AC6ED}.Release|x86.ActiveCfg = Release|Win32
		{FC81FF41-02CD-4CD9-9BC5-45A1E39AC6ED}.Release|x86.Build.0 = Release|Win32
		{CC9B4FA7-D746-4F52-9401-0AD1B4D6B16D}.Debug|ARM.ActiveCfg = Debug|ARM
		{CC9B4FA7-D746-4F52-9401-0AD1B4D6B16D}.Debug|ARM.Build.0 = Debug|ARM
		{CC9B4FA7-D746-4F52-9401-0AD1B4D6B16D}.Debug|ARM64.ActiveCfg = Debug|ARM64
		{CC9B4FA7-D746-4F52-9401-0AD1B4D6B16D}.Debug|x64.ActiveCfg = Debug|x64
		{CC9B4FA7-D746-4F52-9401-0AD1B4D6B16D}.Debug|x64.Build.0 = Debug|x64
		{CC9B4FA7-D746-4F52-9401-0AD1B4D6B16D}.Debug|x86.ActiveCfg = Debug|Win32
		{CC9B4FA7-D746-4F52-9401-0AD1B4D6B16D}.Debug|x86.Build.0 = Debug|Win32
		{CC9B4FA7-D746-4F52-9401-0AD1B4D6B16D}.Release|ARM.ActiveCfg = Release|ARM
		{CC9B4FA7-D746-4F52-9401-0AD1B4D6B16D}.Release|ARM.Build.0 = Release|ARM
		{CC9B4FA7-D746-4F52-9401-0AD1B4D6B16D}.Release|ARM64.ActiveCfg = Release|ARM64
		{CC9B4FA7-D746-4F52-9401-0AD1B4D6B16D}.Release|x64.ActiveCfg = Release|x64
		{CC9B4FA7-D746-4F52-9401-0AD1B4D6B16D}.Release|x64.Build.0 = Release|x64
		{CC9B4FA7-D746-4F52-9401-0AD1B4D6B16D}.Release|x86.ActiveCfg = Release|Win32
		{CC9B4FA7-D746-4F52-9401-0AD1B4D6B16D}.Release|x86.Build.0 = Release|Win32
		{2179CFDE-CDED-4DF0-8C24-A0EF6B425771}.Debug|ARM.ActiveCfg = Debug|ARM
		{2179CFDE-CDED-4DF0-8C24-A0EF6B425771}.Debug|ARM.Build.0 = Debug|ARM
		{2179CFDE-CDED-4DF0-8C24-A0EF6B425771}.Debug|ARM64.ActiveCfg = Debug|ARM64
		{2179CFDE-CDED-4DF0-8C24-A0EF6B425771}.Debug|ARM64.Build.0 = Debug|ARM64
		{2179CFDE-CDED-4DF0-8C24-A0EF6B425771}.Debug|x64.ActiveCfg = Debug|x64
		{2179CFDE-CDED-4DF0-8C24-A0EF6B425771}.Debug|x64.Build.0 = Debug|x64
		{2179CFDE-CDED-4DF0-8C24-A0EF6B425771}.Debug|x86.ActiveCfg = Debug|x86
		{2179CFDE-CDED-4DF0-8C24-A0EF6B425771}.Debug|x86.Build.0 = Debug|x86
		{2179CFDE-CDED-4DF0-8C24-A0EF6B425771}.Release|ARM.ActiveCfg = Release|ARM
		{2179CFDE-CDED-4DF0-8C24-A0EF6B425771}.Release|ARM.Build.0 = Release|ARM
		{2179CFDE-CDED-4DF0-8C24-A0EF6B425771}.Release|ARM64.ActiveCfg = Release|ARM64
		{2179CFDE-CDED-4DF0-8C24-A0EF6B425771}.Release|ARM64.Build.0 = Release|ARM64
		{2179CFDE-CDED-4DF0-8C24-A0EF6B425771}.Release|x64.ActiveCfg = Release|x64
		{2179CFDE-CDED-4DF0-8C24-A0EF6B425771}.Release|x64.Build.0 = Release|x64
		{2179CFDE-CDED-4DF0-8C24-A0EF6B425771}.Release|x86.ActiveCfg = Release|x86
		{2179CFDE-CDED-4DF0-8C24-A0EF6B425771}.Release|x86.Build.0 = Release|x86
	EndGlobalSection
	GlobalSection(SolutionProperties) = preSolution
		HideSolutionNode = FALSE
	EndGlobalSection
	GlobalSection(ExtensibilityGlobals) = postSolution
		SolutionGuid = {0EA53F83-5FA6-46A1-A290-A3C6962D2CAC}
	EndGlobalSection
EndGlobal
