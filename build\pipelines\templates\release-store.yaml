# This template contains jobs to release the app to the Store.

jobs:
- job: ReleaseStore
  dependsOn: Package
  templateContext:
    type: releaseJob
    isProduction: true
    inputs:
    - input: pipelineArtifact
      artifactName: storeBrokerPayload
  variables:
    FlightId: 161f0975-cb5f-475b-8ef6-26383c37621f
    AppId: 9WZDNCRFHVN5
    ProductId: 00009007199266248474
  steps:
  - checkout: none

  - task: MS-RDX-MRO.windows-store-publish.flight-task.store-flight@3
    displayName: Flight StoreBroker Payload to team ring
    name: StoreBrokerFlight
    inputs:
      serviceEndpoint: Calculator StoreBroker FC
      appId: $(AppId)
      flightId: $(FlightId)
      inputMethod: JsonAndZip
      jsonPath: $(Pipeline.Workspace)\SBCalculator.json
      zipPath: $(Pipeline.Workspace)\SBCalculator.zip
      force: true
      skipPolling: true
      targetPublishMode: Immediate
      logPath: $(Pipeline.Workspace)\StoreBroker.log
      deletePackages: true
      numberOfPackagesToKeep: 0

  - task: APS-Aero-Package.aero-upload-task.AeroUploadTask.AeroUpload@2
    displayName: Aero Upload (FC)
    inputs:
      productId: $(ProductId)
      flightId: $(FlightId)
      submissionId: $(StoreBrokerFlight.WS_SubmissionId)
      submissionDataPath: $(Pipeline.Workspace)\SBCalculator.json
      packagePath: $(Pipeline.Workspace)\SBCalculator.zip
      serviceEndpoint: AeroUpload-Calculator-FC
