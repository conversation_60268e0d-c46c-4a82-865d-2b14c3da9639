<?xml version="1.0" encoding="utf-8"?>
<root>
  <!-- 
    Microsoft ResX Schema 
    
    Version 2.0
    
    The primary goals of this format is to allow a simple XML format 
    that is mostly human readable. The generation and parsing of the 
    various data types are done through the TypeConverter classes 
    associated with the data types.
    
    Example:
    
    ... ado.net/XML headers & schema ...
    <resheader name="resmimetype">text/microsoft-resx</resheader>
    <resheader name="version">2.0</resheader>
    <resheader name="reader">System.Resources.ResXResourceReader, System.Windows.Forms, ...</resheader>
    <resheader name="writer">System.Resources.ResXResourceWriter, System.Windows.Forms, ...</resheader>
    <data name="Name1"><value>this is my long string</value><comment>this is a comment</comment></data>
    <data name="Color1" type="System.Drawing.Color, System.Drawing">Blue</data>
    <data name="Bitmap1" mimetype="application/x-microsoft.net.object.binary.base64">
        <value>[base64 mime encoded serialized .NET Framework object]</value>
    </data>
    <data name="Icon1" type="System.Drawing.Icon, System.Drawing" mimetype="application/x-microsoft.net.object.bytearray.base64">
        <value>[base64 mime encoded string representing a byte array form of the .NET Framework object]</value>
        <comment>This is a comment</comment>
    </data>
                
    There are any number of "resheader" rows that contain simple 
    name/value pairs.
    
    Each data row contains a name, and value. The row also contains a 
    type or mimetype. Type corresponds to a .NET class that support 
    text/value conversion through the TypeConverter architecture. 
    Classes that don't support this are serialized and stored with the 
    mimetype set.
    
    The mimetype is used for serialized objects, and tells the 
    ResXResourceReader how to depersist the object. This is currently not 
    extensible. For a given mimetype the value must be set accordingly:
    
    Note - application/x-microsoft.net.object.binary.base64 is the format 
    that the ResXResourceWriter will generate, however the reader can 
    read any of the formats listed below.
    
    mimetype: application/x-microsoft.net.object.binary.base64
    value   : The object must be serialized with 
            : System.Runtime.Serialization.Formatters.Binary.BinaryFormatter
            : and then encoded with base64 encoding.
    
    mimetype: application/x-microsoft.net.object.soap.base64
    value   : The object must be serialized with 
            : System.Runtime.Serialization.Formatters.Soap.SoapFormatter
            : and then encoded with base64 encoding.

    mimetype: application/x-microsoft.net.object.bytearray.base64
    value   : The object must be serialized into a byte array 
            : using a System.ComponentModel.TypeConverter
            : and then encoded with base64 encoding.
    -->
  <xsd:schema id="root" xmlns="" xmlns:xsd="http://www.w3.org/2001/XMLSchema" xmlns:msdata="urn:schemas-microsoft-com:xml-msdata">
    <xsd:import namespace="http://www.w3.org/XML/1998/namespace" />
    <xsd:element name="root" msdata:IsDataSet="true">
      <xsd:complexType>
        <xsd:choice maxOccurs="unbounded">
          <xsd:element name="metadata">
            <xsd:complexType>
              <xsd:sequence>
                <xsd:element name="value" type="xsd:string" minOccurs="0" />
              </xsd:sequence>
              <xsd:attribute name="name" use="required" type="xsd:string" />
              <xsd:attribute name="type" type="xsd:string" />
              <xsd:attribute name="mimetype" type="xsd:string" />
              <xsd:attribute ref="xml:space" />
            </xsd:complexType>
          </xsd:element>
          <xsd:element name="assembly">
            <xsd:complexType>
              <xsd:attribute name="alias" type="xsd:string" />
              <xsd:attribute name="name" type="xsd:string" />
            </xsd:complexType>
          </xsd:element>
          <xsd:element name="data">
            <xsd:complexType>
              <xsd:sequence>
                <xsd:element name="value" type="xsd:string" minOccurs="0" msdata:Ordinal="1" />
                <xsd:element name="comment" type="xsd:string" minOccurs="0" msdata:Ordinal="2" />
              </xsd:sequence>
              <xsd:attribute name="name" type="xsd:string" use="required" msdata:Ordinal="1" />
              <xsd:attribute name="type" type="xsd:string" msdata:Ordinal="3" />
              <xsd:attribute name="mimetype" type="xsd:string" msdata:Ordinal="4" />
              <xsd:attribute ref="xml:space" />
            </xsd:complexType>
          </xsd:element>
          <xsd:element name="resheader">
            <xsd:complexType>
              <xsd:sequence>
                <xsd:element name="value" type="xsd:string" minOccurs="0" msdata:Ordinal="1" />
              </xsd:sequence>
              <xsd:attribute name="name" type="xsd:string" use="required" />
            </xsd:complexType>
          </xsd:element>
        </xsd:choice>
      </xsd:complexType>
    </xsd:element>
  </xsd:schema>
  <resheader name="resmimetype">
    <value>text/microsoft-resx</value>
  </resheader>
  <resheader name="version">
    <value>2.0</value>
  </resheader>
  <resheader name="reader">
    <value>System.Resources.ResXResourceReader, System.Windows.Forms, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </resheader>
  <resheader name="writer">
    <value>System.Resources.ResXResourceWriter, System.Windows.Forms, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </resheader>
  <data name="10" xml:space="preserve">
    <value>Rsh</value>
    <comment>{Locked}The string that represents the function</comment>
  </data>
  <data name="100" xml:space="preserve">
    <value>Invalid input</value>
    <comment>Error message shown when the input makes a function fail, like log(-1)</comment>
  </data>
  <data name="101" xml:space="preserve">
    <value>Result is undefined</value>
    <comment>Error message shown when there's no possible value for a function.</comment>
  </data>
  <data name="105" xml:space="preserve">
    <value>Not enough memory</value>
    <comment>Error message shown when we run out of memory during a calculation.</comment>
  </data>
  <data name="107" xml:space="preserve">
    <value>Overflow</value>
    <comment>Error message shown when there's an overflow during the calculation.</comment>
  </data>
  <data name="108" xml:space="preserve">
    <value>Result not defined</value>
    <comment>Same as 101</comment>
  </data>
  <data name="11" xml:space="preserve">
    <value>÷</value>
    <comment>{Locked}The string that represents the function</comment>
  </data>
  <data name="118" xml:space="preserve">
    <value>Result not defined</value>
    <comment>Same 101</comment>
  </data>
  <data name="119" xml:space="preserve">
    <value>Overflow</value>
    <comment>Same as 107</comment>
  </data>
  <data name="12" xml:space="preserve">
    <value>×</value>
    <comment>{Locked}The string that represents the function</comment>
  </data>
  <data name="120" xml:space="preserve">
    <value>Overflow</value>
    <comment>Same 107</comment>
  </data>
  <data name="13" xml:space="preserve">
    <value>+</value>
    <comment>{Locked}The string that represents the function</comment>
  </data>
  <data name="14" xml:space="preserve">
    <value>-</value>
    <comment>{Locked}The string that represents the function</comment>
  </data>
  <data name="15" xml:space="preserve">
    <value>Mod</value>
    <comment>{Locked}The string that represents the function</comment>
  </data>
  <data name="16" xml:space="preserve">
    <value>yroot</value>
    <comment>{Locked}The string that represents the function</comment>
  </data>
  <data name="17" xml:space="preserve">
    <value>^</value>
    <comment>{Locked}The string that represents the function</comment>
  </data>
  <data name="18" xml:space="preserve">
    <value>Int</value>
    <comment>{Locked}The string that represents the function</comment>
  </data>
  <data name="19" xml:space="preserve">
    <value>RoL</value>
    <comment>{Locked}The string that represents the function</comment>
  </data>
  <data name="2" xml:space="preserve">
    <value>CE</value>
    <comment>{Locked}The string that represents the function</comment>
  </data>
  <data name="20" xml:space="preserve">
    <value>RoR</value>
    <comment>{Locked}The string that represents the function</comment>
  </data>
  <data name="21" xml:space="preserve">
    <value>NOT</value>
    <comment>{Locked}The string that represents the function</comment>
  </data>
  <data name="22" xml:space="preserve">
    <value>sin</value>
    <comment>{Locked}The string that represents the function</comment>
  </data>
  <data name="23" xml:space="preserve">
    <value>cis</value>
    <comment>{Locked}The string that represents the function</comment>
  </data>
  <data name="24" xml:space="preserve">
    <value>tan</value>
    <comment>{Locked}The string that represents the function</comment>
  </data>
  <data name="25" xml:space="preserve">
    <value>sinh</value>
    <comment>{Locked}The string that represents the function</comment>
  </data>
  <data name="26" xml:space="preserve">
    <value>cosh</value>
    <comment>{Locked}The string that represents the function</comment>
  </data>
  <data name="27" xml:space="preserve">
    <value>tanh</value>
    <comment>{Locked}The string that represents the function</comment>
  </data>
  <data name="28" xml:space="preserve">
    <value>ln</value>
    <comment>{Locked}The string that represents the function</comment>
  </data>
  <data name="29" xml:space="preserve">
    <value>log</value>
    <comment>{Locked}The string that represents the function</comment>
  </data>
  <data name="30" xml:space="preserve">
    <value>√</value>
    <comment>{Locked}The string that represents the function</comment>
  </data>
  <data name="35" xml:space="preserve">
    <value>dms</value>
    <comment>{Locked}The string that represents the function</comment>
  </data>
  <data name="37" xml:space="preserve">
    <value>10^</value>
    <comment>{Locked}The string that represents the function</comment>
  </data>
  <data name="38" xml:space="preserve">
    <value>%</value>
    <comment>{Locked}The string that represents the function</comment>
  </data>
  <data name="4" xml:space="preserve">
    <value>.</value>
    <comment>{Locked}The string that represents the function</comment>
  </data>
  <data name="40" xml:space="preserve">
    <value>Pi</value>
    <comment>{Locked}The string that represents the function</comment>
  </data>
  <data name="41" xml:space="preserve">
    <value>=</value>
    <comment>{Locked}The string that represents the function</comment>
  </data>
  <data name="47" xml:space="preserve">
    <value>Exp</value>
    <comment>{Locked}The string that represents the function</comment>
  </data>
  <data name="48" xml:space="preserve">
    <value>(</value>
    <comment>{Locked}The string that represents the function</comment>
  </data>
  <data name="49" xml:space="preserve">
    <value>)</value>
    <comment>{Locked}The string that represents the function</comment>
  </data>
  <data name="6" xml:space="preserve">
    <value>AND</value>
    <comment>{Locked}The string that represents the function</comment>
  </data>
  <data name="66" xml:space="preserve">
    <value>frac</value>
    <comment>{Locked}The string that represents the function</comment>
  </data>
  <data name="67" xml:space="preserve">
    <value>sin₀</value>
    <comment>{Locked}The string that represents the function</comment>
  </data>
  <data name="68" xml:space="preserve">
    <value>cos₀</value>
    <comment>{Locked}The string that represents the function</comment>
  </data>
  <data name="69" xml:space="preserve">
    <value>tan₀</value>
    <comment>{Locked}The string that represents the function</comment>
  </data>
  <data name="7" xml:space="preserve">
    <value>OR</value>
    <comment>{Locked}The string that represents the function</comment>
  </data>
  <data name="70" xml:space="preserve">
    <value>sin₀⁻¹</value>
    <comment>{Locked}The string that represents the function</comment>
  </data>
  <data name="71" xml:space="preserve">
    <value>cos₀⁻¹</value>
    <comment>{Locked}The string that represents the function</comment>
  </data>
  <data name="72" xml:space="preserve">
    <value>tan₀⁻¹</value>
    <comment>{Locked}The string that represents the function</comment>
  </data>
  <data name="73" xml:space="preserve">
    <value>sinᵣ</value>
    <comment>{Locked}The string that represents the function</comment>
  </data>
  <data name="74" xml:space="preserve">
    <value>cosᵣ</value>
    <comment>{Locked}The string that represents the function</comment>
  </data>
  <data name="75" xml:space="preserve">
    <value>tanᵣ</value>
    <comment>{Locked}The string that represents the function</comment>
  </data>
  <data name="76" xml:space="preserve">
    <value>sinᵣ⁻¹</value>
    <comment>{Locked}The string that represents the function</comment>
  </data>
  <data name="77" xml:space="preserve">
    <value>cosᵣ⁻¹</value>
    <comment>{Locked}The string that represents the function</comment>
  </data>
  <data name="78" xml:space="preserve">
    <value>tanᵣ⁻¹</value>
    <comment>{Locked}The string that represents the function</comment>
  </data>
  <data name="79" xml:space="preserve">
    <value>sin₉</value>
    <comment>{Locked}The string that represents the function</comment>
  </data>
  <data name="8" xml:space="preserve">
    <value>XOR</value>
    <comment>{Locked}The string that represents the function</comment>
  </data>
  <data name="80" xml:space="preserve">
    <value>cos₉</value>
    <comment>{Locked}The string that represents the function</comment>
  </data>
  <data name="81" xml:space="preserve">
    <value>tan₉</value>
    <comment>{Locked}The string that represents the function</comment>
  </data>
  <data name="82" xml:space="preserve">
    <value>sin₉⁻¹</value>
    <comment>{Locked}The string that represents the function</comment>
  </data>
  <data name="83" xml:space="preserve">
    <value>cos₉⁻¹</value>
    <comment>{Locked}The string that represents the function</comment>
  </data>
  <data name="84" xml:space="preserve">
    <value>tan₉⁻¹</value>
    <comment>{Locked}The string that represents the function</comment>
  </data>
  <data name="85" xml:space="preserve">
    <value>sinh⁻¹</value>
    <comment>{Locked}The string that represents the function</comment>
  </data>
  <data name="86" xml:space="preserve">
    <value>cosh⁻¹</value>
    <comment>{Locked}The string that represents the function</comment>
  </data>
  <data name="87" xml:space="preserve">
    <value>tanh⁻¹</value>
    <comment>{Locked}The string that represents the function</comment>
  </data>
  <data name="88" xml:space="preserve">
    <value>e^</value>
    <comment>{Locked}The string that represents the function</comment>
  </data>
  <data name="89" xml:space="preserve">
    <value>10^</value>
    <comment>{Locked}The string that represents the function</comment>
  </data>
  <data name="9" xml:space="preserve">
    <value>Lsh</value>
    <comment>{Locked}The string that represents the function</comment>
  </data>
  <data name="90" xml:space="preserve">
    <value>√</value>
    <comment>{Locked}The string that represents the function</comment>
  </data>
  <data name="91" xml:space="preserve">
    <value>sqr</value>
    <comment>{Locked}The string that represents the function</comment>
  </data>
  <data name="92" xml:space="preserve">
    <value>cube</value>
    <comment>{Locked}The string that represents the function</comment>
  </data>
  <data name="94" xml:space="preserve">
    <value>fact</value>
    <comment>{Locked}The string that represents the function</comment>
  </data>
  <data name="95" xml:space="preserve">
    <value>1/</value>
    <comment>{Locked}The string that represents the function</comment>
  </data>
  <data name="96" xml:space="preserve">
    <value>degrees</value>
    <comment>{Locked}The string that represents the function</comment>
  </data>
  <data name="97" xml:space="preserve">
    <value>negate</value>
    <comment>{Locked}The string that represents the function</comment>
  </data>
  <data name="99" xml:space="preserve">
    <value>Cannot divide by zero</value>
    <comment>Error string shown when a divide by zero condition happens during the calculation</comment>
  </data>
  <data name="SecDeg" xml:space="preserve">
    <value>sec₀</value>
    <comment>{Locked}The string that represents the function</comment>
  </data>
  <data name="SecRad" xml:space="preserve">
    <value>secᵣ</value>
    <comment>{Locked}The string that represents the function</comment>
  </data>
  <data name="SecGrad" xml:space="preserve">
    <value>sec₉</value>
    <comment>{Locked}The string that represents the function</comment>
  </data>
  <data name="InverseSecDeg" xml:space="preserve">
    <value>sec₀⁻¹</value>
    <comment>{Locked}The string that represents the function</comment>
  </data>
  <data name="InverseSecRad" xml:space="preserve">
    <value>secᵣ⁻¹</value>
    <comment>{Locked}The string that represents the function</comment>
  </data>
  <data name="InverseSecGrad" xml:space="preserve">
    <value>sec₉⁻¹</value>
    <comment>{Locked}The string that represents the function</comment>
  </data>
  <data name="CscDeg" xml:space="preserve">
    <value>csc₀</value>
    <comment>{Locked}The string that represents the function</comment>
  </data>
  <data name="CscRad" xml:space="preserve">
    <value>cscᵣ</value>
    <comment>{Locked}The string that represents the function</comment>
  </data>
  <data name="CscGrad" xml:space="preserve">
    <value>csc₉</value>
    <comment>{Locked}The string that represents the function</comment>
  </data>
  <data name="InverseCscDeg" xml:space="preserve">
    <value>csc₀⁻¹</value>
    <comment>{Locked}The string that represents the function</comment>
  </data>
  <data name="InverseCscRad" xml:space="preserve">
    <value>cscᵣ⁻¹</value>
    <comment>{Locked}The string that represents the function</comment>
  </data>
  <data name="InverseCscGrad" xml:space="preserve">
    <value>csc₉⁻¹</value>
    <comment>{Locked}The string that represents the function</comment>
  </data>
  <data name="CotDeg" xml:space="preserve">
    <value>cot₀</value>
    <comment>{Locked}The string that represents the function</comment>
  </data>
  <data name="CotRad" xml:space="preserve">
    <value>cotᵣ</value>
    <comment>{Locked}The string that represents the function</comment>
  </data>
  <data name="CotGrad" xml:space="preserve">
    <value>cot₉</value>
    <comment>{Locked}The string that represents the function</comment>
  </data>
  <data name="InverseCotDeg" xml:space="preserve">
    <value>cot₀⁻¹</value>
    <comment>{Locked}The string that represents the function</comment>
  </data>
  <data name="InverseCotRad" xml:space="preserve">
    <value>cotᵣ⁻¹</value>
    <comment>{Locked}The string that represents the function</comment>
  </data>
  <data name="InverseCotGrad" xml:space="preserve">
    <value>cot₉⁻¹</value>
    <comment>{Locked}The string that represents the function</comment>
  </data>
  <data name="Sech" xml:space="preserve">
    <value>sech</value>
    <comment>{Locked}The string that represents the function</comment>
  </data>
  <data name="InverseSech" xml:space="preserve">
    <value>sech⁻¹</value>
    <comment>{Locked}The string that represents the function</comment>
  </data>
  <data name="Csch" xml:space="preserve">
    <value>csch</value>
    <comment>{Locked}The string that represents the function</comment>
  </data>
  <data name="InverseCsch" xml:space="preserve">
    <value>csch⁻¹</value>
    <comment>{Locked}The string that represents the function</comment>
  </data>
  <data name="Coth" xml:space="preserve">
    <value>coth</value>
    <comment>{Locked}The string that represents the function</comment>
  </data>
  <data name="InverseCoth" xml:space="preserve">
    <value>coth⁻¹</value>
    <comment>{Locked}The string that represents the function</comment>
  </data>
  <data name="TwoPowX" xml:space="preserve">
    <value>2^</value>
    <comment>{Locked}The string that represents the function</comment>
  </data>
  <data name="LogBaseY" xml:space="preserve">
    <value>log base</value>
    <comment>{Locked}The string that represents the function</comment>
  </data>
  <data name="Abs" xml:space="preserve">
    <value>abs</value>
    <comment>{Locked}The string that represents the function</comment>
  </data>
  <data name="Ceil" xml:space="preserve">
    <value>ceil</value>
    <comment>{Locked}The string that represents the function</comment>
  </data>
  <data name="Floor" xml:space="preserve">
    <value>floor</value>
    <comment>{Locked}The string that represents the function</comment>
  </data>
  <data name="Nand" xml:space="preserve">
    <value>NAND</value>
    <comment>{Locked}The string that represents the function</comment>
  </data>
  <data name="Nor" xml:space="preserve">
    <value>NOR</value>
    <comment>{Locked}The string that represents the function</comment>
  </data>
  <data name="CubeRoot" xml:space="preserve">
    <value>cuberoot</value>
    <comment>{Locked}The string that represents the function</comment>
  </data>
  <data name="ProgrammerMod" xml:space="preserve">
    <value>%</value>
    <comment>{Locked}The string that represents the function</comment>
  </data>
</root>
