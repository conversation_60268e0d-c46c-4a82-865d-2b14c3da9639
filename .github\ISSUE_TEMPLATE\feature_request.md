---
name: Feature request
about: Propose a new feature in the app
title: ''
labels: 'Enhancement'
assignees: ''

---

<!--
See https://github.com/Microsoft/calculator/blob/main/docs/NewFeatureProcess.md for suggestions on how to write a good feature pitch. Just want to submit an idea quickly? Try Feedback Hub instead: https://insider.windows.com/en-us/fb/?contextid=130
-->

**Problem Statement**
<!--
What problem are we trying to solve? Who’s the target audience? Is there a customer need or pain point we need to remedy? Is there a business goal or metric we are trying to improve? Do we have a hypothesis we want to prove or disprove?
-->

**Evidence or User Insights**
<!--
Why should we do this? Potential sources of data: Feedback Hub, other GitHub issues, other anecdotes from listening to customers in person or online, request from another team, telemetry data, user research, market or competitive research
-->

**Proposal**
<!--
How will the solution/feature help us solve the problem? How will it meet the target audience’s needs? If there are business goals or metrics, how does this improve them?
-->

**Goals**
<!--
What you want to accomplish with this feature. Typical examples include
"User Can *perform some task*"
-->

**Non-Goals**
<!--
Things we are explicitly not doing or supporting or that are out of scope, including reasons why.
-->

**Low-Fidelity Concept**
<!--
Show as much of the experience as needed to explain the idea. This can be as simple as a napkin drawing but can also be a code prototype, or a design comp. Keep it simple at this stage, as it can be refined later during the pre-production stage.
-->

**Requested Assignment**
<!--
Some people just want to suggest a feature and let someone else implement it.
Other people want to not only suggest a feature, but implement it as well.
Both scenarios are completely ok. We just want to know which one it is.
We are likely to prioritize the review of feature requests if they already have someone who can implement them.
Please indicate which bucket you fall into by keeping one and removing the other.
-->
If possible, I would like to implement this.
I'm just suggesting this idea.  I don't want to implement it.
