﻿<?xml version="1.0" encoding="utf-8"?>
<root>
  <!-- 
    Microsoft ResX Schema 
    
    Version 2.0
    
    The primary goals of this format is to allow a simple XML format 
    that is mostly human readable. The generation and parsing of the 
    various data types are done through the TypeConverter classes 
    associated with the data types.
    
    Example:
    
    ... ado.net/XML headers & schema ...
    <resheader name="resmimetype">text/microsoft-resx</resheader>
    <resheader name="version">2.0</resheader>
    <resheader name="reader">System.Resources.ResXResourceReader, System.Windows.Forms, ...</resheader>
    <resheader name="writer">System.Resources.ResXResourceWriter, System.Windows.Forms, ...</resheader>
    <data name="Name1"><value>this is my long string</value><comment>this is a comment</comment></data>
    <data name="Color1" type="System.Drawing.Color, System.Drawing">Blue</data>
    <data name="Bitmap1" mimetype="application/x-microsoft.net.object.binary.base64">
        <value>[base64 mime encoded serialized .NET Framework object]</value>
    </data>
    <data name="Icon1" type="System.Drawing.Icon, System.Drawing" mimetype="application/x-microsoft.net.object.bytearray.base64">
        <value>[base64 mime encoded string representing a byte array form of the .NET Framework object]</value>
        <comment>This is a comment</comment>
    </data>
                
    There are any number of "resheader" rows that contain simple 
    name/value pairs.
    
    Each data row contains a name, and value. The row also contains a 
    type or mimetype. Type corresponds to a .NET class that support 
    text/value conversion through the TypeConverter architecture. 
    Classes that don't support this are serialized and stored with the 
    mimetype set.
    
    The mimetype is used for serialized objects, and tells the 
    ResXResourceReader how to depersist the object. This is currently not 
    extensible. For a given mimetype the value must be set accordingly:
    
    Note - application/x-microsoft.net.object.binary.base64 is the format 
    that the ResXResourceWriter will generate, however the reader can 
    read any of the formats listed below.
    
    mimetype: application/x-microsoft.net.object.binary.base64
    value   : The object must be serialized with 
            : System.Runtime.Serialization.Formatters.Binary.BinaryFormatter
            : and then encoded with base64 encoding.
    
    mimetype: application/x-microsoft.net.object.soap.base64
    value   : The object must be serialized with 
            : System.Runtime.Serialization.Formatters.Soap.SoapFormatter
            : and then encoded with base64 encoding.

    mimetype: application/x-microsoft.net.object.bytearray.base64
    value   : The object must be serialized into a byte array 
            : using a System.ComponentModel.TypeConverter
            : and then encoded with base64 encoding.
    -->
  <xsd:schema id="root" xmlns="" xmlns:xsd="http://www.w3.org/2001/XMLSchema" xmlns:msdata="urn:schemas-microsoft-com:xml-msdata">
    <xsd:import namespace="http://www.w3.org/XML/1998/namespace" />
    <xsd:element name="root" msdata:IsDataSet="true">
      <xsd:complexType>
        <xsd:choice maxOccurs="unbounded">
          <xsd:element name="metadata">
            <xsd:complexType>
              <xsd:sequence>
                <xsd:element name="value" type="xsd:string" minOccurs="0" />
              </xsd:sequence>
              <xsd:attribute name="name" use="required" type="xsd:string" />
              <xsd:attribute name="type" type="xsd:string" />
              <xsd:attribute name="mimetype" type="xsd:string" />
              <xsd:attribute ref="xml:space" />
            </xsd:complexType>
          </xsd:element>
          <xsd:element name="assembly">
            <xsd:complexType>
              <xsd:attribute name="alias" type="xsd:string" />
              <xsd:attribute name="name" type="xsd:string" />
            </xsd:complexType>
          </xsd:element>
          <xsd:element name="data">
            <xsd:complexType>
              <xsd:sequence>
                <xsd:element name="value" type="xsd:string" minOccurs="0" msdata:Ordinal="1" />
                <xsd:element name="comment" type="xsd:string" minOccurs="0" msdata:Ordinal="2" />
              </xsd:sequence>
              <xsd:attribute name="name" type="xsd:string" use="required" msdata:Ordinal="1" />
              <xsd:attribute name="type" type="xsd:string" msdata:Ordinal="3" />
              <xsd:attribute name="mimetype" type="xsd:string" msdata:Ordinal="4" />
              <xsd:attribute ref="xml:space" />
            </xsd:complexType>
          </xsd:element>
          <xsd:element name="resheader">
            <xsd:complexType>
              <xsd:sequence>
                <xsd:element name="value" type="xsd:string" minOccurs="0" msdata:Ordinal="1" />
              </xsd:sequence>
              <xsd:attribute name="name" type="xsd:string" use="required" />
            </xsd:complexType>
          </xsd:element>
        </xsd:choice>
      </xsd:complexType>
    </xsd:element>
  </xsd:schema>
  <resheader name="resmimetype">
    <value>text/microsoft-resx</value>
  </resheader>
  <resheader name="version">
    <value>2.0</value>
  </resheader>
  <resheader name="reader">
    <value>System.Resources.ResXResourceReader, System.Windows.Forms, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </resheader>
  <resheader name="writer">
    <value>System.Resources.ResXResourceWriter, System.Windows.Forms, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </resheader>
  <data name="AppName" xml:space="preserve">
    <value>Kalkulaator</value>
    <comment>{@Appx_ShortDisplayName@}{StringCategory="Feature Title"} This is the title of the official application when published through Windows Store.</comment>
  </data>
  <data name="DevAppName" xml:space="preserve">
    <value>Kalkulaator [Dev]</value>
    <comment>{@Appx_ShortDisplayName@}{StringCategory="Feature Title"} This is the name of the application when built by a user via GitHub. We use a different name to make it easier for users to distinguish the apps when both this version and the Store version are installed on the same device.</comment>
  </data>
  <data name="AppStoreName" xml:space="preserve">
    <value>Windowsi kalkulaator</value>
    <comment>{@Appx_DisplayName@}{StringCategory="Feature Title"} Name that shows up in the app store. It contains "Windows" to distinguish it from 3rd party calculator apps.</comment>
  </data>
  <data name="DevAppStoreName" xml:space="preserve">
    <value>Windowsi kalkulaator [Dev]</value>
    <comment>{@Appx_DisplayName@}{StringCategory="Feature Title"} Name that shows up in the app store. It contains "Windows" to distinguish it from 3rd party calculator apps. This is the the version of the name used when the app is built by a user via GitHub.</comment>
  </data>
  <data name="AppDescription" xml:space="preserve">
    <value>Kalkulaator</value>
    <comment>{@Appx_Description@} This description is used for the official application when published through Windows Store.</comment>
  </data>
  <data name="DevAppDescription" xml:space="preserve">
    <value>Kalkulaator [Dev]</value>
    <comment>{@Appx_Description@} This is the description of the application when built by a user via GitHub. We use a different description to make it easier for users to distinguish the apps when both this version and the Store version are installed on the same device.</comment>
  </data>
  <data name="copyMenuItem" xml:space="preserve">
    <value>Kopeeri</value>
    <comment>Copy context menu string</comment>
  </data>
  <data name="pasteMenuItem" xml:space="preserve">
    <value>Kleebi</value>
    <comment>Paste context menu string</comment>
  </data>
  <data name="SupplementaryResultsHeader.Text" xml:space="preserve">
    <value>Võrdub ligikaudu</value>
    <comment>The text that shows at the bottom of the converter to head the supplementary results. Indicates that the main result is approximately equal to the supplementary results.</comment>
  </data>
  <data name="BitFlipItemAutomationName" xml:space="preserve">
    <value>%1, väärtus %2</value>
    <comment>{Locked="%1","%2"}. String used in automation name for each bit in bit flip. %1 will be replaced by the position of the bit (1st bit, 3rd bit), %2 by a binary value (1 or 0)</comment>
  </data>
  <data name="BitPosition" xml:space="preserve">
    <value>%1 bitt</value>
    <comment>{Locked="%1"}. Sub-string used to indicate the position of a bit (e.g. 1st bit, 2nd bit...)</comment>
  </data>
  <data name="63" xml:space="preserve">
    <value>63.</value>
    <comment>Sub-string used in automation name for 63 bit in bit flip</comment>
  </data>
  <data name="62" xml:space="preserve">
    <value>62.</value>
    <comment>Sub-string used in automation name for 62 bit in bit flip</comment>
  </data>
  <data name="61" xml:space="preserve">
    <value>61.</value>
    <comment>Sub-string used in automation name for 61 bit in bit flip</comment>
  </data>
  <data name="60" xml:space="preserve">
    <value>60.</value>
    <comment>Sub-string used in automation name for 60 bit in bit flip</comment>
  </data>
  <data name="59" xml:space="preserve">
    <value>59.</value>
    <comment>Sub-string used in automation name for 59 bit in bit flip</comment>
  </data>
  <data name="58" xml:space="preserve">
    <value>58.</value>
    <comment>Sub-string used in automation name for 58 bit in bit flip</comment>
  </data>
  <data name="57" xml:space="preserve">
    <value>57.</value>
    <comment>Sub-string used in automation name for 57 bit in bit flip</comment>
  </data>
  <data name="56" xml:space="preserve">
    <value>56.</value>
    <comment>Sub-string used in automation name for 56 bit in bit flip</comment>
  </data>
  <data name="55" xml:space="preserve">
    <value>55.</value>
    <comment>Sub-string used in automation name for 55 bit in bit flip</comment>
  </data>
  <data name="54" xml:space="preserve">
    <value>54.</value>
    <comment>Sub-string used in automation name for 54 bit in bit flip</comment>
  </data>
  <data name="53" xml:space="preserve">
    <value>53.</value>
    <comment>Sub-string used in automation name for 53 bit in bit flip</comment>
  </data>
  <data name="52" xml:space="preserve">
    <value>52.</value>
    <comment>Sub-string used in automation name for 52 bit in bit flip</comment>
  </data>
  <data name="51" xml:space="preserve">
    <value>51.</value>
    <comment>Sub-string used in automation name for 51 bit in bit flip</comment>
  </data>
  <data name="50" xml:space="preserve">
    <value>50.</value>
    <comment>Sub-string used in automation name for 50 bit in bit flip</comment>
  </data>
  <data name="49" xml:space="preserve">
    <value>49.</value>
    <comment>Sub-string used in automation name for 49 bit in bit flip</comment>
  </data>
  <data name="48" xml:space="preserve">
    <value>48.</value>
    <comment>Sub-string used in automation name for 48 bit in bit flip</comment>
  </data>
  <data name="47" xml:space="preserve">
    <value>47.</value>
    <comment>Sub-string used in automation name for 47 bit in bit flip</comment>
  </data>
  <data name="46" xml:space="preserve">
    <value>46.</value>
    <comment>Sub-string used in automation name for 46 bit in bit flip</comment>
  </data>
  <data name="45" xml:space="preserve">
    <value>45.</value>
    <comment>Sub-string used in automation name for 45 bit in bit flip</comment>
  </data>
  <data name="44" xml:space="preserve">
    <value>44.</value>
    <comment>Sub-string used in automation name for 44 bit in bit flip</comment>
  </data>
  <data name="43" xml:space="preserve">
    <value>43.</value>
    <comment>Sub-string used in automation name for 43 bit in bit flip</comment>
  </data>
  <data name="42" xml:space="preserve">
    <value>42.</value>
    <comment>Sub-string used in automation name for 42 bit in bit flip</comment>
  </data>
  <data name="41" xml:space="preserve">
    <value>41.</value>
    <comment>Sub-string used in automation name for 41 bit in bit flip</comment>
  </data>
  <data name="40" xml:space="preserve">
    <value>40.</value>
    <comment>Sub-string used in automation name for 40 bit in bit flip</comment>
  </data>
  <data name="39" xml:space="preserve">
    <value>39.</value>
    <comment>Sub-string used in automation name for 39 bit in bit flip</comment>
  </data>
  <data name="38" xml:space="preserve">
    <value>38.</value>
    <comment>Sub-string used in automation name for 38 bit in bit flip</comment>
  </data>
  <data name="37" xml:space="preserve">
    <value>37.</value>
    <comment>Sub-string used in automation name for 37 bit in bit flip</comment>
  </data>
  <data name="36" xml:space="preserve">
    <value>36.</value>
    <comment>Sub-string used in automation name for 36 bit in bit flip</comment>
  </data>
  <data name="35" xml:space="preserve">
    <value>35.</value>
    <comment>Sub-string used in automation name for 35 bit in bit flip</comment>
  </data>
  <data name="34" xml:space="preserve">
    <value>34.</value>
    <comment>Sub-string used in automation name for 34 bit in bit flip</comment>
  </data>
  <data name="33" xml:space="preserve">
    <value>33.</value>
    <comment>Sub-string used in automation name for 33 bit in bit flip</comment>
  </data>
  <data name="32" xml:space="preserve">
    <value>32.</value>
    <comment>Sub-string used in automation name for 32 bit in bit flip</comment>
  </data>
  <data name="31" xml:space="preserve">
    <value>31.</value>
    <comment>Sub-string used in automation name for 31 bit in bit flip</comment>
  </data>
  <data name="30" xml:space="preserve">
    <value>30.</value>
    <comment>Sub-string used in automation name for 30 bit in bit flip</comment>
  </data>
  <data name="29" xml:space="preserve">
    <value>29.</value>
    <comment>Sub-string used in automation name for 29 bit in bit flip</comment>
  </data>
  <data name="28" xml:space="preserve">
    <value>28.</value>
    <comment>Sub-string used in automation name for 28 bit in bit flip</comment>
  </data>
  <data name="27" xml:space="preserve">
    <value>27.</value>
    <comment>Sub-string used in automation name for 27 bit in bit flip</comment>
  </data>
  <data name="26" xml:space="preserve">
    <value>26.</value>
    <comment>Sub-string used in automation name for 26 bit in bit flip</comment>
  </data>
  <data name="25" xml:space="preserve">
    <value>25.</value>
    <comment>Sub-string used in automation name for 25 bit in bit flip</comment>
  </data>
  <data name="24" xml:space="preserve">
    <value>24.</value>
    <comment>Sub-string used in automation name for 24 bit in bit flip</comment>
  </data>
  <data name="23" xml:space="preserve">
    <value>23.</value>
    <comment>Sub-string used in automation name for 23 bit in bit flip</comment>
  </data>
  <data name="22" xml:space="preserve">
    <value>22.</value>
    <comment>Sub-string used in automation name for 22 bit in bit flip</comment>
  </data>
  <data name="21" xml:space="preserve">
    <value>21.</value>
    <comment>Sub-string used in automation name for 21 bit in bit flip</comment>
  </data>
  <data name="20" xml:space="preserve">
    <value>20.</value>
    <comment>Sub-string used in automation name for 20 bit in bit flip</comment>
  </data>
  <data name="19" xml:space="preserve">
    <value>19.</value>
    <comment>Sub-string used in automation name for 19 bit in bit flip</comment>
  </data>
  <data name="18" xml:space="preserve">
    <value>18.</value>
    <comment>Sub-string used in automation name for 18 bit in bit flip</comment>
  </data>
  <data name="17" xml:space="preserve">
    <value>17.</value>
    <comment>Sub-string used in automation name for 17 bit in bit flip</comment>
  </data>
  <data name="16" xml:space="preserve">
    <value>16.</value>
    <comment>Sub-string used in automation name for 16 bit in bit flip</comment>
  </data>
  <data name="15" xml:space="preserve">
    <value>15.</value>
    <comment>Sub-string used in automation name for 15 bit in bit flip</comment>
  </data>
  <data name="14" xml:space="preserve">
    <value>14.</value>
    <comment>Sub-string used in automation name for 14 bit in bit flip</comment>
  </data>
  <data name="13" xml:space="preserve">
    <value>13.</value>
    <comment>Sub-string used in automation name for 13 bit in bit flip</comment>
  </data>
  <data name="12" xml:space="preserve">
    <value>12.</value>
    <comment>Sub-string used in automation name for 12 bit in bit flip</comment>
  </data>
  <data name="11" xml:space="preserve">
    <value>11.</value>
    <comment>Sub-string used in automation name for 11 bit in bit flip</comment>
  </data>
  <data name="10" xml:space="preserve">
    <value>10.</value>
    <comment>Sub-string used in automation name for 10 bit in bit flip</comment>
  </data>
  <data name="9" xml:space="preserve">
    <value>9.</value>
    <comment>Sub-string used in automation name for 9 bit in bit flip</comment>
  </data>
  <data name="8" xml:space="preserve">
    <value>8.</value>
    <comment>Sub-string used in automation name for 8 bit in bit flip</comment>
  </data>
  <data name="7" xml:space="preserve">
    <value>7.</value>
    <comment>Sub-string used in automation name for 7 bit in bit flip</comment>
  </data>
  <data name="6" xml:space="preserve">
    <value>6.</value>
    <comment>Sub-string used in automation name for 6 bit in bit flip</comment>
  </data>
  <data name="5" xml:space="preserve">
    <value>5.</value>
    <comment>Sub-string used in automation name for 5 bit in bit flip</comment>
  </data>
  <data name="4" xml:space="preserve">
    <value>4.</value>
    <comment>Sub-string used in automation name for 4 bit in bit flip</comment>
  </data>
  <data name="3" xml:space="preserve">
    <value>3.</value>
    <comment>Sub-string used in automation name for 3 bit in bit flip</comment>
  </data>
  <data name="2" xml:space="preserve">
    <value>2.</value>
    <comment>Sub-string used in automation name for 2 bit in bit flip</comment>
  </data>
  <data name="1" xml:space="preserve">
    <value>1.</value>
    <comment>Sub-string used in automation name for 1 bit in bit flip</comment>
  </data>
  <data name="LeastSignificantBit" xml:space="preserve">
    <value>madalaim bitt</value>
    <comment>Used to describe the first bit of a binary number. Used in bit flip</comment>
  </data>
  <data name="MemoryButton_Open" xml:space="preserve">
    <value>Ava mäluhüpik</value>
    <comment>This is the automation name and label for the memory button when the memory flyout is closed.</comment>
  </data>
  <data name="MemoryButton_Close" xml:space="preserve">
    <value>Sule mäluhüpik</value>
    <comment>This is the automation name and label for the memory button when the memory flyout is open.</comment>
  </data>
  <data name="AlwaysOnTop_Enter" xml:space="preserve">
    <value>Hoia kõige peal</value>
    <comment>This is the tool tip automation name for the always-on-top button when out of always-on-top mode.</comment>
  </data>
  <data name="AlwaysOnTop_Exit" xml:space="preserve">
    <value>Tagasi täisekraanvaatesse</value>
    <comment>This is the tool tip automation name for the always-on-top button when in always-on-top mode.</comment>
  </data>
  <data name="MemoryButton.[using:Windows.UI.Xaml.Controls]ToolTipService.ToolTip" xml:space="preserve">
    <value>Mälu</value>
    <comment>This is the tool tip automation name for the memory button.</comment>
  </data>
  <data name="HistoryButton.[using:Windows.UI.Xaml.Controls]ToolTipService.ToolTip" xml:space="preserve">
    <value>Ajalugu (Ctrl+H)</value>
    <comment>This is the tool tip automation name for the history button.</comment>
  </data>
  <data name="bitFlip.[using:Windows.UI.Xaml.Controls]ToolTipService.ToolTip" xml:space="preserve">
    <value>Biti ümberlülituse klahvistik</value>
    <comment>This is the tool tip automation name for the bitFlip button.</comment>
  </data>
  <data name="fullKeypad.[using:Windows.UI.Xaml.Controls]ToolTipService.ToolTip" xml:space="preserve">
    <value>Täisklahvistik</value>
    <comment>This is the tool tip automation name for the numberPad button.</comment>
  </data>
  <data name="ClearMemoryButton.[using:Windows.UI.Xaml.Controls]ToolTipService.ToolTip" xml:space="preserve">
    <value>Tühjenda kogu mälu (Ctrl+L)</value>
    <comment>This is the tool tip automation name for the Clear Memory (MC) button.</comment>
  </data>
  <data name="MemoryLabel.Text" xml:space="preserve">
    <value>Mälu</value>
    <comment>The text that shows as the header for the memory list</comment>
  </data>
  <data name="MemoryPivotItem.[using:Windows.UI.Xaml.Automation]AutomationProperties.Name" xml:space="preserve">
    <value>Mälu</value>
    <comment>The automation name for the Memory pivot item that is shown when Calculator is in wide layout.</comment>
  </data>
  <data name="HistoryLabel.Text" xml:space="preserve">
    <value>Ajalugu</value>
    <comment>The text that shows as the header for the history list</comment>
  </data>
  <data name="HistoryPivotItem.[using:Windows.UI.Xaml.Automation]AutomationProperties.Name" xml:space="preserve">
    <value>Ajalugu</value>
    <comment>The automation name for the History pivot item that is shown when Calculator is in wide layout.</comment>
  </data>
  <data name="converterModeButton.Content" xml:space="preserve">
    <value>Teisendi</value>
    <comment>Label for a control that activates the unit converter mode.</comment>
  </data>
  <data name="scientificModeButton.Content" xml:space="preserve">
    <value>Teaduslik</value>
    <comment>Label for a control that activates scientific mode calculator layout</comment>
  </data>
  <data name="standardModeButton.Content" xml:space="preserve">
    <value>Standardne</value>
    <comment>Label for a control that activates standard mode calculator layout.</comment>
  </data>
  <data name="converterModeButton.[using:Windows.UI.Xaml.Automation]AutomationProperties.Name" xml:space="preserve">
    <value>Teisendirežiim</value>
    <comment>Screen reader prompt for a control that activates the unit converter mode.</comment>
  </data>
  <data name="scientificModeButton.[using:Windows.UI.Xaml.Automation]AutomationProperties.Name" xml:space="preserve">
    <value>Teaduslik režiim</value>
    <comment>Screen reader prompt for a control that activates scientific mode calculator layout</comment>
  </data>
  <data name="standardModeButton.[using:Windows.UI.Xaml.Automation]AutomationProperties.Name" xml:space="preserve">
    <value>Standardrežiim</value>
    <comment>Screen reader prompt for a control that activates standard mode calculator layout.</comment>
  </data>
  <data name="ClearHistory.Name" xml:space="preserve">
    <value>Tühjenda kogu ajalugu</value>
    <comment>"ClearHistory" used on the calculator history pane that stores the calculation history.</comment>
  </data>
  <data name="ClearHistory.[using:Windows.UI.Xaml.Controls]ToolTipService.ToolTip" xml:space="preserve">
    <value>Tühjenda kogu ajalugu</value>
    <comment>This is the tool tip automation name for the Clear History button.</comment>
  </data>
  <data name="HideHistory.Name" xml:space="preserve">
    <value>Peida</value>
    <comment>"HideHistory" used on the calculator history pane that stores the calculation history.</comment>
  </data>
  <data name="StandardModeText" xml:space="preserve">
    <value>Standardne</value>
    <comment>The text that shows in the dropdown navigation control in snapped mode when standard calculator mode is selected.</comment>
  </data>
  <data name="ScientificModeText" xml:space="preserve">
    <value>Teaduslik</value>
    <comment>The text that shows in the dropdown navigation control in snapped mode when scientific calculator mode is selected.</comment>
  </data>
  <data name="ProgrammerModeText" xml:space="preserve">
    <value>Programmeerija</value>
    <comment>The text that shows in the dropdown navigation control in snapped mode when programmer calculator mode is selected.</comment>
  </data>
  <data name="ConverterModeText" xml:space="preserve">
    <value>Teisendi</value>
    <comment>The text that shows in the dropdown navigation control for the converter group. The previous key for this was "ConverterMode.Text".</comment>
  </data>
  <data name="CalculatorModeText" xml:space="preserve">
    <value>Kalkulaator</value>
    <comment>The text that shows in the dropdown navigation control for the calculator group.</comment>
  </data>
  <data name="ConverterModeTextCaps" xml:space="preserve">
    <value>Teisendi</value>
    <comment>The text that shows in the dropdown navigation control for the converter group in upper case. The previous key for this was "ConverterMode.Text".</comment>
  </data>
  <data name="CalculatorModeTextCaps" xml:space="preserve">
    <value>Kalkulaator</value>
    <comment>The text that shows in the dropdown navigation control for the calculator group in upper case.</comment>
  </data>
  <data name="ConverterModePluralText" xml:space="preserve">
    <value>Teisendid</value>
    <comment>Pluralized version of the converter group text, used for the screen reader prompt.</comment>
  </data>
  <data name="CalculatorModePluralText" xml:space="preserve">
    <value>Kalkulaatorid</value>
    <comment>Pluralized version of the calculator group text, used for the screen reader prompt.</comment>
  </data>
  <data name="Format_CalculatorResults" xml:space="preserve">
    <value>Kuva on %1</value>
    <comment>{Locked="%1"}. Screen reader prompt for the Calculator results text block. %1 = Localized display value, e.g. "50".</comment>
  </data>
  <data name="Format_CalculatorAlwaysOnTopResults" xml:space="preserve">
    <value>Avaldis on %1, praegune sisend on %2</value>
    <comment>{Locked="%1","%2"}. Screen reader prompt for the Calculator always-on-top expression. %1 = Expression, e.g. "50 + 2 - 60 +", %2 = Localized display value, e.g. "50".</comment>
  </data>
  <data name="Format_CalculatorResults_Decimal" xml:space="preserve">
    <value>Kuva on %1 koma</value>
    <comment>{Locked="%1"}. Automation label for the calculator display in the specific case where the user has just pressed the decimal separator button. For example, the user wants to input "7.5".  When they have input "7." they will hear "Display is 7 point". "point" should be localized to the locale's appropriate decimal separator.</comment>
  </data>
  <data name="Format_CalculatorExpression" xml:space="preserve">
    <value>Avaldis on %1</value>
    <comment>{Locked="%1"}. Screen reader prompt for the Calculator expression results. %1 = Localized display value, e.g. "50 + 2 - 60 +".</comment>
  </data>
  <data name="Display_Copied" xml:space="preserve">
    <value>Kuvatav väärtus on lõikelauale kopeeritud</value>
    <comment>Screen reader prompt for the Calculator display copy button, when the button is invoked.</comment>
  </data>
  <data name="HistoryPane" xml:space="preserve">
    <value>Ajalugu</value>
    <comment>Screen reader prompt for the history flyout</comment>
  </data>
  <data name="MemoryPane" xml:space="preserve">
    <value>Mälu</value>
    <comment>Screen reader prompt for the memory flyout</comment>
  </data>
  <data name="Format_HexButtonValue" xml:space="preserve">
    <value>Kuueteistkümnendarv %1</value>
    <comment>{Locked="%1"}. Screen reader prompt for the hexadecimal value in Programmer mode. %1 = the localized hexadecimal value, e.g. "21B4 8F73".</comment>
  </data>
  <data name="Format_DecButtonValue" xml:space="preserve">
    <value>Kümnendarv %1</value>
    <comment>{Locked="%1"}. Screen reader prompt for the decimal value in Programmer mode. %1 = the localized decimal value, e.g. "5,732".</comment>
  </data>
  <data name="Format_OctButtonValue" xml:space="preserve">
    <value>Kaheksandarv %1</value>
    <comment>{Locked="%1"}. Screen reader prompt for the octal value in Programmer mode. %1 = the localized octal value, e.g. "155 174".</comment>
  </data>
  <data name="Format_BinButtonValue" xml:space="preserve">
    <value>Kahendarv %1</value>
    <comment>{Locked="%1"}. Screen reader prompt for the binary value in Programmer mode. %1 = the localized binary value, e.g. "0010 1011".</comment>
  </data>
  <data name="ClearHistory.[using:Windows.UI.Xaml.Automation]AutomationProperties.Name" xml:space="preserve">
    <value>Tühjenda kogu ajalugu</value>
    <comment>Screen reader prompt for the Calculator History Clear button</comment>
  </data>
  <data name="HistoryList_Cleared" xml:space="preserve">
    <value>Ajalugu on kustutatud</value>
    <comment>Screen reader prompt for the Calculator History Clear button, when the button is invoked.</comment>
  </data>
  <data name="HideHistory.[using:Windows.UI.Xaml.Automation]AutomationProperties.Name" xml:space="preserve">
    <value>Peida ajalugu</value>
    <comment>Screen reader prompt for the Calculator History Hide button</comment>
  </data>
  <data name="HistoryButton_Open" xml:space="preserve">
    <value>Ava ajaloo hüpik</value>
    <comment>Screen reader prompt for the Calculator History button, when the flyout is closed.</comment>
  </data>
  <data name="HistoryButton_Close" xml:space="preserve">
    <value>Sule ajaloo hüpik</value>
    <comment>Screen reader prompt for the Calculator History button, when the flyout is open.</comment>
  </data>
  <data name="memButton.[using:Windows.UI.Xaml.Automation]AutomationProperties.Name" xml:space="preserve">
    <value>Salvesta mällu</value>
    <comment>Screen reader prompt for the Calculator Memory button</comment>
  </data>
  <data name="memButton.[using:Windows.UI.Xaml.Controls]ToolTipService.ToolTip" xml:space="preserve">
    <value>Salvesta mällu (Ctrl+M)</value>
    <comment>This is the tool tip automation name for the Memory Store (MS) button.</comment>
  </data>
  <data name="ClearMemoryButton.[using:Windows.UI.Xaml.Automation]AutomationProperties.Name" xml:space="preserve">
    <value>Tühjenda kogu mälu</value>
    <comment>Screen reader prompt for the Calculator Clear Memory button</comment>
  </data>
  <data name="Memory_Cleared" xml:space="preserve">
    <value>Mälu on tühjendatud</value>
    <comment>Screen reader prompt for the Calculator Clear Memory button, when the button is invoked.</comment>
  </data>
  <data name="MemRecall.[using:Windows.UI.Xaml.Automation]AutomationProperties.Name" xml:space="preserve">
    <value>Võta mälust</value>
    <comment>Screen reader prompt for the Calculator Memory Recall button</comment>
  </data>
  <data name="MemRecall.[using:Windows.UI.Xaml.Controls]ToolTipService.ToolTip" xml:space="preserve">
    <value>Võta mälust (Ctrl+R)</value>
    <comment>This is the tool tip automation name for the Memory Recall (MR) button.</comment>
  </data>
  <data name="MemPlus.[using:Windows.UI.Xaml.Automation]AutomationProperties.Name" xml:space="preserve">
    <value>Lisa mällu</value>
    <comment>Screen reader prompt for the Calculator Memory Add button</comment>
  </data>
  <data name="MemPlus.[using:Windows.UI.Xaml.Controls]ToolTipService.ToolTip" xml:space="preserve">
    <value>Lisa mällu (Ctrl+P)</value>
    <comment>This is the tool tip automation name for the Memory Add (M+) button.</comment>
  </data>
  <data name="MemMinus.[using:Windows.UI.Xaml.Automation]AutomationProperties.Name" xml:space="preserve">
    <value>Lahuta mälust</value>
    <comment>Screen reader prompt for the Calculator Memory Subtract button</comment>
  </data>
  <data name="MemMinus.[using:Windows.UI.Xaml.Controls]ToolTipService.ToolTip" xml:space="preserve">
    <value>Lahuta mälust (Ctrl+Q)</value>
    <comment>This is the tool tip automation name for the Memory Subtract (M-) button.</comment>
  </data>
  <data name="ClearMemoryItemButton.[using:Windows.UI.Xaml.Automation]AutomationProperties.Name" xml:space="preserve">
    <value>Tühjenda mäluüksus</value>
    <comment>Screen reader prompt for the Calculator Clear Memory button</comment>
  </data>
  <data name="ClearMemoryItemButton.[using:Windows.UI.Xaml.Controls]ToolTipService.ToolTip" xml:space="preserve">
    <value>Tühjenda mäluüksus</value>
    <comment>This is the tool tip automation name for the Clear Memory Item (MC) button in the Memory list.</comment>
  </data>
  <data name="MemPlusItem.[using:Windows.UI.Xaml.Automation]AutomationProperties.Name" xml:space="preserve">
    <value>Lisa mäluüksusesse</value>
    <comment>Screen reader prompt for the Calculator Memory Add button in the Memory list</comment>
  </data>
  <data name="MemPlusItem.[using:Windows.UI.Xaml.Controls]ToolTipService.ToolTip" xml:space="preserve">
    <value>Lisa mäluüksusesse</value>
    <comment>This is the tool tip automation name for the Calculator Memory Add button in the Memory list</comment>
  </data>
  <data name="MemMinusItem.[using:Windows.UI.Xaml.Automation]AutomationProperties.Name" xml:space="preserve">
    <value>Lahuta mäluüksusest</value>
    <comment>Screen reader prompt for the Calculator Memory Subtract button in the Memory list</comment>
  </data>
  <data name="MemMinusItem.[using:Windows.UI.Xaml.Controls]ToolTipService.ToolTip" xml:space="preserve">
    <value>Lahuta mäluüksusest</value>
    <comment>This is the tool tip automation name for the Calculator Memory Subtract button in the Memory list</comment>
  </data>
  <data name="ClearMemorySwipeItem.[using:Windows.UI.Xaml.Automation]AutomationProperties.Name" xml:space="preserve">
    <value>Tühjenda mäluüksus</value>
    <comment>Screen reader prompt for the Calculator Clear Memory button</comment>
  </data>
  <data name="ClearMemoryMenuItem.Text" xml:space="preserve">
    <value>Tühjenda mäluüksus</value>
    <comment>Text string for the Calculator Clear Memory option in the Memory list context menu</comment>
  </data>
  <data name="MemPlusSwipeItem.[using:Windows.UI.Xaml.Automation]AutomationProperties.Name" xml:space="preserve">
    <value>Lisa mäluüksusesse</value>
    <comment>Screen reader prompt for the Calculator Memory Add swipe button in the Memory list</comment>
  </data>
  <data name="MemPlusMenuItem.Text" xml:space="preserve">
    <value>Lisa mäluüksusesse</value>
    <comment>Text string for the Calculator Memory Add option in the Memory list context menu</comment>
  </data>
  <data name="MemMinusSwipeItem.[using:Windows.UI.Xaml.Automation]AutomationProperties.Name" xml:space="preserve">
    <value>Lahuta mäluüksusest</value>
    <comment>Screen reader prompt for the Calculator Memory Subtract swipe button in the Memory list</comment>
  </data>
  <data name="MemMinusMenuItem.Text" xml:space="preserve">
    <value>Lahuta mäluüksusest</value>
    <comment>Text string for the Calculator Memory Subtract option in the Memory list context menu</comment>
  </data>
  <data name="DeleteHistorySwipeItem.Text" xml:space="preserve">
    <value>Kustuta</value>
    <comment>Text string for the Calculator Delete swipe button in the History list</comment>
  </data>
  <data name="CopyHistoryMenuItem.Text" xml:space="preserve">
    <value>Kopeeri</value>
    <comment>Text string for the Calculator Copy option in the History list context menu</comment>
  </data>
  <data name="DeleteHistoryMenuItem.Text" xml:space="preserve">
    <value>Kustuta</value>
    <comment>Text string for the Calculator Delete option in the History list context menu</comment>
  </data>
  <data name="DeleteHistorySwipeItem.[using:Windows.UI.Xaml.Automation]AutomationProperties.Name" xml:space="preserve">
    <value>Kustuta ajalooüksus</value>
    <comment>Screen reader prompt for the Calculator Delete swipe button in the History list</comment>
  </data>
  <data name="DeleteHistoryMenuItem.[using:Windows.UI.Xaml.Automation]AutomationProperties.Name" xml:space="preserve">
    <value>Kustuta ajalooüksus</value>
    <comment>Screen reader prompt for the Calculator Delete option in the History list context menu</comment>
  </data>
  <data name="backSpaceButton.[using:Windows.UI.Xaml.Automation]AutomationProperties.Name" xml:space="preserve">
    <value>Tagasilüke</value>
    <comment>Screen reader prompt for the Calculator Backspace button</comment>
  </data>
  <data name="BinaryZero.Text" xml:space="preserve">
    <value>0</value>
    <comment>Screen reader prompt for the Calculator number "0" button</comment>
  </data>
  <data name="BinaryOne.Text" xml:space="preserve">
    <value>1</value>
    <comment>Screen reader prompt for the Calculator number "1" button</comment>
  </data>
  <data name="num0Button.[using:Windows.UI.Xaml.Automation]AutomationProperties.Name" xml:space="preserve">
    <value>Null</value>
    <comment>Screen reader prompt for the Calculator number "0" button</comment>
  </data>
  <data name="num1Button.[using:Windows.UI.Xaml.Automation]AutomationProperties.Name" xml:space="preserve">
    <value>Üks</value>
    <comment>Screen reader prompt for the Calculator number "1" button</comment>
  </data>
  <data name="num2Button.[using:Windows.UI.Xaml.Automation]AutomationProperties.Name" xml:space="preserve">
    <value>Kaks</value>
    <comment>Screen reader prompt for the Calculator number "2" button</comment>
  </data>
  <data name="num3Button.[using:Windows.UI.Xaml.Automation]AutomationProperties.Name" xml:space="preserve">
    <value>Kolm</value>
    <comment>Screen reader prompt for the Calculator number "3" button</comment>
  </data>
  <data name="num4Button.[using:Windows.UI.Xaml.Automation]AutomationProperties.Name" xml:space="preserve">
    <value>Neli</value>
    <comment>Screen reader prompt for the Calculator number "4" button</comment>
  </data>
  <data name="num5Button.[using:Windows.UI.Xaml.Automation]AutomationProperties.Name" xml:space="preserve">
    <value>Viis</value>
    <comment>Screen reader prompt for the Calculator number "5" button</comment>
  </data>
  <data name="num6Button.[using:Windows.UI.Xaml.Automation]AutomationProperties.Name" xml:space="preserve">
    <value>Kuus</value>
    <comment>Screen reader prompt for the Calculator number "6" button</comment>
  </data>
  <data name="num7Button.[using:Windows.UI.Xaml.Automation]AutomationProperties.Name" xml:space="preserve">
    <value>Seitse</value>
    <comment>Screen reader prompt for the Calculator number "7" button</comment>
  </data>
  <data name="num8Button.[using:Windows.UI.Xaml.Automation]AutomationProperties.Name" xml:space="preserve">
    <value>Kaheksa</value>
    <comment>Screen reader prompt for the Calculator number "8" button</comment>
  </data>
  <data name="num9Button.[using:Windows.UI.Xaml.Automation]AutomationProperties.Name" xml:space="preserve">
    <value>Üheksa</value>
    <comment>Screen reader prompt for the Calculator number "9" button</comment>
  </data>
  <data name="aButton.[using:Windows.UI.Xaml.Automation]AutomationProperties.Name" xml:space="preserve">
    <value>A</value>
    <comment>Screen reader prompt for the Calculator number "A" button</comment>
  </data>
  <data name="bButton.[using:Windows.UI.Xaml.Automation]AutomationProperties.Name" xml:space="preserve">
    <value>B</value>
    <comment>Screen reader prompt for the Calculator number "B" button</comment>
  </data>
  <data name="cButton.[using:Windows.UI.Xaml.Automation]AutomationProperties.Name" xml:space="preserve">
    <value>C</value>
    <comment>Screen reader prompt for the Calculator number "C" button</comment>
  </data>
  <data name="dButton.[using:Windows.UI.Xaml.Automation]AutomationProperties.Name" xml:space="preserve">
    <value>D</value>
    <comment>Screen reader prompt for the Calculator number "D" button</comment>
  </data>
  <data name="eButton.[using:Windows.UI.Xaml.Automation]AutomationProperties.Name" xml:space="preserve">
    <value>E</value>
    <comment>Screen reader prompt for the Calculator number "E" button</comment>
  </data>
  <data name="fButton.[using:Windows.UI.Xaml.Automation]AutomationProperties.Name" xml:space="preserve">
    <value>F</value>
    <comment>Screen reader prompt for the Calculator number "F" button</comment>
  </data>
  <data name="andButton.[using:Windows.UI.Xaml.Automation]AutomationProperties.Name" xml:space="preserve">
    <value>Ja</value>
    <comment>Screen reader prompt for the Calculator And button</comment>
  </data>
  <data name="orButton.[using:Windows.UI.Xaml.Automation]AutomationProperties.Name" xml:space="preserve">
    <value>Või</value>
    <comment>Screen reader prompt for the Calculator Or button</comment>
  </data>
  <data name="notButton.[using:Windows.UI.Xaml.Automation]AutomationProperties.Name" xml:space="preserve">
    <value>Pole</value>
    <comment>Screen reader prompt for the Calculator Not button</comment>
  </data>
  <data name="rolButton.[using:Windows.UI.Xaml.Automation]AutomationProperties.Name" xml:space="preserve">
    <value>Ringnihe vasakule</value>
    <comment>Screen reader prompt for the Calculator ROL button</comment>
  </data>
  <data name="rorButton.[using:Windows.UI.Xaml.Automation]AutomationProperties.Name" xml:space="preserve">
    <value>Ringnihe paremale</value>
    <comment>Screen reader prompt for the Calculator ROR button</comment>
  </data>
  <data name="lshButton.[using:Windows.UI.Xaml.Automation]AutomationProperties.Name" xml:space="preserve">
    <value>Nihe vasakule</value>
    <comment>Screen reader prompt for the Calculator LSH button</comment>
  </data>
  <data name="rshButton.[using:Windows.UI.Xaml.Automation]AutomationProperties.Name" xml:space="preserve">
    <value>Nihe paremale</value>
    <comment>Screen reader prompt for the Calculator RSH button</comment>
  </data>
  <data name="xorButton.[using:Windows.UI.Xaml.Automation]AutomationProperties.Name" xml:space="preserve">
    <value>Välistav VÕI</value>
    <comment>Screen reader prompt for the Calculator XOR button</comment>
  </data>
  <data name="qwordButton.[using:Windows.UI.Xaml.Automation]AutomationProperties.Name" xml:space="preserve">
    <value>Neliksõna tumblernupp</value>
    <comment>Screen reader prompt for the Calculator qword button. Should read as "Quadruple word toggle button".</comment>
  </data>
  <data name="dwordButton.[using:Windows.UI.Xaml.Automation]AutomationProperties.Name" xml:space="preserve">
    <value>Topeltsõna tumblernupp</value>
    <comment>Screen reader prompt for the Calculator dword button. Should read as "Double word toggle button".</comment>
  </data>
  <data name="wordButton.[using:Windows.UI.Xaml.Automation]AutomationProperties.Name" xml:space="preserve">
    <value>Sõna tumblernupp</value>
    <comment>Screen reader prompt for the Calculator word button. Should read as "Word toggle button".</comment>
  </data>
  <data name="byteButton.[using:Windows.UI.Xaml.Automation]AutomationProperties.Name" xml:space="preserve">
    <value>Baidi tumblernupp</value>
    <comment>Screen reader prompt for the Calculator byte button. Should read as "Byte toggle button".</comment>
  </data>
  <data name="bitFlip.[using:Windows.UI.Xaml.Automation]AutomationProperties.Name" xml:space="preserve">
    <value>Biti ümberlülituse klahvistik</value>
    <comment>Screen reader prompt for the Calculator bitFlip button</comment>
  </data>
  <data name="fullKeypad.[using:Windows.UI.Xaml.Automation]AutomationProperties.Name" xml:space="preserve">
    <value>Täisklahvistik</value>
    <comment>Screen reader prompt for the Calculator numberPad button</comment>
  </data>
  <data name="decimalSeparatorButton.[using:Windows.UI.Xaml.Automation]AutomationProperties.Name" xml:space="preserve">
    <value>Murdosa eraldaja</value>
    <comment>Screen reader prompt for the "." button</comment>
  </data>
  <data name="clearEntryButton.[using:Windows.UI.Xaml.Automation]AutomationProperties.Name" xml:space="preserve">
    <value>Tühjenda sisestus</value>
    <comment>Screen reader prompt for the "CE" button</comment>
  </data>
  <data name="clearButton.[using:Windows.UI.Xaml.Automation]AutomationProperties.Name" xml:space="preserve">
    <value>Tühjenda</value>
    <comment>Screen reader prompt for the "C" button</comment>
  </data>
  <data name="divideButton.[using:Windows.UI.Xaml.Automation]AutomationProperties.Name" xml:space="preserve">
    <value>Jaga arvuga</value>
    <comment>Screen reader prompt for the divide button on the number pad</comment>
  </data>
  <data name="multiplyButton.[using:Windows.UI.Xaml.Automation]AutomationProperties.Name" xml:space="preserve">
    <value>Korruta arvuga</value>
    <comment>Screen reader prompt for the multiply button on the number pad</comment>
  </data>
  <data name="equalButton.[using:Windows.UI.Xaml.Automation]AutomationProperties.Name" xml:space="preserve">
    <value>Võrdub</value>
    <comment>Screen reader prompt for the equals button on the scientific operator keypad</comment>
  </data>
  <data name="shiftButton.[using:Windows.UI.Xaml.Automation]AutomationProperties.Name" xml:space="preserve">
    <value>Pöördfunktsioon</value>
    <comment>Screen reader prompt for the shift button on the number pad in scientific mode.</comment>
  </data>
  <data name="minusButton.[using:Windows.UI.Xaml.Automation]AutomationProperties.Name" xml:space="preserve">
    <value>Miinus</value>
    <comment>Screen reader prompt for the minus button on the number pad</comment>
  </data>
  <data name="minus" xml:space="preserve">
    <value>Miinus</value>
    <comment>We use this resource to replace "-" sign for accessibility. So expression like, 1 - 3 = -2 becomes 1 minus 3 = minus 2</comment>
  </data>
  <data name="plusButton.[using:Windows.UI.Xaml.Automation]AutomationProperties.Name" xml:space="preserve">
    <value>Pluss</value>
    <comment>Screen reader prompt for the plus button on the number pad</comment>
  </data>
  <data name="squareRootButton.[using:Windows.UI.Xaml.Automation]AutomationProperties.Name" xml:space="preserve">
    <value>Ruutjuur</value>
    <comment>Screen reader prompt for the square root button on the scientific operator keypad</comment>
  </data>
  <data name="percentButton.[using:Windows.UI.Xaml.Automation]AutomationProperties.Name" xml:space="preserve">
    <value>Protsent</value>
    <comment>Screen reader prompt for the percent button on the scientific operator keypad</comment>
  </data>
  <data name="negateButton.[using:Windows.UI.Xaml.Automation]AutomationProperties.Name" xml:space="preserve">
    <value>Positiivne negatiivne</value>
    <comment>Screen reader prompt for the negate button on the scientific operator keypad</comment>
  </data>
  <data name="converterNegateButton.[using:Windows.UI.Xaml.Automation]AutomationProperties.Name" xml:space="preserve">
    <value>Positiivne negatiivne</value>
    <comment>Screen reader prompt for the negate button on the converter operator keypad</comment>
  </data>
  <data name="invertButton.[using:Windows.UI.Xaml.Automation]AutomationProperties.Name" xml:space="preserve">
    <value>Pöördväärtus</value>
    <comment>Screen reader prompt for the invert button on the scientific operator keypad</comment>
  </data>
  <data name="openParenthesisButton.[using:Windows.UI.Xaml.Automation]AutomationProperties.Name" xml:space="preserve">
    <value>Vasaksulg</value>
    <comment>Screen reader prompt for the Calculator "(" button on the scientific operator keypad</comment>
  </data>
  <data name="Format_OpenParenthesisAutomationNamePrefix" xml:space="preserve">
    <value>Vasaksulg, avasulgude arv %1</value>
    <comment>{Locked="%1"} Screen reader prompt for the Calculator "(" button on the scientific operator keypad. %1 is the localized count of open parenthesis, e.g. "2".</comment>
  </data>
  <data name="closeParenthesisButton.[using:Windows.UI.Xaml.Automation]AutomationProperties.Name" xml:space="preserve">
    <value>Paremsulg</value>
    <comment>Screen reader prompt for the Calculator ")" button on the scientific operator keypad</comment>
  </data>
  <data name="Format_OpenParenthesisCountAutomationNamePrefix" xml:space="preserve">
    <value>Avavate sulgude arv %1</value>
    <comment>{Locked="%1"} Screen reader prompt for the Calculator "(" button on the scientific and programmer operator keypad. %1 is the localized count of open parenthesis, e.g. "2".</comment>
  </data>
  <data name="NoRightParenthesisAdded_Announcement" xml:space="preserve">
    <value>Avavaid sulge, mis vajavad sulgemist, ei ole.</value>
    <comment>{Locked="%1"} Screen reader prompt for the Calculator when the ")" button on the scientific and programmer operator keypad cannot be added to the equation. e.g. "1+)".</comment>
  </data>
  <data name="ftoeButton.[using:Windows.UI.Xaml.Automation]AutomationProperties.Name" xml:space="preserve">
    <value>Teaduskuju</value>
    <comment>Screen reader prompt for the Calculator F-E the scientific operator keypad</comment>
  </data>
  <data name="hyperbolicButton.[using:Windows.UI.Xaml.Automation]AutomationProperties.Name" xml:space="preserve">
    <value>Hüperboolne funktsioon</value>
    <comment>Screen reader prompt for the Calculator button HYP in the scientific operator keypad</comment>
  </data>
  <data name="piButton.[using:Windows.UI.Xaml.Automation]AutomationProperties.Name" xml:space="preserve">
    <value>Pii</value>
    <comment>Screen reader prompt for the Calculator pi button  on the scientific operator keypad</comment>
  </data>
  <data name="sinButton.[using:Windows.UI.Xaml.Automation]AutomationProperties.Name" xml:space="preserve">
    <value>Siinus</value>
    <comment>Screen reader prompt for the Calculator sin button  on the scientific operator keypad</comment>
  </data>
  <data name="cosButton.[using:Windows.UI.Xaml.Automation]AutomationProperties.Name" xml:space="preserve">
    <value>Koosinus</value>
    <comment>Screen reader prompt for the Calculator cos button  on the scientific operator keypad</comment>
  </data>
  <data name="tanButton.[using:Windows.UI.Xaml.Automation]AutomationProperties.Name" xml:space="preserve">
    <value>Tangens</value>
    <comment>Screen reader prompt for the Calculator tan button  on the scientific operator keypad</comment>
  </data>
  <data name="sinhButton.[using:Windows.UI.Xaml.Automation]AutomationProperties.Name" xml:space="preserve">
    <value>Hüperboolne siinus</value>
    <comment>Screen reader prompt for the Calculator sinh button  on the scientific operator keypad</comment>
  </data>
  <data name="coshButton.[using:Windows.UI.Xaml.Automation]AutomationProperties.Name" xml:space="preserve">
    <value>Hüperboolne koosinus</value>
    <comment>Screen reader prompt for the Calculator cosh button  on the scientific operator keypad</comment>
  </data>
  <data name="tanhButton.[using:Windows.UI.Xaml.Automation]AutomationProperties.Name" xml:space="preserve">
    <value>Hüperboolne tangens</value>
    <comment>Screen reader prompt for the Calculator tanh button  on the scientific operator keypad</comment>
  </data>
  <data name="xpower2Button.[using:Windows.UI.Xaml.Automation]AutomationProperties.Name" xml:space="preserve">
    <value>Ruut</value>
    <comment>Screen reader prompt for the x squared on the scientific operator keypad. </comment>
  </data>
  <data name="xpower3Button.[using:Windows.UI.Xaml.Automation]AutomationProperties.Name" xml:space="preserve">
    <value>Kuup</value>
    <comment>Screen reader prompt for the x cubed on the scientific operator keypad. </comment>
  </data>
  <data name="invsinButton.[using:Windows.UI.Xaml.Automation]AutomationProperties.Name" xml:space="preserve">
    <value>Arkussiinus</value>
    <comment>Screen reader prompt for the inverted sin on the scientific operator keypad.</comment>
  </data>
  <data name="invcosButton.[using:Windows.UI.Xaml.Automation]AutomationProperties.Name" xml:space="preserve">
    <value>Arkuskoosinus</value>
    <comment>Screen reader prompt for the inverted cos on the scientific operator keypad.</comment>
  </data>
  <data name="invtanButton.[using:Windows.UI.Xaml.Automation]AutomationProperties.Name" xml:space="preserve">
    <value>Arkustangens</value>
    <comment>Screen reader prompt for the inverted tan on the scientific operator keypad.</comment>
  </data>
  <data name="invsinhButton.[using:Windows.UI.Xaml.Automation]AutomationProperties.Name" xml:space="preserve">
    <value>Hüperboolne arkussiinus</value>
    <comment>Screen reader prompt for the inverted sinh on the scientific operator keypad.</comment>
  </data>
  <data name="invcoshButton.[using:Windows.UI.Xaml.Automation]AutomationProperties.Name" xml:space="preserve">
    <value>Hüperboolne arkuskoosinus</value>
    <comment>Screen reader prompt for the inverted cosh on the scientific operator keypad.</comment>
  </data>
  <data name="invtanhButton.[using:Windows.UI.Xaml.Automation]AutomationProperties.Name" xml:space="preserve">
    <value>Hüperboolne arkustangens</value>
    <comment>Screen reader prompt for the inverted tanh on the scientific operator keypad.</comment>
  </data>
  <data name="powerButton.[using:Windows.UI.Xaml.Automation]AutomationProperties.Name" xml:space="preserve">
    <value>x-i aste</value>
    <comment>Screen reader prompt for x power y button on the scientific operator keypad. </comment>
  </data>
  <data name="powerOf10Button.[using:Windows.UI.Xaml.Automation]AutomationProperties.Name" xml:space="preserve">
    <value>Kümne aste</value>
    <comment>Screen reader prompt for the 10 power x button on the scientific operator keypad.</comment>
  </data>
  <data name="powerOfEButton.[using:Windows.UI.Xaml.Automation]AutomationProperties.Name" xml:space="preserve">
    <value>e aste</value>
    <comment>Screen reader for the e power x on the scientific operator keypad.</comment>
  </data>
  <data name="ySquareRootButton.[using:Windows.UI.Xaml.Automation]AutomationProperties.Name" xml:space="preserve">
    <value>y-astme juur x-ist</value>
    <comment>Screen reader for the yth root of x on the scientific operator keypad. Note: String is meant to read out whatever the "Yth root" mathematical operator sounds like.</comment>
  </data>
  <data name="logBase10Button.[using:Windows.UI.Xaml.Automation]AutomationProperties.Name" xml:space="preserve">
    <value>Logaritm</value>
    <comment>Screen reader for the log base 10  on the scientific operator keypad</comment>
  </data>
  <data name="logBaseEButton.[using:Windows.UI.Xaml.Automation]AutomationProperties.Name" xml:space="preserve">
    <value>Naturaallogaritm</value>
    <comment>Screen reader for the log base e on the scientific operator keypad</comment>
  </data>
  <data name="modButton.[using:Windows.UI.Xaml.Automation]AutomationProperties.Name" xml:space="preserve">
    <value>Mooduli järgi</value>
    <comment>Screen reader for the mod button on the scientific operator keypad</comment>
  </data>
  <data name="expButton.[using:Windows.UI.Xaml.Automation]AutomationProperties.Name" xml:space="preserve">
    <value>Eksponentsiaalne</value>
    <comment>Screen reader for the exp button on the scientific operator keypad</comment>
  </data>
  <data name="dmsButton.[using:Windows.UI.Xaml.Automation]AutomationProperties.Name" xml:space="preserve">
    <value>Kraad, minut, sekund</value>
    <comment>Screen reader for the exp button on the scientific operator keypad</comment>
  </data>
  <data name="degreesButton.[using:Windows.UI.Xaml.Automation]AutomationProperties.Name" xml:space="preserve">
    <value>kraadi</value>
    <comment>Screen reader for the exp button on the scientific operator keypad</comment>
  </data>
  <data name="intButton.[using:Windows.UI.Xaml.Automation]AutomationProperties.Name" xml:space="preserve">
    <value>täisosa</value>
    <comment>Screen reader for the int button on the scientific operator keypad</comment>
  </data>
  <data name="fractButton.[using:Windows.UI.Xaml.Automation]AutomationProperties.Name" xml:space="preserve">
    <value>Murdosa</value>
    <comment>Screen reader for the frac button on the scientific operator keypad</comment>
  </data>
  <data name="factorialButton.[using:Windows.UI.Xaml.Automation]AutomationProperties.Name" xml:space="preserve">
    <value>faktoriaali</value>
    <comment>Screen reader for the factorial button on the basic operator keypad</comment>
  </data>
  <data name="degButton.[using:Windows.UI.Xaml.Automation]AutomationProperties.Name" xml:space="preserve">
    <value>Kraadide tumblernupp</value>
    <comment>This the Deg button's Degree mode automation nameon the scientific operator keypad. Should read as "Degrees toggle button".</comment>
  </data>
  <data name="gradButton.[using:Windows.UI.Xaml.Automation]AutomationProperties.Name" xml:space="preserve">
    <value>Goonide tumblernupp</value>
    <comment>This is the Deg button's Grad mode automation name on the scientific operator keypad. Should read as "Gradians toggle button".</comment>
  </data>
  <data name="radButton.[using:Windows.UI.Xaml.Automation]AutomationProperties.Name" xml:space="preserve">
    <value>Radiaanide tumblernupp</value>
    <comment>This is the Deg button's Rad mode automation name on the scientific operator keypad. Should read as "Radians toggle button".</comment>
  </data>
  <data name="FlyoutNav.[using:Windows.UI.Xaml.Automation]AutomationProperties.Name" xml:space="preserve">
    <value>Režiimi ripploend</value>
    <comment>Screen reader prompt for the Mode dropdown field in Snapped and Portrait modes.</comment>
  </data>
  <data name="Categories.[using:Windows.UI.Xaml.Automation]AutomationProperties.Name" xml:space="preserve">
    <value>Kategooriate ripploend</value>
    <comment>Screen reader prompt for the Categories dropdown field.</comment>
  </data>
  <data name="EnterAlwaysOnTopButton.[using:Windows.UI.Xaml.Automation]AutomationProperties.Name" xml:space="preserve">
    <value>Hoia kõige peal</value>
    <comment>Screen reader prompt for the Always-on-Top button when in normal mode.</comment>
  </data>
  <data name="ExitAlwaysOnTopButton.[using:Windows.UI.Xaml.Automation]AutomationProperties.Name" xml:space="preserve">
    <value>Tagasi täisekraanvaatesse</value>
    <comment>Screen reader prompt for the Always-on-Top button when in Always-on-Top mode.</comment>
  </data>
  <data name="EnterAlwaysOnTopButton.[using:Windows.UI.Xaml.Controls]ToolTipService.ToolTip" xml:space="preserve">
    <value>Alati kõige peal (Alt+Up)</value>
    <comment>This is the tool tip automation name for the Always-on-Top button when in normal mode.</comment>
  </data>
  <data name="ExitAlwaysOnTopButton.[using:Windows.UI.Xaml.Controls]ToolTipService.ToolTip" xml:space="preserve">
    <value>Tagasi täisekraanvaatesse (Alt+Down)</value>
    <comment>This is the tool tip automation name for the Always-on-Top button when in Always-on-Top mode.</comment>
  </data>
  <data name="Format_ValueFrom" xml:space="preserve">
    <value>Teisenda %1 %2</value>
    <comment>Screen reader prompt for the Unit Converter Value1 i.e. top number field. %1 = DisplayValue, %2 = Unit field localized name.</comment>
  </data>
  <data name="Format_ValueFrom_Decimal" xml:space="preserve">
    <value>Teisendage %1 koma %2</value>
    <comment>{Locked="%1"}. Automation label for the calculator display in the specific case where the user has just pressed the decimal separator button. For example, the user wants to input "7.5".  When they have input "7." they will hear "Convert from 7 point _current_unit_". "point" should be localized to the locale's appropriate decimal separator.</comment>
  </data>
  <data name="Format_ValueTo" xml:space="preserve">
    <value>Teisenduse saadus %1 %2</value>
    <comment>Screen reader prompt for the Unit Converter Value2 i.e. bottom number field. %1 = DisplayValue, %2 = Unit field localized name.</comment>
  </data>
  <data name="Format_ConversionResult" xml:space="preserve">
    <value>%1 %2 on %3 %4</value>
    <comment>Screen reader prompt for a conversion result, ie "2 liters is 2,000 milliliters" . %1 = From unit display value, %2 = From unit, %3 = To unit display value, %4 = To unit.</comment>
  </data>
  <data name="InputUnit_Name" xml:space="preserve">
    <value>Sisendi ühik</value>
    <comment>Screen reader prompt for the Unit Converter Units1 i.e. top units field.</comment>
  </data>
  <data name="OutputUnit_Name" xml:space="preserve">
    <value>Väljundi ühik</value>
    <comment>Screen reader prompt for the Unit Converter Units2 i.e. bottom units field.</comment>
  </data>
  <data name="CategoryName_AreaText" xml:space="preserve">
    <value>Pindala</value>
    <comment>Unit conversion category name called Area (eg. area of a sports field in square meters)</comment>
  </data>
  <data name="CategoryName_DataText" xml:space="preserve">
    <value>Andmed</value>
    <comment>Unit conversion category name called Data</comment>
  </data>
  <data name="CategoryName_EnergyText" xml:space="preserve">
    <value>Energia</value>
    <comment>Unit conversion category name called Energy. (eg. the energy in a battery or in food)</comment>
  </data>
  <data name="CategoryName_LengthText" xml:space="preserve">
    <value>Pikkus</value>
    <comment>Unit conversion category name called Length</comment>
  </data>
  <data name="CategoryName_PowerText" xml:space="preserve">
    <value>Võimsus</value>
    <comment>Unit conversion category name called Power (eg. the power of an engine or a light bulb)</comment>
  </data>
  <data name="CategoryName_SpeedText" xml:space="preserve">
    <value>Kiirus</value>
    <comment>Unit conversion category name called Speed</comment>
  </data>
  <data name="CategoryName_TimeText" xml:space="preserve">
    <value>Aeg</value>
    <comment>Unit conversion category name called Time</comment>
  </data>
  <data name="CategoryName_VolumeText" xml:space="preserve">
    <value>Maht</value>
    <comment>Unit conversion category name called Volume (eg. cups, teaspoons, milliliters)</comment>
  </data>
  <data name="CategoryName_TemperatureText" xml:space="preserve">
    <value>Temperatuur</value>
    <comment>Unit conversion category name called Temperature</comment>
  </data>
  <data name="CategoryName_WeightText" xml:space="preserve">
    <value>Kaal ja mass</value>
    <comment>Unit conversion category name called Weight and mass. Note that this category includes units of both mass and weight. People use the word "weight" in everyday life for measuring things such as food and people. In case a language has same word for "weight" and "mass" please use one word only.</comment>
  </data>
  <data name="CategoryName_PressureText" xml:space="preserve">
    <value>Rõhk</value>
    <comment>Unit conversion category name called Pressure</comment>
  </data>
  <data name="CategoryName_AngleText" xml:space="preserve">
    <value>Nurk</value>
    <comment>Unit conversion category name called Angle</comment>
  </data>
  <data name="CategoryName_CurrencyText" xml:space="preserve">
    <value>Valuuta</value>
    <comment>Unit conversion category name called Currency</comment>
  </data>
  <data name="UnitName_FluidOunceUK" xml:space="preserve">
    <value>vedelikuuntsi (Briti)</value>
    <comment>A measurement unit for volume, in plural. (Please choose the most appropriate plural form to fit any number between 0 and 999,999,999,999,999)</comment>
  </data>
  <data name="UnitAbbreviation_FluidOunceUK" xml:space="preserve">
    <value>fl oz (Briti)</value>
    <comment>An abbreviation for a measurement unit of volume</comment>
  </data>
  <data name="UnitName_FluidOunceUS" xml:space="preserve">
    <value>vedelikuuntsi (USA)</value>
    <comment>A measurement unit for volume. (Please choose the most appropriate plural form to fit any number between 0 and 999,999,999,999,999)</comment>
  </data>
  <data name="UnitAbbreviation_FluidOunceUS" xml:space="preserve">
    <value>fl oz (USA)</value>
    <comment>An abbreviation for a measurement unit of volume</comment>
  </data>
  <data name="UnitName_GallonUK" xml:space="preserve">
    <value>gallonit (Briti)</value>
    <comment>A measurement unit for volume. (Please choose the most appropriate plural form to fit any number between 0 and 999,999,999,999,999)</comment>
  </data>
  <data name="UnitAbbreviation_GallonUK" xml:space="preserve">
    <value>gal (Briti)</value>
    <comment>An abbreviation for a measurement unit of volume</comment>
  </data>
  <data name="UnitName_GallonUS" xml:space="preserve">
    <value>gallonit (USA)</value>
    <comment>A measurement unit for volume. (Please choose the most appropriate plural form to fit any number between 0 and 999,999,999,999,999)</comment>
  </data>
  <data name="UnitAbbreviation_GallonUS" xml:space="preserve">
    <value>gal (USA)</value>
    <comment>An abbreviation for a measurement unit of volume</comment>
  </data>
  <data name="UnitName_Liter" xml:space="preserve">
    <value>liitrit</value>
    <comment>A measurement unit for volume. (Please choose the most appropriate plural form to fit any number between 0 and 999,999,999,999,999)</comment>
  </data>
  <data name="UnitAbbreviation_Liter" xml:space="preserve">
    <value>L</value>
    <comment>An abbreviation for a measurement unit of volume</comment>
  </data>
  <data name="UnitName_Milliliter" xml:space="preserve">
    <value>milliliitrit</value>
    <comment>A measurement unit for volume. (Please choose the most appropriate plural form to fit any number between 0 and 999,999,999,999,999)</comment>
  </data>
  <data name="UnitAbbreviation_Milliliter" xml:space="preserve">
    <value>mL</value>
    <comment>An abbreviation for a measurement unit of volume</comment>
  </data>
  <data name="UnitName_PintUK" xml:space="preserve">
    <value>pinti (Briti)</value>
    <comment>A measurement unit for volume. (Please choose the most appropriate plural form to fit any number between 0 and 999,999,999,999,999)</comment>
  </data>
  <data name="UnitAbbreviation_PintUK" xml:space="preserve">
    <value>pt (Briti)</value>
    <comment>An abbreviation for a measurement unit of volume</comment>
  </data>
  <data name="UnitName_PintUS" xml:space="preserve">
    <value>pinti (USA)</value>
    <comment>A measurement unit for volume. (Please choose the most appropriate plural form to fit any number between 0 and 999,999,999,999,999)</comment>
  </data>
  <data name="UnitAbbreviation_PintUS" xml:space="preserve">
    <value>pt (USA)</value>
    <comment>An abbreviation for a measurement unit of volume</comment>
  </data>
  <data name="UnitName_TablespoonUS" xml:space="preserve">
    <value>supilusikat (USA)</value>
    <comment>A measurement unit for volume. (Please choose the most appropriate plural form to fit any number between 0 and 999,999,999,999,999)</comment>
  </data>
  <data name="UnitAbbreviation_TablespoonUS" xml:space="preserve">
    <value>sl (USA)</value>
    <comment>An abbreviation for a measurement unit of volume</comment>
  </data>
  <data name="UnitName_TeaspoonUS" xml:space="preserve">
    <value>teelusikat (USA)</value>
    <comment>A measurement unit for volume. (Please choose the most appropriate plural form to fit any number between 0 and 999,999,999,999,999)</comment>
  </data>
  <data name="UnitAbbreviation_TeaspoonUS" xml:space="preserve">
    <value>tl (USA)</value>
    <comment>An abbreviation for a measurement unit of volume</comment>
  </data>
  <data name="UnitName_TablespoonUK" xml:space="preserve">
    <value>supilusikat (Briti)</value>
    <comment>A measurement unit for volume. (Please choose the most appropriate plural form to fit any number between 0 and 999,999,999,999,999)</comment>
  </data>
  <data name="UnitAbbreviation_TablespoonUK" xml:space="preserve">
    <value>sl (Briti)</value>
    <comment>An abbreviation for a measurement unit of volume</comment>
  </data>
  <data name="UnitName_TeaspoonUK" xml:space="preserve">
    <value>teelusikat (Briti)</value>
    <comment>A measurement unit for volume. (Please choose the most appropriate plural form to fit any number between 0 and 999,999,999,999,999)</comment>
  </data>
  <data name="UnitAbbreviation_TeaspoonUK" xml:space="preserve">
    <value>tl (Briti)</value>
    <comment>An abbreviation for a measurement unit of volume</comment>
  </data>
  <data name="UnitName_QuartUK" xml:space="preserve">
    <value>kvarti (Briti)</value>
    <comment>A measurement unit for volume. (Please choose the most appropriate plural form to fit any number between 0 and 999,999,999,999,999)</comment>
  </data>
  <data name="UnitAbbreviation_QuartUK" xml:space="preserve">
    <value>qt (Briti)</value>
    <comment>An abbreviation for a measurement unit of volume</comment>
  </data>
  <data name="UnitName_QuartUS" xml:space="preserve">
    <value>kvarti (USA)</value>
    <comment>A measurement unit for volume. (Please choose the most appropriate plural form to fit any number between 0 and 999,999,999,999,999)</comment>
  </data>
  <data name="UnitAbbreviation_QuartUS" xml:space="preserve">
    <value>qt (USA)</value>
    <comment>An abbreviation for a measurement unit of volume</comment>
  </data>
  <data name="UnitName_CupUS" xml:space="preserve">
    <value>tassi (USA)</value>
    <comment>A measurement unit for volume. (Please choose the most appropriate plural form to fit any number between 0 and 999,999,999,999,999)</comment>
  </data>
  <data name="UnitAbbreviation_CupUS" xml:space="preserve">
    <value>tass (USA)</value>
    <comment>An abbreviation for a measurement unit of volume</comment>
  </data>
  <data name="UnitAbbreviation_Angstrom" xml:space="preserve">
    <value>Å</value>
    <comment>An abbreviation for a measurement unit of length</comment>
  </data>
  <data name="UnitAbbreviation_Acre" xml:space="preserve">
    <value>ac</value>
    <comment>An abbreviation for a measurement unit of volume</comment>
  </data>
  <data name="UnitAbbreviation_Bit" xml:space="preserve">
    <value>bit</value>
    <comment>An abbreviation for a measurement unit of data</comment>
  </data>
  <data name="UnitAbbreviation_BritishThermalUnit" xml:space="preserve">
    <value>BTU</value>
    <comment>An abbreviation for a measurement unit of volume</comment>
  </data>
  <data name="UnitAbbreviation_BTUPerMinute" xml:space="preserve">
    <value>BTU/min</value>
    <comment>An abbreviation for a measurement unit of power</comment>
  </data>
  <data name="UnitAbbreviation_Byte" xml:space="preserve">
    <value>B</value>
    <comment>An abbreviation for a measurement unit of data</comment>
  </data>
  <data name="UnitAbbreviation_Calorie" xml:space="preserve">
    <value>cal</value>
    <comment>An abbreviation for a measurement unit of energy</comment>
  </data>
  <data name="UnitAbbreviation_Centimeter" xml:space="preserve">
    <value>cm</value>
    <comment>An abbreviation for a measurement unit of length</comment>
  </data>
  <data name="UnitAbbreviation_CentimetersPerSecond" xml:space="preserve">
    <value>cm/s</value>
    <comment>An abbreviation for a measurement unit of speed</comment>
  </data>
  <data name="UnitAbbreviation_CubicCentimeter" xml:space="preserve">
    <value>cm³</value>
    <comment>An abbreviation for a measurement unit of volume</comment>
  </data>
  <data name="UnitAbbreviation_CubicFoot" xml:space="preserve">
    <value>ft³</value>
    <comment>An abbreviation for a measurement unit of volume</comment>
  </data>
  <data name="UnitAbbreviation_CubicInch" xml:space="preserve">
    <value>in³</value>
    <comment>An abbreviation for a measurement unit of volume</comment>
  </data>
  <data name="UnitAbbreviation_CubicMeter" xml:space="preserve">
    <value>m³</value>
    <comment>An abbreviation for a measurement unit of volume</comment>
  </data>
  <data name="UnitAbbreviation_CubicYard" xml:space="preserve">
    <value>yd³</value>
    <comment>An abbreviation for a measurement unit of volume</comment>
  </data>
  <data name="UnitAbbreviation_Day" xml:space="preserve">
    <value>p</value>
    <comment>An abbreviation for a measurement unit of time</comment>
  </data>
  <data name="UnitAbbreviation_DegreesCelsius" xml:space="preserve">
    <value>°C</value>
    <comment>An abbreviation for "degrees Celsius"</comment>
  </data>
  <data name="UnitAbbreviation_DegreesFahrenheit" xml:space="preserve">
    <value>°F</value>
    <comment>An abbreviation for a "degrees Fahrenheit"</comment>
  </data>
  <data name="UnitAbbreviation_Electron-Volt" xml:space="preserve">
    <value>eV</value>
    <comment>An abbreviation for a measurement unit of energy</comment>
  </data>
  <data name="UnitAbbreviation_Foot" xml:space="preserve">
    <value>ft</value>
    <comment>An abbreviation for a measurement unit of length</comment>
  </data>
  <data name="UnitAbbreviation_FeetPerSecond" xml:space="preserve">
    <value>ft/s</value>
    <comment>An abbreviation for a measurement unit of speed</comment>
  </data>
  <data name="UnitAbbreviation_Foot-Pound" xml:space="preserve">
    <value>ft•lb</value>
    <comment>An abbreviation for a measurement unit of energy</comment>
  </data>
  <data name="UnitAbbreviation_Gigabit" xml:space="preserve">
    <value>Gbit</value>
    <comment>An abbreviation for a measurement unit of data</comment>
  </data>
  <data name="UnitAbbreviation_Gigabyte" xml:space="preserve">
    <value>GB</value>
    <comment>An abbreviation for a measurement unit of data</comment>
  </data>
  <data name="UnitAbbreviation_Hectare" xml:space="preserve">
    <value>ha</value>
    <comment>An abbreviation for a measurement unit of area</comment>
  </data>
  <data name="UnitAbbreviation_Horsepower" xml:space="preserve">
    <value>hp (USA)</value>
    <comment>An abbreviation for a measurement unit of power</comment>
  </data>
  <data name="UnitAbbreviation_Hour" xml:space="preserve">
    <value>h</value>
    <comment>An abbreviation for a measurement unit of time</comment>
  </data>
  <data name="UnitAbbreviation_Inch" xml:space="preserve">
    <value>toll</value>
    <comment>An abbreviation for a measurement unit of length</comment>
  </data>
  <data name="UnitAbbreviation_Joule" xml:space="preserve">
    <value>J</value>
    <comment>An abbreviation for a measurement unit of energy</comment>
  </data>
  <data name="UnitAbbreviation_Kilowatthour" xml:space="preserve">
    <value>kWh</value>
    <comment>An abbreviation for a measurement unit of electricity consumption</comment>
  </data>
  <data name="UnitAbbreviation_Kelvin" xml:space="preserve">
    <value>K</value>
    <comment>An abbreviation for the temperature system "Kelvin" (eg. 0 degrees Celsius = 273 Kelvin)</comment>
  </data>
  <data name="UnitAbbreviation_Kilobit" xml:space="preserve">
    <value>kbit</value>
    <comment>An abbreviation for a measurement unit of data</comment>
  </data>
  <data name="UnitAbbreviation_Kilobyte" xml:space="preserve">
    <value>kB</value>
    <comment>An abbreviation for a measurement unit of data</comment>
  </data>
  <data name="UnitAbbreviation_Kilocalorie" xml:space="preserve">
    <value>kcal</value>
    <comment>An abbreviation for a measurement unit of energy</comment>
  </data>
  <data name="UnitAbbreviation_Kilojoule" xml:space="preserve">
    <value>kJ</value>
    <comment>An abbreviation for a measurement unit of energy</comment>
  </data>
  <data name="UnitAbbreviation_Kilometer" xml:space="preserve">
    <value>km</value>
    <comment>An abbreviation for a measurement unit of length</comment>
  </data>
  <data name="UnitAbbreviation_KilometersPerHour" xml:space="preserve">
    <value>km/h</value>
    <comment>An abbreviation for a measurement unit of speed</comment>
  </data>
  <data name="UnitAbbreviation_Kilowatt" xml:space="preserve">
    <value>kW</value>
    <comment>An abbreviation for a measurement unit of power</comment>
  </data>
  <data name="UnitAbbreviation_Knot" xml:space="preserve">
    <value>kn</value>
    <comment>An abbreviation for a measurement unit of speed</comment>
  </data>
  <data name="UnitAbbreviation_Mach" xml:space="preserve">
    <value>M</value>
    <comment>An abbreviation for a measurement of speed (Mach is the speed of sound, Mach 2 is 2 times the speed of sound)</comment>
  </data>
  <data name="UnitAbbreviation_Megabit" xml:space="preserve">
    <value>Mbit</value>
    <comment>An abbreviation for a measurement unit of data</comment>
  </data>
  <data name="UnitAbbreviation_Megabyte" xml:space="preserve">
    <value>MB</value>
    <comment>An abbreviation for a measurement unit of data</comment>
  </data>
  <data name="UnitAbbreviation_Meter" xml:space="preserve">
    <value>m</value>
    <comment>An abbreviation for a measurement unit of length</comment>
  </data>
  <data name="UnitAbbreviation_MetersPerSecond" xml:space="preserve">
    <value>m/s</value>
    <comment>An abbreviation for a measurement unit of speed</comment>
  </data>
  <data name="UnitAbbreviation_Micron" xml:space="preserve">
    <value>µm</value>
    <comment>An abbreviation for a measurement unit of length</comment>
  </data>
  <data name="UnitAbbreviation_Microsecond" xml:space="preserve">
    <value>µs</value>
    <comment>An abbreviation for a measurement unit of time</comment>
  </data>
  <data name="UnitAbbreviation_Mile" xml:space="preserve">
    <value>mi</value>
    <comment>An abbreviation for a measurement unit of length</comment>
  </data>
  <data name="UnitAbbreviation_MilesPerHour" xml:space="preserve">
    <value>miili/h</value>
    <comment>An abbreviation for a measurement unit of speed</comment>
  </data>
  <data name="UnitAbbreviation_Millimeter" xml:space="preserve">
    <value>mm</value>
    <comment>An abbreviation for a measurement unit of length</comment>
  </data>
  <data name="UnitAbbreviation_Millisecond" xml:space="preserve">
    <value>ms</value>
    <comment>An abbreviation for a measurement unit of time</comment>
  </data>
  <data name="UnitAbbreviation_Minute" xml:space="preserve">
    <value>min</value>
    <comment>An abbreviation for a measurement unit of time</comment>
  </data>
  <data name="UnitAbbreviation_Nanometer" xml:space="preserve">
    <value>nm</value>
    <comment>An abbreviation for a measurement unit of length</comment>
  </data>
  <data name="UnitAbbreviation_NauticalMile" xml:space="preserve">
    <value>nmi</value>
    <comment>An abbreviation for a measurement unit of length</comment>
  </data>
  <data name="UnitAbbreviation_Petabit" xml:space="preserve">
    <value>Pbit</value>
    <comment>An abbreviation for a measurement unit of data</comment>
  </data>
  <data name="UnitAbbreviation_Petabyte" xml:space="preserve">
    <value>PB</value>
    <comment>An abbreviation for a measurement unit of data</comment>
  </data>
  <data name="UnitAbbreviation_Foot-PoundPerMinute" xml:space="preserve">
    <value>ft•lb/min</value>
    <comment>An abbreviation for a measurement unit of power</comment>
  </data>
  <data name="UnitAbbreviation_Second" xml:space="preserve">
    <value>s</value>
    <comment>An abbreviation for a measurement unit of time</comment>
  </data>
  <data name="UnitAbbreviation_SquareCentimeter" xml:space="preserve">
    <value>cm²</value>
    <comment>An abbreviation for a measurement unit of area</comment>
  </data>
  <data name="UnitAbbreviation_SquareFoot" xml:space="preserve">
    <value>ft²</value>
    <comment>An abbreviation for a measurement unit of area</comment>
  </data>
  <data name="UnitAbbreviation_SquareInch" xml:space="preserve">
    <value>in²</value>
    <comment>An abbreviation for a measurement unit of area</comment>
  </data>
  <data name="UnitAbbreviation_SquareKilometer" xml:space="preserve">
    <value>km²</value>
    <comment>An abbreviation for a measurement unit of area</comment>
  </data>
  <data name="UnitAbbreviation_SquareMeter" xml:space="preserve">
    <value>m²</value>
    <comment>An abbreviation for a measurement unit of area</comment>
  </data>
  <data name="UnitAbbreviation_SquareMile" xml:space="preserve">
    <value>mi²</value>
    <comment>An abbreviation for a measurement unit of area</comment>
  </data>
  <data name="UnitAbbreviation_SquareMillimeter" xml:space="preserve">
    <value>mm²</value>
    <comment>An abbreviation for a measurement unit of area</comment>
  </data>
  <data name="UnitAbbreviation_SquareYard" xml:space="preserve">
    <value>yd²</value>
    <comment>An abbreviation for a measurement unit of area</comment>
  </data>
  <data name="UnitAbbreviation_Terabit" xml:space="preserve">
    <value>Tbit</value>
    <comment>An abbreviation for a measurement unit of data</comment>
  </data>
  <data name="UnitAbbreviation_Terabyte" xml:space="preserve">
    <value>TB</value>
    <comment>An abbreviation for a measurement unit of data</comment>
  </data>
  <data name="UnitAbbreviation_Watt" xml:space="preserve">
    <value>W</value>
    <comment>An abbreviation for a measurement unit of power</comment>
  </data>
  <data name="UnitAbbreviation_Week" xml:space="preserve">
    <value>näd</value>
    <comment>An abbreviation for a measurement unit of time</comment>
  </data>
  <data name="UnitAbbreviation_Yard" xml:space="preserve">
    <value>yd</value>
    <comment>An abbreviation for a measurement unit of length</comment>
  </data>
  <data name="UnitAbbreviation_Year" xml:space="preserve">
    <value>a</value>
    <comment>An abbreviation for a measurement unit of time</comment>
  </data>
  <data name="UnitAbbreviation_Gibibits" xml:space="preserve">
    <value>Gi</value>
    <comment>An abbreviation for a measurement unit of data</comment>
  </data>
  <data name="UnitAbbreviation_Gibibytes" xml:space="preserve">
    <value>GiB</value>
    <comment>An abbreviation for a measurement unit of data</comment>
  </data>
  <data name="UnitAbbreviation_Kibibits" xml:space="preserve">
    <value>Ki</value>
    <comment>An abbreviation for a measurement unit of data</comment>
  </data>
  <data name="UnitAbbreviation_Kibibytes" xml:space="preserve">
    <value>KiB</value>
    <comment>An abbreviation for a measurement unit of data</comment>
  </data>
  <data name="UnitAbbreviation_Mebibits" xml:space="preserve">
    <value>Mi</value>
    <comment>An abbreviation for a measurement unit of data</comment>
  </data>
  <data name="UnitAbbreviation_Mebibytes" xml:space="preserve">
    <value>MiB</value>
    <comment>An abbreviation for a measurement unit of data</comment>
  </data>
  <data name="UnitAbbreviation_Nibble" xml:space="preserve">
    <value>nybl</value>
    <comment>An abbreviation for a measurement unit of data</comment>
  </data>
  <data name="UnitAbbreviation_Pebibits" xml:space="preserve">
    <value>Pii</value>
    <comment>An abbreviation for a measurement unit of data</comment>
  </data>
  <data name="UnitAbbreviation_Pebibytes" xml:space="preserve">
    <value>PiB</value>
    <comment>An abbreviation for a measurement unit of data</comment>
  </data>
  <data name="UnitAbbreviation_Tebibits" xml:space="preserve">
    <value>Ti</value>
    <comment>An abbreviation for a measurement unit of data</comment>
  </data>
  <data name="UnitAbbreviation_Tebibytes" xml:space="preserve">
    <value>TiB</value>
    <comment>An abbreviation for a measurement unit of data</comment>
  </data>
  <data name="UnitAbbreviation_Exabits" xml:space="preserve">
    <value>E</value>
    <comment>An abbreviation for a measurement unit of data</comment>
  </data>
  <data name="UnitAbbreviation_Exabytes" xml:space="preserve">
    <value>EB</value>
    <comment>An abbreviation for a measurement unit of data</comment>
  </data>
  <data name="UnitAbbreviation_Exbibits" xml:space="preserve">
    <value>Ei</value>
    <comment>An abbreviation for a measurement unit of data</comment>
  </data>
  <data name="UnitAbbreviation_Exbibytes" xml:space="preserve">
    <value>EiB</value>
    <comment>An abbreviation for a measurement unit of data</comment>
  </data>
  <data name="UnitAbbreviation_Zetabits" xml:space="preserve">
    <value>Z</value>
    <comment>An abbreviation for a measurement unit of data</comment>
  </data>
  <data name="UnitAbbreviation_Zetabytes" xml:space="preserve">
    <value>ZB</value>
    <comment>An abbreviation for a measurement unit of data</comment>
  </data>
  <data name="UnitAbbreviation_Zebibits" xml:space="preserve">
    <value>Zi</value>
    <comment>An abbreviation for a measurement unit of data</comment>
  </data>
  <data name="UnitAbbreviation_Zebibytes" xml:space="preserve">
    <value>ZiB</value>
    <comment>An abbreviation for a measurement unit of data</comment>
  </data>
  <data name="UnitAbbreviation_Yottabit" xml:space="preserve">
    <value>Y</value>
    <comment>An abbreviation for a measurement unit of data</comment>
  </data>
  <data name="UnitAbbreviation_Yottabyte" xml:space="preserve">
    <value>YB</value>
    <comment>An abbreviation for a measurement unit of data</comment>
  </data>
  <data name="UnitAbbreviation_Yobibits" xml:space="preserve">
    <value>Yi</value>
    <comment>An abbreviation for a measurement unit of data</comment>
  </data>
  <data name="UnitAbbreviation_Yobibytes" xml:space="preserve">
    <value>YiB</value>
    <comment>An abbreviation for a measurement unit of data</comment>
  </data>
  <data name="UnitName_Acre" xml:space="preserve">
    <value>aakrit</value>
    <comment>A measurement unit for area. (Please choose the most appropriate plural form to fit any number between 0 and 999,999,999,999,999)</comment>
  </data>
  <data name="UnitName_Bit" xml:space="preserve">
    <value>bitti</value>
    <comment>A measurement unit for data. (Please choose the most appropriate plural form to fit any number between 0 and 999,999,999,999,999)</comment>
  </data>
  <data name="UnitName_BritishThermalUnit" xml:space="preserve">
    <value>Briti soojusühikut</value>
    <comment>A measurement unit for energy. (Please choose the most appropriate plural form to fit any number between 0 and 999,999,999,999,999)</comment>
  </data>
  <data name="UnitName_BTUPerMinute" xml:space="preserve">
    <value>Briti soojusühikut minutis</value>
    <comment>A measurement unit for power: British Thermal Units per minute. (Please choose the most appropriate plural form to fit any number between 0 and 999,999,999,999,999)</comment>
  </data>
  <data name="UnitName_Byte" xml:space="preserve">
    <value>baiti</value>
    <comment>A measurement unit for data. (Please choose the most appropriate plural form to fit any number between 0 and 999,999,999,999,999)</comment>
  </data>
  <data name="UnitName_Calorie" xml:space="preserve">
    <value>kalorit</value>
    <comment>A measurement unit for energy. Please note that this is the "small calorie" used in science for measuring heat energy, not the "large calorie" commonly used for measuring food energy. If there is a simple term to distinguish this one from the large "Food calorie", please use that. (Please choose the most appropriate plural form to fit any number between 0 and 999,999,999,999,999)</comment>
  </data>
  <data name="UnitName_Centimeter" xml:space="preserve">
    <value>sentimeetrit</value>
    <comment>A measurement unit for length. (Please choose the most appropriate plural form to fit any number between 0 and 999,999,999,999,999)</comment>
  </data>
  <data name="UnitName_CentimetersPerSecond" xml:space="preserve">
    <value>sentimeetrit sekundis</value>
    <comment>A measurement unit for speed. (Please choose the most appropriate plural form to fit any number between 0 and 999,999,999,999,999)</comment>
  </data>
  <data name="UnitName_CubicCentimeter" xml:space="preserve">
    <value>kuupsentimeetrit</value>
    <comment>A measurement unit for volume. (Please choose the most appropriate plural form to fit any number between 0 and 999,999,999,999,999)</comment>
  </data>
  <data name="UnitName_CubicFoot" xml:space="preserve">
    <value>kuupjalga</value>
    <comment>A measurement unit for volume. (Please choose the most appropriate plural form to fit any number between 0 and 999,999,999,999,999)</comment>
  </data>
  <data name="UnitName_CubicInch" xml:space="preserve">
    <value>kuuptolli</value>
    <comment>A measurement unit for volume. (Please choose the most appropriate plural form to fit any number between 0 and 999,999,999,999,999)</comment>
  </data>
  <data name="UnitName_CubicMeter" xml:space="preserve">
    <value>kuupmeetrit</value>
    <comment>A measurement unit for volume. (Please choose the most appropriate plural form to fit any number between 0 and 999,999,999,999,999)</comment>
  </data>
  <data name="UnitName_CubicYard" xml:space="preserve">
    <value>kuupjardi</value>
    <comment>A measurement unit for volume. (Please choose the most appropriate plural form to fit any number between 0 and 999,999,999,999,999)</comment>
  </data>
  <data name="UnitName_Day" xml:space="preserve">
    <value>päeva</value>
    <comment>A measurement unit for time. (Please choose the most appropriate plural form to fit any number between 0 and 999,999,999,999,999)</comment>
  </data>
  <data name="UnitName_DegreesCelsius" xml:space="preserve">
    <value>Celsiuse kraadi</value>
    <comment>An option in the unit converter to select degrees Celsius</comment>
  </data>
  <data name="UnitName_DegreesFahrenheit" xml:space="preserve">
    <value>Fahrenheiti kraadi</value>
    <comment>An option in the unit converter to select degrees Fahrenheit</comment>
  </data>
  <data name="UnitName_Electron-Volt" xml:space="preserve">
    <value>elektronvolti</value>
    <comment>A measurement unit for energy. (Please choose the most appropriate plural form to fit any number between 0 and 999,999,999,999,999)</comment>
  </data>
  <data name="UnitName_Foot" xml:space="preserve">
    <value>jalga</value>
    <comment>A measurement unit for length. (Please choose the most appropriate plural form to fit any number between 0 and 999,999,999,999,999)</comment>
  </data>
  <data name="UnitName_FeetPerSecond" xml:space="preserve">
    <value>jalga sekundis</value>
    <comment>A measurement unit for speed. (Please choose the most appropriate plural form to fit any number between 0 and 999,999,999,999,999)</comment>
  </data>
  <data name="UnitName_Foot-Pound" xml:space="preserve">
    <value>jalgnaela</value>
    <comment>A measurement unit for energy. (Please choose the most appropriate plural form to fit any number between 0 and 999,999,999,999,999)</comment>
  </data>
  <data name="UnitName_Foot-PoundPerMinute" xml:space="preserve">
    <value>jalgnaela minutis</value>
    <comment>A measurement unit for power. (Please choose the most appropriate plural form to fit any number between 0 and 999,999,999,999,999)</comment>
  </data>
  <data name="UnitName_Gigabit" xml:space="preserve">
    <value>gigabitti</value>
    <comment>A measurement unit for data. (Please choose the most appropriate plural form to fit any number between 0 and 999,999,999,999,999)</comment>
  </data>
  <data name="UnitName_Gigabyte" xml:space="preserve">
    <value>gigabaiti</value>
    <comment>A measurement unit for data. (Please choose the most appropriate plural form to fit any number between 0 and 999,999,999,999,999)</comment>
  </data>
  <data name="UnitName_Hectare" xml:space="preserve">
    <value>hektarit</value>
    <comment>A measurement unit for area. (Please choose the most appropriate plural form to fit any number between 0 and 999,999,999,999,999)</comment>
  </data>
  <data name="UnitName_Horsepower" xml:space="preserve">
    <value>hobujõudu (USA)</value>
    <comment>A measurement unit for power</comment>
  </data>
  <data name="UnitName_Hour" xml:space="preserve">
    <value>tundi</value>
    <comment>A measurement unit for time. (Please choose the most appropriate plural form to fit any number between 0 and 999,999,999,999,999)</comment>
  </data>
  <data name="UnitName_Inch" xml:space="preserve">
    <value>tolli</value>
    <comment>A measurement unit for length. (Please choose the most appropriate plural form to fit any number between 0 and 999,999,999,999,999)</comment>
  </data>
  <data name="UnitName_Joule" xml:space="preserve">
    <value>džauli</value>
    <comment>A measurement unit for energy. (Please choose the most appropriate plural form to fit any number between 0 and 999,999,999,999,999)</comment>
  </data>
  <data name="UnitName_Kilowatthour" xml:space="preserve">
    <value>Kilovatt-tunnid</value>
    <comment>A measurement unit for electricity consumption. (Please choose the most appropriate plural form to fit any number between 0 and 999,999,999,999,999)</comment>
  </data>
  <data name="UnitName_Kelvin" xml:space="preserve">
    <value>kelvinit</value>
    <comment>An option in the unit converter to select the temperature system "Kelvin" (eg. 0 degrees Celsius = 273 Kelvin). At least in English, Kelvin does not use "degrees". A measurement is just stated as "273 Kelvin".</comment>
  </data>
  <data name="UnitName_Kilobit" xml:space="preserve">
    <value>kilobitti</value>
    <comment>A measurement unit for data. (Please choose the most appropriate plural form to fit any number between 0 and 999,999,999,999,999)</comment>
  </data>
  <data name="UnitName_Kilobyte" xml:space="preserve">
    <value>kilobaiti</value>
    <comment>A measurement unit for data. (Please choose the most appropriate plural form to fit any number between 0 and 999,999,999,999,999)</comment>
  </data>
  <data name="UnitName_Kilocalorie" xml:space="preserve">
    <value>kilokalorit</value>
    <comment>A measurement unit for energy. The scientific name is kilocalorie, but this is what is commonly referred to as a "calorie" or "large calorie" when talking about food. Please use the everyday-use word for food energy calories if there is one. (Please choose the most appropriate plural form to fit any number between 0 and 999,999,999,999,999)</comment>
  </data>
  <data name="UnitName_Kilojoule" xml:space="preserve">
    <value>kilodžauli</value>
    <comment>A measurement unit for energy. (Please choose the most appropriate plural form to fit any number between 0 and 999,999,999,999,999)</comment>
  </data>
  <data name="UnitName_Kilometer" xml:space="preserve">
    <value>kilomeetrit</value>
    <comment>A measurement unit for length. (Please choose the most appropriate plural form to fit any number between 0 and 999,999,999,999,999)</comment>
  </data>
  <data name="UnitName_KilometersPerHour" xml:space="preserve">
    <value>kilomeetrit tunnis</value>
    <comment>A measurement unit for speed. (Please choose the most appropriate plural form to fit any number between 0 and 999,999,999,999,999)</comment>
  </data>
  <data name="UnitName_Kilowatt" xml:space="preserve">
    <value>kilovatti</value>
    <comment>A measurement unit for power. (Please choose the most appropriate plural form to fit any number between 0 and 999,999,999,999,999)</comment>
  </data>
  <data name="UnitName_Knot" xml:space="preserve">
    <value>sõlme</value>
    <comment>A nautical/aerial measurement unit for speed. (Please choose the most appropriate plural form to fit any number between 0 and 999,999,999,999,999)</comment>
  </data>
  <data name="UnitName_Mach" xml:space="preserve">
    <value>Machi arvu</value>
    <comment>A measurement of speed (Mach is the speed of sound, Mach 2 is 2 times the speed of sound)</comment>
  </data>
  <data name="UnitName_Megabit" xml:space="preserve">
    <value>megabitti</value>
    <comment>A measurement unit for data. (Please choose the most appropriate plural form to fit any number between 0 and 999,999,999,999,999)</comment>
  </data>
  <data name="UnitName_Megabyte" xml:space="preserve">
    <value>megabaiti</value>
    <comment>A measurement unit for data. (Please choose the most appropriate plural form to fit any number between 0 and 999,999,999,999,999)</comment>
  </data>
  <data name="UnitName_Meter" xml:space="preserve">
    <value>meetrit</value>
    <comment>A measurement unit for length. (Please choose the most appropriate plural form to fit any number between 0 and 999,999,999,999,999)</comment>
  </data>
  <data name="UnitName_MetersPerSecond" xml:space="preserve">
    <value>meetrit sekundis</value>
    <comment>A measurement unit for speed. (Please choose the most appropriate plural form to fit any number between 0 and 999,999,999,999,999)</comment>
  </data>
  <data name="UnitName_Micron" xml:space="preserve">
    <value>mikronit</value>
    <comment>A measurement unit for length. (Please choose the most appropriate plural form to fit any number between 0 and 999,999,999,999,999)</comment>
  </data>
  <data name="UnitName_Microsecond" xml:space="preserve">
    <value>mikrosekundit</value>
    <comment>A measurement unit for time. (Please choose the most appropriate plural form to fit any number between 0 and 999,999,999,999,999)</comment>
  </data>
  <data name="UnitName_Mile" xml:space="preserve">
    <value>miili</value>
    <comment>A measurement unit for length. (Please choose the most appropriate plural form to fit any number between 0 and 999,999,999,999,999)</comment>
  </data>
  <data name="UnitName_MilesPerHour" xml:space="preserve">
    <value>miili tunnis</value>
    <comment>A measurement unit for speed. (Please choose the most appropriate plural form to fit any number between 0 and 999,999,999,999,999)</comment>
  </data>
  <data name="UnitName_Millimeter" xml:space="preserve">
    <value>millimeetrit</value>
    <comment>A measurement unit for length. (Please choose the most appropriate plural form to fit any number between 0 and 999,999,999,999,999)</comment>
  </data>
  <data name="UnitName_Millisecond" xml:space="preserve">
    <value>millisekundit</value>
    <comment>A measurement unit for time. (Please choose the most appropriate plural form to fit any number between 0 and 999,999,999,999,999)</comment>
  </data>
  <data name="UnitName_Minute" xml:space="preserve">
    <value>minutit</value>
    <comment>A measurement unit for time. (Please choose the most appropriate plural form to fit any number between 0 and 999,999,999,999,999)</comment>
  </data>
  <data name="UnitName_Nibble" xml:space="preserve">
    <value>Näks(i)</value>
    <comment>A measurement unit for data. (Please choose the most appropriate plural form to fit any number between 0 and 999,999,999,999,999)</comment>
  </data>
  <data name="UnitName_Nanometer" xml:space="preserve">
    <value>nanomeetrit</value>
    <comment>A measurement unit for length. (Please choose the most appropriate plural form to fit any number between 0 and 999,999,999,999,999)</comment>
  </data>
  <data name="UnitName_Angstrom" xml:space="preserve">
    <value>Ongströmi</value>
    <comment>A measurement unit for length. (Please choose the most appropriate plural form to fit any number between 0 and 999,999,999,999,999)</comment>
  </data>
  <data name="UnitName_NauticalMile" xml:space="preserve">
    <value>Meremiilid</value>
    <comment>A measurement unit for length. (Please choose the most appropriate plural form to fit any number between 0 and 999,999,999,999,999)</comment>
  </data>
  <data name="UnitName_Petabit" xml:space="preserve">
    <value>petabitti</value>
    <comment>A measurement unit for data. (Please choose the most appropriate plural form to fit any number between 0 and 999,999,999,999,999)</comment>
  </data>
  <data name="UnitName_Petabyte" xml:space="preserve">
    <value>petabaiti</value>
    <comment>A measurement unit for data. (Please choose the most appropriate plural form to fit any number between 0 and 999,999,999,999,999)</comment>
  </data>
  <data name="UnitName_Second" xml:space="preserve">
    <value>sekundit</value>
    <comment>A measurement unit for time. (Please choose the most appropriate plural form to fit any number between 0 and 999,999,999,999,999)</comment>
  </data>
  <data name="UnitName_SquareCentimeter" xml:space="preserve">
    <value>ruutsentimeetrit</value>
    <comment>A measurement unit for area. (Please choose the most appropriate plural form to fit any number between 0 and 999,999,999,999,999)</comment>
  </data>
  <data name="UnitName_SquareFoot" xml:space="preserve">
    <value>ruutjalga</value>
    <comment>A measurement unit for area. (Please choose the most appropriate plural form to fit any number between 0 and 999,999,999,999,999)</comment>
  </data>
  <data name="UnitName_SquareInch" xml:space="preserve">
    <value>ruuttolli</value>
    <comment>A measurement unit for area. (Please choose the most appropriate plural form to fit any number between 0 and 999,999,999,999,999)</comment>
  </data>
  <data name="UnitName_SquareKilometer" xml:space="preserve">
    <value>ruutkilomeetrit</value>
    <comment>A measurement unit for area. (Please choose the most appropriate plural form to fit any number between 0 and 999,999,999,999,999)</comment>
  </data>
  <data name="UnitName_SquareMeter" xml:space="preserve">
    <value>ruutmeetrit</value>
    <comment>A measurement unit for area. (Please choose the most appropriate plural form to fit any number between 0 and 999,999,999,999,999)</comment>
  </data>
  <data name="UnitName_SquareMile" xml:space="preserve">
    <value>ruutmiili</value>
    <comment>A measurement unit for area. (Please choose the most appropriate plural form to fit any number between 0 and 999,999,999,999,999)</comment>
  </data>
  <data name="UnitName_SquareMillimeter" xml:space="preserve">
    <value>ruutmillimeetrit</value>
    <comment>A measurement unit for area. (Please choose the most appropriate plural form to fit any number between 0 and 999,999,999,999,999)</comment>
  </data>
  <data name="UnitName_SquareYard" xml:space="preserve">
    <value>ruutjardi</value>
    <comment>A measurement unit for area. (Please choose the most appropriate plural form to fit any number between 0 and 999,999,999,999,999)</comment>
  </data>
  <data name="UnitName_Terabit" xml:space="preserve">
    <value>terabitti</value>
    <comment>A measurement unit for data. (Please choose the most appropriate plural form to fit any number between 0 and 999,999,999,999,999)</comment>
  </data>
  <data name="UnitName_Terabyte" xml:space="preserve">
    <value>terabaiti</value>
    <comment>A measurement unit for data. (Please choose the most appropriate plural form to fit any number between 0 and 999,999,999,999,999)</comment>
  </data>
  <data name="UnitName_Watt" xml:space="preserve">
    <value>vatti</value>
    <comment>A measurement unit for power. (Please choose the most appropriate plural form to fit any number between 0 and 999,999,999,999,999)</comment>
  </data>
  <data name="UnitName_Week" xml:space="preserve">
    <value>nädalat</value>
    <comment>A measurement unit for time. (Please choose the most appropriate plural form to fit any number between 0 and 999,999,999,999,999)</comment>
  </data>
  <data name="UnitName_Yard" xml:space="preserve">
    <value>jardi</value>
    <comment>A measurement unit for length. (Please choose the most appropriate plural form to fit any number between 0 and 999,999,999,999,999)</comment>
  </data>
  <data name="UnitName_Year" xml:space="preserve">
    <value>aastat</value>
    <comment>A measurement unit for time. (Please choose the most appropriate plural form to fit any number between 0 and 999,999,999,999,999)</comment>
  </data>
  <data name="UnitAbbreviation_Carat" xml:space="preserve">
    <value>ct</value>
    <comment>An abbreviation for a measurement unit of weight</comment>
  </data>
  <data name="UnitAbbreviation_Degree" xml:space="preserve">
    <value>krd</value>
    <comment>An abbreviation for a measurement unit of Angle</comment>
  </data>
  <data name="UnitAbbreviation_Radian" xml:space="preserve">
    <value>rad</value>
    <comment>An abbreviation for a measurement unit of Angle</comment>
  </data>
  <data name="UnitAbbreviation_Gradian" xml:space="preserve">
    <value>g</value>
    <comment>An abbreviation for a measurement unit of Angle</comment>
  </data>
  <data name="UnitAbbreviation_Atmosphere" xml:space="preserve">
    <value>atm</value>
    <comment>An abbreviation for a measurement unit of Pressure</comment>
  </data>
  <data name="UnitAbbreviation_Bar" xml:space="preserve">
    <value>ba</value>
    <comment>An abbreviation for a measurement unit of Pressure</comment>
  </data>
  <data name="UnitAbbreviation_KiloPascal" xml:space="preserve">
    <value>kPa</value>
    <comment>An abbreviation for a measurement unit of Pressure</comment>
  </data>
  <data name="UnitAbbreviation_MillimeterOfMercury" xml:space="preserve">
    <value>mmHg</value>
    <comment>An abbreviation for a measurement unit of Pressure</comment>
  </data>
  <data name="UnitAbbreviation_Pascal" xml:space="preserve">
    <value>Pa</value>
    <comment>An abbreviation for a measurement unit of Pressure</comment>
  </data>
  <data name="UnitAbbreviation_PSI" xml:space="preserve">
    <value>psi</value>
    <comment>An abbreviation for a measurement unit of Pressure</comment>
  </data>
  <data name="UnitAbbreviation_Centigram" xml:space="preserve">
    <value>cg</value>
    <comment>An abbreviation for a measurement unit of weight</comment>
  </data>
  <data name="UnitAbbreviation_Decagram" xml:space="preserve">
    <value>dag</value>
    <comment>An abbreviation for a measurement unit of weight</comment>
  </data>
  <data name="UnitAbbreviation_Decigram" xml:space="preserve">
    <value>dg</value>
    <comment>An abbreviation for a measurement unit of weight</comment>
  </data>
  <data name="UnitAbbreviation_Gram" xml:space="preserve">
    <value>g</value>
    <comment>An abbreviation for a measurement unit of weight</comment>
  </data>
  <data name="UnitAbbreviation_Hectogram" xml:space="preserve">
    <value>hg</value>
    <comment>An abbreviation for a measurement unit of weight</comment>
  </data>
  <data name="UnitAbbreviation_Kilogram" xml:space="preserve">
    <value>kg</value>
    <comment>An abbreviation for a measurement unit of weight</comment>
  </data>
  <data name="UnitAbbreviation_LongTon" xml:space="preserve">
    <value>t (Briti)</value>
    <comment>An abbreviation for a measurement unit of weight</comment>
  </data>
  <data name="UnitAbbreviation_Milligram" xml:space="preserve">
    <value>mg</value>
    <comment>An abbreviation for a measurement unit of weight</comment>
  </data>
  <data name="UnitAbbreviation_Ounce" xml:space="preserve">
    <value>oz</value>
    <comment>An abbreviation for a measurement unit of weight</comment>
  </data>
  <data name="UnitAbbreviation_Pound" xml:space="preserve">
    <value>lb</value>
    <comment>An abbreviation for a measurement unit of weight</comment>
  </data>
  <data name="UnitAbbreviation_ShortTon" xml:space="preserve">
    <value>t (USA)</value>
    <comment>An abbreviation for a measurement unit of weight</comment>
  </data>
  <data name="UnitAbbreviation_Stone" xml:space="preserve">
    <value>st</value>
    <comment>An abbreviation for a measurement unit of weight</comment>
  </data>
  <data name="UnitAbbreviation_Tonne" xml:space="preserve">
    <value>t</value>
    <comment>An abbreviation for a measurement unit of weight</comment>
  </data>
  <data name="UnitName_Carat" xml:space="preserve">
    <value>karaati</value>
    <comment>A measurement unit for weight. (Please choose the most appropriate plural form to fit any number between 0 and 999,999,999,999,999)</comment>
  </data>
  <data name="UnitName_Degree" xml:space="preserve">
    <value>kraadi</value>
    <comment>A measurement unit for Angle.</comment>
  </data>
  <data name="UnitName_Radian" xml:space="preserve">
    <value>radiaani</value>
    <comment>A measurement unit for Angle.</comment>
  </data>
  <data name="UnitName_Gradian" xml:space="preserve">
    <value>gooni</value>
    <comment>A measurement unit for Angle.</comment>
  </data>
  <data name="UnitName_Atmosphere" xml:space="preserve">
    <value>atmosfääri</value>
    <comment>A measurement unit for Pressure.</comment>
  </data>
  <data name="UnitName_Bar" xml:space="preserve">
    <value>baari</value>
    <comment>A measurement unit for Pressure.</comment>
  </data>
  <data name="UnitName_KiloPascal" xml:space="preserve">
    <value>kilopaskalit</value>
    <comment>A measurement unit for Pressure.</comment>
  </data>
  <data name="UnitName_MillimeterOfMercury" xml:space="preserve">
    <value>millimeetrit elavhõbedasammast</value>
    <comment>A measurement unit for Pressure.</comment>
  </data>
  <data name="UnitName_Pascal" xml:space="preserve">
    <value>Paskalit</value>
    <comment>A measurement unit for Pressure.</comment>
  </data>
  <data name="UnitName_PSI" xml:space="preserve">
    <value>naela ruuttolli kohta</value>
    <comment>A measurement unit for Pressure.</comment>
  </data>
  <data name="UnitName_Centigram" xml:space="preserve">
    <value>sentigrammi</value>
    <comment>A measurement unit for weight. (Please choose the most appropriate plural form to fit any number between 0 and 999,999,999,999,999)</comment>
  </data>
  <data name="UnitName_Decagram" xml:space="preserve">
    <value>dekagrammi</value>
    <comment>A measurement unit for weight. Note: Dekagram is spelled "decagram" everywhere except where US English is used. (EN-US dekagram, elsewhere decagram). (Please choose the most appropriate plural form to fit any number between 0 and 999,999,999,999,999)</comment>
  </data>
  <data name="UnitName_Decigram" xml:space="preserve">
    <value>detsigrammi</value>
    <comment>A measurement unit for weight. (Please choose the most appropriate plural form to fit any number between 0 and 999,999,999,999,999)</comment>
  </data>
  <data name="UnitName_Gram" xml:space="preserve">
    <value>grammi</value>
    <comment>A measurement unit for weight. (Please choose the most appropriate plural form to fit any number between 0 and 999,999,999,999,999)</comment>
  </data>
  <data name="UnitName_Hectogram" xml:space="preserve">
    <value>hektogrammi</value>
    <comment>A measurement unit for weight. (Please choose the most appropriate plural form to fit any number between 0 and 999,999,999,999,999)</comment>
  </data>
  <data name="UnitName_Kilogram" xml:space="preserve">
    <value>kilogrammi</value>
    <comment>A measurement unit for weight. (Please choose the most appropriate plural form to fit any number between 0 and 999,999,999,999,999)</comment>
  </data>
  <data name="UnitName_LongTon" xml:space="preserve">
    <value>tonni (Briti)</value>
    <comment>A measurement unit for weight. This is the UK version of ton. (Please choose the most appropriate plural form to fit any number between 0 and 999,999,999,999,999)</comment>
  </data>
  <data name="UnitName_Milligram" xml:space="preserve">
    <value>milligrammi</value>
    <comment>A measurement unit for weight. (Please choose the most appropriate plural form to fit any number between 0 and 999,999,999,999,999)</comment>
  </data>
  <data name="UnitName_Ounce" xml:space="preserve">
    <value>untsi</value>
    <comment>A measurement unit for weight. (Please choose the most appropriate plural form to fit any number between 0 and 999,999,999,999,999)</comment>
  </data>
  <data name="UnitName_Pound" xml:space="preserve">
    <value>naela</value>
    <comment>A measurement unit for weight. (Please choose the most appropriate plural form to fit any number between 0 and 999,999,999,999,999)</comment>
  </data>
  <data name="UnitName_ShortTon" xml:space="preserve">
    <value>tonni (USA)</value>
    <comment>A measurement unit for weight. This is the US version of ton. (Please choose the most appropriate plural form to fit any number between 0 and 999,999,999,999,999)</comment>
  </data>
  <data name="UnitName_Stone" xml:space="preserve">
    <value>kivi</value>
    <comment>A measurement unit for weight. Equal to 14 pounds. Note that this is the plural form of the word in English (eg. "This man weighs 11 stone."). (Please choose the most appropriate plural form to fit any number between 0 and 999,999,999,999,999)</comment>
  </data>
  <data name="UnitName_Tonne" xml:space="preserve">
    <value>tonni</value>
    <comment>A measurement unit for weight. This is the metric version of tonne. (Please choose the most appropriate plural form to fit any number between 0 and 999,999,999,999,999)</comment>
  </data>
  <data name="UnitName_CD" xml:space="preserve">
    <value>CD-d</value>
    <comment>A compact disc, used as a comparison measurement unit for data storage. (Please choose the most appropriate plural form to fit any number between 0 and 999,999,999,999,999)</comment>
  </data>
  <data name="UnitAbbreviation_CD" xml:space="preserve">
    <value>CD-d</value>
    <comment>A compact disc, used as a comparison measurement unit for data storage. (Please choose the most appropriate plural form to fit any number between 0 and 999,999,999,999,999)</comment>
  </data>
  <data name="UnitName_SoccerField" xml:space="preserve">
    <value>jalgpalliväljakut</value>
    <comment>A professional-sized soccer field, used as a comparison measurement unit for area. (Please choose the most appropriate plural form to fit any number between 0 and 999,999,999,999,999)</comment>
  </data>
  <data name="UnitAbbreviation_SoccerField" xml:space="preserve">
    <value>jalgpalliväljakut</value>
    <comment>A professional-sized soccer field, used as a comparison measurement unit for area. (Please choose the most appropriate plural form to fit any number between 0 and 999,999,999,999,999)</comment>
  </data>
  <data name="UnitName_FloppyDisk" xml:space="preserve">
    <value>disketti</value>
    <comment>A 1.44 MB floppy disk, used as a comparison measurement unit for data storage. (Please choose the most appropriate plural form to fit any number between 0 and 999,999,999,999,999)</comment>
  </data>
  <data name="UnitAbbreviation_FloppyDisk" xml:space="preserve">
    <value>disketti</value>
    <comment>A 1.44 MB floppy disk, used as a comparison measurement unit for data storage. (Please choose the most appropriate plural form to fit any number between 0 and 999,999,999,999,999)</comment>
  </data>
  <data name="UnitName_DVD" xml:space="preserve">
    <value>DVD-d</value>
    <comment>A DVD, used as a comparison measurement unit for data storage. (Please choose the most appropriate plural form to fit any number between 0 and 999,999,999,999,999)</comment>
  </data>
  <data name="UnitAbbreviation_DVD" xml:space="preserve">
    <value>DVD-d</value>
    <comment>A DVD, used as a comparison measurement unit for data storage. (Please choose the most appropriate plural form to fit any number between 0 and 999,999,999,999,999)</comment>
  </data>
  <data name="UnitName_Battery" xml:space="preserve">
    <value>patareid</value>
    <comment>AA-cell battery, used as a comparison measurement unit for data storage. (Please choose the most appropriate plural form to fit any number between 0 and 999,999,999,999,999)</comment>
  </data>
  <data name="UnitAbbreviation_Battery" xml:space="preserve">
    <value>patareid</value>
    <comment>AA-cell battery, used as a comparison measurement unit for data storage. (Please choose the most appropriate plural form to fit any number between 0 and 999,999,999,999,999)</comment>
  </data>
  <data name="UnitName_Paperclip" xml:space="preserve">
    <value>kirjaklambrit</value>
    <comment>A standard paperclip, used as a comparison measurement unit for length. (Please choose the most appropriate plural form to fit any number between 0 and 999,999,999,999,999)</comment>
  </data>
  <data name="UnitAbbreviation_Paperclip" xml:space="preserve">
    <value>kirjaklambrit</value>
    <comment>A standard paperclip, used as a comparison measurement unit for length. (Please choose the most appropriate plural form to fit any number between 0 and 999,999,999,999,999)</comment>
  </data>
  <data name="UnitName_JumboJet" xml:space="preserve">
    <value>hiidlennukit</value>
    <comment>A jumbo jet (eg. Boeing 747), used as a comparison measurement unit for length. (Please choose the most appropriate plural form to fit any number between 0 and 999,999,999,999,999)</comment>
  </data>
  <data name="UnitAbbreviation_JumboJet" xml:space="preserve">
    <value>hiidlennukit</value>
    <comment>A jumbo jet (eg. Boeing 747), used as a comparison measurement unit for length. (Please choose the most appropriate plural form to fit any number between 0 and 999,999,999,999,999)</comment>
  </data>
  <data name="UnitName_LightBulb" xml:space="preserve">
    <value>lambipirni</value>
    <comment>A light bulb, used as a comparison measurement unit for power (60 watts). (Please choose the most appropriate plural form to fit any number between 0 and 999,999,999,999,999)</comment>
  </data>
  <data name="UnitAbbreviation_LightBulb" xml:space="preserve">
    <value>lambipirni</value>
    <comment>A light bulb, used as a comparison measurement unit for power (60 watts). (Please choose the most appropriate plural form to fit any number between 0 and 999,999,999,999,999)</comment>
  </data>
  <data name="UnitName_Horse" xml:space="preserve">
    <value>hobust</value>
    <comment>A horse, used as a comparison measurement unit for power (~1 horsepower) or speed. (Please choose the most appropriate plural form to fit any number between 0 and 999,999,999,999,999)</comment>
  </data>
  <data name="UnitAbbreviation_Horse" xml:space="preserve">
    <value>hobust</value>
    <comment>A horse, used as a comparison measurement unit for power (~1 horsepower) or speed. (Please choose the most appropriate plural form to fit any number between 0 and 999,999,999,999,999)</comment>
  </data>
  <data name="UnitName_Bathtub" xml:space="preserve">
    <value>vanni</value>
    <comment>A bathtub full of water, used as a comparison measurement unit for volume. (Please choose the most appropriate plural form to fit any number between 0 and 999,999,999,999,999)</comment>
  </data>
  <data name="UnitAbbreviation_Bathtub" xml:space="preserve">
    <value>vanni</value>
    <comment>A bathtub full of water, used as a comparison measurement unit for volume. (Please choose the most appropriate plural form to fit any number between 0 and 999,999,999,999,999)</comment>
  </data>
  <data name="UnitName_Snowflake" xml:space="preserve">
    <value>lumehelvest</value>
    <comment>A snowflake, used as a comparison measurement unit for weight. (Please choose the most appropriate plural form to fit any number between 0 and 999,999,999,999,999)</comment>
  </data>
  <data name="UnitAbbreviation_Snowflake" xml:space="preserve">
    <value>lumehelvest</value>
    <comment>A snowflake, used as a comparison measurement unit for weight. (Please choose the most appropriate plural form to fit any number between 0 and 999,999,999,999,999)</comment>
  </data>
  <data name="UnitName_Elephant" xml:space="preserve">
    <value>elevanti</value>
    <comment>An elephant, used as a comparison measurement unit for weight. (Please choose the most appropriate plural form to fit any number between 0 and 999,999,999,999,999)</comment>
  </data>
  <data name="UnitAbbreviation_Elephant" xml:space="preserve">
    <value>elevanti</value>
    <comment>An elephant, used as a comparison measurement unit for weight. (Please choose the most appropriate plural form to fit any number between 0 and 999,999,999,999,999)</comment>
  </data>
  <data name="UnitName_Turtle" xml:space="preserve">
    <value>kilpkonna</value>
    <comment>A turtle, used as a comparison measurement unit for speed. (Please choose the most appropriate plural form to fit any number between 0 and 999,999,999,999,999)</comment>
  </data>
  <data name="UnitAbbreviation_Turtle" xml:space="preserve">
    <value>kilpkonna</value>
    <comment>A turtle, used as a comparison measurement unit for speed. (Please choose the most appropriate plural form to fit any number between 0 and 999,999,999,999,999)</comment>
  </data>
  <data name="UnitName_Jet" xml:space="preserve">
    <value>reaktiivlennukit</value>
    <comment>A jet plane, used as a comparison measurement unit for speed. (Please choose the most appropriate plural form to fit any number between 0 and 999,999,999,999,999)</comment>
  </data>
  <data name="UnitAbbreviation_Jet" xml:space="preserve">
    <value>reaktiivlennukit</value>
    <comment>A jet plane, used as a comparison measurement unit for speed. (Please choose the most appropriate plural form to fit any number between 0 and 999,999,999,999,999)</comment>
  </data>
  <data name="UnitName_Whale" xml:space="preserve">
    <value>vaala</value>
    <comment>A blue whale, used as a comparison measurement unit for weight. (Please choose the most appropriate plural form to fit any number between 0 and 999,999,999,999,999)</comment>
  </data>
  <data name="UnitAbbreviation_Whale" xml:space="preserve">
    <value>vaala</value>
    <comment>A blue whale, used as a comparison measurement unit for weight. (Please choose the most appropriate plural form to fit any number between 0 and 999,999,999,999,999)</comment>
  </data>
  <data name="UnitName_CoffeeCup" xml:space="preserve">
    <value>kohvitassi</value>
    <comment>A coffee cup, used as a comparison measurement unit for volume. (Please choose the most appropriate plural form to fit any number between 0 and 999,999,999,999,999)</comment>
  </data>
  <data name="UnitAbbreviation_CoffeeCup" xml:space="preserve">
    <value>kohvitassi</value>
    <comment>A coffee cup, used as a comparison measurement unit for volume. (Please choose the most appropriate plural form to fit any number between 0 and 999,999,999,999,999)</comment>
  </data>
  <data name="UnitName_SwimmingPool" xml:space="preserve">
    <value>basseini</value>
    <comment>An Olympic-sized swimming pool, used as a comparison measurement unit for volume. (Please choose the most appropriate plural form to fit any number between 0 and 999,999,999,999,999)</comment>
  </data>
  <data name="UnitAbbreviation_SwimmingPool" xml:space="preserve">
    <value>basseini</value>
    <comment>An Olympic-sized swimming pool, used as a comparison measurement unit for volume. (Please choose the most appropriate plural form to fit any number between 0 and 999,999,999,999,999)</comment>
  </data>
  <data name="UnitName_Hand" xml:space="preserve">
    <value>käelaba</value>
    <comment>A human hand, used as a comparison measurement unit for length or area. (Please choose the most appropriate plural form to fit any number between 0 and 999,999,999,999,999)</comment>
  </data>
  <data name="UnitAbbreviation_Hand" xml:space="preserve">
    <value>käelaba</value>
    <comment>A human hand, used as a comparison measurement unit for length or area. (Please choose the most appropriate plural form to fit any number between 0 and 999,999,999,999,999)</comment>
  </data>
  <data name="UnitName_Paper" xml:space="preserve">
    <value>paberilehte</value>
    <comment>A sheet of 8.5 x 11 inch paper, used as a comparison measurement unit for area. (Please choose the most appropriate plural form to fit any number between 0 and 999,999,999,999,999)</comment>
  </data>
  <data name="UnitAbbreviation_Paper" xml:space="preserve">
    <value>paberilehte</value>
    <comment>A sheet of 8.5 x 11 inch paper, used as a comparison measurement unit for area. (Please choose the most appropriate plural form to fit any number between 0 and 999,999,999,999,999)</comment>
  </data>
  <data name="UnitName_Castle" xml:space="preserve">
    <value>lossi</value>
    <comment>A castle, used as a comparison measurement unit for area (floorspace). (Please choose the most appropriate plural form to fit any number between 0 and 999,999,999,999,999)</comment>
  </data>
  <data name="UnitAbbreviation_Castle" xml:space="preserve">
    <value>lossi</value>
    <comment>A castle, used as a comparison measurement unit for area (floorspace). (Please choose the most appropriate plural form to fit any number between 0 and 999,999,999,999,999)</comment>
  </data>
  <data name="UnitName_Banana" xml:space="preserve">
    <value>banaani</value>
    <comment>A banana, used as a comparison measurement unit for food energy. (Please choose the most appropriate plural form to fit any number between 0 and 999,999,999,999,999)</comment>
  </data>
  <data name="UnitAbbreviation_Banana" xml:space="preserve">
    <value>banaani</value>
    <comment>A banana, used as a comparison measurement unit for food energy. (Please choose the most appropriate plural form to fit any number between 0 and 999,999,999,999,999)</comment>
  </data>
  <data name="UnitName_SliceOfCake" xml:space="preserve">
    <value>koogilõiku</value>
    <comment>A slice of chocolate cake, used as a comparison measurement unit for food energy. (Please choose the most appropriate plural form to fit any number between 0 and 999,999,999,999,999)</comment>
  </data>
  <data name="UnitAbbreviation_SliceOfCake" xml:space="preserve">
    <value>koogilõiku</value>
    <comment>A slice of chocolate cake, used as a comparison measurement unit for food energy. (Please choose the most appropriate plural form to fit any number between 0 and 999,999,999,999,999)</comment>
  </data>
  <data name="UnitName_TrainEngine" xml:space="preserve">
    <value>rongivedurit</value>
    <comment>A train engine, used as a comparison measurement unit for power. (Please choose the most appropriate plural form to fit any number between 0 and 999,999,999,999,999)</comment>
  </data>
  <data name="UnitAbbreviation_TrainEngine" xml:space="preserve">
    <value>rongivedurit</value>
    <comment>A train engine, used as a comparison measurement unit for power. (Please choose the most appropriate plural form to fit any number between 0 and 999,999,999,999,999)</comment>
  </data>
  <data name="UnitName_SoccerBall" xml:space="preserve">
    <value>jalgpalli</value>
    <comment>A soccer ball, used as a comparison measurement unit for weight. (Please choose the most appropriate plural form to fit any number between 0 and 999,999,999,999,999)</comment>
  </data>
  <data name="UnitAbbreviation_SoccerBall" xml:space="preserve">
    <value>jalgpalli</value>
    <comment>A soccer ball, used as a comparison measurement unit for weight. (Please choose the most appropriate plural form to fit any number between 0 and 999,999,999,999,999)</comment>
  </data>
  <data name="MemoryItemHelpText" xml:space="preserve">
    <value>Mäluüksus</value>
    <comment>Help text used by accessibility tools to indicate that an item in the list of memory values is a memory item.</comment>
  </data>
  <data name="SupplementaryUnit_AutomationName" xml:space="preserve">
    <value>%1 %2</value>
    <comment>This string is what is read by Narrator, and other screen readers, for the supplementary value at the bottom of the converter view, %1 = the value of the supplementary unit (i.e. 0.5), %2 = the unit itself (i.e. inches, meters, etc)</comment>
  </data>
  <data name="AboutControlBackButton.[using:Windows.UI.Xaml.Automation]AutomationProperties.Name" xml:space="preserve">
    <value>Tagasi</value>
    <comment>Screen reader prompt for the About panel back button</comment>
  </data>
  <data name="AboutControlBackButton.[using:Windows.UI.Xaml.Controls]ToolTipService.ToolTip" xml:space="preserve">
    <value>Tagasi</value>
    <comment>Content of tooltip being displayed on AboutControlBackButton</comment>
  </data>
  <data name="AboutEULA.Text" xml:space="preserve">
    <value>Microsofti tarkvara litsentsitingimused</value>
    <comment>Displayed on a link to the Microsoft Software License Terms on the About panel</comment>
  </data>
  <data name="PreviewTag.Text" xml:space="preserve">
    <value>Eeltutvustus</value>
    <comment>Label displayed next to upcoming features</comment>
  </data>
  <data name="AboutControlPrivacyStatement.Text" xml:space="preserve">
    <value>Microsofti privaatsusavaldus</value>
    <comment>Displayed on a link to the Microsoft Privacy Statement on the About panel</comment>
  </data>
  <data name="AboutControlCopyright" xml:space="preserve">
    <value>© %1 Microsoft. Kõik õigused on reserveeritud.</value>
    <comment>{Locked="%1"}. Copyright statement, displayed on the About panel. %1 = the current year (4 digits)</comment>
  </data>
  <data name="AboutControlContribute" xml:space="preserve">
    <value>Kui soovite teada, kuidas saate Windowsi kalkulaator lisada, vaadake projekti %HL%GitHub%HL%.</value>
    <comment>{Locked="%HL%GitHub%HL%"}. GitHub link, Displayed on the About panel</comment>
  </data>
  <data name="AboutGroupTitle.Text" xml:space="preserve">
    <value>Teave</value>
    <comment>Subtitle of about message on Settings page</comment>
  </data>
  <data name="FeedbackButton.Content" xml:space="preserve">
    <value>Saada tagasisidet</value>
    <comment>The text that shows in the dropdown navigation control to give the user the option to send feedback about the app and it launches Windows Feedback app</comment>
  </data>
  <data name="HistoryEmpty.Text" xml:space="preserve">
    <value>Ajalugu pole veel.</value>
    <comment>The text that shows as the header for the history list</comment>
  </data>
  <data name="MemoryPaneEmpty.Text" xml:space="preserve">
    <value>Mällu pole midagi salvestatud.</value>
    <comment>The text that shows as the header for the memory list</comment>
  </data>
  <data name="MemoryFlyout.[using:Windows.UI.Xaml.Automation]AutomationProperties.Name" xml:space="preserve">
    <value>Mälu</value>
    <comment>Screen reader prompt for the negate button on the converter operator keypad</comment>
  </data>
  <data name="CannotPaste" xml:space="preserve">
    <value>Seda avaldist ei saa kleepida</value>
    <comment>The paste operation cannot be performed, if the expression is invalid.</comment>
  </data>
  <data name="UnitName_Gibibits" xml:space="preserve">
    <value>Gibibitti</value>
    <comment>A measurement unit for data. (Please choose the most appropriate plural form to fit any number between 0 and 999,999,999,999,999)</comment>
  </data>
  <data name="UnitName_Gibibytes" xml:space="preserve">
    <value>Gibibaiti</value>
    <comment>A measurement unit for data. (Please choose the most appropriate plural form to fit any number between 0 and 999,999,999,999,999)</comment>
  </data>
  <data name="UnitName_Kibibits" xml:space="preserve">
    <value>kibibitti</value>
    <comment>A measurement unit for data. (Please choose the most appropriate plural form to fit any number between 0 and 999,999,999,999,999)</comment>
  </data>
  <data name="UnitName_Kibibytes" xml:space="preserve">
    <value>kibibaiti</value>
    <comment>A measurement unit for data. (Please choose the most appropriate plural form to fit any number between 0 and 999,999,999,999,999)</comment>
  </data>
  <data name="UnitName_Mebibits" xml:space="preserve">
    <value>Mebibitti</value>
    <comment>A measurement unit for data. (Please choose the most appropriate plural form to fit any number between 0 and 999,999,999,999,999)</comment>
  </data>
  <data name="UnitName_Mebibytes" xml:space="preserve">
    <value>mebibaiti</value>
    <comment>A measurement unit for data. (Please choose the most appropriate plural form to fit any number between 0 and 999,999,999,999,999)</comment>
  </data>
  <data name="UnitName_Pebibits" xml:space="preserve">
    <value>Pebibitti</value>
    <comment>A measurement unit for data. (Please choose the most appropriate plural form to fit any number between 0 and 999,999,999,999,999)</comment>
  </data>
  <data name="UnitName_Pebibytes" xml:space="preserve">
    <value>pebibaiti</value>
    <comment>A measurement unit for data. (Please choose the most appropriate plural form to fit any number between 0 and 999,999,999,999,999)</comment>
  </data>
  <data name="UnitName_Tebibits" xml:space="preserve">
    <value>Tebibitti</value>
    <comment>A measurement unit for data. (Please choose the most appropriate plural form to fit any number between 0 and 999,999,999,999,999)</comment>
  </data>
  <data name="UnitName_Tebibytes" xml:space="preserve">
    <value>tebibaiti</value>
    <comment>A measurement unit for data. (Please choose the most appropriate plural form to fit any number between 0 and 999,999,999,999,999)</comment>
  </data>
  <data name="UnitName_Exabits" xml:space="preserve">
    <value>eksabitti</value>
    <comment>A measurement unit for data. (Please choose the most appropriate plural form to fit any number between 0 and 999,999,999,999,999)</comment>
  </data>
  <data name="UnitName_Exabytes" xml:space="preserve">
    <value>eksabaiti</value>
    <comment>A measurement unit for data. (Please choose the most appropriate plural form to fit any number between 0 and 999,999,999,999,999)</comment>
  </data>
  <data name="UnitName_Exbibits" xml:space="preserve">
    <value>eksbibitti</value>
    <comment>A measurement unit for data. (Please choose the most appropriate plural form to fit any number between 0 and 999,999,999,999,999)</comment>
  </data>
  <data name="UnitName_Exbibytes" xml:space="preserve">
    <value>Eksbibaiti</value>
    <comment>A measurement unit for data. (Please choose the most appropriate plural form to fit any number between 0 and 999,999,999,999,999)</comment>
  </data>
  <data name="UnitName_Zetabits" xml:space="preserve">
    <value>Zetabitti</value>
    <comment>A measurement unit for data. (Please choose the most appropriate plural form to fit any number between 0 and 999,999,999,999,999)</comment>
  </data>
  <data name="UnitName_Zetabytes" xml:space="preserve">
    <value>Zetabaiti</value>
    <comment>A measurement unit for data. (Please choose the most appropriate plural form to fit any number between 0 and 999,999,999,999,999)</comment>
  </data>
  <data name="UnitName_Zebibits" xml:space="preserve">
    <value>Zebibitti</value>
    <comment>A measurement unit for data. (Please choose the most appropriate plural form to fit any number between 0 and 999,999,999,999,999)</comment>
  </data>
  <data name="UnitName_Zebibytes" xml:space="preserve">
    <value>Zebibaiti</value>
    <comment>A measurement unit for data. (Please choose the most appropriate plural form to fit any number between 0 and 999,999,999,999,999)</comment>
  </data>
  <data name="UnitName_Yottabit" xml:space="preserve">
    <value>Yottabits</value>
    <comment>A measurement unit for data. (Please choose the most appropriate plural form to fit any number between 0 and 999,999,999,999,999)</comment>
  </data>
  <data name="UnitName_Yottabyte" xml:space="preserve">
    <value>Yottabytes</value>
    <comment>A measurement unit for data. (Please choose the most appropriate plural form to fit any number between 0 and 999,999,999,999,999)</comment>
  </data>
  <data name="UnitName_Yobibits" xml:space="preserve">
    <value>Yobibitti</value>
    <comment>A measurement unit for data. (Please choose the most appropriate plural form to fit any number between 0 and 999,999,999,999,999)</comment>
  </data>
  <data name="UnitName_Yobibytes" xml:space="preserve">
    <value>yobibaiti</value>
    <comment>A measurement unit for data. (Please choose the most appropriate plural form to fit any number between 0 and 999,999,999,999,999)</comment>
  </data>
  <data name="DateCalculationModeText" xml:space="preserve">
    <value>Kuupäevaarvutus</value>
  </data>
  <data name="DateCalculationOption.[using:Windows.UI.Xaml.Automation]AutomationProperties.Name" xml:space="preserve">
    <value>Arvutamisrežiim</value>
    <comment>Automation label for the Date calculation Mode combobox. Users will hear "Calculation mode combobox".</comment>
  </data>
  <data name="AddOption.Content" xml:space="preserve">
    <value>Liida</value>
    <comment>Add toggle button text</comment>
  </data>
  <data name="Date_AddSubtractOption.Content" xml:space="preserve">
    <value>Liida või lahuta päevi</value>
    <comment>Add or Subtract days option</comment>
  </data>
  <data name="DateLabel.Text" xml:space="preserve">
    <value>Kuupäev</value>
    <comment>Date result label</comment>
  </data>
  <data name="Date_DifferenceOption.Content" xml:space="preserve">
    <value>Kuupäevade vahe</value>
    <comment>Date difference option</comment>
  </data>
  <data name="DaysLabel.Text" xml:space="preserve">
    <value>päeva</value>
    <comment>Add/Subtract Days label</comment>
  </data>
  <data name="Date_DifferenceLabel.Text" xml:space="preserve">
    <value>Vahe</value>
    <comment>Difference result label</comment>
  </data>
  <data name="DateDiff_FromHeader.Header" xml:space="preserve">
    <value>Alates</value>
    <comment>From Date Header for Difference Date Picker</comment>
  </data>
  <data name="MonthsLabel.Text" xml:space="preserve">
    <value>Kuud</value>
    <comment>Add/Subtract Months label</comment>
  </data>
  <data name="SubtractOption.Content" xml:space="preserve">
    <value>Lahuta</value>
    <comment>Subtract toggle button text</comment>
  </data>
  <data name="DateDiff_ToHeader.Header" xml:space="preserve">
    <value>Kuni</value>
    <comment>To Date Header for Difference Date Picker</comment>
  </data>
  <data name="YearsLabel.Text" xml:space="preserve">
    <value>aastat</value>
    <comment>Add/Subtract Years label</comment>
  </data>
  <data name="Date_OutOfBoundMessage" xml:space="preserve">
    <value>Kuupäev on piiridest väljas</value>
    <comment>Out of bound message shown as result when the date calculation exceeds the bounds</comment>
  </data>
  <data name="Date_Day" xml:space="preserve">
    <value>päev</value>
  </data>
  <data name="Date_Days" xml:space="preserve">
    <value>päeva</value>
  </data>
  <data name="Date_Month" xml:space="preserve">
    <value>kuu</value>
  </data>
  <data name="Date_Months" xml:space="preserve">
    <value>kuud</value>
  </data>
  <data name="Date_SameDates" xml:space="preserve">
    <value>Samad kuupäevad</value>
  </data>
  <data name="Date_Week" xml:space="preserve">
    <value>nädal</value>
  </data>
  <data name="Date_Weeks" xml:space="preserve">
    <value>nädalat</value>
  </data>
  <data name="Date_Year" xml:space="preserve">
    <value>aasta</value>
  </data>
  <data name="Date_Years" xml:space="preserve">
    <value>aastat</value>
  </data>
  <data name="Date_DifferenceResultAutomationName" xml:space="preserve">
    <value>Vahe %1</value>
    <comment>Automation name for reading out the date difference. %1 =  Date difference</comment>
  </data>
  <data name="Date_ResultingDateAutomationName" xml:space="preserve">
    <value>Tulemuseks saadav kuupäev %1</value>
    <comment>Automation name for reading out the resulting date in Add/Subtract mode. %1 = Resulting date</comment>
  </data>
  <data name="HeaderAutomationName_Calculator" xml:space="preserve">
    <value>Kalkulaatorirežiim %1</value>
    <comment>{Locked='%1'} Automation name for when the mode header is focused. %1 = the current calculator mode: Scientific, Standard, or Programmer.</comment>
  </data>
  <data name="HeaderAutomationName_Converter" xml:space="preserve">
    <value>%1 teisendirežiim</value>
    <comment>{Locked='%1'} Automation name for when the mode header is focused. %1 = the current converter mode: "Weight and mass", "Energy", "Volume", etc.</comment>
  </data>
  <data name="HeaderAutomationName_Date" xml:space="preserve">
    <value>Kuupäevaarvutuse režiim</value>
    <comment>Automation name for when the mode header is focused and the current mode is Date calculation.</comment>
  </data>
  <data name="DockPanel.[using:Windows.UI.Xaml.Automation]AutomationProperties.Name" xml:space="preserve">
    <value>Ajaloo- ja mäluloendid</value>
    <comment>Automation name for the group of controls for history and memory lists.</comment>
  </data>
  <data name="MemoryPanel.[using:Windows.UI.Xaml.Automation]AutomationProperties.Name" xml:space="preserve">
    <value>Mälu juhtelemendid</value>
    <comment>Automation name for the group of memory controls (Mem Clear, Mem Recall, Mem Add, Mem Subtract, Mem Store, Memory flyout toggle)</comment>
  </data>
  <data name="StandardFunctions.[using:Windows.UI.Xaml.Automation]AutomationProperties.Name" xml:space="preserve">
    <value>Standardfunktsioonid</value>
    <comment>Automation name for the group of standard function buttons (Percent, Square Root, Square, Cube, Reciprocal)</comment>
  </data>
  <data name="DisplayControls.[using:Windows.UI.Xaml.Automation]AutomationProperties.Name" xml:space="preserve">
    <value>Kuva juhtelemendid</value>
    <comment>Automation name for the group of display control buttons (Clear, Clear Entry, and Backspace)</comment>
  </data>
  <data name="StandardOperators.[using:Windows.UI.Xaml.Automation]AutomationProperties.Name" xml:space="preserve">
    <value>Standardsed tehtemärgid</value>
    <comment>Automation name for the group of standard operator buttons (Add, Subtract, Multiply, Divide, and Equals)</comment>
  </data>
  <data name="NumberPad.[using:Windows.UI.Xaml.Automation]AutomationProperties.Name" xml:space="preserve">
    <value>Numbriklahvistik</value>
    <comment>Automation name for the group of NumberPad buttons (0-9, A-F and Decimal button)</comment>
  </data>
  <data name="ScientificAngleOperators.[using:Windows.UI.Xaml.Automation]AutomationProperties.Name" xml:space="preserve">
    <value>Nurkade tehtemärgid</value>
    <comment>Automation name for the group of Scientific angle operators (Degree mode, Hyperbolic toggle, and Precision button)</comment>
  </data>
  <data name="ScientificFunctions.[using:Windows.UI.Xaml.Automation]AutomationProperties.Name" xml:space="preserve">
    <value>Teaduslikud funktsioonid</value>
    <comment>Automation name for the group of Scientific functions.</comment>
  </data>
  <data name="RadixGroup.[using:Windows.UI.Xaml.Automation]AutomationProperties.Name" xml:space="preserve">
    <value>Arvusüsteemi aluse valimine</value>
    <comment>Automation name for the group of radices (Hexadecimal, Decimal, Octal, Binary). https://en.wikipedia.org/wiki/Radix </comment>
  </data>
  <data name="ProgrammerOperators.[using:Windows.UI.Xaml.Automation]AutomationProperties.Name" xml:space="preserve">
    <value>Programmeerimise tehtemärgid</value>
    <comment>Automation name for the group of programmer operators (RoL, RoR, Lsh, Rsh, OR, XOR, NOT, AND).</comment>
  </data>
  <data name="InputModeSelectionGroup.[using:Windows.UI.Xaml.Automation]AutomationProperties.Name" xml:space="preserve">
    <value>Sisestusrežiimi valimine</value>
    <comment>Automation name for the group of input mode toggling buttons.</comment>
  </data>
  <data name="BitFlipPanel.[using:Windows.UI.Xaml.Automation]AutomationProperties.Name" xml:space="preserve">
    <value>Bitivaheldusklahvistik</value>
    <comment>Automation name for the group of bit toggling buttons.</comment>
  </data>
  <data name="scrollLeft.[using:Windows.UI.Xaml.Automation]AutomationProperties.Name" xml:space="preserve">
    <value>Keri avaldist vasakule</value>
    <comment>Automation label for the "scroll left" button that appears when an expression is too large to fit in the window.</comment>
  </data>
  <data name="scrollRight.[using:Windows.UI.Xaml.Automation]AutomationProperties.Name" xml:space="preserve">
    <value>Keri avaldist paremale</value>
    <comment>Automation label for the "scroll right" button that appears when an expression is too large to fit in the window.</comment>
  </data>
  <data name="Format_MaxDigitsReached" xml:space="preserve">
    <value>Maksimaalne numbrite arv. %1</value>
    <comment>{Locked='%1'} Formatting string for a Narrator announcement when user reaches max digits. The %1 is the automation name of the display. Users will hear "Max digits reached. Display is _current_value_".</comment>
  </data>
  <data name="Format_ButtonPressAuditoryFeedback" xml:space="preserve">
    <value>%1 %2</value>
    <comment>{Locked='%1','%2'} Formatting string for a Narrator announcement when user presses a button with auditory feedback. "%1" is the display value and "%2" is the button press feedback. Example, user presses "plus" and hears "Display is 7 plus".</comment>
  </data>
  <data name="Format_MemorySave" xml:space="preserve">
    <value>%1 on salvestatud mällu</value>
    <comment>{Locked='%1'} Formatting string for a Narrator announcement when the user stores a number to memory. The %1 is the automation name of the display. Users will hear "_current_value_ saved to memory".</comment>
  </data>
  <data name="Format_MemorySlotChanged" xml:space="preserve">
    <value>Mälupesa %1 on %2</value>
    <comment>{Locked='%1','%2'} Formatting string for a Narrator announcement when the user changes a memory slot. The %1 is the index of the memory slot and %2 is the new value. For example, users might hear "Memory slot 2 is 37".</comment>
  </data>
  <data name="Format_MemorySlotCleared" xml:space="preserve">
    <value>Mälupesa %1 on tühjendatud</value>
    <comment>{Locked='%1'} Formatting string for a Narrator announcement when the user clears a memory slot. The %1 is the index of the memory slot. For example, users might hear "Memory slot 2 cleared".</comment>
  </data>
  <data name="divideButton.[using:CalculatorApp.Controls]CalculatorButton.AuditoryFeedback" xml:space="preserve">
    <value>jagatud arvuga</value>
    <comment>Auditory feedback for screen reader users. Users will hear "Display is 7 divided by" when the button is pressed.</comment>
  </data>
  <data name="multiplyButton.[using:CalculatorApp.Controls]CalculatorButton.AuditoryFeedback" xml:space="preserve">
    <value>korda</value>
    <comment>Auditory feedback for screen reader users. Users will hear "Display is 7 times" when the button is pressed.</comment>
  </data>
  <data name="minusButton.[using:CalculatorApp.Controls]CalculatorButton.AuditoryFeedback" xml:space="preserve">
    <value>miinus</value>
    <comment>Auditory feedback for screen reader users. Users will hear "Display is 7 minus" when the button is pressed.</comment>
  </data>
  <data name="plusButton.[using:CalculatorApp.Controls]CalculatorButton.AuditoryFeedback" xml:space="preserve">
    <value>pluss</value>
    <comment>Auditory feedback for screen reader users. Users will hear "Display is 7 plus" when the button is pressed.</comment>
  </data>
  <data name="powerButton.[using:CalculatorApp.Controls]CalculatorButton.AuditoryFeedback" xml:space="preserve">
    <value>astmes</value>
    <comment>Auditory feedback for screen reader users. Users will hear "Display is 7 to the power of" when the button is pressed.</comment>
  </data>
  <data name="ySquareRootButton.[using:CalculatorApp.Controls]CalculatorButton.AuditoryFeedback" xml:space="preserve">
    <value>ruutjuur arvust y</value>
    <comment>Auditory feedback for screen reader users. Users will hear "Display is 7 y root" when the button is pressed.</comment>
  </data>
  <data name="modButton.[using:CalculatorApp.Controls]CalculatorButton.AuditoryFeedback" xml:space="preserve">
    <value>mooduli järgi</value>
    <comment>Auditory feedback for screen reader users. Users will hear "Display is 7 mod" when the button is pressed.</comment>
  </data>
  <data name="lshButton.[using:CalculatorApp.Controls]CalculatorButton.AuditoryFeedback" xml:space="preserve">
    <value>vasak tõstuklahv</value>
    <comment>Auditory feedback for screen reader users. Users will hear "Display is 7 left shift" when the button is pressed.</comment>
  </data>
  <data name="rshButton.[using:CalculatorApp.Controls]CalculatorButton.AuditoryFeedback" xml:space="preserve">
    <value>parem tõstuklahv</value>
    <comment>Auditory feedback for screen reader users. Users will hear "Display is 7 right shift" when the button is pressed.</comment>
  </data>
  <data name="orButton.[using:CalculatorApp.Controls]CalculatorButton.AuditoryFeedback" xml:space="preserve">
    <value>või</value>
    <comment>Auditory feedback for screen reader users. Users will hear "Display is 7 or" when the button is pressed. OR is a mathematical operation on two binary values.</comment>
  </data>
  <data name="xorButton.[using:CalculatorApp.Controls]CalculatorButton.AuditoryFeedback" xml:space="preserve">
    <value>välistav või</value>
    <comment>Auditory feedback for screen reader users. Users will hear "Display is 7 x or" when the button is pressed. XOR is a mathematical operation on two binary values. Here the feedback is "x or" in order to get the correct pronunciation.</comment>
  </data>
  <data name="andButton.[using:CalculatorApp.Controls]CalculatorButton.AuditoryFeedback" xml:space="preserve">
    <value>ja</value>
    <comment>Auditory feedback for screen reader users. Users will hear "Display is 7 and" when the button is pressed. AND is a mathematical operation on two binary values.</comment>
  </data>
  <data name="CurrencyFromToRatioFormat" xml:space="preserve">
    <value>%1 %2 = %3 %4</value>
    <comment>The exact ratio between converted currencies, e.g. "1 USD = 0.8885 EUR". %1 will always be '1'. %2 is the From currency code. %3 is the formatted conversion ratio. %4 is the To currency code.</comment>
  </data>
  <data name="CurrencyTimestampFormat" xml:space="preserve">
    <value>Värskendatud %1 kell %2</value>
    <comment>The timestamp of currency conversion ratios fetched from an online service. %1 is the date. %2 is the time. Example: "Updated Sep 28, 2016 5:42 PM"</comment>
  </data>
  <data name="RefreshButtonText.Content" xml:space="preserve">
    <value>Värskenda valuutakursse</value>
    <comment>The text displayed for a hyperlink button that refreshes currency converter ratios.</comment>
  </data>
  <data name="DataChargesMayApply" xml:space="preserve">
    <value>Kehtida võivad andmesidetasud.</value>
    <comment>The text displayed when users are on a metered connection and using currency converter.</comment>
  </data>
  <data name="FailedToRefresh" xml:space="preserve">
    <value>Uusi valuutakursse ei saanud tuua. Proovige hiljem uuesti.</value>
    <comment>The text displayed when currency ratio data fails to load.</comment>
  </data>
  <data name="OfflineStatusHyperlinkText" xml:space="preserve">
    <value>Võrguühendus puudub. Kontrollige oma %HL%võrgusätteid%HL%</value>
    <comment>Status text displayed when currency converter is disconnected from the internet. The text "Notification Settings" should be surrounded by %HL% since they are used to indicate that that text should be the hyperlink text. {Locked="%HL%"}</comment>
  </data>
  <data name="UpdatingCurrencyRates" xml:space="preserve">
    <value>Valuutakursside värskendamine</value>
    <comment>This string is what is read by Narrator, and other screen readers, when the "Update rates" button in the Currency Converter is clicked.</comment>
  </data>
  <data name="CurrencyRatesUpdated" xml:space="preserve">
    <value>Valuutakursid on värskendatud</value>
    <comment>This string is what is read by Narrator, and other screen readers, when the currency rates in Currency converter have successfully updated.</comment>
  </data>
  <data name="CurrencyRatesUpdateFailed" xml:space="preserve">
    <value>Kursse ei saanud värskendada</value>
    <comment>This string is what is read by Narrator, and other screen readers, when the currency rates in Currency converter have failed to update.</comment>
  </data>
  <data name="HistoryButton.AccessKey" xml:space="preserve">
    <value>I</value>
    <comment>Access key for the History button. {StringCategory="Accelerator"}</comment>
  </data>
  <data name="MemoryButton.AccessKey" xml:space="preserve">
    <value>M</value>
    <comment>Access key for the Memory button. {StringCategory="Accelerator"}</comment>
  </data>
  <data name="NavView.AccessKey" xml:space="preserve">
    <value>H</value>
    <comment>Access key for the Hamburger button. {StringCategory="Accelerator"}</comment>
  </data>
  <data name="CategoryName_AngleAccessKey" xml:space="preserve">
    <value>AN</value>
    <comment>AccessKey for the angle converter navbar item. {StringCategory="Accelerator"}</comment>
  </data>
  <data name="CategoryName_AreaAccessKey" xml:space="preserve">
    <value>AR</value>
    <comment>AccessKey for the area converter navbar item. {StringCategory="Accelerator"}</comment>
  </data>
  <data name="CategoryName_CurrencyAccessKey" xml:space="preserve">
    <value>C</value>
    <comment>AccessKey for the currency converter navbar item. {StringCategory="Accelerator"}</comment>
  </data>
  <data name="CategoryName_DataAccessKey" xml:space="preserve">
    <value>D</value>
    <comment>AccessKey for the data converter navbar item. {StringCategory="Accelerator"}</comment>
  </data>
  <data name="CategoryName_EnergyAccessKey" xml:space="preserve">
    <value>E</value>
    <comment>AccessKey for the energy converter navbar item. {StringCategory="Accelerator"}</comment>
  </data>
  <data name="CategoryName_LengthAccessKey" xml:space="preserve">
    <value>L</value>
    <comment>AccessKey for the length converter navbar item. {StringCategory="Accelerator"}</comment>
  </data>
  <data name="CategoryName_PowerAccessKey" xml:space="preserve">
    <value>PO</value>
    <comment>AccessKey for the power converter navbar item. {StringCategory="Accelerator"}</comment>
  </data>
  <data name="CategoryName_PressureAccessKey" xml:space="preserve">
    <value>PR</value>
    <comment>AccessKey for the pressure converter navbar item. {StringCategory="Accelerator"}</comment>
  </data>
  <data name="CategoryName_SpeedAccessKey" xml:space="preserve">
    <value>S</value>
    <comment>AccessKey for the speed converter navbar item. {StringCategory="Accelerator"}</comment>
  </data>
  <data name="CategoryName_TimeAccessKey" xml:space="preserve">
    <value>TI</value>
    <comment>AccessKey for the time converter navbar item. {StringCategory="Accelerator"}</comment>
  </data>
  <data name="CategoryName_VolumeAccessKey" xml:space="preserve">
    <value>V</value>
    <comment>AccessKey for the volume converter navbar item. {StringCategory="Accelerator"}</comment>
  </data>
  <data name="CategoryName_WeightAccessKey" xml:space="preserve">
    <value>W</value>
    <comment>AccessKey for the weight converter navbar item. {StringCategory="Accelerator"}</comment>
  </data>
  <data name="CategoryName_TemperatureAccessKey" xml:space="preserve">
    <value>TE</value>
    <comment>AccessKey for the temperature converter navbar item. {StringCategory="Accelerator"}</comment>
  </data>
  <data name="ClearHistory.AccessKey" xml:space="preserve">
    <value>C</value>
    <comment>Access key for the Clear history button.{StringCategory="Accelerator"}</comment>
  </data>
  <data name="ClearMemory.AccessKey" xml:space="preserve">
    <value>C</value>
    <comment>Access key for the Clear memory button. {StringCategory="Accelerator"}</comment>
  </data>
  <data name="ClearMemory.[using:Windows.UI.Xaml.Controls]ToolTipService.ToolTip" xml:space="preserve">
    <value>Tühjenda kogu mälu (Ctrl+L)</value>
    <comment>This is the tool tip automation name for the Clear Memory button in the Memory Pane.</comment>
  </data>
  <data name="ClearMemory.[using:Windows.UI.Xaml.Automation]AutomationProperties.Name" xml:space="preserve">
    <value>Tühjenda kogu mälu</value>
    <comment>Screen reader prompt for the Calculator Clear Memory button in the Memory Pane</comment>
  </data>
  <data name="HistoryLabel.AccessKey" xml:space="preserve">
    <value>I</value>
    <comment>Access key for the History pivot item.{StringCategory="Accelerator"}</comment>
  </data>
  <data name="MemoryLabel.AccessKey" xml:space="preserve">
    <value>M</value>
    <comment>Access key for the Memory pivot item.{StringCategory="Accelerator"}</comment>
  </data>
  <data name="SineDegrees" xml:space="preserve">
    <value>siinuse kraadid</value>
    <comment>Name for the sine function in degrees mode. Used by screen readers.</comment>
  </data>
  <data name="SineRadians" xml:space="preserve">
    <value>siinuse radiaanid</value>
    <comment>Name for the sine function in radians mode. Used by screen readers.</comment>
  </data>
  <data name="SineGradians" xml:space="preserve">
    <value>siinuse goonid</value>
    <comment>Name for the sine function in gradians mode. Used by screen readers.</comment>
  </data>
  <data name="InverseSineDegrees" xml:space="preserve">
    <value>siinuse pöördkraadid</value>
    <comment>Name for the inverse sine function in degrees mode. Used by screen readers.</comment>
  </data>
  <data name="InverseSineRadians" xml:space="preserve">
    <value>pöördsiinuse radiaanid</value>
    <comment>Name for the inverse sine function in radians mode. Used by screen readers.</comment>
  </data>
  <data name="InverseSineGradians" xml:space="preserve">
    <value>pöördsiinuse goonid</value>
    <comment>Name for the inverse sine function in gradians mode. Used by screen readers.</comment>
  </data>
  <data name="HyperbolicSine" xml:space="preserve">
    <value>hüperboolne siinus</value>
    <comment>Name for the hyperbolic sine function. Used by screen readers.</comment>
  </data>
  <data name="InverseHyperbolicSine" xml:space="preserve">
    <value>hüperboolse siinuse pöördfunktsioon</value>
    <comment>Name for the inverse hyperbolic sine function. Used by screen readers.</comment>
  </data>
  <data name="CosineDegrees" xml:space="preserve">
    <value>koosinuse kraadid</value>
    <comment>Name for the cosine function in degrees mode. Used by screen readers.</comment>
  </data>
  <data name="CosineRadians" xml:space="preserve">
    <value>koosinuse radiaanid</value>
    <comment>Name for the cosine function in radians mode. Used by screen readers.</comment>
  </data>
  <data name="CosineGradians" xml:space="preserve">
    <value>koosinuse goonid</value>
    <comment>Name for the cosine function in gradians mode. Used by screen readers.</comment>
  </data>
  <data name="InverseCosineDegrees" xml:space="preserve">
    <value>pöördkoosinuse kraadid</value>
    <comment>Name for the inverse cosine function in degrees mode. Used by screen readers.</comment>
  </data>
  <data name="InverseCosineRadians" xml:space="preserve">
    <value>pöördkoosinuse radiaanid</value>
    <comment>Name for the inverse cosine function in radians mode. Used by screen readers.</comment>
  </data>
  <data name="InverseCosineGradians" xml:space="preserve">
    <value>pöördkoosinuse goonid</value>
    <comment>Name for the inverse cosine function in gradians mode. Used by screen readers.</comment>
  </data>
  <data name="HyperbolicCosine" xml:space="preserve">
    <value>hüperboolne koosinus</value>
    <comment>Name for the hyperbolic cosine function. Used by screen readers.</comment>
  </data>
  <data name="InverseHyperbolicCosine" xml:space="preserve">
    <value>hüperboolse koosinuse pöördfunktsioon</value>
    <comment>Name for the inverse hyperbolic cosine function. Used by screen readers.</comment>
  </data>
  <data name="TangentDegrees" xml:space="preserve">
    <value>tangensi kraadid</value>
    <comment>Name for the tangent function in degrees mode. Used by screen readers.</comment>
  </data>
  <data name="TangentRadians" xml:space="preserve">
    <value>tangensi radiaanid</value>
    <comment>Name for the tangent function in radians mode. Used by screen readers.</comment>
  </data>
  <data name="TangentGradians" xml:space="preserve">
    <value>tangensi goonid</value>
    <comment>Name for the tangent function in gradians mode. Used by screen readers.</comment>
  </data>
  <data name="InverseTangentDegrees" xml:space="preserve">
    <value>pöördtangensi kraadid</value>
    <comment>Name for the inverse tangent function in degrees mode. Used by screen readers.</comment>
  </data>
  <data name="InverseTangentRadians" xml:space="preserve">
    <value>pöördtangensi radiaanid</value>
    <comment>Name for the inverse tangent function in radians mode. Used by screen readers.</comment>
  </data>
  <data name="InverseTangentGradians" xml:space="preserve">
    <value>pöördtangensi goonid</value>
    <comment>Name for the inverse tangent function in gradians mode. Used by screen readers.</comment>
  </data>
  <data name="HyperbolicTangent" xml:space="preserve">
    <value>hüperboolne tangens</value>
    <comment>Name for the hyperbolic tangent function. Used by screen readers.</comment>
  </data>
  <data name="InverseHyperbolicTangent" xml:space="preserve">
    <value>hüperboolse tangensi pöördfunktsioon</value>
    <comment>Name for the inverse hyperbolic tangent function. Used by screen readers.</comment>
  </data>
  <data name="SecantDegrees" xml:space="preserve">
    <value>seekansi kraadid</value>
    <comment>Name for the secant function in degrees mode. Used by screen readers.</comment>
  </data>
  <data name="SecantRadians" xml:space="preserve">
    <value>seekansi radiaanid</value>
    <comment>Name for the secant function in radians mode. Used by screen readers.</comment>
  </data>
  <data name="SecantGradians" xml:space="preserve">
    <value>seekansi gradiaanid</value>
    <comment>Name for the secant function in gradians mode. Used by screen readers.</comment>
  </data>
  <data name="InverseSecantDegrees" xml:space="preserve">
    <value>seekansi pöördkraadid</value>
    <comment>Name for the inverse secant function in degrees mode. Used by screen readers.</comment>
  </data>
  <data name="InverseSecantRadians" xml:space="preserve">
    <value>seekansi pöördradiaanid</value>
    <comment>Name for the inverse secant function in radians mode. Used by screen readers.</comment>
  </data>
  <data name="InverseSecantGradians" xml:space="preserve">
    <value>seekansi pöördgradiaanid</value>
    <comment>Name for the inverse secant function in gradians mode. Used by screen readers.</comment>
  </data>
  <data name="HyperbolicSecant" xml:space="preserve">
    <value>hüperboolne seekans</value>
    <comment>Name for the hyperbolic secant function. Used by screen readers.</comment>
  </data>
  <data name="InverseHyperbolicSecant" xml:space="preserve">
    <value>hüperboolse seekansi pöördfunktsioon</value>
    <comment>Name for the inverse hyperbolic secant function. Used by screen readers.</comment>
  </data>
  <data name="CosecantDegrees" xml:space="preserve">
    <value>koosekansi kraadid</value>
    <comment>Name for the cosecant function in degrees mode. Used by screen readers.</comment>
  </data>
  <data name="CosecantRadians" xml:space="preserve">
    <value>koosekansi radiaanid</value>
    <comment>Name for the cosecant function in radians mode. Used by screen readers.</comment>
  </data>
  <data name="CosecantGradians" xml:space="preserve">
    <value>koosekansi gradiaanid</value>
    <comment>Name for the cosecant function in gradians mode. Used by screen readers.</comment>
  </data>
  <data name="InverseCosecantDegrees" xml:space="preserve">
    <value>koosekansi pöördkraadid</value>
    <comment>Name for the inverse cosecant function in degrees mode. Used by screen readers.</comment>
  </data>
  <data name="InverseCosecantRadians" xml:space="preserve">
    <value>koosekansi pöördradiaanid</value>
    <comment>Name for the inverse cosecant function in radians mode. Used by screen readers.</comment>
  </data>
  <data name="InverseCosecantGradians" xml:space="preserve">
    <value>koosekansi pöördgradiaanid</value>
    <comment>Name for the inverse cosecant function in gradians mode. Used by screen readers.</comment>
  </data>
  <data name="HyperbolicCosecant" xml:space="preserve">
    <value>hüperboolne koosekans</value>
    <comment>Name for the hyperbolic cosecant function. Used by screen readers.</comment>
  </data>
  <data name="InverseHyperbolicCosecant" xml:space="preserve">
    <value>hüperboolne pöördkoosekans</value>
    <comment>Name for the inverse hyperbolic cosecant function. Used by screen readers.</comment>
  </data>
  <data name="CotangentDegrees" xml:space="preserve">
    <value>kootangensi kraadid</value>
    <comment>Name for the cotangent function in degrees mode. Used by screen readers.</comment>
  </data>
  <data name="CotangentRadians" xml:space="preserve">
    <value>Kootangensi radiaanid</value>
    <comment>Name for the cotangent function in radians mode. Used by screen readers.</comment>
  </data>
  <data name="CotangentGradians" xml:space="preserve">
    <value>kootangensi gradiaanid</value>
    <comment>Name for the cotangent function in gradians mode. Used by screen readers.</comment>
  </data>
  <data name="InverseCotangentDegrees" xml:space="preserve">
    <value>kootangensi pöördkraadid</value>
    <comment>Name for the inverse cotangent function in degrees mode. Used by screen readers.</comment>
  </data>
  <data name="InverseCotangentRadians" xml:space="preserve">
    <value>kootangensi pöördradiaanid</value>
    <comment>Name for the inverse cotangent function in radians mode. Used by screen readers.</comment>
  </data>
  <data name="InverseCotangentGradians" xml:space="preserve">
    <value>kootangensi pöördgradiaanid</value>
    <comment>Name for the inverse cotangent function in gradians mode. Used by screen readers.</comment>
  </data>
  <data name="HyperbolicCotangent" xml:space="preserve">
    <value>hüperboolne kootangens</value>
    <comment>Name for the hyperbolic cotangent function. Used by screen readers.</comment>
  </data>
  <data name="InverseHyperbolicCotangent" xml:space="preserve">
    <value>hüperboolse kootangensi pöördfunktsioon</value>
    <comment>Name for the inverse hyperbolic cotangent function. Used by screen readers.</comment>
  </data>
  <data name="CubeRoot" xml:space="preserve">
    <value>Kuubi juur</value>
    <comment>Name for the cube root function. Used by screen readers.</comment>
  </data>
  <data name="Logy" xml:space="preserve">
    <value>Logaritmi alus</value>
    <comment>Name for the logbasey function. Used by screen readers.</comment>
  </data>
  <data name="AbsoluteValue" xml:space="preserve">
    <value>Absoluutväärtus</value>
    <comment>Name for the absolute value function. Used by screen readers.</comment>
  </data>
  <data name="LeftShift" xml:space="preserve">
    <value>nihe vasakule</value>
    <comment>Name for the programmer function that shifts bits to the left. Used by screen readers.</comment>
  </data>
  <data name="RightShift" xml:space="preserve">
    <value>nihe paremale</value>
    <comment>Name for the programmer function that shifts bits to the right. Used by screen readers.</comment>
  </data>
  <data name="Factorial" xml:space="preserve">
    <value>faktoriaal</value>
    <comment>Name for the factorial function. Used by screen readers.</comment>
  </data>
  <data name="DegreeMinuteSecond" xml:space="preserve">
    <value>kraad minut sekund</value>
    <comment>Name for the degree minute second (dms) function. Used by screen readers.</comment>
  </data>
  <data name="NaturalLog" xml:space="preserve">
    <value>naturaallogaritm</value>
    <comment>Name for the natural log (ln) function. Used by screen readers.</comment>
  </data>
  <data name="Square" xml:space="preserve">
    <value>ruut</value>
    <comment>Name for the square function. Used by screen readers.</comment>
  </data>
  <data name="YRoot" xml:space="preserve">
    <value>ruutjuur arvust y</value>
    <comment>Name for the y root function. Used by screen readers.</comment>
  </data>
  <data name="NavCategoryItem_AutomationNameFormat" xml:space="preserve">
    <value>%1 %2</value>
    <comment>{Locked='%1','%2'}.  Format string for the accessible name of a Calculator menu item, used by screen readers. "%1" is the item name, e.g. Standard, Programmer, etc. %2 is the category name, e.g. Calculator, Converter. An example when formatted is "Standard Calculator" or "Currency Converter".</comment>
  </data>
  <data name="NavCategoryHeader_AutomationNameFormat" xml:space="preserve">
    <value>Kategooria %1</value>
    <comment>{Locked='%1'} Format string for the accessible name of a Calculator menu category header, used by screen readers. "%1" is the pluralized category name, e.g. Calculators, Converters. An example when formatted is "Calculators category".</comment>
  </data>
  <data name="AboutControlServicesAgreement.Text" xml:space="preserve">
    <value>Microsofti teenuselepe</value>
    <comment>Displayed on a link to the Microsoft Services Agreement in the about this app information</comment>
  </data>
  <data name="UnitAbbreviation_Pyeong" xml:space="preserve">
    <value>Pyeong</value>
    <comment>An abbreviation for a measurement unit of area.</comment>
  </data>
  <data name="UnitName_Pyeong" xml:space="preserve">
    <value>Pyeong</value>
    <comment>A measurement unit for area.</comment>
  </data>
  <data name="AddSubtract_Date_FromHeader.Header" xml:space="preserve">
    <value>Alates</value>
    <comment>From Date Header for AddSubtract Date Picker</comment>
  </data>
  <data name="CalculationResultScrollLeft.[using:Windows.UI.Xaml.Automation]AutomationProperties.Name" xml:space="preserve">
    <value>Keri arvutustulemust vasakule</value>
    <comment>Automation label for the "Scroll Left" button that appears when a calculation result is too large to fit in calculation result text box.</comment>
  </data>
  <data name="CalculationResultScrollRight.[using:Windows.UI.Xaml.Automation]AutomationProperties.Name" xml:space="preserve">
    <value>Keri arvutustulemust paremale</value>
    <comment>Automation label for the "Scroll Right" button that appears when a calculation result is too large to fit in calculation result text box.</comment>
  </data>
  <data name="CalculationFailed" xml:space="preserve">
    <value>Arvutamine nurjus</value>
    <comment>Text displayed when the application is not able to do a calculation</comment>
  </data>
  <data name="logBaseY.[using:Windows.UI.Xaml.Automation]AutomationProperties.Name" xml:space="preserve">
    <value>Logaritmi alus Y</value>
    <comment>Screen reader prompt for the logBaseY button</comment>
  </data>
  <data name="trigButton.Text" xml:space="preserve">
    <value>Trigonomeetria</value>
    <comment>Displayed on the button that contains a flyout for the trig functions in scientific mode.</comment>
  </data>
  <data name="funcButton.Text" xml:space="preserve">
    <value>Funktsioon</value>
    <comment>Displayed on the button that contains a flyout for the general functions in scientific mode.</comment>
  </data>
  <data name="inequalityButton.Text" xml:space="preserve">
    <value>Võrratused</value>
    <comment>Displayed on the button that contains a flyout for the inequality functions.</comment>
  </data>
  <data name="inequalityButton.[using:Windows.UI.Xaml.Automation]AutomationProperties.Name" xml:space="preserve">
    <value>Võrratused</value>
    <comment>Screen reader prompt for the Inequalities button</comment>
  </data>
  <data name="bitwiseButton.Text" xml:space="preserve">
    <value>Bitwise</value>
    <comment>Displayed on the button that contains a flyout for the bitwise functions in programmer mode.</comment>
  </data>
  <data name="bitShiftButton.Text" xml:space="preserve">
    <value>Bitine nihe</value>
    <comment>Displayed on the button that contains a flyout for the bit shift functions in programmer mode.</comment>
  </data>
  <data name="trigShiftButton.[using:Windows.UI.Xaml.Automation]AutomationProperties.Name" xml:space="preserve">
    <value>Pöördfunktsioon</value>
    <comment>Screen reader prompt for the shift button in the trig flyout in scientific mode.</comment>
  </data>
  <data name="hypButton.[using:Windows.UI.Xaml.Automation]AutomationProperties.Name" xml:space="preserve">
    <value>Hüperboolne funktsioon</value>
    <comment>Screen reader prompt for the Calculator button HYP in the scientific flyout keypad</comment>
  </data>
  <data name="secButton.[using:Windows.UI.Xaml.Automation]AutomationProperties.Name" xml:space="preserve">
    <value>Seekans</value>
    <comment>Screen reader prompt for the Calculator button sec in the scientific flyout keypad</comment>
  </data>
  <data name="sechButton.[using:Windows.UI.Xaml.Automation]AutomationProperties.Name" xml:space="preserve">
    <value>Hüperboolne seekans</value>
    <comment>Screen reader prompt for the Calculator button sech in the scientific flyout keypad</comment>
  </data>
  <data name="invsecButton.[using:Windows.UI.Xaml.Automation]AutomationProperties.Name" xml:space="preserve">
    <value>Arkusseekans</value>
    <comment>Screen reader prompt for the Calculator button arc sec in the scientific flyout keypad</comment>
  </data>
  <data name="invsechButton.[using:Windows.UI.Xaml.Automation]AutomationProperties.Name" xml:space="preserve">
    <value>Hüperboolne arkusseekans</value>
    <comment>Screen reader prompt for the Calculator button arc sec in the scientific flyout keypad</comment>
  </data>
  <data name="cscButton.[using:Windows.UI.Xaml.Automation]AutomationProperties.Name" xml:space="preserve">
    <value>Koosekans</value>
    <comment>Screen reader prompt for the Calculator button csc in the scientific flyout keypad</comment>
  </data>
  <data name="cschButton.[using:Windows.UI.Xaml.Automation]AutomationProperties.Name" xml:space="preserve">
    <value>Hüperboolne koosekans</value>
    <comment>Screen reader prompt for the Calculator button csch in the scientific flyout keypad</comment>
  </data>
  <data name="invcscButton.[using:Windows.UI.Xaml.Automation]AutomationProperties.Name" xml:space="preserve">
    <value>Arkuskoosekans</value>
    <comment>Screen reader prompt for the Calculator button arc csc in the scientific flyout keypad</comment>
  </data>
  <data name="invcschButton.[using:Windows.UI.Xaml.Automation]AutomationProperties.Name" xml:space="preserve">
    <value>Hüperboolne arkuskoosekans</value>
    <comment>Screen reader prompt for the Calculator button arc csc in the scientific flyout keypad</comment>
  </data>
  <data name="cotButton.[using:Windows.UI.Xaml.Automation]AutomationProperties.Name" xml:space="preserve">
    <value>Kootangens</value>
    <comment>Screen reader prompt for the Calculator button cot in the scientific flyout keypad</comment>
  </data>
  <data name="cothButton.[using:Windows.UI.Xaml.Automation]AutomationProperties.Name" xml:space="preserve">
    <value>Hüperboolne kootangens</value>
    <comment>Screen reader prompt for the Calculator button coth in the scientific flyout keypad</comment>
  </data>
  <data name="invcotButton.[using:Windows.UI.Xaml.Automation]AutomationProperties.Name" xml:space="preserve">
    <value>Arkuskootangens</value>
    <comment>Screen reader prompt for the Calculator button arc cot in the scientific flyout keypad</comment>
  </data>
  <data name="invcothButton.[using:Windows.UI.Xaml.Automation]AutomationProperties.Name" xml:space="preserve">
    <value>Hüperboolne arkuskootangens</value>
    <comment>Screen reader prompt for the Calculator button arc coth in the scientific flyout keypad</comment>
  </data>
  <data name="floorButton.[using:Windows.UI.Xaml.Automation]AutomationProperties.Name" xml:space="preserve">
    <value>Korrus</value>
    <comment>Screen reader prompt for the Calculator button floor in the scientific flyout keypad</comment>
  </data>
  <data name="ceilButton.[using:Windows.UI.Xaml.Automation]AutomationProperties.Name" xml:space="preserve">
    <value>Ülemmäär</value>
    <comment>Screen reader prompt for the Calculator button ceiling in the scientific flyout keypad</comment>
  </data>
  <data name="randButton.[using:Windows.UI.Xaml.Automation]AutomationProperties.Name" xml:space="preserve">
    <value>Juhuslik</value>
    <comment>Screen reader prompt for the Calculator button random in the scientific flyout keypad</comment>
  </data>
  <data name="absButton.[using:Windows.UI.Xaml.Automation]AutomationProperties.Name" xml:space="preserve">
    <value>Absoluutväärtus</value>
    <comment>Screen reader prompt for the Calculator button abs in the scientific flyout keypad</comment>
  </data>
  <data name="eulerButton.[using:Windows.UI.Xaml.Automation]AutomationProperties.Name" xml:space="preserve">
    <value>Euleri number</value>
    <comment>Screen reader prompt for the Calculator button e in the scientific flyout keypad</comment>
  </data>
  <data name="twoPowerXButton.[using:Windows.UI.Xaml.Automation]AutomationProperties.Name" xml:space="preserve">
    <value>Kahe aste</value>
    <comment>Screen reader prompt for the Calculator button 2^x in the scientific flyout keypad</comment>
  </data>
  <data name="nandButton.[using:Windows.UI.Xaml.Automation]AutomationProperties.Name" xml:space="preserve">
    <value>Nand</value>
    <comment>Screen reader prompt for the Calculator button nand in the scientific flyout keypad</comment>
  </data>
  <data name="nandButton.[using:CalculatorApp.Controls]CalculatorButton.AuditoryFeedback" xml:space="preserve">
    <value>Nand</value>
    <comment>Auditory feedback for screen reader users. Users will hear "Display is 7 nand" when the button is pressed. NAND is a mathematical operation on two binary values.</comment>
  </data>
  <data name="norButton.[using:Windows.UI.Xaml.Automation]AutomationProperties.Name" xml:space="preserve">
    <value>Ega</value>
    <comment>Screen reader prompt for the Calculator button nor in the scientific flyout keypad</comment>
  </data>
  <data name="norButton.[using:CalculatorApp.Controls]CalculatorButton.AuditoryFeedback" xml:space="preserve">
    <value>Ega</value>
    <comment>Auditory feedback for screen reader users. Users will hear "Display is 7 nor" when the button is pressed. NAND is a mathematical operation on two binary values.</comment>
  </data>
  <data name="rolCarryButton.[using:Windows.UI.Xaml.Automation]AutomationProperties.Name" xml:space="preserve">
    <value>Pööra vasakule koos kandega</value>
    <comment>Screen reader prompt for the Calculator button rol with carry in the scientific flyout keypad</comment>
  </data>
  <data name="rorCarryButton.[using:Windows.UI.Xaml.Automation]AutomationProperties.Name" xml:space="preserve">
    <value>Pööra paremale koos kandega</value>
    <comment>Screen reader prompt for the Calculator button ror with carry in the scientific flyout keypad</comment>
  </data>
  <data name="lshLogicalButton.[using:Windows.UI.Xaml.Automation]AutomationProperties.Name" xml:space="preserve">
    <value>Nihe vasakule</value>
    <comment>Screen reader prompt for the Calculator button lshLogical in the scientific flyout keypad</comment>
  </data>
  <data name="lshLogicalButton.[using:CalculatorApp.Controls]CalculatorButton.AuditoryFeedback" xml:space="preserve">
    <value>Nihe vasakule</value>
    <comment>Auditory feedback for screen reader users. Users will hear "Display is 7 left shift" when the button is pressed. NAND is a mathematical operation on two binary values.</comment>
  </data>
  <data name="rshLogicalButton.[using:Windows.UI.Xaml.Automation]AutomationProperties.Name" xml:space="preserve">
    <value>Nihe paremale</value>
    <comment>Screen reader prompt for the Calculator button rshLogical in the scientific flyout keypad</comment>
  </data>
  <data name="rshLogicalButton.[using:CalculatorApp.Controls]CalculatorButton.AuditoryFeedback" xml:space="preserve">
    <value>Nihe paremale</value>
    <comment>Auditory feedback for screen reader users. Users will hear "Display is 7 right shift" when the button is pressed. NAND is a mathematical operation on two binary values.</comment>
  </data>
  <data name="arithmeticShiftButton.Content" xml:space="preserve">
    <value>Aritmeetiline nihe</value>
    <comment>Label for a radio button that toggles arithmetic shift behavior for the shift operations.</comment>
  </data>
  <data name="logicalShiftButton.Content" xml:space="preserve">
    <value>Loogiline nihe</value>
    <comment>Label for a radio button that toggles logical shift behavior for the shift operations.</comment>
  </data>
  <data name="rotateCircularButton.Content" xml:space="preserve">
    <value>Pööra ringikujuline nihe</value>
    <comment>Label for a radio button that toggles rotate circular behavior for the shift operations.</comment>
  </data>
  <data name="rotateCarryShiftButton.Content" xml:space="preserve">
    <value>Pööra läbi kande ringikujuline nihe</value>
    <comment>Label for a radio button that toggles rotate circular with carry behavior for the shift operations.</comment>
  </data>
  <data name="cubeRootButton.[using:Windows.UI.Xaml.Automation]AutomationProperties.Name" xml:space="preserve">
    <value>Kuubi juur</value>
    <comment>Screen reader prompt for the cube root button on the scientific operator keypad</comment>
  </data>
  <data name="trigButton.[using:Windows.UI.Xaml.Automation]AutomationProperties.Name" xml:space="preserve">
    <value>Trigonomeetria</value>
    <comment>Screen reader prompt for the square root button on the scientific operator keypad</comment>
  </data>
  <data name="funcButton.[using:Windows.UI.Xaml.Automation]AutomationProperties.Name" xml:space="preserve">
    <value>Funktsioonid</value>
    <comment>Screen reader prompt for the square root button on the scientific operator keypad</comment>
  </data>
  <data name="bitwiseButton.[using:Windows.UI.Xaml.Automation]AutomationProperties.Name" xml:space="preserve">
    <value>Bitwise</value>
    <comment>Screen reader prompt for the square root button on the scientific operator keypad</comment>
  </data>
  <data name="bitShiftButton.[using:Windows.UI.Xaml.Automation]AutomationProperties.Name" xml:space="preserve">
    <value>Bitshift</value>
    <comment>Screen reader prompt for the square root button on the scientific operator keypad</comment>
  </data>
  <data name="ScientificOperatorPanel.[using:Windows.UI.Xaml.Automation]AutomationProperties.Name" xml:space="preserve">
    <value>Teaduslike tehtemärkide paneelid</value>
    <comment>Screen reader prompt for the Scientific Operator Panels on the scientific operator keypad</comment>
  </data>
  <data name="ProgrammerOperatorPanel.[using:Windows.UI.Xaml.Automation]AutomationProperties.Name" xml:space="preserve">
    <value>Programmeerija tehtemärkide paneelid</value>
    <comment>Screen reader prompt for the Programmer Operator Panels on the programmer operator keypad</comment>
  </data>
  <data name="MostSignificantBit" xml:space="preserve">
    <value>kõrgeim bitt</value>
    <comment>Used to describe the last bit of a binary number. Used in bit flip</comment>
  </data>
  <data name="GraphingCalculatorModeText" xml:space="preserve">
    <value>Graafiline esitus</value>
    <comment>Name of the Graphing mode of the Calculator app. Displayed in the navigation menu.</comment>
  </data>
  <data name="plotButton.[using:Windows.UI.Xaml.Automation]AutomationProperties.Name" xml:space="preserve">
    <value>Diagramm</value>
    <comment>Screen reader prompt for the plot button on the graphing calculator operator keypad</comment>
  </data>
  <data name="graphViewButton.[using:Windows.UI.Xaml.Controls]ToolTipService.ToolTip" xml:space="preserve">
    <value>Värskenda vaadet automaatselt (Ctrl + 0)</value>
    <comment>This is the tool tip automation name for the Calculator graph view button.</comment>
  </data>
  <data name="graphViewButton.[using:Windows.UI.Xaml.Automation]AutomationProperties.Name" xml:space="preserve">
    <value>Graafiku vaade</value>
    <comment>Screen reader prompt for the graph view button.</comment>
  </data>
  <data name="GraphViewAutomaticBestFitAnnouncement" xml:space="preserve">
    <value>Automaatne parim sobivus</value>
    <comment>Announcement used in Graphing Calculator when graph view button is clicked and automatic best fit is set</comment>
  </data>
  <data name="GraphViewManualAdjustmentAnnouncement" xml:space="preserve">
    <value>Käsitsi korrigeerimine</value>
    <comment>Announcement used in Graphing Calculator when graph view button is clicked and manual adjustment is set</comment>
  </data>
  <data name="GridResetAnnouncement" xml:space="preserve">
    <value>Graafivaade on lähtestatud</value>
    <comment>Announcement used in Graphing Calculator when graph view button is clicked and automatic best fit is set, resetting the graph</comment>
  </data>
  <data name="zoomInButton.[using:Windows.UI.Xaml.Controls]ToolTipService.ToolTip" xml:space="preserve">
    <value>Suurenda (Ctrl + plussmärgiklahv)</value>
    <comment>This is the tool tip automation name for the Calculator zoom in button.</comment>
  </data>
  <data name="zoomInButton.[using:Windows.UI.Xaml.Automation]AutomationProperties.Name" xml:space="preserve">
    <value>Suurenda</value>
    <comment>Screen reader prompt for the zoom in button.</comment>
  </data>
  <data name="zoomOutButton.[using:Windows.UI.Xaml.Controls]ToolTipService.ToolTip" xml:space="preserve">
    <value>Vähenda (Ctrl + miinusmärgiklahv)</value>
    <comment>This is the tool tip automation name for the Calculator zoom out button.</comment>
  </data>
  <data name="zoomOutButton.[using:Windows.UI.Xaml.Automation]AutomationProperties.Name" xml:space="preserve">
    <value>Vähenda</value>
    <comment>Screen reader prompt for the zoom out button.</comment>
  </data>
  <data name="EquationTextBoxAddPanel.[using:Windows.UI.Xaml.Controls]ToolTipService.ToolTip" xml:space="preserve">
    <value>Lisa võrrand</value>
    <comment>Placeholder text for the equation input button</comment>
  </data>
  <data name="ShareActionErrorMessage" xml:space="preserve">
    <value>Jagamine pole praegu võimalik.</value>
    <comment>If there is an error in the sharing action will display a dialog with this text.</comment>
  </data>
  <data name="ShareActionErrorOk" xml:space="preserve">
    <value>OK</value>
    <comment>Used on the dismiss button of the share action error dialog.</comment>
  </data>
  <data name="ShareActionTitle" xml:space="preserve">
    <value>Vaadake, millest ma Windowsi kalkulaatoriga graafiku koostasin</value>
    <comment>Sent as part of the shared content. The title for the share.</comment>
  </data>
  <data name="EquationsShareHeader" xml:space="preserve">
    <value>Võrrandid</value>
    <comment>Header that appears over the equations section when sharing</comment>
  </data>
  <data name="VariablesShareHeader" xml:space="preserve">
    <value>Muutujad</value>
    <comment>Header that appears over the variables section when sharing</comment>
  </data>
  <data name="GraphImageAltText" xml:space="preserve">
    <value>Võrranditega graafiku pilt</value>
    <comment>Alt text for the graph image when output via Share</comment>
  </data>
  <data name="VaiablesHeader.Text" xml:space="preserve">
    <value>Muutujad</value>
    <comment>Header text for variables area</comment>
  </data>
  <data name="StepTextBlock.Text" xml:space="preserve">
    <value>Etapp</value>
    <comment>Label text for the step text box</comment>
  </data>
  <data name="MinTextBlock.Text" xml:space="preserve">
    <value>Min</value>
    <comment>Label text for the min text box</comment>
  </data>
  <data name="MaxTextBlock.Text" xml:space="preserve">
    <value>Max</value>
    <comment>Label text for the max text box</comment>
  </data>
  <data name="LineColorText.Text" xml:space="preserve">
    <value>Värv</value>
    <comment>Label for the Line Color section of the style picker</comment>
  </data>
  <data name="StyleChooserBoxHeading.Text" xml:space="preserve">
    <value>Laad</value>
    <comment>Label for the Line Style section of the style picker</comment>
  </data>
  <data name="KeyGraphFeaturesLabel.Text" xml:space="preserve">
    <value>Funktsiooni analüüs</value>
    <comment>Title for KeyGraphFeatures Control</comment>
  </data>
  <data name="KGFHorizontalAsymptotesNone" xml:space="preserve">
    <value>Funktsioonil pole horisontaalseid asümptoote.</value>
    <comment>Message displayed when the graph does not have any horizontal asymptotes</comment>
  </data>
  <data name="KGFInflectionPointsNone" xml:space="preserve">
    <value>Funktsioonil pole käänupunkte.</value>
    <comment>Message displayed when the graph does not have any inflection points</comment>
  </data>
  <data name="KGFMaximaNone" xml:space="preserve">
    <value>Funktsioonil pole maksimumpunkte.</value>
    <comment>Message displayed when the graph does not have any maxima</comment>
  </data>
  <data name="KGFMinimaNone" xml:space="preserve">
    <value>Funktsioonil pole miinimumpunkte.</value>
    <comment>Message displayed when the graph does not have any minima</comment>
  </data>
  <data name="KGFMonotonicityConstant" xml:space="preserve">
    <value>Konstant</value>
    <comment>String describing constant monotonicity of a function</comment>
  </data>
  <data name="KGFMonotonicityDecreasing" xml:space="preserve">
    <value>Vähenev</value>
    <comment>String describing decreasing monotonicity of a function</comment>
  </data>
  <data name="KGFMonotonicityError" xml:space="preserve">
    <value>Funktsiooni monotoonsust ei saa määratleda.</value>
    <comment>Error displayed when monotonicity cannot be determined</comment>
  </data>
  <data name="KGFMonotonicityIncreasing" xml:space="preserve">
    <value>Suurenev</value>
    <comment>String describing increasing monotonicity of a function</comment>
  </data>
  <data name="KGFMonotonicityUnknown" xml:space="preserve">
    <value>Funktsiooni monotoonsus pole teada.</value>
    <comment>Error displayed when monotonicity is unknown</comment>
  </data>
  <data name="KGFObliqueAsymptotesNone" xml:space="preserve">
    <value>Funktsioonil pole kaldasümptoote.</value>
    <comment>Message displayed when the graph does not have any oblique asymptotes</comment>
  </data>
  <data name="KGFParityError" xml:space="preserve">
    <value>Funktsiooni paarsust ei saa määratleda.</value>
    <comment>Error displayed when parity is cannot be determined</comment>
  </data>
  <data name="KGFParityEven" xml:space="preserve">
    <value>Funktsioon on paaris.</value>
    <comment>Message displayed with the function parity is even</comment>
  </data>
  <data name="KGFParityNeither" xml:space="preserve">
    <value>Funktsioon pole paaris ega paaritu.</value>
    <comment>Message displayed with the function parity is neither even nor odd</comment>
  </data>
  <data name="KGFParityOdd" xml:space="preserve">
    <value>Funktsioon on paaritu.</value>
    <comment>Message displayed with the function parity is odd</comment>
  </data>
  <data name="KGFParityUnknown" xml:space="preserve">
    <value>Funktsiooni paarsus pole teada.</value>
    <comment>Error displayed when parity is unknown</comment>
  </data>
  <data name="KGFPeriodicityError" xml:space="preserve">
    <value>See funktsioon ei toeta perioodilisust.</value>
    <comment>Error displayed when periodicity is not supported</comment>
  </data>
  <data name="KGFPeriodicityNotPeriodic" xml:space="preserve">
    <value>Funktsioon pole perioodiline.</value>
    <comment>Message displayed with the function periodicity is not periodic</comment>
  </data>
  <data name="KGFPeriodicityUnknown" xml:space="preserve">
    <value>Funktsiooni perioodilisus pole teada.</value>
    <comment>Message displayed with the function periodicity is unknown</comment>
  </data>
  <data name="KGFTooComplexFeaturesError" xml:space="preserve">
    <value>Need funktsioonid on kalkulaatori jaoks arvutamiseks liiga keerukad:</value>
    <comment>Error displayed when analysis features cannot be calculated</comment>
  </data>
  <data name="KGFVerticalAsymptotesNone" xml:space="preserve">
    <value>Funktsioonil pole vertikaalseid asümptoote.</value>
    <comment>Message displayed when the graph does not have any vertical asymptotes</comment>
  </data>
  <data name="KGFXInterceptNone" xml:space="preserve">
    <value>Funktsioonil pole ühtegi x-telje lõikepunkti.</value>
    <comment>Message displayed when the graph does not have any x-intercepts</comment>
  </data>
  <data name="KGFYInterceptNone" xml:space="preserve">
    <value>Funktsioonil pole ühtegi y-telje lõikepunkti.</value>
    <comment>Message displayed when the graph does not have any y-intercepts</comment>
  </data>
  <data name="Domain" xml:space="preserve">
    <value>Domeen</value>
    <comment>Title for KeyGraphFeatures Domain Property</comment>
  </data>
  <data name="HorizontalAsymptotes" xml:space="preserve">
    <value>Horisontaalsed asümptoodid</value>
    <comment>Title for KeyGraphFeatures Horizontal aysmptotes Property</comment>
  </data>
  <data name="InflectionPoints" xml:space="preserve">
    <value>Käänupunktid</value>
    <comment>Title for KeyGraphFeatures Inflection points Property</comment>
  </data>
  <data name="KGFAnalysisNotSupported" xml:space="preserve">
    <value>See funktsioon ei toeta analüüsi.</value>
    <comment>Error displayed when graph analysis is not supported or had an error.</comment>
  </data>
  <data name="KGFVariableIsNotX" xml:space="preserve">
    <value>Analüüsi toetatakse ainult f(x)-vormingus funktsioonide korral. Näide: y=x</value>
    <comment>Error displayed when graph analysis detects the function format is not f(x).</comment>
  </data>
  <data name="Maxima" xml:space="preserve">
    <value>Maksimum</value>
    <comment>Title for KeyGraphFeatures Maxima Property</comment>
  </data>
  <data name="Minima" xml:space="preserve">
    <value>Miinimum</value>
    <comment>Title for KeyGraphFeatures Minima Property</comment>
  </data>
  <data name="Monotonicity" xml:space="preserve">
    <value>Monotoonsus</value>
    <comment>Title for KeyGraphFeatures Monotonicity Property</comment>
  </data>
  <data name="ObliqueAsymptotes" xml:space="preserve">
    <value>Kaldasümptoodid</value>
    <comment>Title for KeyGraphFeatures Oblique asymptotes Property</comment>
  </data>
  <data name="Parity" xml:space="preserve">
    <value>Paarsus</value>
    <comment>Title for KeyGraphFeatures Parity Property</comment>
  </data>
  <data name="Periodicity" xml:space="preserve">
    <value>Perioodilisus</value>
    <comment>Title for KeyGraphFeatures Periodicity Property. The period of a mathematical function is the smallest interval in its input values such that its output values repeat every such interval.</comment>
  </data>
  <data name="Range" xml:space="preserve">
    <value>Vahemik</value>
    <comment>Title for KeyGraphFeatures Range Property</comment>
  </data>
  <data name="VerticalAsymptotes" xml:space="preserve">
    <value>Vertikaalsed asümptoodid</value>
    <comment>Title for KeyGraphFeatures Vertical asymptotes Property</comment>
  </data>
  <data name="XIntercept" xml:space="preserve">
    <value>X-telje lõikepunkt</value>
    <comment>Title for KeyGraphFeatures XIntercept Property</comment>
  </data>
  <data name="YIntercept" xml:space="preserve">
    <value>Y-telje lõikepunkt</value>
    <comment>Title for KeyGraphFeatures YIntercept Property</comment>
  </data>
  <data name="KGFAnalysisCouldNotBePerformed" xml:space="preserve">
    <value>Funktsiooni analüüsi ei saanud teha.</value>
  </data>
  <data name="KGFDomainNone" xml:space="preserve">
    <value>Selle funktsiooni jaoks ei saanud domeeni arvutada.</value>
    <comment>Error displayed when Domain is not returned from the analyzer.</comment>
  </data>
  <data name="KGFRangeNone" xml:space="preserve">
    <value>Selle funktsiooni vahemikku ei saa arvutada.</value>
    <comment>Error displayed when Range is not returned from the analyzer.</comment>
  </data>
  <data name="Overflow" xml:space="preserve">
    <value>Ületäitumine (arv on liiga suur)</value>
    <comment>Error that occurs during graphing when the number is too large. To see this error, assign a large number to variable a, then keep doing "a:=a*a" until it happens.</comment>
  </data>
  <data name="RequireRadiansMode" xml:space="preserve">
    <value>Selle võrrandi graafiliseks esitamiseks on vaja kasutada radiaanirežiimi.</value>
    <comment>Error that occurs during graphing when radians is required.</comment>
  </data>
  <data name="TooComplexToSolve" xml:space="preserve">
    <value>See funktsioon on graafiliseks esitamiseks liiga keerukas</value>
    <comment>Error that occurs during graphing when the equation is too complex.</comment>
  </data>
  <data name="RequireDegreesMode" xml:space="preserve">
    <value>Selle võrrandi graafiliseks esitamiseks on vaja kasutada kraadirežiimi.</value>
    <comment>Error that occurs during graphing when degrees is required</comment>
  </data>
  <data name="FactorialInvalidArgument" xml:space="preserve">
    <value>Faktoriaalfunktsioon sisaldab kehtetut argumenti</value>
    <comment>Error that occurs during graphing when a factorial function has an invalid argument.</comment>
  </data>
  <data name="FactorialCannotPerformOnLargeNumber" xml:space="preserve">
    <value>Faktoriaalfunktsioon sisaldab graafiliselt esitamiseks liiga suurt argumenti</value>
    <comment>Error that occurs during graphing when a factorial has a large n</comment>
  </data>
  <data name="ModuloCannotPerformOnFloat" xml:space="preserve">
    <value>Mooduli järgi arvutamist saab kasutada ainult täisarvudega</value>
    <comment>Error that occurs during graphing when modulo is used with a float.</comment>
  </data>
  <data name="EquationHasNoSolution" xml:space="preserve">
    <value>Võrrandil pole lahendust</value>
    <comment>Error that occurs during graphing when the equation has no solution.</comment>
  </data>
  <data name="DivideByZero" xml:space="preserve">
    <value>Nulliga ei saa jagada</value>
    <comment>Error that occurs during graphing when a divison by zero occurs.</comment>
  </data>
  <data name="MutuallyExclusiveConditions" xml:space="preserve">
    <value>Võrrand sisaldab üksteist välistavaid loogilisi tingimusi</value>
    <comment>Error that occurs during graphing when mutually exclusive conditions are used.</comment>
  </data>
  <data name="OutOfDomain" xml:space="preserve">
    <value>Võrrand jääb piirkonnast välja</value>
    <comment>Error that occurs during graphing when the equation is out of domain.</comment>
  </data>
  <data name="GE_NotSupported" xml:space="preserve">
    <value>Selle võrrandi graafilist esitust ei toetata</value>
    <comment>Error that occurs during graphing when the equation is not supported.</comment>
  </data>
  <data name="ParenthesisMismatch" xml:space="preserve">
    <value>Võrrandis puudub avasulg</value>
    <comment>Error that occurs during graphing when the equation is missing a (</comment>
  </data>
  <data name="UnmatchedParenthesis" xml:space="preserve">
    <value>Võrrandis puudub lõpusulg</value>
    <comment>Error that occurs during graphing when the equation is missing a )</comment>
  </data>
  <data name="TooManyDecimalPoints" xml:space="preserve">
    <value>Arv on liiga palju kümnendkohti</value>
    <comment>Error that occurs during graphing when a number has too many decimals. Ex: 1.2.3</comment>
  </data>
  <data name="DecimalPointWithoutDigits" xml:space="preserve">
    <value>Komakohale ei järgne numbreid</value>
    <comment>Error that occurs during graphing with a decimal point without digits</comment>
  </data>
  <data name="UnexpectedEndOfExpression" xml:space="preserve">
    <value>Avaldis lõpeb ootamatult</value>
    <comment>Error that occurs during graphing when the expression ends unexpectedly. Ex: 3-4*</comment>
  </data>
  <data name="UnexpectedToken" xml:space="preserve">
    <value>Avaldis sisaldab ootamatuid märke</value>
    <comment>Error that occurs during graphing when there is an unexpected token.</comment>
  </data>
  <data name="InvalidToken" xml:space="preserve">
    <value>Avaldis sisaldab sobimatuid märke</value>
    <comment>Error that occurs during graphing when there is an invalid token.</comment>
  </data>
  <data name="TooManyEquals" xml:space="preserve">
    <value>Võrdusmärke on liiga palju</value>
    <comment>Error that occurs during graphing when there are too many equals.</comment>
  </data>
  <data name="EqualWithoutGraphVariable" xml:space="preserve">
    <value>Funktsioon peab sisaldama vähemalt ühte x- või y-muutujat</value>
    <comment>Error that occurs during graphing when the equation is missing x or y.</comment>
  </data>
  <data name="InvalidEquationSyntax" xml:space="preserve">
    <value>Kehtetu avaldis</value>
    <comment>Error that occurs during graphing when an invalid syntax is used.</comment>
  </data>
  <data name="EmptyExpression" xml:space="preserve">
    <value>Avaldis on tühi</value>
    <comment>Error that occurs during graphing when the expression is empty</comment>
  </data>
  <data name="EqualWithoutEquation" xml:space="preserve">
    <value>Võrdusmärki kasutati ilma võrrandita</value>
    <comment>Error that occurs during graphing when equal is used without an equation. Ex: sin(x=y)</comment>
  </data>
  <data name="ExpectParenthesisAfterFunctionName" xml:space="preserve">
    <value>Funktsioonime järel on sulg puudu</value>
    <comment>Error that occurs during graphing when parenthesis are missing after a function.</comment>
  </data>
  <data name="IncorrectNumParameter" xml:space="preserve">
    <value>Matemaatikatehtel on vale arv parameetreid</value>
    <comment>Error that occurs during graphing when a function has the wrong number of parameters</comment>
  </data>
  <data name="InvalidVariableNameFormat" xml:space="preserve">
    <value>Muutuja nimi ei sobi</value>
    <comment>Error that occurs during graphing when a variable name is invalid.</comment>
  </data>
  <data name="BracketMismatch" xml:space="preserve">
    <value>Võrrandis puudub avanurksulg</value>
    <comment>Error that occurs during graphing when a { is missing</comment>
  </data>
  <data name="UnmatchedBracket" xml:space="preserve">
    <value>Võrrandis puudub lõpunurksulg</value>
    <comment>Error that occurs during graphing when a } is missing.</comment>
  </data>
  <data name="CannotUseIInReal" xml:space="preserve">
    <value>Tähti "i" ja "I" ei saa muutujate nimedena kasutada</value>
    <comment>Error that occurs during graphing when i or I is used.</comment>
  </data>
  <data name="GeneralError" xml:space="preserve">
    <value>Võrrandit ei saanud graafiliselt esitada</value>
    <comment>General error that occurs during graphing.</comment>
  </data>
  <data name="InvalidNumberDigit" xml:space="preserve">
    <value>Numbrit ei saanud selle aluse jaoks lahendada</value>
    <comment>Error that occurs during graphing when trying to use bases incorrect. Ex: base(2,1020).</comment>
  </data>
  <data name="InvalidNumberBase" xml:space="preserve">
    <value>Alus peab olema suurem kui 2 ja väiksem kui 36</value>
    <comment>Error that occurs during graphing when the base is out of range.</comment>
  </data>
  <data name="InvalidVariableSpecification" xml:space="preserve">
    <value>Ühe matemaatikatehte üks parameeter peab olema muutuja</value>
    <comment>Error that occurs during graphing when a function requires a variable in a particular position. Ex: 2nd argument of deriv.</comment>
  </data>
  <data name="ExpectingLogicalOperands" xml:space="preserve">
    <value>Võrrandis on loogilisi ja skalaaroperande segamini kasutatud</value>
    <comment>Error that occurs during graphing when operands are mixed. Such as true and 1.</comment>
  </data>
  <data name="CannotUseIndexVarInOpLimits" xml:space="preserve">
    <value>x-i ega y-d ei saa ülemise ega alumise piirväärtuse jaoks kasutada</value>
    <comment>Error that occurs during graphing when x or y is used in integral upper limits.</comment>
  </data>
  <data name="CannotUseIndexVarInLimPoint" xml:space="preserve">
    <value>x-i ega y-d ei saa piirpunktis kasutada</value>
    <comment>Error that occurs during graphing when x or y is used in the limit point.</comment>
  </data>
  <data name="CannotUseComplexInfinityInReal" xml:space="preserve">
    <value>Komplekslõpmatust ei saa kasutada</value>
    <comment>Error that occurs during graphing when complex infinity is used</comment>
  </data>
  <data name="CannotUseIInInequalitySolving" xml:space="preserve">
    <value>Võrratustes ei saa kompleksarve kasutada</value>
    <comment>Error that occurs during graphing when complex numbers are used in inequalities.</comment>
  </data>
  <data name="equationAnalysisBack.[using:Windows.UI.Xaml.Controls]ToolTipService.ToolTip" xml:space="preserve">
    <value>Tagasi funktsioonide loendisse</value>
    <comment>This is the tooltip for the back button in the equation analysis page in the graphing calculator</comment>
  </data>
  <data name="equationAnalysisBack.[using:Windows.UI.Xaml.Automation]AutomationProperties.Name" xml:space="preserve">
    <value>Tagasi funktsioonide loendisse</value>
    <comment>This is the automation name for the back button in the equation analysis page in the graphing calculator</comment>
  </data>
  <data name="functionAnalysisButton.[using:Windows.UI.Xaml.Controls]ToolTipService.ToolTip" xml:space="preserve">
    <value>Analüüsi funktsiooni</value>
    <comment>This is the tooltip for the analyze function button</comment>
  </data>
  <data name="functionAnalysisButton.[using:Windows.UI.Xaml.Automation]AutomationProperties.Name" xml:space="preserve">
    <value>Analüüsi funktsiooni</value>
    <comment>This is the automation name for the analyze function button</comment>
  </data>
  <data name="functionAnalysisMenuItem" xml:space="preserve">
    <value>Analüüsi funktsiooni</value>
    <comment>This is the text for the for the analyze function context menu command</comment>
  </data>
  <data name="removeButton.[using:Windows.UI.Xaml.Controls]ToolTipService.ToolTip" xml:space="preserve">
    <value>Saate eemaldada võrrandi</value>
    <comment>This is the tooltip for the graphing calculator remove equation buttons</comment>
  </data>
  <data name="removeButton.[using:Windows.UI.Xaml.Automation]AutomationProperties.Name" xml:space="preserve">
    <value>Eemalda võrrand</value>
    <comment>This is the automation name for the graphing calculator remove equation buttons</comment>
  </data>
  <data name="removeMenuItem" xml:space="preserve">
    <value>Eemalda võrrand</value>
    <comment>This is the text for the for the remove equation context menu command</comment>
  </data>
  <data name="shareButton.[using:Windows.UI.Xaml.Controls]ToolTipService.ToolTip" xml:space="preserve">
    <value>Jaga</value>
    <comment>This is the automation name for the graphing calculator share button.</comment>
  </data>
  <data name="shareButton.[using:Windows.UI.Xaml.Automation]AutomationProperties.Name" xml:space="preserve">
    <value>Jaga</value>
    <comment>This is the tooltip for the graphing calculator share button.</comment>
  </data>
  <data name="colorChooserButton.[using:Windows.UI.Xaml.Controls]ToolTipService.ToolTip" xml:space="preserve">
    <value>Saate muuta võrrandi laadi</value>
    <comment>This is the tooltip for the graphing calculator equation style button</comment>
  </data>
  <data name="colorChooserButton.[using:Windows.UI.Xaml.Automation]AutomationProperties.Name" xml:space="preserve">
    <value>Muuda võrrandi laadi</value>
    <comment>This is the automation name for the graphing calculator equation style button</comment>
  </data>
  <data name="colorChooserMenuItem" xml:space="preserve">
    <value>Muuda võrrandi laadi</value>
    <comment>This is the text for the for the equation style context menu command</comment>
  </data>
  <data name="showEquationButtonToolTip" xml:space="preserve">
    <value>Kuva võrrand</value>
    <comment>This is the tooltip/automation name shown when visibility is set to hidden in the graphing calculator.</comment>
  </data>
  <data name="hideEquationButtonToolTip" xml:space="preserve">
    <value>Peida võrrand</value>
    <comment>This is the tooltip/automation name shown when visibility is set to visible in the graphing calculator.</comment>
  </data>
  <data name="showEquationButtonAutomationName" xml:space="preserve">
    <value>Kuva võrrand %1</value>
    <comment>{Locked="%1"}, This is the tooltip/automation name shown when visibility is set to hidden in the graphing calculator. %1 is the equation number.</comment>
  </data>
  <data name="hideEquationButtonAutomationName" xml:space="preserve">
    <value>Peida võrrand %1</value>
    <comment>{Locked="%1"}, This is the tooltip/automation name shown when visibility is set to visible in the graphing calculator. %1 is the equation number.</comment>
  </data>
  <data name="disableTracingButtonToolTip" xml:space="preserve">
    <value>Lõpeta jälgimine</value>
    <comment>This is the tooltip/automation name for the graphing calculator stop tracing button</comment>
  </data>
  <data name="enableTracingButtonToolTip" xml:space="preserve">
    <value>Alusta jälgimist</value>
    <comment>This is the tooltip/automation name for the graphing calculator start tracing button</comment>
  </data>
  <data name="graphAutomationName" xml:space="preserve">
    <value>Graphi kuvamine akna, x-telje bounded %1 ja %2, y-telje bounded %3 ja %4, kuvamine %5 võrrandid</value>
    <comment>{Locked="%1","%2", "%3", "%4", "%5"}. </comment>
  </data>
  <data name="sliderOptionsButton.[using:Windows.UI.Xaml.Controls]ToolTipService.ToolTip" xml:space="preserve">
    <value>Saate konfigureerida liuguri</value>
    <comment>This is the tooltip text for the slider options button in Graphing Calculator</comment>
  </data>
  <data name="sliderOptionsButton.[using:Windows.UI.Xaml.Automation]AutomationProperties.Name" xml:space="preserve">
    <value>Konfigureeri liugur</value>
    <comment>This is the automation name text for the slider options button in Graphing Calculator</comment>
  </data>
  <data name="GraphSwitchToEquationMode" xml:space="preserve">
    <value>Aktiveeri võrrandirežiim</value>
    <comment>Used in Graphing Calculator to switch the view to the equation mode</comment>
  </data>
  <data name="GraphSwitchToGraphMode" xml:space="preserve">
    <value>Saate aktiveerida graafikurežiimi</value>
    <comment>Used in Graphing Calculator to switch the view to the graph mode</comment>
  </data>
  <data name="SwitchModeToggleButton.[using:Windows.UI.Xaml.Automation]AutomationProperties.Name" xml:space="preserve">
    <value>Aktiveeri võrrandirežiim</value>
    <comment>Used in Graphing Calculator to switch the view to the equation mode</comment>
  </data>
  <data name="GraphSwitchedToEquationModeAnnouncement" xml:space="preserve">
    <value>Praegune režiim on võrrandirežiim</value>
    <comment>Announcement used in Graphing Calculator when switching to the equation mode</comment>
  </data>
  <data name="GraphSwitchedToGraphModeAnnouncement" xml:space="preserve">
    <value>Praegune režiim on graafikurežiim</value>
    <comment>Announcement used in Graphing Calculator when switching to the graph mode</comment>
  </data>
  <data name="GridHeading.Text" xml:space="preserve">
    <value>Aken</value>
    <comment>Heading for window extents on the settings </comment>
  </data>
  <data name="TrigModeDegrees.Content" xml:space="preserve">
    <value>Kraadid</value>
    <comment>Degrees mode on settings page</comment>
  </data>
  <data name="TrigModeGradians.Content" xml:space="preserve">
    <value>Goonid</value>
    <comment>Gradian mode on settings page</comment>
  </data>
  <data name="TrigModeRadians.Content" xml:space="preserve">
    <value>Radiaanid</value>
    <comment>Radians mode on settings page</comment>
  </data>
  <data name="UnitsHeading.Text" xml:space="preserve">
    <value>Ühikud</value>
    <comment>Heading for Unit's on the settings</comment>
  </data>
  <data name="ResetViewButton.Content" xml:space="preserve">
    <value>Lähtesta vaade</value>
    <comment>Hyperlink button to reset the view of the graph</comment>
  </data>
  <data name="GraphSettingsXMax.Header" xml:space="preserve">
    <value>X-telje maksimumväärtus</value>
    <comment>X maximum value header</comment>
  </data>
  <data name="GraphSettingsXMin.Header" xml:space="preserve">
    <value>X-telje miinimumväärtus</value>
    <comment>X minimum value header</comment>
  </data>
  <data name="GraphSettingsYMax.Header" xml:space="preserve">
    <value>Y-telje maksimumväärtus</value>
    <comment>Y Maximum value header</comment>
  </data>
  <data name="GraphSettingsYMin.Header" xml:space="preserve">
    <value>Y-telje miinimumväärtus</value>
    <comment>Y minimum value header</comment>
  </data>
  <data name="graphSettingsButton.[using:Windows.UI.Xaml.Controls]ToolTipService.ToolTip" xml:space="preserve">
    <value>Graafiliselt esitamise suvandid</value>
    <comment>This is the tooltip text for the graph options button in Graphing Calculator</comment>
  </data>
  <data name="graphSettingsButton.[using:Windows.UI.Xaml.Automation]AutomationProperties.Name" xml:space="preserve">
    <value>Graafiliselt esitamise suvandid</value>
    <comment>This is the automation name text for the graph options button in Graphing Calculator</comment>
  </data>
  <data name="GraphOptionsHeading.Text" xml:space="preserve">
    <value>Graafikusuvandid</value>
    <comment>Heading for the Graph options flyout in Graphing mode.</comment>
  </data>
  <data name="VariableAreaSettings.[using:Windows.UI.Xaml.Automation]AutomationProperties.Name" xml:space="preserve">
    <value>Muutuja suvandid</value>
    <comment>Screen reader prompt for the variable settings toggle button</comment>
  </data>
  <data name="VariableAreaSettings.[using:Windows.UI.Xaml.Controls]ToolTipService.ToolTip" xml:space="preserve">
    <value>Lülita muutuja suvandid sisse või välja</value>
    <comment>Tool tip for the variable settings toggle button</comment>
  </data>
  <data name="LineThicknessBoxHeading.Text" xml:space="preserve">
    <value>Joone jämedus</value>
    <comment>Heading for the Graph options flyout in Graphing mode.</comment>
  </data>
  <data name="LineOptionsHeading.Text" xml:space="preserve">
    <value>Joonesuvandid</value>
    <comment>Heading for the equation style flyout in Graphing mode.</comment>
  </data>
  <data name="SmallLineWidthAutomationName" xml:space="preserve">
    <value>Väike joonelaius</value>
    <comment>Automation name for line width setting</comment>
  </data>
  <data name="MediumLineWidthAutomationName" xml:space="preserve">
    <value>Keskmine joonelaius</value>
    <comment>Automation name for line width setting</comment>
  </data>
  <data name="LargeLineWidthAutomationName" xml:space="preserve">
    <value>Suur joonelaius</value>
    <comment>Automation name for line width setting</comment>
  </data>
  <data name="ExtraLargeLineWidthAutomationName" xml:space="preserve">
    <value>Ülisuur joonelaius</value>
    <comment>Automation name for line width setting</comment>
  </data>
  <data name="mathRichEditBox.PlaceholderText" xml:space="preserve">
    <value>Sisestage avaldis</value>
    <comment>this is the placeholder text used by the textbox to enter an equation</comment>
  </data>
  <data name="GraphCopyMenuItem.Text" xml:space="preserve">
    <value>Kopeeri</value>
    <comment>Copy menu item for the graph context menu</comment>
  </data>
  <data name="cutEquationMenuItem.Text" xml:space="preserve">
    <value>Lõika</value>
    <comment>Cut menu item from the Equation TextBox</comment>
  </data>
  <data name="copyEquationMenuItem.Text" xml:space="preserve">
    <value>Kopeeri</value>
    <comment>Copy menu item from the Equation TextBox</comment>
  </data>
  <data name="pasteEquationMenuItem.Text" xml:space="preserve">
    <value>Kleebi</value>
    <comment>Paste menu item from the Equation TextBox</comment>
  </data>
  <data name="undoEquationMenuItem.Text" xml:space="preserve">
    <value>Võta tagasi</value>
    <comment>Undo menu item from the Equation TextBox</comment>
  </data>
  <data name="selectAllEquationMenuItem.Text" xml:space="preserve">
    <value>Vali kõik</value>
    <comment>Select all menu item from the Equation TextBox</comment>
  </data>
  <data name="EquationInputButton.[using:Windows.UI.Xaml.Automation]AutomationProperties.Name" xml:space="preserve">
    <value>Funktsiooni sisend</value>
    <comment>The automation name for the Equation Input ListView item that is shown when Calculator is in graphing mode.</comment>
  </data>
  <data name="EquationInputList.[using:Windows.UI.Xaml.Automation]AutomationProperties.Name" xml:space="preserve">
    <value>Funktsiooni sisend</value>
    <comment>The automation name for the Equation Input ListView that is shown when Calculator is in graphing mode.</comment>
  </data>
  <data name="EquationInputPanel.[using:Windows.UI.Xaml.Automation]AutomationProperties.Name" xml:space="preserve">
    <value>Funktsiooni sisestuspaneel</value>
    <comment>The automation name for the Equation Input StackPanel that is shown when Calculator is in graphing mode.</comment>
  </data>
  <data name="VariableStackPanel.[using:Windows.UI.Xaml.Automation]AutomationProperties.Name" xml:space="preserve">
    <value>Muutujate paneel</value>
    <comment>The automation name for the Variable StackPanel that is shown when Calculator is in graphing mode.</comment>
  </data>
  <data name="VariableListView.[using:Windows.UI.Xaml.Automation]AutomationProperties.Name" xml:space="preserve">
    <value>Muutujate loend</value>
    <comment>The automation name for the Variable ListView that is shown when Calculator is in graphing mode.</comment>
  </data>
  <data name="VariableListViewItem" xml:space="preserve">
    <value>Muutuja %1 loendiüksus</value>
    <comment>The automation name for the Variable ListViewItem that is shown when Calculator is in graphing mode.</comment>
  </data>
  <data name="VariableValueTextBox.[using:Windows.UI.Xaml.Automation]AutomationProperties.Name" xml:space="preserve">
    <value>Muutuvväärtuse tekstiväli</value>
    <comment>The automation name for the Variable Value Textbox that is shown when Calculator is in graphing mode.</comment>
  </data>
  <data name="VariableValueSlider.[using:Windows.UI.Xaml.Automation]AutomationProperties.Name" xml:space="preserve">
    <value>Muutuja väärtuse liugur</value>
    <comment>The automation name for the Variable Value Slider that is shown when Calculator is in graphing mode.</comment>
  </data>
  <data name="VariableMinTextBox.[using:Windows.UI.Xaml.Automation]AutomationProperties.Name" xml:space="preserve">
    <value>Muutuja miinimumväärtuse tekstiväli</value>
    <comment>The automation name for the Variable Min Value Textbox that is shown when Calculator is in graphing mode.</comment>
  </data>
  <data name="VariableStepTextBox.[using:Windows.UI.Xaml.Automation]AutomationProperties.Name" xml:space="preserve">
    <value>Muutuja sammu väärtuse tekstiväli</value>
    <comment>The automation name for the Variable Step Textbox that is shown when Calculator is in graphing mode.</comment>
  </data>
  <data name="VariableMaxTextBox.[using:Windows.UI.Xaml.Automation]AutomationProperties.Name" xml:space="preserve">
    <value>Muutuja maksimumväärtuse tekstiväli</value>
    <comment>The automation name for the Variable Max Value Textbox that is shown when Calculator is in graphing mode.</comment>
  </data>
  <data name="solidLineStyleAutomationName" xml:space="preserve">
    <value>Pidevjoone laad</value>
    <comment>Name of the solid line style for a graphed equation</comment>
  </data>
  <data name="dotLineStyleAutomationName" xml:space="preserve">
    <value>Punktiirjoone laad</value>
    <comment>Name of the dotted line style for a graphed equation</comment>
  </data>
  <data name="dashLineStyleAutomationName" xml:space="preserve">
    <value>Kriipsjoone laad</value>
    <comment>Name of the dashed line style for a graphed equation</comment>
  </data>
  <data name="equationColor1AutomationName" xml:space="preserve">
    <value>Meresinine</value>
    <comment>Name of color in the color picker</comment>
  </data>
  <data name="equationColor2AutomationName" xml:space="preserve">
    <value>Merevaht</value>
    <comment>Name of color in the color picker</comment>
  </data>
  <data name="equationColor3AutomationName" xml:space="preserve">
    <value>Violetne</value>
    <comment>Name of color in the color picker</comment>
  </data>
  <data name="equationColor4AutomationName" xml:space="preserve">
    <value>Roheline</value>
    <comment>Name of color in the color picker</comment>
  </data>
  <data name="equationColor5AutomationName" xml:space="preserve">
    <value>Mündiroheline</value>
    <comment>Name of color in the color picker</comment>
  </data>
  <data name="equationColor6AutomationName" xml:space="preserve">
    <value>Tumeroheline</value>
    <comment>Name of color in the color picker</comment>
  </data>
  <data name="equationColor7AutomationName" xml:space="preserve">
    <value>Süsihall</value>
    <comment>Name of color in the color picker</comment>
  </data>
  <data name="equationColor8AutomationName" xml:space="preserve">
    <value>Punane</value>
    <comment>Name of color in the color picker</comment>
  </data>
  <data name="equationColor9AutomationName" xml:space="preserve">
    <value>Hele ploomililla</value>
    <comment>Name of color in the color picker</comment>
  </data>
  <data name="equationColor10AutomationName" xml:space="preserve">
    <value>Magenta</value>
    <comment>Name of color in the color picker</comment>
  </data>
  <data name="equationColor11AutomationName" xml:space="preserve">
    <value>Kuldkollane</value>
    <comment>Name of color in the color picker</comment>
  </data>
  <data name="equationColor12AutomationName" xml:space="preserve">
    <value>Erkoranž</value>
    <comment>Name of color in the color picker</comment>
  </data>
  <data name="equationColor13AutomationName" xml:space="preserve">
    <value>Pruun</value>
    <comment>Name of color in the color picker</comment>
  </data>
  <data name="equationColor14BlackAutomationName" xml:space="preserve">
    <value>Must</value>
    <comment>Name of color in the color picker</comment>
  </data>
  <data name="equationColor14WhiteAutomationName" xml:space="preserve">
    <value>Valge</value>
    <comment>Name of color in the color picker</comment>
  </data>
  <data name="equationHighContrastColor1AutomationName" xml:space="preserve">
    <value>Värv 1</value>
    <comment>Name of color in the color picker</comment>
  </data>
  <data name="equationHighContrastColor2AutomationName" xml:space="preserve">
    <value>Värv 2</value>
    <comment>Name of color in the color picker</comment>
  </data>
  <data name="equationHighContrastColor3AutomationName" xml:space="preserve">
    <value>Värv 3</value>
    <comment>Name of color in the color picker</comment>
  </data>
  <data name="equationHighContrastColor4AutomationName" xml:space="preserve">
    <value>Värv 4</value>
    <comment>Name of color in the color picker</comment>
  </data>
  <data name="GraphThemeHeading.Text" xml:space="preserve">
    <value>Graafiku kujundus</value>
    <comment>Graph settings heading for the theme options</comment>
  </data>
  <data name="AlwaysLightTheme.Content" xml:space="preserve">
    <value>Alati hele</value>
    <comment>Graph settings option to set graph to light theme</comment>
  </data>
  <data name="MatchAppTheme.Content" xml:space="preserve">
    <value>Vastavalt rakenduse kujundusele</value>
    <comment>Graph settings option to set graph to match the app theme</comment>
  </data>
  <data name="GraphThemeHeading.[using:Windows.UI.Xaml.Automation]AutomationProperties.Name" xml:space="preserve">
    <value>Kujundus</value>
    <comment>This is the automation name text for the Graph settings heading for the theme options</comment>
  </data>
  <data name="AlwaysLightTheme.[using:Windows.UI.Xaml.Automation]AutomationProperties.Name" xml:space="preserve">
    <value>Alati hele</value>
    <comment>This is the automation name text for the Graph settings option to set graph to light theme</comment>
  </data>
  <data name="MatchAppTheme.[using:Windows.UI.Xaml.Automation]AutomationProperties.Name" xml:space="preserve">
    <value>Vastavalt rakenduse kujundusele</value>
    <comment>This is the automation name text for the Graph settings option to set graph to match the app theme</comment>
  </data>
  <data name="FunctionRemovedAnnouncement" xml:space="preserve">
    <value>Funktsioon on eemaldatud</value>
    <comment>Announcement used in Graphing Calculator when a function is removed from the function list</comment>
  </data>
  <data name="KGFEquationTextBox.[using:Windows.UI.Xaml.Automation]AutomationProperties.Name" xml:space="preserve">
    <value>Funktsiooni analüüsi võrrandi väli</value>
    <comment>This is the automation name text for the equation box in the function analysis panel</comment>
  </data>
  <data name="graphingEqualButton.[using:Windows.UI.Xaml.Automation]AutomationProperties.Name" xml:space="preserve">
    <value>Võrdub</value>
    <comment>Screen reader prompt for the equal button on the graphing calculator operator keypad</comment>
  </data>
  <data name="lessThanFlyoutButton.[using:Windows.UI.Xaml.Automation]AutomationProperties.Name" xml:space="preserve">
    <value>on väiksem kui</value>
    <comment>Screen reader prompt for the Less than button</comment>
  </data>
  <data name="lessThanOrEqualFlyoutButton.[using:Windows.UI.Xaml.Automation]AutomationProperties.Name" xml:space="preserve">
    <value>on väiksem või võrdne</value>
    <comment>Screen reader prompt for the Less than or equal button</comment>
  </data>
  <data name="equalsFlyoutButton.[using:Windows.UI.Xaml.Automation]AutomationProperties.Name" xml:space="preserve">
    <value>Võrdub</value>
    <comment>Screen reader prompt for the Equal button</comment>
  </data>
  <data name="greaterThanOrEqualFlyoutButton.[using:Windows.UI.Xaml.Automation]AutomationProperties.Name" xml:space="preserve">
    <value>on suurem või võrdne</value>
    <comment>Screen reader prompt for the Greater than or equal button</comment>
  </data>
  <data name="greaterThanFlyoutButton.[using:Windows.UI.Xaml.Automation]AutomationProperties.Name" xml:space="preserve">
    <value>on suurem kui</value>
    <comment>Screen reader prompt for the Greater than button</comment>
  </data>
  <data name="xButton.[using:Windows.UI.Xaml.Automation]AutomationProperties.Name" xml:space="preserve">
    <value>X</value>
    <comment>Screen reader prompt for the X button on the graphing calculator operator keypad</comment>
  </data>
  <data name="yButton.[using:Windows.UI.Xaml.Automation]AutomationProperties.Name" xml:space="preserve">
    <value>Y</value>
    <comment>Screen reader prompt for the Y button on the graphing calculator operator keypad</comment>
  </data>
  <data name="submitButton.[using:Windows.UI.Xaml.Automation]AutomationProperties.Name" xml:space="preserve">
    <value>Edasta</value>
    <comment>Screen reader prompt for the submit button on the graphing calculator operator keypad</comment>
  </data>
  <data name="FunctionAnalysisGrid.[using:Windows.UI.Xaml.Automation]AutomationProperties.Name" xml:space="preserve">
    <value>Funktsiooni analüüs</value>
    <comment>Screen reader prompt for the function analysis grid</comment>
  </data>
  <data name="GraphSettingsGrid.[using:Windows.UI.Xaml.Automation]AutomationProperties.Name" xml:space="preserve">
    <value>Graafikusuvandid</value>
    <comment>Screen reader prompt for the graph options panel</comment>
  </data>
  <data name="DockPanel_HistoryMemoryLists" xml:space="preserve">
    <value>Ajaloo- ja mäluloendid</value>
    <comment>Automation name for the group of controls for history and memory lists.</comment>
  </data>
  <data name="DockPanel_MemoryList" xml:space="preserve">
    <value>Mäluloend</value>
    <comment>Automation name for the group of controls for memory list.</comment>
  </data>
  <data name="Format_HistorySlotCleared" xml:space="preserve">
    <value>Ajaloopesa %1 on tühjendatud</value>
    <comment>{Locked='%1'} Formatting string for a Narrator announcement when the user clears a history slot. The %1 is the index of the history slot. For example, users might hear "History slot 2 cleared".</comment>
  </data>
  <data name="CalcAlwaysOnTop" xml:space="preserve">
    <value>Kalkulaator kuvatakse alati kõige peal</value>
    <comment>Announcement to indicate calculator window is always shown on top.</comment>
  </data>
  <data name="CalcBackToFullView" xml:space="preserve">
    <value>Kalkulaator on taas kuvatud täielikult</value>
    <comment>Announcement to indicate calculator window is now back to full view.</comment>
  </data>
  <data name="arithmeticShiftButtonSelected" xml:space="preserve">
    <value>Valitud on aritmeetika vahetus</value>
    <comment>Label for a radio button that toggles arithmetic shift behavior for the shift operations.</comment>
  </data>
  <data name="logicalShiftButtonSelected" xml:space="preserve">
    <value>Valitud on loogiline vahetus</value>
    <comment>Label for a radio button that toggles logical shift behavior for the shift operations.</comment>
  </data>
  <data name="rotateCircularButtonSelected" xml:space="preserve">
    <value>Valitud on ringikujulise nihkega pööramine</value>
    <comment>Label for a radio button that toggles rotate circular behavior for the shift operations.</comment>
  </data>
  <data name="rotateCarryShiftButtonSelected" xml:space="preserve">
    <value>Valitud on läbi kande ringikujulise nihkega pööramine</value>
    <comment>Label for a radio button that toggles rotate circular with carry behavior for the shift operations.</comment>
  </data>
  <data name="SettingsHeader.Text" xml:space="preserve">
    <value>Sätted</value>
    <comment>Header text of Settings page</comment>
  </data>
  <data name="SettingsAppearance.Text" xml:space="preserve">
    <value>Välimus</value>
    <comment>Subtitle of appearance setting on Settings page</comment>
  </data>
  <data name="AppThemeExpander.Header" xml:space="preserve">
    <value>Rakenduse kujundus</value>
    <comment>Title of App theme expander</comment>
  </data>
  <data name="AppThemeExpander.Description" xml:space="preserve">
    <value>Valige kuvatav rakenduse kujundus</value>
    <comment>Description of App theme expander</comment>
  </data>
  <data name="LightThemeRadioButton.Content" xml:space="preserve">
    <value>Hele</value>
    <comment>Lable for light theme option</comment>
  </data>
  <data name="DarkThemeRadioButton.Content" xml:space="preserve">
    <value>Tume</value>
    <comment>Lable for dark theme option</comment>
  </data>
  <data name="SystemThemeRadioButton.Content" xml:space="preserve">
    <value>Süsteemisätte kasutus</value>
    <comment>Lable for the app theme option to use system setting</comment>
  </data>
  <data name="TitleBarBackButton.[using:Windows.UI.Xaml.Automation]AutomationProperties.Name" xml:space="preserve">
    <value>Tagasi</value>
    <comment>Screen reader prompt for the Back button in title bar to back to main page</comment>
  </data>
  <data name="SettingsPageOpenedAnnouncement" xml:space="preserve">
    <value>Sätete leht</value>
    <comment>Announcement used when Settings page is opened</comment>
  </data>
  <data name="MathRichEditBox.[using:Windows.UI.Xaml.Automation]AutomationProperties.Name" xml:space="preserve">
    <value>Saadaolevate toimingute kontekstimenüü avamine.</value>
    <comment>Screen reader prompt for the context menu of the expression box</comment>
  </data>
  <data name="ErrorButtonOk" xml:space="preserve">
    <value>OK</value>
    <comment>The text of OK button to dismiss an error dialog.</comment>
  </data>
  <data name="SnapshotRestoreError" xml:space="preserve">
    <value>Seda hetktõmmist ei saanud taastada.</value>
    <comment>The error message to notify user that restoring from snapshot has failed.</comment>
  </data>
</root>