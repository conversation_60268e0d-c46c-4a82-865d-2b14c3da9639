#
# Release
# This pipeline builds a version of the app in a production configuration to be released to the
# Store and the Windows image. This pipeline relies on Microsoft-internal resources to run.
#

trigger: none
pr: none

variables:
  - name: versionMajor
    value: 11
  - name: versionMinor
    value: 2506
  - name: versionBuild
    value: $[counter(format('{0}.{1}.*', variables['versionMajor'], variables['versionMinor']), 0)]
  - name: versionPatch
    value: 0
  - group: CalculatorTSAConfig

name: '$(versionMajor).$(versionMinor).$(versionBuild).$(versionPatch)'

parameters:
- name: publishStore
  displayName: Publish and flight the package on Store
  type: boolean
  default: true

- name: publishVPack
  displayName: Publish as undocked inbox app via VPack
  type: boolean
  default: true

resources:
  repositories:
  - repository: 1esPipelines
    type: git
    name: 1ESPipelineTemplates/1ESPipelineTemplates
    ref: refs/tags/release

extends:
  template: v1/1ES.Official.PipelineTemplate.yml@1esPipelines
  parameters:
    pool:
      name: EssentialExperiences-windows-2022
      image: MMSWindows2022-Secure
      os: windows
    sdl:
      tsa:
        enabled: true
        config:
          codebaseName: $(TSA.CodebaseName)
          notificationAliases: $(TSA.NotificationAliases)
          instanceUrl: $(TSA.InstanceUrl)
          projectName: $(TSA.ProjectName)
          areaPath: $(TSA.AreaPath)
          serviceTreeID: $(TSA.ServiceTreeID)
          allTools: true
      codeql:
        tsaEnabled: true
      policheck:
        enabled: true
        exclusionsFile: '$(Build.SourcesDirectory)\build\config\PoliCheckExclusions.xml'

    stages:
    - stage: Calculator
      jobs:
      - template: /build/pipelines/templates/build-single-architecture.yaml@self
        parameters:
          platform: x64
          isReleaseBuild: true
          useReleaseAppxmanifest: true

      - template: /build/pipelines/templates/build-single-architecture.yaml@self
        parameters:
          platform: x86
          isReleaseBuild: true
          useReleaseAppxmanifest: true
          condition: not(eq(variables['Build.Reason'], 'PullRequest'))

      - template: /build/pipelines/templates/build-single-architecture.yaml@self
        parameters:
          platform: ARM
          isReleaseBuild: true
          useReleaseAppxmanifest: true
          condition: not(eq(variables['Build.Reason'], 'PullRequest'))

      - template: /build/pipelines/templates/build-single-architecture.yaml@self
        parameters:
          platform: ARM64
          isReleaseBuild: true
          useReleaseAppxmanifest: true
          condition: not(eq(variables['Build.Reason'], 'PullRequest'))

      - template: /build/pipelines/templates/run-ui-tests.yaml@self
        parameters:
          platform: x64
          runsettingsFileName: CalculatorUITests.release.runsettings

      - template: /build/pipelines/templates/run-ui-tests.yaml@self
        parameters:
          platform: x86
          runsettingsFileName: CalculatorUITests.release.runsettings

      - template: /build/pipelines/templates/run-unit-tests.yaml@self
        parameters:
          platform: x64

      - template: /build/pipelines/templates/run-unit-tests.yaml@self
        parameters:
          platform: x86

      - template: /build/pipelines/templates/package-msixbundle.yaml@self
        parameters:
          signBundle: true
          createStoreBrokerPackages: true

      - ${{ if eq(parameters.publishStore, true) }}:
        - template: /build/pipelines/templates/release-store.yaml@self
      - ${{ if eq(parameters.publishVPack, true) }}:
        - template: /build/pipelines/templates/release-vpack.yaml@self
