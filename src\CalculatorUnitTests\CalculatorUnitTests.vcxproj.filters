﻿<?xml version="1.0" encoding="utf-8"?>
<Project ToolsVersion="4.0" xmlns="http://schemas.microsoft.com/developer/msbuild/2003">
  <ItemGroup>
    <ApplicationDefinition Include="UnitTestApp.xaml" />
  </ItemGroup>
  <ItemGroup>
    <PRIResource Include="..\Calculator\Resources\en-US\CEngineStrings.resw" />
    <PRIResource Include="..\Calculator\Resources\en-US\Resources.resw" />
    <PRIResource Include="Test.resw" />
  </ItemGroup>
  <ItemGroup>
    <ClCompile Include="CalcEngineTests.cpp" />
    <ClCompile Include="CalcInputTest.cpp" />
    <ClCompile Include="CalculatorManagerTest.cpp" />
    <ClCompile Include="CopyPasteManagerTest.cpp" />
    <ClCompile Include="CurrencyConverterUnitTests.cpp" />
    <ClCompile Include="DateCalculatorUnitTests.cpp" />
    <ClCompile Include="HistoryTests.cpp" />
    <ClCompile Include="MultiWindowUnitTests.cpp" />
    <ClCompile Include="NavCategoryUnitTests.cpp" />
    <ClCompile Include="StandardViewModelUnitTests.cpp" />
    <ClCompile Include="UnitConverterTest.cpp" />
    <ClCompile Include="UnitConverterViewModelUnitTests.cpp" />
    <ClCompile Include="UnitTestApp.xaml.cpp" />
    <ClCompile Include="pch.cpp" />
    <ClCompile Include="UtilsTests.cpp" />
    <ClCompile Include="LocalizationServiceUnitTests.cpp" />
    <ClCompile Include="RationalTest.cpp" />
    <ClCompile Include="LocalizationSettingsUnitTests.cpp" />
    <ClCompile Include="NarratorAnnouncementUnitTests.cpp" />
  </ItemGroup>
  <ItemGroup>
    <ClInclude Include="DateUtils.h" />
    <ClInclude Include="Helpers.h" />
    <ClInclude Include="pch.h" />
    <ClInclude Include="UnitConverterViewModelUnitTests.h" />
    <ClInclude Include="UnitTestApp.xaml.h" />
  </ItemGroup>
  <ItemGroup>
    <Xml Include="UnitTestApp.rd.xml" />
  </ItemGroup>
  <ItemGroup>
    <Image Include="Assets\SplashScreen.scale-200.png">
      <Filter>Assets</Filter>
    </Image>
    <Image Include="Assets\Square44x44Logo.scale-200.png">
      <Filter>Assets</Filter>
    </Image>
    <Image Include="Assets\Square44x44Logo.targetsize-24_altform-unplated.png">
      <Filter>Assets</Filter>
    </Image>
    <Image Include="Assets\Square150x150Logo.scale-200.png">
      <Filter>Assets</Filter>
    </Image>
    <Image Include="Assets\StoreLogo.png">
      <Filter>Assets</Filter>
    </Image>
    <Image Include="Assets\Wide310x150Logo.scale-200.png">
      <Filter>Assets</Filter>
    </Image>
    <Image Include="Assets\LockScreenLogo.scale-200.png">
      <Filter>Assets</Filter>
    </Image>
  </ItemGroup>
  <ItemGroup>
    <AppxManifest Include="Package.appxmanifest" />
  </ItemGroup>
  <ItemGroup>
    <Filter Include="Assets">
      <UniqueIdentifier>{f2987b0a-9832-46fc-b818-d5347362b3d8}</UniqueIdentifier>
    </Filter>
  </ItemGroup>
</Project>