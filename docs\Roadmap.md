# Windows Calculator Roadmap

Windows Calculator is under active development by Microsoft.

## Focus

In 2021, the Windows Calculator team is focused on:
* Iterating upon the existing app design based on the latest guidelines for [Fluent Design](https://developer.microsoft.com/en-us/windows/apps/design) and [WinUI](https://github.com/microsoft/microsoft-ui-xaml).
* Unblocking community contributions by identifying and addressing bottlenecks affecting developers, including:
  * Migrating the codebase to C# ([#893](https://github.com/microsoft/calculator/issues/893))
  * Releasing infinite-precision engine as standalone package ([#1545](https://github.com/microsoft/calculator/issues/1545)) and adding support for arbitrary expression parsing ([#526](https://github.com/microsoft/calculator/issues/526))
  * Adding a settings page ([#596](https://github.com/microsoft/calculator/issues/596))
* [Your feature idea here] - please review our [new feature development process](https://github.com/Microsoft/calculator/blob/main/docs/NewFeatureProcess.md) to get started!

We welcome contributions of all kinds from the community, but especially those that support the efforts above. Please see our [contributing guidelines](https://github.com/Microsoft/calculator/blob/main/CONTRIBUTING.md) for more information on how to get involved.

## Releases

Windows Calculator is included in every Windows 10 release as a [provisioned Windows app](https://docs.microsoft.com/en-us/windows/application-management/apps-in-windows-10#provisioned-windows-apps). We also deliver updates through the [Microsoft Store](https://www.microsoft.com/store/productId/9WZDNCRFHVN5) approximately monthly.
