﻿// Copyright (c) Microsoft Corporation. All rights reserved.
// Licensed under the MIT License.

#pragma once

namespace CalculatorApp::ViewModel
{
    namespace Common
    {
    public
        enum class MyVirtualKey
        {
            None = 0,
            LeftButton = 1,
            RightButton = 2,
            Cancel = 3,
            MiddleButton = 4,
            XButton1 = 5,
            XButton2 = 6,
            Back = 8,
            Tab = 9,
            Clear = 12,
            Enter = 13,
            Shift = 16,
            Control = 17,
            Menu = 18,
            Pause = 19,
            CapitalLock = 20,
            Kana = 21,
            Hangul = 21,
            Junja = 23,
            Final = 24,
            Hanja = 25,
            Kanji = 25,
            Escape = 27,
            Convert = 28,
            NonConvert = 29,
            Accept = 30,
            ModeChange = 31,
            Space = 32,
            PageUp = 33,
            PageDown = 34,
            End = 35,
            Home = 36,
            Left = 37,
            Up = 38,
            Right = 39,
            Down = 40,
            Select = 41,
            Print = 42,
            Execute = 43,
            Snapshot = 44,
            Insert = 45,
            Delete = 46,
            Help = 47,
            Number0 = 48,
            Number1 = 49,
            Number2 = 50,
            Number3 = 51,
            Number4 = 52,
            Number5 = 53,
            Number6 = 54,
            Number7 = 55,
            Number8 = 56,
            Number9 = 57,
            <PERSON> = 65,
            <PERSON> = 66,
            <PERSON> = 67,
            <PERSON> = 68,
            <PERSON> = 69,
            <PERSON> = 70,
            <PERSON> = 71,
            <PERSON> = 72,
            I = 73,
            <PERSON> = 74,
            <PERSON> = 75,
            <PERSON> = 76,
            <PERSON> = 77,
            <PERSON> = 78,
            <PERSON> = 79,
            P = 80,
            Q = 81,
            R = 82,
            S = 83,
            T = 84,
            U = 85,
            V = 86,
            W = 87,
            X = 88,
            Y = 89,
            Z = 90,
            LeftWindows = 91,
            RightWindows = 92,
            Application = 93,
            Sleep = 95,
            NumberPad0 = 96,
            NumberPad1 = 97,
            NumberPad2 = 98,
            NumberPad3 = 99,
            NumberPad4 = 100,
            NumberPad5 = 101,
            NumberPad6 = 102,
            NumberPad7 = 103,
            NumberPad8 = 104,
            NumberPad9 = 105,
            Multiply = 106,
            Add = 107,
            Separator = 108,
            Subtract = 109,
            Decimal = 110,
            Divide = 111,
            F1 = 112,
            F2 = 113,
            F3 = 114,
            F4 = 115,
            F5 = 116,
            F6 = 117,
            F7 = 118,
            F8 = 119,
            F9 = 120,
            F10 = 121,
            F11 = 122,
            F12 = 123,
            F13 = 124,
            F14 = 125,
            F15 = 126,
            F16 = 127,
            F17 = 128,
            F18 = 129,
            F19 = 130,
            F20 = 131,
            F21 = 132,
            F22 = 133,
            F23 = 134,
            F24 = 135,
            NumberKeyLock = 144,
            Scroll = 145,
            LeftShift = 160,
            RightShift = 161,
            LeftControl = 162,
            RightControl = 163,
            LeftMenu = 164,
            RightMenu = 165
        };
    }
}
