<UserControl x:Class="CalculatorApp.TitleBar"
             xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
             xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
             xmlns:d="http://schemas.microsoft.com/expression/blend/2008"
             xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006"
             xmlns:utils="using:CalculatorApp.Utils"
             Height="32"
             mc:Ignorable="d">
    <Grid x:Name="LayoutRoot"
          HorizontalAlignment="Stretch"
          VerticalAlignment="Stretch">
        <VisualStateManager.VisualStateGroups>
            <VisualStateGroup x:Name="WindowFocusStates">
                <VisualState x:Name="WindowFocused"/>
                <VisualState x:Name="WindowNotFocused"/>
            </VisualStateGroup>
            <VisualStateGroup x:Name="AOTStates">
                <VisualState x:Name="AOTNormalState"/>
                <VisualState x:Name="AOTMiniState">
                    <VisualState.Setters>
                        <Setter Target="TitleHolder.Visibility" Value="Collapsed"/>
                        <Setter Target="ExitAlwaysOnTopButton.Visibility" Value="Visible"/>
                    </VisualState.Setters>
                </VisualState>
            </VisualStateGroup>

            <VisualStateGroup x:Name="BackButtonVisibilityState">
                <VisualState x:Name="BackButtonCollapsed"/>
                <VisualState x:Name="BackButtonVisible">
                    <VisualState.Setters>
                        <Setter Target="AppIcon.Margin" Value="48,0,0,0"/>
                    </VisualState.Setters>
                </VisualState>
            </VisualStateGroup>
        </VisualStateManager.VisualStateGroups>

        <Grid>
            <Grid.ColumnDefinitions>
                <ColumnDefinition Width="Auto"/>
                <ColumnDefinition Width="*"/>
            </Grid.ColumnDefinitions>

            <Grid.Transitions>
                <TransitionCollection>
                    <RepositionThemeTransition/>
                </TransitionCollection>
            </Grid.Transitions>

            <Grid x:Name="BackgroundElement"
                  Grid.Column="1"
                  Background="Transparent">
                <Grid x:Name="TitleHolder">
                    <Grid.ColumnDefinitions>
                        <ColumnDefinition Width="Auto"/>
                        <ColumnDefinition Width="*"/>
                    </Grid.ColumnDefinitions>

                    <Image x:Name="AppIcon"
                           Width="20"
                           Height="20"
                           Margin="16,0,0,0"
                           VerticalAlignment="Center"
                           AutomationProperties.AccessibilityView="Raw"
                           Source="ms-appx:///Assets/CalculatorAppList.png"/>
                    <TextBlock x:Name="AppName"
                               x:Uid="AppName"
                               Grid.Column="1"
                               Margin="12,0,0,0"
                               HorizontalAlignment="Left"
                               VerticalAlignment="Center"
                               FontSize="12"
                               TextTrimming="CharacterEllipsis"/>
                </Grid>
            </Grid>
        </Grid>
        <Button x:Name="ExitAlwaysOnTopButton"
                Width="46"
                Height="Auto"
                HorizontalAlignment="Left"
                HorizontalContentAlignment="Center"
                Style="{ThemeResource CommandBarFlyoutEllipsisButtonStyle}"
                Background="Transparent"
                FontFamily="{StaticResource CalculatorFontFamily}"
                FontSize="14"
                FontWeight="Thin"
                AutomationProperties.AutomationId="ExitAlwaysOnTopButton"
                AutomationProperties.Name="{utils:ResourceString Name=ExitAlwaysOnTopButton/[using:Windows.UI.Xaml.Automation]AutomationProperties/Name}"
                Click="AlwaysOnTopButton_Click"
                Content="&#xEE47;"
                ToolTipService.ToolTip="{utils:ResourceString Name=ExitAlwaysOnTopButton/[using:Windows.UI.Xaml.Controls]ToolTipService/ToolTip}"
                Visibility="Collapsed"
                x:Load="False">
            <Button.KeyboardAccelerators>
                <KeyboardAccelerator Key="Down" Modifiers="Menu"/>
            </Button.KeyboardAccelerators>
        </Button>
    </Grid>
</UserControl>
