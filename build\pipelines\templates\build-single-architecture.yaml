# This template contains a job to build the app for a single architecture.

parameters:
  isReleaseBuild: false
  useReleaseAppxManifest: false
  platform: ''
  condition: ''

jobs:
- job: Build${{ parameters.platform }}
  displayName: Build ${{ parameters.platform }}
  condition: ${{ parameters.condition }}
  variables:
    BuildConfiguration: Release
    BuildPlatform: ${{ parameters.platform }}
    ${{ if eq(parameters.isReleaseBuild, true) }}:
      ${{ if eq(parameters.useReleaseAppxManifest, true) }}:
        ExtraMSBuildArgs: '/p:IsStoreBuild=true /p:UseReleaseAppxManifest=true'
      ${{ if eq(parameters.useReleaseAppxManifest, false) }}:
        ExtraMSBuildArgs: '/p:IsStoreBuild=true'
    ${{ if eq(parameters.isReleaseBuild, false) }}:
      ${{ if eq(parameters.useReleaseAppxManifest, true) }}:
        ExtraMSBuildArgs: '/p:UseReleaseAppxManifest=true'
      ${{ if eq(parameters.useReleaseAppxManifest, false) }}:
        ExtraMSBuildArgs: ''
    ${{ if eq(parameters.useReleaseAppxManifest, false) }}:
      ManifestFileName: 'Package.appxmanifest'
    ${{ if eq(parameters.useReleaseAppxManifest, true) }}:
      ManifestFileName: 'Package.Release.appxmanifest'
  templateContext:
    sdl:
      binskim:
        analyzeTargetGlob: +:f|$(Agent.BuildDirectory)\binskim\**\*
    outputs:
    - output: pipelineArtifact
      displayName: Publish drop artifact
      targetPath: $(Build.BinariesDirectory)\$(BuildConfiguration)\${{ parameters.platform }}
      artifactName: drop-${{ parameters.platform }}

  steps:
  - checkout: self
    fetchDepth: 1

  - ${{ if eq(parameters.isReleaseBuild, true) }}:
    - task: UniversalPackages@0
      displayName: Download internals package
      inputs:
        command: download
        downloadDirectory: $(Build.SourcesDirectory)
        vstsFeed: WindowsInboxApps
        vstsFeedPackage: calculator-internals
        vstsPackageVersion: 0.0.117

  - task: NuGetToolInstaller@1
    displayName: Use NuGet 6.x
    inputs:
      versionSpec: 6.x

  - task: NuGetCommand@2
    displayName: NuGet restore src/Calculator.sln
    inputs:
      command: custom
      arguments: restore src/Calculator.sln -Verbosity Detailed

  - task: PowerShell@2
    displayName: Set version number in AppxManifest
    inputs:
      filePath: $(Build.SourcesDirectory)\build\scripts\UpdateAppxManifestVersion.ps1
      arguments: '-AppxManifest $(Build.SourcesDirectory)\src\Calculator\$(ManifestFileName) -Version $(Build.BuildNumber)'

  - task: VSBuild@1
    displayName: 'Build solution src/Calculator.sln'
    inputs:
      solution: src/Calculator.sln
      vsVersion: 17.0
      msbuildArgs: /bl:$(Build.BinariesDirectory)\$(BuildConfiguration)\$(BuildPlatform)\Calculator.binlog /p:OutDir=$(Build.BinariesDirectory)\$(BuildConfiguration)\$(BuildPlatform)\ /p:GenerateProjectSpecificOutputFolder=true /p:Version=$(Build.BuildNumber) /t:Publish /p:PublishDir=$(Build.BinariesDirectory)\$(BuildConfiguration)\$(BuildPlatform)\publish\ $(ExtraMSBuildArgs)
      platform: $(BuildPlatform)
      configuration: $(BuildConfiguration)
      maximumCpuCount: true

  - ${{ if eq(parameters.isReleaseBuild, true) }}:
    - task: CopyFiles@2
      displayName: Copy Files for BinSkim analysis
      inputs:
        SourceFolder: '$(Build.BinariesDirectory)\$(BuildConfiguration)\$(BuildPlatform)\Calculator\'
        # Setting up a folder to store all the binary files that we need BinSkim to scan.
        # If we put more things than we produce pdbs for and can index (such as nuget packages that ship without pdbs), binskim will fail.
        # Below are ignored files
        #   - clrcompression.dll
        #   - WebView2Loader.dll
        #   - Microsoft.Web.WebView2.Core.dll
        Contents: |
          **\*.dll
          **\*.exe
          !**\clrcompression.dll
          !**\WebView2Loader.dll
          !**\Microsoft.Web.WebView2.Core.dll
        TargetFolder: '$(Agent.BuildDirectory)\binskim'
        CleanTargetFolder: true
        OverWrite: true
        flattenFolders: false
        analyzeTarget: '$(Agent.BuildDirectory)\binskim\*'

    - task: PublishSymbols@2
      displayName: Publish symbols
      inputs:
        symbolsFolder: $(Build.BinariesDirectory)\$(BuildConfiguration)\$(BuildPlatform)
        searchPattern: '**/*.pdb'
        symbolServerType: teamServices
        treatNotIndexedAsWarning: true

    - task: securedevelopmentteam.vss-secure-development-tools.build-task-policheck.PoliCheck@1
      displayName: Run PoliCheck
      inputs:
        targetType: F
