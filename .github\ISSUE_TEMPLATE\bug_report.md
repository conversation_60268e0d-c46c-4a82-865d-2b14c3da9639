---
name: Bug report
about: Report a problem with Calculator
title: ''
labels: ''
assignees: ''

---
<!--
Before filing a bug
- Ensure the bug reproduces on the latest version of the app.
- Search existing issues and make sure this issue is not already filed.
-->

**Describe the bug**
<!-- A clear and concise description of what the bug is. -->

**Steps To Reproduce**
<!--
Steps to reproduce the behavior:
1. Go to '...'
2. Click on '....'
3. Scroll down to '....'
4. See error
-->

**Expected behavior**
<!-- A clear and concise description of what you expected to happen. -->

**Screenshots**
<!-- If applicable, add screenshots to help explain your problem. -->

**Device and Application Information**
 - OS Build:
 - Architecture:
 - Application Version:
 - Region:
 - Dev Version Installed:

<!--
Run the following commands in Powershell and copy/paste the output.
" - OS Build: $([Environment]::OSVersion.Version)"
" - Architecture: $((Get-AppxPackage -Name Microsoft.WindowsCalculator).Architecture)"
" - Application Version: $((Get-AppxPackage -Name Microsoft.WindowsCalculator).Version)"
" - Region: $((Get-Culture).Name)"
" - Dev Version Installed: $($null -ne (Get-AppxPackage -Name Microsoft.WindowsCalculator.Dev))"
-->

**Additional context**
<!-- Add any other context about the problem here. -->

**Requested Assignment**
<!--
Some people just want to report a bug and let someone else fix it.
Other people want to not only submit the bug report, but fix it as well.
Both scenarios are completely ok. We just want to know which one it is.
Please indicate which bucket you fall into by keeping one and removing the other.
-->
If possible, I would like to fix this.
I'm just reporting this problem.  I don't want to fix it.
