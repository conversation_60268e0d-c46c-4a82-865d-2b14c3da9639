<UserControl x:Class="CalculatorApp.CalculatorScientificAngleButtons"
             xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
             xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
             xmlns:Controls="using:CalculatorApp.Controls"
             xmlns:common="using:CalculatorApp.Common"
             xmlns:d="http://schemas.microsoft.com/expression/blend/2008"
             xmlns:local="using:CalculatorApp.ViewModel.Common"
             xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006"
             xmlns:utils="using:CalculatorApp.Utils"
             x:Name="ControlRoot"
             d:DesignHeight="315"
             d:DesignWidth="400"
             mc:Ignorable="d">

    <UserControl.Resources>
        <Style x:Key="CaptionButtonSmallStyle"
               BasedOn="{StaticResource CaptionButtonStyle}"
               TargetType="Button">
            <Setter Property="FontFamily" Value="{StaticResource ContentControlThemeFontFamily}"/>
            <Setter Property="FontSize" Value="13"/>
        </Style>

        <Style x:Key="CaptionButtonZeroMinHeightStyle"
               BasedOn="{StaticResource CaptionButtonSmallStyle}"
               TargetType="Button">
            <Setter Property="MinHeight" Value="0"/>
        </Style>
    </UserControl.Resources>

    <Grid x:Name="ScientificAngleOperators"
          Margin="3,0,3,0"
          AutomationProperties.HeadingLevel="Level1"
          AutomationProperties.Name="{utils:ResourceString Name=ScientificAngleOperators/[using:Windows.UI.Xaml.Automation]AutomationProperties/Name}">
        <Grid.ColumnDefinitions>
            <ColumnDefinition Width="1*" MaxWidth="80"/>
            <ColumnDefinition Width="1*" MaxWidth="80"/>
            <ColumnDefinition Width="1*" MaxWidth="80"/>
            <ColumnDefinition Width="1*" MaxWidth="80"/>
            <ColumnDefinition Width="1*" MaxWidth="80"/>
            <ColumnDefinition Width="0.01*"/>
            <ColumnDefinition Width="1*" MaxWidth="80"/>
        </Grid.ColumnDefinitions>

        <VisualStateManager.VisualStateGroups>
            <VisualStateGroup x:Name="Sizing">
                <VisualState x:Name="MinSizeLayout">
                    <VisualState.StateTriggers>
                        <AdaptiveTrigger MinWindowHeight="{StaticResource AppMinWindowHeight}" MinWindowWidth="{StaticResource AppMinWindowWidth}"/>
                    </VisualState.StateTriggers>
                </VisualState>
                <VisualState x:Name="DefaultLayout">
                    <VisualState.StateTriggers>
                        <AdaptiveTrigger MinWindowHeight="0" MinWindowWidth="0"/>
                    </VisualState.StateTriggers>
                    <VisualState.Setters>
                        <Setter Target="DegreeButton.Style" Value="{StaticResource CaptionButtonZeroMinHeightStyle}"/>
                        <Setter Target="RadianButton.Style" Value="{StaticResource CaptionButtonZeroMinHeightStyle}"/>
                        <Setter Target="GradsButton.Style" Value="{StaticResource CaptionButtonZeroMinHeightStyle}"/>
                        <Setter Target="FtoeButton.Style" Value="{StaticResource CaptionToggleButtonWithIndicatorSmallStyle}"/>
                    </VisualState.Setters>
                </VisualState>
            </VisualStateGroup>
            <VisualStateGroup x:Name="ErrorVisualStates">
                <VisualState x:Name="NoErrorLayout"/>
                <VisualState x:Name="ErrorLayout">
                    <VisualState.Setters>
                        <Setter Target="DegreeButton.IsEnabled" Value="False"/>
                        <Setter Target="RadianButton.IsEnabled" Value="False"/>
                        <Setter Target="GradsButton.IsEnabled" Value="False"/>
                        <Setter Target="FtoeButton.IsEnabled" Value="False"/>
                    </VisualState.Setters>
                </VisualState>
            </VisualStateGroup>
        </VisualStateManager.VisualStateGroups>
        <Button x:Name="DegreeButton"
                Style="{StaticResource CaptionButtonSmallStyle}"
                common:KeyboardShortcutManager.VirtualKey="{utils:ResourceVirtualKey Name=degButton/[using:CalculatorApp.Common]KeyboardShortcutManager/VirtualKey}"
                AutomationProperties.AutomationId="degButton"
                AutomationProperties.Name="{utils:ResourceString Name=degButton/[using:Windows.UI.Xaml.Automation]AutomationProperties/Name}"
                Command="{x:Bind ButtonPressed}"
                CommandParameter="0"
                Content="DEG"/>
        <Button x:Name="RadianButton"
                Style="{StaticResource CaptionButtonSmallStyle}"
                common:KeyboardShortcutManager.VirtualKey="{utils:ResourceVirtualKey Name=radButton/[using:CalculatorApp.Common]KeyboardShortcutManager/VirtualKey}"
                AutomationProperties.AutomationId="radButton"
                AutomationProperties.Name="{utils:ResourceString Name=radButton/[using:Windows.UI.Xaml.Automation]AutomationProperties/Name}"
                Command="{x:Bind ButtonPressed}"
                CommandParameter="1"
                Content="RAD"
                Visibility="Collapsed"/>
        <Button x:Name="GradsButton"
                Style="{StaticResource CaptionButtonSmallStyle}"
                common:KeyboardShortcutManager.VirtualKey="{utils:ResourceVirtualKey Name=gradButton/[using:CalculatorApp.Common]KeyboardShortcutManager/VirtualKey}"
                AutomationProperties.AutomationId="gradButton"
                AutomationProperties.Name="{utils:ResourceString Name=gradButton/[using:Windows.UI.Xaml.Automation]AutomationProperties/Name}"
                Command="{x:Bind ButtonPressed}"
                CommandParameter="2"
                Content="GRAD"
                Visibility="Collapsed"/>
        <ToggleButton x:Name="FtoeButton"
                      Grid.Column="1"
                      Style="{StaticResource CaptionToggleButtonWithIndicatorStyle}"
                      Background="{ThemeResource SystemControlBackgroundTransparentBrush}"
                      common:KeyboardShortcutManager.VirtualKey="{utils:ResourceVirtualKey Name=ftoeButton/[using:CalculatorApp.Common]KeyboardShortcutManager/VirtualKey}"
                      AutomationProperties.AutomationId="ftoeButton"
                      AutomationProperties.Name="{utils:ResourceString Name=ftoeButton/[using:Windows.UI.Xaml.Automation]AutomationProperties/Name}"
                      Checked="FToEButton_Toggled"
                      Content="F-E"
                      IsChecked="{x:Bind Model.IsFToEChecked, Mode=OneWay}"
                      Unchecked="FToEButton_Toggled"
                      IsEnabled="{x:Bind Model.IsFToEEnabled, Mode=OneWay}">
            <ToggleButton.CommandParameter>
                <local:NumbersAndOperatorsEnum>FToE</local:NumbersAndOperatorsEnum>
            </ToggleButton.CommandParameter>
        </ToggleButton>
    </Grid>
</UserControl>
