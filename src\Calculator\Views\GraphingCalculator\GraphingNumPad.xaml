<UserControl x:Class="CalculatorApp.GraphingNumPad"
             xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
             xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
             xmlns:Windows10version1803="http://schemas.microsoft.com/winfx/2006/xaml/presentation?IsApiContractNotPresent(Windows.Foundation.UniversalApiContract, 7)"
             xmlns:Windows10version1809="http://schemas.microsoft.com/winfx/2006/xaml/presentation?IsApiContractPresent(Windows.Foundation.UniversalApiContract, 7)"
             xmlns:common="using:CalculatorApp.Common"
             xmlns:controls="using:CalculatorApp.Controls"
             xmlns:converters="using:CalculatorApp.Converters"
             xmlns:d="http://schemas.microsoft.com/expression/blend/2008"
             xmlns:local="using:CalculatorApp"
             xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006"
             xmlns:triggers="using:CalculatorApp.Views.StateTriggers"
             xmlns:utils="using:CalculatorApp.Utils"
             d:DesignHeight="300"
             d:DesignWidth="400"
             mc:Ignorable="d">
    <UserControl.Resources>
        <converters:BooleanToVisibilityConverter x:Key="BooleanToVisibilityConverter"/>
        <converters:BooleanToVisibilityNegationConverter x:Key="BooleanToVisibilityNegationConverter"/>
    </UserControl.Resources>

    <Border Background="{ThemeResource AppOperatorPanelBackground}" PointerPressed="GraphingNumPad_PointerPressed">
        <VisualStateManager.VisualStateGroups>
            <VisualStateGroup x:Name="SizeLayouts">
                <VisualState x:Name="Large">
                    <VisualState.StateTriggers>
                        <triggers:ControlSizeTrigger MinWidth="878"
                                                     MinHeight="851"
                                                     Source="{x:Bind GraphingOperators}"/>
                    </VisualState.StateTriggers>
                    <VisualState.Setters>
                        <Setter Target="NegateButton.FontSize" Value="{StaticResource CalcStandardOperatorCaptionSizeExtraLarge}"/>
                        <Setter Target="PiButton.FontSize" Value="{ThemeResource CalcButtonCaptionSize}"/>
                        <Setter Target="OpenParenthesisButton.FontSize" Value="{ThemeResource CalcButtonCaptionSize}"/>
                        <Setter Target="CloseParenthesisButton.FontSize" Value="{ThemeResource CalcButtonCaptionSize}"/>

                        <Setter Target="SinButton.FontSize" Value="{ThemeResource CalcButtonCaptionSize}"/>
                        <Setter Target="CosButton.FontSize" Value="{ThemeResource CalcButtonCaptionSize}"/>
                        <Setter Target="TanButton.FontSize" Value="{ThemeResource CalcButtonCaptionSize}"/>
                        <Setter Target="SinhButton.FontSize" Value="{ThemeResource CalcButtonCaptionSize}"/>
                        <Setter Target="CoshButton.FontSize" Value="{ThemeResource CalcButtonCaptionSize}"/>
                        <Setter Target="TanhButton.FontSize" Value="{ThemeResource CalcButtonCaptionSize}"/>
                        <Setter Target="InvsinButton.FontSize" Value="{ThemeResource CalcButtonCaptionSize}"/>
                        <Setter Target="InvcosButton.FontSize" Value="{ThemeResource CalcButtonCaptionSize}"/>
                        <Setter Target="InvtanButton.FontSize" Value="{ThemeResource CalcButtonCaptionSize}"/>
                        <Setter Target="InvsinhButton.FontSize" Value="{ThemeResource CalcButtonCaptionSize}"/>
                        <Setter Target="InvcoshButton.FontSize" Value="{ThemeResource CalcButtonCaptionSize}"/>
                        <Setter Target="InvtanhButton.FontSize" Value="{ThemeResource CalcButtonCaptionSize}"/>

                        <Setter Target="PowerButton.FontSize" Value="{ThemeResource CalcButtonCaptionSize}"/>
                        <Setter Target="PowerOf10Button.FontSize" Value="{ThemeResource CalcButtonCaptionSize}"/>
                        <Setter Target="LogBase10Button.FontSize" Value="{ThemeResource CalcButtonCaptionSize}"/>
                        <Setter Target="XButton.FontSize" Value="{ThemeResource CalcButtonCaptionSize}"/>
                        <Setter Target="YButton.FontSize" Value="{ThemeResource CalcButtonCaptionSize}"/>
                        <Setter Target="YSquareRootButton.FontSize" Value="{ThemeResource CalcButtonCaptionSize}"/>
                        <Setter Target="PowerOfEButton.FontSize" Value="{ThemeResource CalcButtonCaptionSize}"/>
                        <Setter Target="LogBaseEButton.FontSize" Value="{ThemeResource CalcButtonCaptionSize}"/>
                        <Setter Target="XPower2Button.FontSize" Value="{ThemeResource CalcButtonCaptionSize}"/>
                        <Setter Target="XPower3Button.FontSize" Value="{ThemeResource CalcButtonCaptionSize}"/>
                        <Setter Target="SquareRootButton.FontSize" Value="{ThemeResource CalcButtonCaptionSize}"/>
                        <Setter Target="InvertButton.FontSize" Value="{ThemeResource CalcButtonCaptionSize}"/>

                        <Setter Target="ClearButton.FontSize" Value="{ThemeResource CalcButtonCaptionSize}"/>
                        <Setter Target="BackSpaceButton.FontSize" Value="{ThemeResource CalcButtonCaptionSize}"/>
                        <Setter Target="DivideButton.FontSize" Value="{ThemeResource CalcButtonCaptionSize}"/>
                        <Setter Target="MultiplyButton.FontSize" Value="{ThemeResource CalcButtonCaptionSize}"/>
                        <Setter Target="MinusButton.FontSize" Value="{ThemeResource CalcButtonCaptionSize}"/>
                        <Setter Target="PlusButton.FontSize" Value="{ThemeResource CalcButtonCaptionSize}"/>
                        <Setter Target="EqualButton.FontSize" Value="{ThemeResource CalcButtonCaptionSize}"/>
                        <Setter Target="SubmitButton.FontSize" Value="{ThemeResource CalcButtonCaptionSize}"/>
                        <Setter Target="DecimalSeparatorButton.Style" Value="{ThemeResource NumericButtonStyle38}"/>
                        <Setter Target="Num0Button.Style" Value="{ThemeResource NumericButtonStyle38}"/>
                        <Setter Target="Num1Button.Style" Value="{ThemeResource NumericButtonStyle38}"/>
                        <Setter Target="Num2Button.Style" Value="{ThemeResource NumericButtonStyle38}"/>
                        <Setter Target="Num3Button.Style" Value="{ThemeResource NumericButtonStyle38}"/>
                        <Setter Target="Num4Button.Style" Value="{ThemeResource NumericButtonStyle38}"/>
                        <Setter Target="Num5Button.Style" Value="{ThemeResource NumericButtonStyle38}"/>
                        <Setter Target="Num6Button.Style" Value="{ThemeResource NumericButtonStyle38}"/>
                        <Setter Target="Num7Button.Style" Value="{ThemeResource NumericButtonStyle38}"/>
                        <Setter Target="Num8Button.Style" Value="{ThemeResource NumericButtonStyle38}"/>
                        <Setter Target="Num9Button.Style" Value="{ThemeResource NumericButtonStyle38}"/>

                        <Setter Target="EulerButton.FontSize" Value="{ThemeResource CalcButtonCaptionSize}"/>
                        <Setter Target="AbsButton.FontSize" Value="{ThemeResource CalcButtonCaptionSize}"/>
                        <Setter Target="LogBaseY.FontSize" Value="{ThemeResource CalcButtonCaptionSize}"/>
                        <Setter Target="TwoPowerXButton.FontSize" Value="{ThemeResource CalcButtonCaptionSize}"/>
                        <Setter Target="CubeRootButton.FontSize" Value="{ThemeResource CalcButtonCaptionSize}"/>

                        <Setter Target="ShiftButton.FontSize" Value="{ThemeResource CalcButtonCaptionSize}"/>
                        <Setter Target="TrigShiftButton.FontSize" Value="{ThemeResource CalcButtonCaptionSize}"/>
                        <Setter Target="HypButton.FontSize" Value="{ThemeResource CalcButtonCaptionSize}"/>
                        <Setter Target="SecButton.FontSize" Value="{ThemeResource CalcButtonCaptionSize}"/>
                        <Setter Target="CscButton.FontSize" Value="{ThemeResource CalcButtonCaptionSize}"/>
                        <Setter Target="CotButton.FontSize" Value="{ThemeResource CalcButtonCaptionSize}"/>
                        <Setter Target="InvsecButton.FontSize" Value="{ThemeResource CalcButtonCaptionSize}"/>
                        <Setter Target="InvcscButton.FontSize" Value="{ThemeResource CalcButtonCaptionSize}"/>
                        <Setter Target="InvcotButton.FontSize" Value="{ThemeResource CalcButtonCaptionSize}"/>
                        <Setter Target="SechButton.FontSize" Value="{ThemeResource CalcButtonCaptionSize}"/>
                        <Setter Target="CschButton.FontSize" Value="{ThemeResource CalcButtonCaptionSize}"/>
                        <Setter Target="CothButton.FontSize" Value="{ThemeResource CalcButtonCaptionSize}"/>
                        <Setter Target="InvsechButton.FontSize" Value="{ThemeResource CalcButtonCaptionSize}"/>
                        <Setter Target="InvcschButton.FontSize" Value="{ThemeResource CalcButtonCaptionSize}"/>
                        <Setter Target="InvcothButton.FontSize" Value="{ThemeResource CalcButtonCaptionSize}"/>

                        <Setter Target="AbsFlyoutButton.FontSize" Value="{ThemeResource CalcButtonTextIconCaptionSize}"/>
                        <Setter Target="FloorButton.FontSize" Value="{ThemeResource CalcButtonTextIconCaptionSize}"/>
                        <Setter Target="CeilButton.FontSize" Value="{ThemeResource CalcButtonTextIconCaptionSize}"/>

                        <Setter Target="LessThanFlyoutButton.FontSize" Value="{ThemeResource CalcButtonTextIconCaptionSize}"/>
                        <Setter Target="LessThanOrEqualFlyoutButton.FontSize" Value="{ThemeResource CalcButtonTextIconCaptionSize}"/>
                        <Setter Target="EqualsFlyoutButton.FontSize" Value="{ThemeResource CalcButtonTextIconCaptionSize}"/>
                        <Setter Target="GreaterThanOrEqualFlyoutButton.FontSize" Value="{ThemeResource CalcButtonTextIconCaptionSize}"/>
                        <Setter Target="GreaterThanFlyoutButton.FontSize" Value="{ThemeResource CalcButtonTextIconCaptionSize}"/>

                        <Setter Target="OperatorPanelRow.MinHeight" Value="{StaticResource OperatorPanelButtonRowSizeLarge}"/>

                        <Setter Target="TrigButton.Style" Value="{StaticResource OperatorPanelButtonLargeStyle}"/>
                        <Setter Target="InequalityButton.Style" Value="{StaticResource OperatorPanelButtonLargeStyle}"/>
                        <Setter Target="FuncButton.Style" Value="{StaticResource OperatorPanelButtonLargeStyle}"/>

                        <Setter Target="TrigGrid.MinWidth" Value="516"/>
                        <Setter Target="TrigGrid.MinHeight" Value="192"/>
                        <Setter Target="FuncGrid.MinWidth" Value="387"/>
                        <Setter Target="FuncGrid.MinHeight" Value="96"/>

                        <Setter Target="InequalityGrid.MinWidth" Value="628"/>
                        <Setter Target="InequalityGrid.MinHeight" Value="96"/>
                    </VisualState.Setters>
                </VisualState>
                <VisualState x:Name="Medium">
                    <VisualState.StateTriggers>
                        <triggers:ControlSizeTrigger MinWidth="527"
                                                     MinHeight="523"
                                                     Source="{x:Bind GraphingOperators}"/>
                    </VisualState.StateTriggers>
                    <VisualState.Setters>
                        <Setter Target="NegateButton.FontSize" Value="{ThemeResource CalcStandardOperatorCaptionSizeLarge}"/>
                        <Setter Target="PiButton.FontSize" Value="{ThemeResource CalcStandardOperatorCaptionSize}"/>
                        <Setter Target="OpenParenthesisButton.FontSize" Value="{ThemeResource CalcStandardOperatorCaptionSize}"/>
                        <Setter Target="CloseParenthesisButton.FontSize" Value="{ThemeResource CalcStandardOperatorCaptionSize}"/>

                        <Setter Target="SinButton.FontSize" Value="{ThemeResource CalcStandardOperatorCaptionSize}"/>
                        <Setter Target="CosButton.FontSize" Value="{ThemeResource CalcStandardOperatorCaptionSize}"/>
                        <Setter Target="TanButton.FontSize" Value="{ThemeResource CalcStandardOperatorCaptionSize}"/>
                        <Setter Target="SinhButton.FontSize" Value="{ThemeResource CalcStandardOperatorCaptionSize}"/>
                        <Setter Target="CoshButton.FontSize" Value="{ThemeResource CalcStandardOperatorCaptionSize}"/>
                        <Setter Target="TanhButton.FontSize" Value="{ThemeResource CalcStandardOperatorCaptionSize}"/>
                        <Setter Target="InvsinButton.FontSize" Value="{ThemeResource CalcStandardOperatorCaptionSize}"/>
                        <Setter Target="InvcosButton.FontSize" Value="{ThemeResource CalcStandardOperatorCaptionSize}"/>
                        <Setter Target="InvtanButton.FontSize" Value="{ThemeResource CalcStandardOperatorCaptionSize}"/>
                        <Setter Target="InvsinhButton.FontSize" Value="{ThemeResource CalcStandardOperatorCaptionSize}"/>
                        <Setter Target="InvcoshButton.FontSize" Value="{ThemeResource CalcStandardOperatorCaptionSize}"/>
                        <Setter Target="InvtanhButton.FontSize" Value="{ThemeResource CalcStandardOperatorCaptionSize}"/>

                        <Setter Target="PowerButton.FontSize" Value="{ThemeResource CalcStandardOperatorCaptionSize}"/>
                        <Setter Target="PowerOf10Button.FontSize" Value="{ThemeResource CalcStandardOperatorCaptionSize}"/>
                        <Setter Target="LogBase10Button.FontSize" Value="{ThemeResource CalcStandardOperatorCaptionSize}"/>
                        <Setter Target="XButton.FontSize" Value="{ThemeResource CalcStandardOperatorCaptionSize}"/>
                        <Setter Target="YButton.FontSize" Value="{ThemeResource CalcStandardOperatorCaptionSize}"/>
                        <Setter Target="YSquareRootButton.FontSize" Value="{ThemeResource CalcStandardOperatorCaptionSize}"/>
                        <Setter Target="PowerOfEButton.FontSize" Value="{ThemeResource CalcStandardOperatorCaptionSize}"/>
                        <Setter Target="LogBaseEButton.FontSize" Value="{ThemeResource CalcStandardOperatorCaptionSize}"/>
                        <Setter Target="XPower2Button.FontSize" Value="{ThemeResource CalcStandardOperatorCaptionSize}"/>
                        <Setter Target="XPower3Button.FontSize" Value="{ThemeResource CalcStandardOperatorCaptionSize}"/>
                        <Setter Target="SquareRootButton.FontSize" Value="{ThemeResource CalcStandardOperatorCaptionSize}"/>
                        <Setter Target="InvertButton.FontSize" Value="{ThemeResource CalcStandardOperatorCaptionSize}"/>

                        <Setter Target="ClearButton.FontSize" Value="{ThemeResource CalcStandardOperatorCaptionSize}"/>
                        <Setter Target="BackSpaceButton.FontSize" Value="{ThemeResource CalcStandardOperatorCaptionSize}"/>
                        <Setter Target="DivideButton.FontSize" Value="{ThemeResource CalcStandardOperatorCaptionSize}"/>
                        <Setter Target="MultiplyButton.FontSize" Value="{ThemeResource CalcStandardOperatorCaptionSize}"/>
                        <Setter Target="MinusButton.FontSize" Value="{ThemeResource CalcStandardOperatorCaptionSize}"/>
                        <Setter Target="PlusButton.FontSize" Value="{ThemeResource CalcStandardOperatorCaptionSize}"/>
                        <Setter Target="EqualButton.FontSize" Value="{ThemeResource CalcStandardOperatorCaptionSize}"/>
                        <Setter Target="SubmitButton.FontSize" Value="{ThemeResource CalcStandardOperatorCaptionSize}"/>
                        <Setter Target="DecimalSeparatorButton.Style" Value="{ThemeResource NumericButtonStyle24}"/>
                        <Setter Target="Num0Button.Style" Value="{ThemeResource NumericButtonStyle24}"/>
                        <Setter Target="Num1Button.Style" Value="{ThemeResource NumericButtonStyle24}"/>
                        <Setter Target="Num2Button.Style" Value="{ThemeResource NumericButtonStyle24}"/>
                        <Setter Target="Num3Button.Style" Value="{ThemeResource NumericButtonStyle24}"/>
                        <Setter Target="Num4Button.Style" Value="{ThemeResource NumericButtonStyle24}"/>
                        <Setter Target="Num5Button.Style" Value="{ThemeResource NumericButtonStyle24}"/>
                        <Setter Target="Num6Button.Style" Value="{ThemeResource NumericButtonStyle24}"/>
                        <Setter Target="Num7Button.Style" Value="{ThemeResource NumericButtonStyle24}"/>
                        <Setter Target="Num8Button.Style" Value="{ThemeResource NumericButtonStyle24}"/>
                        <Setter Target="Num9Button.Style" Value="{ThemeResource NumericButtonStyle24}"/>

                        <Setter Target="EulerButton.FontSize" Value="{ThemeResource CalcStandardOperatorCaptionSize}"/>
                        <Setter Target="AbsButton.FontSize" Value="{ThemeResource CalcStandardOperatorCaptionSize}"/>
                        <Setter Target="LogBaseY.FontSize" Value="{ThemeResource CalcStandardOperatorCaptionSize}"/>
                        <Setter Target="TwoPowerXButton.FontSize" Value="{ThemeResource CalcStandardOperatorCaptionSize}"/>
                        <Setter Target="CubeRootButton.FontSize" Value="{ThemeResource CalcStandardOperatorCaptionSize}"/>

                        <Setter Target="ShiftButton.FontSize" Value="{ThemeResource CalcStandardOperatorCaptionSize}"/>
                        <Setter Target="TrigShiftButton.FontSize" Value="{ThemeResource CalcStandardOperatorCaptionSize}"/>
                        <Setter Target="HypButton.FontSize" Value="{ThemeResource CalcStandardOperatorCaptionSize}"/>
                        <Setter Target="SecButton.FontSize" Value="{ThemeResource CalcStandardOperatorCaptionSize}"/>
                        <Setter Target="CscButton.FontSize" Value="{ThemeResource CalcStandardOperatorCaptionSize}"/>
                        <Setter Target="CotButton.FontSize" Value="{ThemeResource CalcStandardOperatorCaptionSize}"/>
                        <Setter Target="InvsecButton.FontSize" Value="{ThemeResource CalcStandardOperatorCaptionSize}"/>
                        <Setter Target="InvcscButton.FontSize" Value="{ThemeResource CalcStandardOperatorCaptionSize}"/>
                        <Setter Target="InvcotButton.FontSize" Value="{ThemeResource CalcStandardOperatorCaptionSize}"/>
                        <Setter Target="SechButton.FontSize" Value="{ThemeResource CalcStandardOperatorCaptionSize}"/>
                        <Setter Target="CschButton.FontSize" Value="{ThemeResource CalcStandardOperatorCaptionSize}"/>
                        <Setter Target="CothButton.FontSize" Value="{ThemeResource CalcStandardOperatorCaptionSize}"/>
                        <Setter Target="InvsechButton.FontSize" Value="{ThemeResource CalcStandardOperatorCaptionSize}"/>
                        <Setter Target="InvcschButton.FontSize" Value="{ThemeResource CalcStandardOperatorCaptionSize}"/>
                        <Setter Target="InvcothButton.FontSize" Value="{ThemeResource CalcStandardOperatorCaptionSize}"/>

                        <Setter Target="AbsFlyoutButton.FontSize" Value="{ThemeResource CalcStandardOperatorTextIconCaptionSize}"/>
                        <Setter Target="FloorButton.FontSize" Value="{ThemeResource CalcStandardOperatorTextIconCaptionSize}"/>
                        <Setter Target="CeilButton.FontSize" Value="{ThemeResource CalcStandardOperatorTextIconCaptionSize}"/>

                        <Setter Target="LessThanFlyoutButton.FontSize" Value="{ThemeResource CalcStandardOperatorTextIconCaptionSize}"/>
                        <Setter Target="LessThanOrEqualFlyoutButton.FontSize" Value="{ThemeResource CalcStandardOperatorTextIconCaptionSize}"/>
                        <Setter Target="EqualsFlyoutButton.FontSize" Value="{ThemeResource CalcStandardOperatorTextIconCaptionSize}"/>
                        <Setter Target="GreaterThanOrEqualFlyoutButton.FontSize" Value="{ThemeResource CalcStandardOperatorTextIconCaptionSize}"/>
                        <Setter Target="GreaterThanFlyoutButton.FontSize" Value="{ThemeResource CalcStandardOperatorTextIconCaptionSize}"/>

                        <Setter Target="OperatorPanelRow.MinHeight" Value="{StaticResource OperatorPanelButtonRowSizeMedium}"/>

                        <Setter Target="TrigButton.Style" Value="{StaticResource OperatorPanelButtonMediumStyle}"/>
                        <Setter Target="InequalityButton.Style" Value="{StaticResource OperatorPanelButtonMediumStyle}"/>
                        <Setter Target="FuncButton.Style" Value="{StaticResource OperatorPanelButtonMediumStyle}"/>

                        <Setter Target="TrigGrid.MinWidth" Value="480"/>
                        <Setter Target="TrigGrid.MinHeight" Value="144"/>
                        <Setter Target="FuncGrid.MinWidth" Value="360"/>
                        <Setter Target="FuncGrid.MinHeight" Value="72"/>
                        <Setter Target="InequalityGrid.MinWidth" Value="585"/>
                        <Setter Target="InequalityGrid.MinHeight" Value="72"/>

                    </VisualState.Setters>
                </VisualState>
                <VisualState x:Name="Small">
                    <VisualState.StateTriggers>
                        <AdaptiveTrigger MinWindowHeight="0" MinWindowWidth="0"/>
                    </VisualState.StateTriggers>
                    <VisualState.Setters>
                        <Setter Target="NegateButton.FontSize" Value="{ThemeResource CalcOperatorCaptionSize}"/>
                        <Setter Target="PiButton.FontSize" Value="{ThemeResource CalcOperatorCaptionSize}"/>
                        <Setter Target="OpenParenthesisButton.FontSize" Value="{ThemeResource CalcOperatorCaptionSize}"/>
                        <Setter Target="CloseParenthesisButton.FontSize" Value="{ThemeResource CalcOperatorCaptionSize}"/>

                        <Setter Target="SinButton.FontSize" Value="{ThemeResource CalcOperatorCaptionSize}"/>
                        <Setter Target="CosButton.FontSize" Value="{ThemeResource CalcOperatorCaptionSize}"/>
                        <Setter Target="TanButton.FontSize" Value="{ThemeResource CalcOperatorCaptionSize}"/>
                        <Setter Target="SinhButton.FontSize" Value="{ThemeResource CalcOperatorCaptionSize}"/>
                        <Setter Target="CoshButton.FontSize" Value="{ThemeResource CalcOperatorCaptionSize}"/>
                        <Setter Target="TanhButton.FontSize" Value="{ThemeResource CalcOperatorCaptionSize}"/>
                        <Setter Target="InvsinButton.FontSize" Value="{ThemeResource CalcOperatorCaptionSize}"/>
                        <Setter Target="InvcosButton.FontSize" Value="{ThemeResource CalcOperatorCaptionSize}"/>
                        <Setter Target="InvtanButton.FontSize" Value="{ThemeResource CalcOperatorCaptionSize}"/>
                        <Setter Target="InvsinhButton.FontSize" Value="{ThemeResource CalcOperatorCaptionSize}"/>
                        <Setter Target="InvcoshButton.FontSize" Value="{ThemeResource CalcOperatorCaptionSize}"/>
                        <Setter Target="InvtanhButton.FontSize" Value="{ThemeResource CalcOperatorCaptionSize}"/>

                        <Setter Target="PowerButton.FontSize" Value="{ThemeResource CalcOperatorCaptionSize}"/>
                        <Setter Target="PowerOf10Button.FontSize" Value="{ThemeResource CalcOperatorCaptionSize}"/>
                        <Setter Target="LogBase10Button.FontSize" Value="{ThemeResource CalcOperatorCaptionSize}"/>
                        <Setter Target="XButton.FontSize" Value="{ThemeResource CalcOperatorCaptionSize}"/>
                        <Setter Target="YButton.FontSize" Value="{ThemeResource CalcOperatorCaptionSize}"/>
                        <Setter Target="YSquareRootButton.FontSize" Value="{ThemeResource CalcOperatorCaptionSize}"/>
                        <Setter Target="PowerOfEButton.FontSize" Value="{ThemeResource CalcOperatorCaptionSize}"/>
                        <Setter Target="LogBaseEButton.FontSize" Value="{ThemeResource CalcOperatorCaptionSize}"/>
                        <Setter Target="XPower2Button.FontSize" Value="{ThemeResource CalcOperatorCaptionSize}"/>
                        <Setter Target="XPower3Button.FontSize" Value="{ThemeResource CalcOperatorCaptionSize}"/>
                        <Setter Target="SquareRootButton.FontSize" Value="{ThemeResource CalcOperatorCaptionSize}"/>
                        <Setter Target="InvertButton.FontSize" Value="{ThemeResource CalcOperatorCaptionSize}"/>

                        <Setter Target="ClearButton.FontSize" Value="{ThemeResource CalcOperatorCaptionSize}"/>
                        <Setter Target="BackSpaceButton.FontSize" Value="{ThemeResource CalcOperatorCaptionSize}"/>
                        <Setter Target="DivideButton.FontSize" Value="{ThemeResource CalcOperatorCaptionSize}"/>
                        <Setter Target="MultiplyButton.FontSize" Value="{ThemeResource CalcOperatorCaptionSize}"/>
                        <Setter Target="MinusButton.FontSize" Value="{ThemeResource CalcOperatorCaptionSize}"/>
                        <Setter Target="PlusButton.FontSize" Value="{ThemeResource CalcOperatorCaptionSize}"/>
                        <Setter Target="EqualButton.FontSize" Value="{ThemeResource CalcOperatorCaptionSize}"/>
                        <Setter Target="SubmitButton.FontSize" Value="{ThemeResource CalcOperatorCaptionSize}"/>
                        <Setter Target="DecimalSeparatorButton.Style" Value="{ThemeResource NumericButtonStyle18}"/>
                        <Setter Target="Num0Button.Style" Value="{ThemeResource NumericButtonStyle18}"/>
                        <Setter Target="Num1Button.Style" Value="{ThemeResource NumericButtonStyle18}"/>
                        <Setter Target="Num2Button.Style" Value="{ThemeResource NumericButtonStyle18}"/>
                        <Setter Target="Num3Button.Style" Value="{ThemeResource NumericButtonStyle18}"/>
                        <Setter Target="Num4Button.Style" Value="{ThemeResource NumericButtonStyle18}"/>
                        <Setter Target="Num5Button.Style" Value="{ThemeResource NumericButtonStyle18}"/>
                        <Setter Target="Num6Button.Style" Value="{ThemeResource NumericButtonStyle18}"/>
                        <Setter Target="Num7Button.Style" Value="{ThemeResource NumericButtonStyle18}"/>
                        <Setter Target="Num8Button.Style" Value="{ThemeResource NumericButtonStyle18}"/>
                        <Setter Target="Num9Button.Style" Value="{ThemeResource NumericButtonStyle18}"/>

                        <Setter Target="EulerButton.FontSize" Value="{ThemeResource CalcOperatorCaptionSize}"/>
                        <Setter Target="AbsButton.FontSize" Value="{ThemeResource CalcOperatorCaptionSize}"/>
                        <Setter Target="LogBaseY.FontSize" Value="16"/>
                        <Setter Target="TwoPowerXButton.FontSize" Value="{ThemeResource CalcOperatorCaptionSize}"/>
                        <Setter Target="CubeRootButton.FontSize" Value="{ThemeResource CalcOperatorCaptionSize}"/>

                        <Setter Target="ShiftButton.FontSize" Value="{ThemeResource CalcOperatorCaptionSize}"/>
                        <Setter Target="TrigShiftButton.FontSize" Value="{ThemeResource CalcOperatorCaptionSize}"/>
                        <Setter Target="HypButton.FontSize" Value="{ThemeResource CalcOperatorCaptionSize}"/>
                        <Setter Target="SecButton.FontSize" Value="{ThemeResource CalcOperatorCaptionSize}"/>
                        <Setter Target="CscButton.FontSize" Value="{ThemeResource CalcOperatorCaptionSize}"/>
                        <Setter Target="CotButton.FontSize" Value="{ThemeResource CalcOperatorCaptionSize}"/>
                        <Setter Target="InvsecButton.FontSize" Value="{ThemeResource CalcOperatorCaptionSize}"/>
                        <Setter Target="InvcscButton.FontSize" Value="{ThemeResource CalcOperatorCaptionSize}"/>
                        <Setter Target="InvcotButton.FontSize" Value="{ThemeResource CalcOperatorCaptionSize}"/>
                        <Setter Target="SechButton.FontSize" Value="{ThemeResource CalcOperatorCaptionSize}"/>
                        <Setter Target="CschButton.FontSize" Value="{ThemeResource CalcOperatorCaptionSize}"/>
                        <Setter Target="CothButton.FontSize" Value="{ThemeResource CalcOperatorCaptionSize}"/>
                        <Setter Target="InvsechButton.FontSize" Value="{ThemeResource CalcOperatorCaptionSize}"/>
                        <Setter Target="InvcschButton.FontSize" Value="{ThemeResource CalcOperatorCaptionSize}"/>
                        <Setter Target="InvcothButton.FontSize" Value="{ThemeResource CalcOperatorCaptionSize}"/>

                        <Setter Target="AbsFlyoutButton.FontSize" Value="{ThemeResource CalcOperatorTextIconCaptionSize}"/>
                        <Setter Target="FloorButton.FontSize" Value="{ThemeResource CalcOperatorTextIconCaptionSize}"/>
                        <Setter Target="CeilButton.FontSize" Value="{ThemeResource CalcOperatorTextIconCaptionSize}"/>

                        <Setter Target="LessThanFlyoutButton.FontSize" Value="{ThemeResource CalcOperatorTextIconCaptionSize}"/>
                        <Setter Target="LessThanOrEqualFlyoutButton.FontSize" Value="{ThemeResource CalcOperatorTextIconCaptionSize}"/>
                        <Setter Target="EqualsFlyoutButton.FontSize" Value="{ThemeResource CalcOperatorTextIconCaptionSize}"/>
                        <Setter Target="GreaterThanOrEqualFlyoutButton.FontSize" Value="{ThemeResource CalcOperatorTextIconCaptionSize}"/>
                        <Setter Target="GreaterThanFlyoutButton.FontSize" Value="{ThemeResource CalcOperatorTextIconCaptionSize}"/>

                        <Setter Target="OperatorPanelRow.MinHeight" Value="{ThemeResource OperatorPanelButtonRowSizeSmall}"/>

                        <Setter Target="TrigButton.Style" Value="{StaticResource OperatorPanelButtonSmallStyle}"/>
                        <Setter Target="InequalityButton.Style" Value="{StaticResource OperatorPanelButtonSmallStyle}"/>
                        <Setter Target="FuncButton.Style" Value="{StaticResource OperatorPanelButtonSmallStyle}"/>

                        <Setter Target="TrigGrid.MinWidth" Value="258"/>
                        <Setter Target="TrigGrid.MinHeight" Value="96"/>
                        <Setter Target="FuncGrid.MinWidth" Value="194"/>
                        <Setter Target="FuncGrid.MinHeight" Value="48"/>
                        <Setter Target="InequalityGrid.MinWidth" Value="312"/>
                        <Setter Target="InequalityGrid.MinHeight" Value="48"/>

                    </VisualState.Setters>
                </VisualState>
            </VisualStateGroup>
        </VisualStateManager.VisualStateGroups>
        <Grid x:Name="GraphingOperators">
            <Grid.RowDefinitions>
                <RowDefinition x:Name="OperatorPanelRow" Height="Auto"/>
                <RowDefinition Height="1*"/>
                <RowDefinition Height="1*"/>
                <RowDefinition Height="1*"/>
                <RowDefinition Height="1*"/>
                <RowDefinition Height="1*"/>
                <RowDefinition Height="1*"/>
                <RowDefinition Height="1*"/>
            </Grid.RowDefinitions>
            <Grid.ColumnDefinitions>
                <ColumnDefinition Width="1*"/>
                <ColumnDefinition Width="1*"/>
                <ColumnDefinition Width="1*"/>
                <ColumnDefinition Width="1*"/>
                <ColumnDefinition Width="1*"/>
            </Grid.ColumnDefinitions>

            <controls:OperatorPanelListView Grid.ColumnSpan="5"
                                            AutomationProperties.HeadingLevel="Level1"
                                            AutomationProperties.Name="{utils:ResourceString Name=ScientificOperatorPanel/[using:Windows.UI.Xaml.Automation]AutomationProperties/Name}">

                <controls:OperatorPanelButton x:Name="TrigButton"
                                              Style="{StaticResource OperatorPanelButtonStyle}"
                                              AutomationProperties.AutomationId="trigButton"
                                              AutomationProperties.Name="{utils:ResourceString Name=trigButton/[using:Windows.UI.Xaml.Automation]AutomationProperties/Name}"
                                              Glyph="&#xF892;"
                                              IsTabStop="false"
                                              Text="{utils:ResourceString Name=trigButton/Text}">

                    <controls:OperatorPanelButton.FlyoutMenu>
                        <Flyout x:Name="Trigflyout"
                                Windows10version1803:Placement="Bottom"
                                Windows10version1809:AreOpenCloseAnimationsEnabled="False"
                                Windows10version1809:Placement="BottomEdgeAlignedLeft"
                                AllowFocusOnInteraction="False"
                                FlyoutPresenterStyle="{ThemeResource OperatorPanelFlyoutStyle}"
                                Opening="Flyout_Opening">
                            <Grid x:Name="TrigGrid"
                                  MinWidth="258"
                                  MinHeight="96"
                                  XYFocusKeyboardNavigation="Enabled">
                                <Grid.RowDefinitions>
                                    <RowDefinition Height="1*"/>
                                    <RowDefinition Height="1*"/>
                                </Grid.RowDefinitions>
                                <Grid.ColumnDefinitions>
                                    <ColumnDefinition Width="1*"/>
                                    <ColumnDefinition Width="1*"/>
                                    <ColumnDefinition Width="1*"/>
                                    <ColumnDefinition Width="1*"/>
                                </Grid.ColumnDefinitions>
                                <!--
                                    Because of the way we handle keyboard shortcuts, the IsEnabled property must be bound to a button outside the flyout.
                                    This is because the content from within the Flyout do not inherit the IsEnabled property of the flyout parent,
                                    causing the shortcut keys to be used when the control would normally be disabled.
                                -->
                                <ToggleButton x:Name="TrigShiftButton"
                                              Style="{StaticResource CaptionToggleEmphasizedButtonStyle}"
                                              FontFamily="{StaticResource CalculatorFontFamily}"
                                              AutomationProperties.AutomationId="trigShiftButton"
                                              AutomationProperties.Name="{utils:ResourceString Name=trigShiftButton/[using:Windows.UI.Xaml.Automation]AutomationProperties/Name}"
                                              Checked="TrigFlyoutShift_Toggle"
                                              Content="&#xF897;"
                                              IsTabStop="false"
                                              Unchecked="TrigFlyoutShift_Toggle"
                                              IsEnabled="{x:Bind TrigButton.IsEnabled, Mode=OneWay}"/>

                                <ToggleButton x:Name="HypButton"
                                              Grid.Row="1"
                                              Style="{StaticResource CaptionToggleEmphasizedButtonStyle}"
                                              AutomationProperties.AutomationId="hypShiftButton"
                                              AutomationProperties.Name="{utils:ResourceString Name=hypButton/[using:Windows.UI.Xaml.Automation]AutomationProperties/Name}"
                                              Checked="TrigFlyoutHyp_Toggle"
                                              Content="hyp"
                                              IsEnabledChanged="ShiftButton_IsEnabledChanged"
                                              IsTabStop="false"
                                              Unchecked="TrigFlyoutHyp_Toggle"
                                              IsEnabled="{x:Bind TrigButton.IsEnabled, Mode=OneWay}"/>

                                <Grid x:Name="TrigFunctions"
                                      Grid.RowSpan="2"
                                      Grid.Column="1"
                                      Grid.ColumnSpan="3">
                                    <Grid.RowDefinitions>
                                        <RowDefinition Height="1*"/>
                                        <RowDefinition Height="1*"/>
                                    </Grid.RowDefinitions>

                                    <Grid.ColumnDefinitions>
                                        <ColumnDefinition Width="1*"/>
                                        <ColumnDefinition Width="1*"/>
                                        <ColumnDefinition Width="1*"/>
                                    </Grid.ColumnDefinitions>

                                    <controls:CalculatorButton x:Name="SinButton"
                                                               Style="{StaticResource OperatorButtonStyle}"
                                                               common:KeyboardShortcutManager.VirtualKey="{utils:ResourceVirtualKey Name=sinButton/[using:CalculatorApp.Common]KeyboardShortcutManager/VirtualKey}"
                                                               AutomationProperties.AutomationId="sinButton"
                                                               AutomationProperties.Name="{utils:ResourceString Name=sinButton/[using:Windows.UI.Xaml.Automation]AutomationProperties/Name}"
                                                               ButtonId="Sin"
                                                               Click="FlyoutButton_Clicked"
                                                               Content="sin"
                                                               IsTabStop="false"/>

                                    <controls:CalculatorButton x:Name="CosButton"
                                                               Grid.Column="1"
                                                               Style="{StaticResource OperatorButtonStyle}"
                                                               common:KeyboardShortcutManager.VirtualKey="{utils:ResourceVirtualKey Name=cosButton/[using:CalculatorApp.Common]KeyboardShortcutManager/VirtualKey}"
                                                               AutomationProperties.AutomationId="cosButton"
                                                               AutomationProperties.Name="{utils:ResourceString Name=cosButton/[using:Windows.UI.Xaml.Automation]AutomationProperties/Name}"
                                                               ButtonId="Cos"
                                                               Click="FlyoutButton_Clicked"
                                                               Content="cos"
                                                               IsTabStop="false"
                                                               IsEnabled="{x:Bind TrigButton.IsEnabled, Mode=OneWay}"/>

                                    <controls:CalculatorButton x:Name="TanButton"
                                                               Grid.Column="2"
                                                               Style="{StaticResource OperatorButtonStyle}"
                                                               common:KeyboardShortcutManager.VirtualKey="{utils:ResourceVirtualKey Name=tanButton/[using:CalculatorApp.Common]KeyboardShortcutManager/VirtualKey}"
                                                               AutomationProperties.AutomationId="tanButton"
                                                               AutomationProperties.Name="{utils:ResourceString Name=tanButton/[using:Windows.UI.Xaml.Automation]AutomationProperties/Name}"
                                                               ButtonId="Tan"
                                                               Click="FlyoutButton_Clicked"
                                                               Content="tan"
                                                               IsTabStop="false"
                                                               IsEnabled="{x:Bind TrigButton.IsEnabled, Mode=OneWay}"/>

                                    <controls:CalculatorButton x:Name="SecButton"
                                                               Grid.Row="1"
                                                               Style="{StaticResource OperatorButtonStyle}"
                                                               common:KeyboardShortcutManager.VirtualKey="{utils:ResourceVirtualKey Name=secButton/[using:CalculatorApp.Common]KeyboardShortcutManager/VirtualKey}"
                                                               AutomationProperties.AutomationId="secButton"
                                                               AutomationProperties.Name="{utils:ResourceString Name=secButton/[using:Windows.UI.Xaml.Automation]AutomationProperties/Name}"
                                                               ButtonId="Sec"
                                                               Click="FlyoutButton_Clicked"
                                                               Content="sec"
                                                               IsTabStop="false"
                                                               IsEnabled="{x:Bind TrigButton.IsEnabled, Mode=OneWay}"/>

                                    <controls:CalculatorButton x:Name="CscButton"
                                                               Grid.Row="1"
                                                               Grid.Column="1"
                                                               Style="{StaticResource OperatorButtonStyle}"
                                                               common:KeyboardShortcutManager.VirtualKey="{utils:ResourceVirtualKey Name=cscButton/[using:CalculatorApp.Common]KeyboardShortcutManager/VirtualKey}"
                                                               AutomationProperties.AutomationId="cscButton"
                                                               AutomationProperties.Name="{utils:ResourceString Name=cscButton/[using:Windows.UI.Xaml.Automation]AutomationProperties/Name}"
                                                               ButtonId="Csc"
                                                               Click="FlyoutButton_Clicked"
                                                               Content="csc"
                                                               IsTabStop="false"
                                                               IsEnabled="{x:Bind TrigButton.IsEnabled, Mode=OneWay}"/>

                                    <controls:CalculatorButton x:Name="CotButton"
                                                               Grid.Row="1"
                                                               Grid.Column="2"
                                                               Style="{StaticResource OperatorButtonStyle}"
                                                               common:KeyboardShortcutManager.VirtualKey="{utils:ResourceVirtualKey Name=cotButton/[using:CalculatorApp.Common]KeyboardShortcutManager/VirtualKey}"
                                                               AutomationProperties.AutomationId="cotButton"
                                                               AutomationProperties.Name="{utils:ResourceString Name=cotButton/[using:Windows.UI.Xaml.Automation]AutomationProperties/Name}"
                                                               ButtonId="Cot"
                                                               Click="FlyoutButton_Clicked"
                                                               Content="cot"
                                                               IsTabStop="false"
                                                               IsEnabled="{x:Bind TrigButton.IsEnabled, Mode=OneWay}"/>
                                </Grid>

                                <Grid x:Name="InverseTrigFunctions"
                                      Grid.RowSpan="2"
                                      Grid.Column="1"
                                      Grid.ColumnSpan="3"
                                      Visibility="Collapsed">
                                    <Grid.RowDefinitions>
                                        <RowDefinition Height="1*"/>
                                        <RowDefinition Height="1*"/>
                                    </Grid.RowDefinitions>
                                    <Grid.ColumnDefinitions>
                                        <ColumnDefinition Width="1*"/>
                                        <ColumnDefinition Width="1*"/>
                                        <ColumnDefinition Width="1*"/>
                                    </Grid.ColumnDefinitions>

                                    <controls:CalculatorButton x:Name="InvsinButton"
                                                               Style="{StaticResource OperatorButtonStyle}"
                                                               common:KeyboardShortcutManager.VirtualKeyShiftChord="{utils:ResourceVirtualKey Name=invsinButton/[using:CalculatorApp.Common]KeyboardShortcutManager/VirtualKeyShiftChord}"
                                                               AutomationProperties.AutomationId="invsinButton"
                                                               AutomationProperties.Name="{utils:ResourceString Name=invsinButton/[using:Windows.UI.Xaml.Automation]AutomationProperties/Name}"
                                                               ButtonId="InvSin"
                                                               Click="FlyoutButton_Clicked"
                                                               Content="sin⁻¹"
                                                               IsTabStop="false"
                                                               IsEnabled="{x:Bind TrigButton.IsEnabled, Mode=OneWay}"/>

                                    <controls:CalculatorButton x:Name="InvcosButton"
                                                               Grid.Column="1"
                                                               Style="{StaticResource OperatorButtonStyle}"
                                                               common:KeyboardShortcutManager.VirtualKeyShiftChord="{utils:ResourceVirtualKey Name=invcosButton/[using:CalculatorApp.Common]KeyboardShortcutManager/VirtualKeyShiftChord}"
                                                               AutomationProperties.AutomationId="invcosButton"
                                                               AutomationProperties.Name="{utils:ResourceString Name=invcosButton/[using:Windows.UI.Xaml.Automation]AutomationProperties/Name}"
                                                               ButtonId="InvCos"
                                                               Click="FlyoutButton_Clicked"
                                                               Content="cos⁻¹"
                                                               IsTabStop="false"
                                                               IsEnabled="{x:Bind TrigButton.IsEnabled, Mode=OneWay}"/>

                                    <controls:CalculatorButton x:Name="InvtanButton"
                                                               Grid.Column="2"
                                                               Style="{StaticResource OperatorButtonStyle}"
                                                               common:KeyboardShortcutManager.VirtualKeyShiftChord="{utils:ResourceVirtualKey Name=invtanButton/[using:CalculatorApp.Common]KeyboardShortcutManager/VirtualKeyShiftChord}"
                                                               AutomationProperties.AutomationId="invtanButton"
                                                               AutomationProperties.Name="{utils:ResourceString Name=invtanButton/[using:Windows.UI.Xaml.Automation]AutomationProperties/Name}"
                                                               ButtonId="InvTan"
                                                               Click="FlyoutButton_Clicked"
                                                               Content="tan⁻¹"
                                                               IsTabStop="false"
                                                               IsEnabled="{x:Bind TrigButton.IsEnabled, Mode=OneWay}"/>

                                    <controls:CalculatorButton x:Name="InvsecButton"
                                                               Grid.Row="1"
                                                               Style="{StaticResource OperatorButtonStyle}"
                                                               common:KeyboardShortcutManager.VirtualKeyShiftChord="{utils:ResourceVirtualKey Name=invsecButton/[using:CalculatorApp.Common]KeyboardShortcutManager/VirtualKeyShiftChord}"
                                                               AutomationProperties.AutomationId="invsecButton"
                                                               AutomationProperties.Name="{utils:ResourceString Name=invsecButton/[using:Windows.UI.Xaml.Automation]AutomationProperties/Name}"
                                                               ButtonId="InvSec"
                                                               Click="FlyoutButton_Clicked"
                                                               Content="sec⁻¹"
                                                               IsTabStop="false"
                                                               IsEnabled="{x:Bind TrigButton.IsEnabled, Mode=OneWay}"/>

                                    <controls:CalculatorButton x:Name="InvcscButton"
                                                               Grid.Row="1"
                                                               Grid.Column="1"
                                                               Style="{StaticResource OperatorButtonStyle}"
                                                               common:KeyboardShortcutManager.VirtualKeyShiftChord="{utils:ResourceVirtualKey Name=invcscButton/[using:CalculatorApp.Common]KeyboardShortcutManager/VirtualKeyShiftChord}"
                                                               AutomationProperties.AutomationId="invcscButton"
                                                               AutomationProperties.Name="{utils:ResourceString Name=invcscButton/[using:Windows.UI.Xaml.Automation]AutomationProperties/Name}"
                                                               ButtonId="InvCsc"
                                                               Click="FlyoutButton_Clicked"
                                                               Content="csc⁻¹"
                                                               IsTabStop="false"
                                                               IsEnabled="{x:Bind TrigButton.IsEnabled, Mode=OneWay}"/>

                                    <controls:CalculatorButton x:Name="InvcotButton"
                                                               Grid.Row="1"
                                                               Grid.Column="2"
                                                               Style="{StaticResource OperatorButtonStyle}"
                                                               common:KeyboardShortcutManager.VirtualKeyShiftChord="{utils:ResourceVirtualKey Name=invcotButton/[using:CalculatorApp.Common]KeyboardShortcutManager/VirtualKeyShiftChord}"
                                                               AutomationProperties.AutomationId="invcotButton"
                                                               AutomationProperties.Name="{utils:ResourceString Name=invcotButton/[using:Windows.UI.Xaml.Automation]AutomationProperties/Name}"
                                                               ButtonId="InvCot"
                                                               Click="FlyoutButton_Clicked"
                                                               Content="cot⁻¹"
                                                               IsTabStop="false"
                                                               IsEnabled="{x:Bind TrigButton.IsEnabled, Mode=OneWay}"/>
                                </Grid>

                                <Grid x:Name="HyperbolicTrigFunctions"
                                      Grid.RowSpan="2"
                                      Grid.Column="1"
                                      Grid.ColumnSpan="3"
                                      Visibility="Collapsed">
                                    <Grid.RowDefinitions>
                                        <RowDefinition Height="1*"/>
                                        <RowDefinition Height="1*"/>
                                    </Grid.RowDefinitions>
                                    <Grid.ColumnDefinitions>
                                        <ColumnDefinition Width="1*"/>
                                        <ColumnDefinition Width="1*"/>
                                        <ColumnDefinition Width="1*"/>
                                    </Grid.ColumnDefinitions>

                                    <controls:CalculatorButton x:Name="SinhButton"
                                                               Style="{StaticResource OperatorButtonStyle}"
                                                               common:KeyboardShortcutManager.VirtualKeyControlChord="{utils:ResourceVirtualKey Name=sinhButton/[using:CalculatorApp.Common]KeyboardShortcutManager/VirtualKeyControlChord}"
                                                               AutomationProperties.AutomationId="sinhButton"
                                                               AutomationProperties.Name="{utils:ResourceString Name=sinhButton/[using:Windows.UI.Xaml.Automation]AutomationProperties/Name}"
                                                               ButtonId="Sinh"
                                                               Click="FlyoutButton_Clicked"
                                                               Content="sinh"
                                                               IsTabStop="false"
                                                               IsEnabled="{x:Bind TrigButton.IsEnabled, Mode=OneWay}"/>

                                    <controls:CalculatorButton x:Name="CoshButton"
                                                               Grid.Column="1"
                                                               Style="{StaticResource OperatorButtonStyle}"
                                                               common:KeyboardShortcutManager.VirtualKeyControlChord="{utils:ResourceVirtualKey Name=coshButton/[using:CalculatorApp.Common]KeyboardShortcutManager/VirtualKeyControlChord}"
                                                               AutomationProperties.AutomationId="coshButton"
                                                               AutomationProperties.Name="{utils:ResourceString Name=coshButton/[using:Windows.UI.Xaml.Automation]AutomationProperties/Name}"
                                                               ButtonId="Cosh"
                                                               Click="FlyoutButton_Clicked"
                                                               Content="cosh"
                                                               IsTabStop="false"
                                                               IsEnabled="{x:Bind TrigButton.IsEnabled, Mode=OneWay}"/>

                                    <controls:CalculatorButton x:Name="TanhButton"
                                                               Grid.Column="2"
                                                               Style="{StaticResource OperatorButtonStyle}"
                                                               common:KeyboardShortcutManager.VirtualKeyControlChord="{utils:ResourceVirtualKey Name=tanhButton/[using:CalculatorApp.Common]KeyboardShortcutManager/VirtualKeyControlChord}"
                                                               AutomationProperties.AutomationId="tanhButton"
                                                               AutomationProperties.Name="{utils:ResourceString Name=tanhButton/[using:Windows.UI.Xaml.Automation]AutomationProperties/Name}"
                                                               ButtonId="Tanh"
                                                               Click="FlyoutButton_Clicked"
                                                               Content="tanh"
                                                               IsTabStop="false"
                                                               IsEnabled="{x:Bind TrigButton.IsEnabled, Mode=OneWay}"/>

                                    <controls:CalculatorButton x:Name="SechButton"
                                                               Grid.Row="1"
                                                               Style="{StaticResource OperatorButtonStyle}"
                                                               common:KeyboardShortcutManager.VirtualKeyControlChord="{utils:ResourceVirtualKey Name=sechButton/[using:CalculatorApp.Common]KeyboardShortcutManager/VirtualKeyControlChord}"
                                                               AutomationProperties.AutomationId="sechButton"
                                                               AutomationProperties.Name="{utils:ResourceString Name=sechButton/[using:Windows.UI.Xaml.Automation]AutomationProperties/Name}"
                                                               ButtonId="Sech"
                                                               Click="FlyoutButton_Clicked"
                                                               Content="sech"
                                                               IsTabStop="false"
                                                               IsEnabled="{x:Bind TrigButton.IsEnabled, Mode=OneWay}"/>

                                    <controls:CalculatorButton x:Name="CschButton"
                                                               Grid.Row="1"
                                                               Grid.Column="1"
                                                               Style="{StaticResource OperatorButtonStyle}"
                                                               common:KeyboardShortcutManager.VirtualKeyControlChord="{utils:ResourceVirtualKey Name=cschButton/[using:CalculatorApp.Common]KeyboardShortcutManager/VirtualKeyControlChord}"
                                                               AutomationProperties.AutomationId="cschButton"
                                                               AutomationProperties.Name="{utils:ResourceString Name=cschButton/[using:Windows.UI.Xaml.Automation]AutomationProperties/Name}"
                                                               ButtonId="Csch"
                                                               Click="FlyoutButton_Clicked"
                                                               Content="csch"
                                                               IsTabStop="false"
                                                               IsEnabled="{x:Bind TrigButton.IsEnabled, Mode=OneWay}"/>

                                    <controls:CalculatorButton x:Name="CothButton"
                                                               Grid.Row="1"
                                                               Grid.Column="2"
                                                               Style="{StaticResource OperatorButtonStyle}"
                                                               common:KeyboardShortcutManager.VirtualKeyControlChord="{utils:ResourceVirtualKey Name=cothButton/[using:CalculatorApp.Common]KeyboardShortcutManager/VirtualKeyControlChord}"
                                                               AutomationProperties.AutomationId="cothButton"
                                                               AutomationProperties.Name="{utils:ResourceString Name=cothButton/[using:Windows.UI.Xaml.Automation]AutomationProperties/Name}"
                                                               ButtonId="Coth"
                                                               Click="FlyoutButton_Clicked"
                                                               Content="coth"
                                                               IsTabStop="false"
                                                               IsEnabled="{x:Bind TrigButton.IsEnabled, Mode=OneWay}"/>
                                </Grid>

                                <Grid x:Name="InverseHyperbolicTrigFunctions"
                                      Grid.RowSpan="2"
                                      Grid.Column="1"
                                      Grid.ColumnSpan="3"
                                      Visibility="Collapsed">
                                    <Grid.RowDefinitions>
                                        <RowDefinition Height="1*"/>
                                        <RowDefinition Height="1*"/>
                                    </Grid.RowDefinitions>
                                    <Grid.ColumnDefinitions>
                                        <ColumnDefinition Width="1*"/>
                                        <ColumnDefinition Width="1*"/>
                                        <ColumnDefinition Width="1*"/>
                                    </Grid.ColumnDefinitions>
                                    <controls:CalculatorButton x:Name="InvsinhButton"
                                                               Style="{StaticResource OperatorButtonStyle}"
                                                               common:KeyboardShortcutManager.VirtualKeyControlShiftChord="{utils:ResourceVirtualKey Name=invsinhButton/[using:CalculatorApp.Common]KeyboardShortcutManager/VirtualKeyControlShiftChord}"
                                                               AutomationProperties.AutomationId="invsinhButton"
                                                               AutomationProperties.Name="{utils:ResourceString Name=invsinhButton/[using:Windows.UI.Xaml.Automation]AutomationProperties/Name}"
                                                               ButtonId="InvSinh"
                                                               Click="FlyoutButton_Clicked"
                                                               Content="sinh⁻¹"
                                                               IsTabStop="false"
                                                               IsEnabled="{x:Bind TrigButton.IsEnabled, Mode=OneWay}"/>

                                    <controls:CalculatorButton x:Name="InvcoshButton"
                                                               Grid.Column="1"
                                                               Style="{StaticResource OperatorButtonStyle}"
                                                               common:KeyboardShortcutManager.VirtualKeyControlShiftChord="{utils:ResourceVirtualKey Name=invcoshButton/[using:CalculatorApp.Common]KeyboardShortcutManager/VirtualKeyControlShiftChord}"
                                                               AutomationProperties.AutomationId="invcoshButton"
                                                               AutomationProperties.Name="{utils:ResourceString Name=invcoshButton/[using:Windows.UI.Xaml.Automation]AutomationProperties/Name}"
                                                               ButtonId="InvCosh"
                                                               Click="FlyoutButton_Clicked"
                                                               Content="cosh⁻¹"
                                                               IsTabStop="false"
                                                               IsEnabled="{x:Bind TrigButton.IsEnabled, Mode=OneWay}"/>

                                    <controls:CalculatorButton x:Name="InvtanhButton"
                                                               Grid.Column="2"
                                                               Style="{StaticResource OperatorButtonStyle}"
                                                               common:KeyboardShortcutManager.VirtualKeyControlShiftChord="{utils:ResourceVirtualKey Name=invtanhButton/[using:CalculatorApp.Common]KeyboardShortcutManager/VirtualKeyControlShiftChord}"
                                                               AutomationProperties.AutomationId="invtanhButton"
                                                               AutomationProperties.Name="{utils:ResourceString Name=invtanhButton/[using:Windows.UI.Xaml.Automation]AutomationProperties/Name}"
                                                               ButtonId="InvTanh"
                                                               Click="FlyoutButton_Clicked"
                                                               Content="tanh⁻¹"
                                                               IsTabStop="false"
                                                               IsEnabled="{x:Bind TrigButton.IsEnabled, Mode=OneWay}"/>

                                    <controls:CalculatorButton x:Name="InvsechButton"
                                                               Grid.Row="1"
                                                               Style="{StaticResource OperatorButtonStyle}"
                                                               common:KeyboardShortcutManager.VirtualKeyControlShiftChord="{utils:ResourceVirtualKey Name=invsechButton/[using:CalculatorApp.Common]KeyboardShortcutManager/VirtualKeyControlShiftChord}"
                                                               AutomationProperties.AutomationId="invsechButton"
                                                               AutomationProperties.Name="{utils:ResourceString Name=invsechButton/[using:Windows.UI.Xaml.Automation]AutomationProperties/Name}"
                                                               ButtonId="InvSech"
                                                               Click="FlyoutButton_Clicked"
                                                               Content="sech⁻¹"
                                                               IsTabStop="false"
                                                               IsEnabled="{x:Bind TrigButton.IsEnabled, Mode=OneWay}"/>

                                    <controls:CalculatorButton x:Name="InvcschButton"
                                                               Grid.Row="1"
                                                               Grid.Column="1"
                                                               Style="{StaticResource OperatorButtonStyle}"
                                                               common:KeyboardShortcutManager.VirtualKeyControlShiftChord="{utils:ResourceVirtualKey Name=invcschButton/[using:CalculatorApp.Common]KeyboardShortcutManager/VirtualKeyControlShiftChord}"
                                                               AutomationProperties.AutomationId="invcschButton"
                                                               AutomationProperties.Name="{utils:ResourceString Name=invcschButton/[using:Windows.UI.Xaml.Automation]AutomationProperties/Name}"
                                                               ButtonId="InvCsch"
                                                               Click="FlyoutButton_Clicked"
                                                               Content="csch⁻¹"
                                                               IsTabStop="false"
                                                               IsEnabled="{x:Bind TrigButton.IsEnabled, Mode=OneWay}"/>

                                    <controls:CalculatorButton x:Name="InvcothButton"
                                                               Grid.Row="1"
                                                               Grid.Column="2"
                                                               Style="{StaticResource OperatorButtonStyle}"
                                                               common:KeyboardShortcutManager.VirtualKeyControlShiftChord="{utils:ResourceVirtualKey Name=invcothButton/[using:CalculatorApp.Common]KeyboardShortcutManager/VirtualKeyControlShiftChord}"
                                                               AutomationProperties.AutomationId="invcothButton"
                                                               AutomationProperties.Name="{utils:ResourceString Name=invcothButton/[using:Windows.UI.Xaml.Automation]AutomationProperties/Name}"
                                                               ButtonId="InvCoth"
                                                               Click="FlyoutButton_Clicked"
                                                               Content="coth⁻¹"
                                                               IsTabStop="false"
                                                               IsEnabled="{x:Bind TrigButton.IsEnabled, Mode=OneWay}"/>
                                </Grid>
                            </Grid>
                        </Flyout>
                    </controls:OperatorPanelButton.FlyoutMenu>
                </controls:OperatorPanelButton>

                <controls:OperatorPanelButton x:Name="InequalityButton"
                                              Style="{StaticResource OperatorPanelButtonStyle}"
                                              AutomationProperties.AutomationId="inequalityButton"
                                              AutomationProperties.Name="{utils:ResourceString Name=inequalityButton/[using:Windows.UI.Xaml.Automation]AutomationProperties/Name}"
                                              Glyph="&#xF894;"
                                              IsTabStop="false"
                                              Text="{utils:ResourceString Name=inequalityButton/Text}">
                    <controls:OperatorPanelButton.FlyoutMenu>

                        <Flyout x:Name="InequalityFlyout"
                                Windows10version1809:AreOpenCloseAnimationsEnabled="False"
                                AllowFocusOnInteraction="False"
                                FlyoutPresenterStyle="{ThemeResource OperatorPanelFlyoutStyle}"
                                Opening="Flyout_Opening"
                                Placement="Bottom">

                            <Grid x:Name="InequalityGrid"
                                  MinWidth="312"
                                  MinHeight="48">
                                <Grid.RowDefinitions>
                                    <RowDefinition Height="1*"/>
                                </Grid.RowDefinitions>
                                <Grid.ColumnDefinitions>
                                    <ColumnDefinition Width="1*"/>
                                    <ColumnDefinition Width="1*"/>
                                    <ColumnDefinition Width="1*"/>
                                    <ColumnDefinition Width="1*"/>
                                    <ColumnDefinition Width="1*"/>
                                </Grid.ColumnDefinitions>

                                <controls:CalculatorButton x:Name="LessThanFlyoutButton"
                                                           Style="{StaticResource SymbolOperatorButtonStyle}"
                                                           AutomationProperties.AutomationId="lessThanFlyoutButton"
                                                           AutomationProperties.Name="{utils:ResourceString Name=lessThanFlyoutButton/[using:Windows.UI.Xaml.Automation]AutomationProperties/Name}"
                                                           ButtonId="LessThan"
                                                           Click="FlyoutButton_Clicked"
                                                           Content="&#xF88A;"
                                                           IsTabStop="false"
                                                           IsEnabled="{x:Bind InequalityButton.IsEnabled, Mode=OneWay}"/>

                                <controls:CalculatorButton x:Name="LessThanOrEqualFlyoutButton"
                                                           Grid.Column="1"
                                                           Style="{StaticResource SymbolOperatorButtonStyle}"
                                                           AutomationProperties.AutomationId="lessThanOrEqualFlyoutButton"
                                                           AutomationProperties.Name="{utils:ResourceString Name=lessThanOrEqualFlyoutButton/[using:Windows.UI.Xaml.Automation]AutomationProperties/Name}"
                                                           ButtonId="LessThanOrEqualTo"
                                                           Click="FlyoutButton_Clicked"
                                                           Content="&#xF88B;"
                                                           IsTabStop="false"
                                                           IsEnabled="{x:Bind InequalityButton.IsEnabled, Mode=OneWay}"/>

                                <controls:CalculatorButton x:Name="EqualsFlyoutButton"
                                                           Grid.Column="2"
                                                           Style="{StaticResource SymbolOperatorButtonStyle}"
                                                           AutomationProperties.AutomationId="equalsFlyoutButton"
                                                           AutomationProperties.Name="{utils:ResourceString Name=equalsFlyoutButton/[using:Windows.UI.Xaml.Automation]AutomationProperties/Name}"
                                                           ButtonId="Equals"
                                                           Click="FlyoutButton_Clicked"
                                                           Content="&#xE94E;"
                                                           IsTabStop="false"
                                                           IsEnabled="{x:Bind InequalityButton.IsEnabled, Mode=OneWay}"/>

                                <controls:CalculatorButton x:Name="GreaterThanOrEqualFlyoutButton"
                                                           Grid.Column="3"
                                                           Style="{StaticResource SymbolOperatorButtonStyle}"
                                                           AutomationProperties.AutomationId="greaterThanOrEqualFlyoutButton"
                                                           AutomationProperties.Name="{utils:ResourceString Name=greaterThanOrEqualFlyoutButton/[using:Windows.UI.Xaml.Automation]AutomationProperties/Name}"
                                                           ButtonId="GreaterThanOrEqualTo"
                                                           Click="FlyoutButton_Clicked"
                                                           Content="&#xF88D;"
                                                           IsTabStop="false"
                                                           IsEnabled="{x:Bind InequalityButton.IsEnabled, Mode=OneWay}"/>

                                <controls:CalculatorButton x:Name="GreaterThanFlyoutButton"
                                                           Grid.Column="4"
                                                           Style="{StaticResource SymbolOperatorButtonStyle}"
                                                           AutomationProperties.AutomationId="greaterThanFlyoutButton"
                                                           AutomationProperties.Name="{utils:ResourceString Name=greaterThanFlyoutButton/[using:Windows.UI.Xaml.Automation]AutomationProperties/Name}"
                                                           ButtonId="GreaterThan"
                                                           Click="FlyoutButton_Clicked"
                                                           Content="&#xF88C;"
                                                           IsTabStop="false"
                                                           IsEnabled="{x:Bind InequalityButton.IsEnabled, Mode=OneWay}"/>

                            </Grid>
                        </Flyout>
                    </controls:OperatorPanelButton.FlyoutMenu>
                </controls:OperatorPanelButton>

                <controls:OperatorPanelButton x:Name="FuncButton"
                                              Style="{StaticResource OperatorPanelButtonStyle}"
                                              AutomationProperties.AutomationId="funcButton"
                                              AutomationProperties.Name="{utils:ResourceString Name=funcButton/[using:Windows.UI.Xaml.Automation]AutomationProperties/Name}"
                                              Glyph="&#xF893;"
                                              IsTabStop="false"
                                              Text="{utils:ResourceString Name=funcButton/Text}">

                    <controls:OperatorPanelButton.FlyoutMenu>
                        <Flyout x:Name="FuncFlyout"
                                Windows10version1809:AreOpenCloseAnimationsEnabled="False"
                                AllowFocusOnInteraction="False"
                                FlyoutPresenterStyle="{ThemeResource OperatorPanelFlyoutStyle}"
                                Opening="Flyout_Opening"
                                Placement="Bottom">

                            <Grid x:Name="FuncGrid"
                                  MinWidth="194"
                                  MinHeight="48"
                                  XYFocusKeyboardNavigation="Enabled">
                                <Grid.RowDefinitions>
                                    <RowDefinition Height="1*"/>
                                </Grid.RowDefinitions>
                                <Grid.ColumnDefinitions>
                                    <ColumnDefinition Width="1*"/>
                                    <ColumnDefinition Width="1*"/>
                                    <ColumnDefinition Width="1*"/>
                                </Grid.ColumnDefinitions>
                                <controls:CalculatorButton x:Name="AbsFlyoutButton"
                                                           Style="{StaticResource SymbolOperatorButtonStyle}"
                                                           common:KeyboardShortcutManager.Character="{utils:ResourceString Name=absButton/[using:CalculatorApp.Common]KeyboardShortcutManager/Character}"
                                                           AutomationProperties.AutomationId="absButton"
                                                           AutomationProperties.Name="{utils:ResourceString Name=absButton/[using:Windows.UI.Xaml.Automation]AutomationProperties/Name}"
                                                           ButtonId="Abs"
                                                           Click="FlyoutButton_Clicked"
                                                           Content="&#xF884;"
                                                           IsTabStop="false"
                                                           IsEnabled="{x:Bind FuncButton.IsEnabled, Mode=OneWay}"/>

                                <controls:CalculatorButton x:Name="FloorButton"
                                                           Grid.Column="1"
                                                           Style="{StaticResource SymbolOperatorButtonStyle}"
                                                           common:KeyboardShortcutManager.Character="{utils:ResourceString Name=floorButton/[using:CalculatorApp.Common]KeyboardShortcutManager/Character}"
                                                           AutomationProperties.AutomationId="floorButton"
                                                           AutomationProperties.Name="{utils:ResourceString Name=floorButton/[using:Windows.UI.Xaml.Automation]AutomationProperties/Name}"
                                                           ButtonId="Floor"
                                                           Click="FlyoutButton_Clicked"
                                                           Content="&#xF885;"
                                                           IsTabStop="false"
                                                           IsEnabled="{x:Bind FuncButton.IsEnabled, Mode=OneWay}"/>

                                <controls:CalculatorButton x:Name="CeilButton"
                                                           Grid.Column="2"
                                                           Style="{StaticResource SymbolOperatorButtonStyle}"
                                                           common:KeyboardShortcutManager.Character="{utils:ResourceString Name=ceilButton/[using:CalculatorApp.Common]KeyboardShortcutManager/Character}"
                                                           AutomationProperties.AutomationId="ceilButton"
                                                           AutomationProperties.Name="{utils:ResourceString Name=ceilButton/[using:Windows.UI.Xaml.Automation]AutomationProperties/Name}"
                                                           ButtonId="Ceil"
                                                           Click="FlyoutButton_Clicked"
                                                           Content="&#xF886;"
                                                           IsTabStop="false"
                                                           IsEnabled="{x:Bind FuncButton.IsEnabled, Mode=OneWay}"/>
                            </Grid>
                        </Flyout>
                    </controls:OperatorPanelButton.FlyoutMenu>
                </controls:OperatorPanelButton>
            </controls:OperatorPanelListView>

            <ToggleButton x:Name="ShiftButton"
                          Grid.Row="1"
                          Style="{StaticResource CaptionToggleEmphasizedButtonStyle}"
                          FontFamily="{StaticResource CalculatorFontFamily}"
                          FontSize="20"
                          AutomationProperties.AutomationId="shiftButton"
                          AutomationProperties.Name="{utils:ResourceString Name=shiftButton/[using:Windows.UI.Xaml.Automation]AutomationProperties/Name}"
                          Checked="ShiftButton_Check"
                          Content="&#xF897;"
                          IsEnabledChanged="ShiftButton_IsEnabledChanged"
                          IsTabStop="false"
                          Unchecked="ShiftButton_Check"
                          IsEnabled="{x:Bind FuncButton.IsEnabled, Mode=OneWay}"/>

            <controls:CalculatorButton x:Name="PiButton"
                                       Grid.Row="1"
                                       Grid.Column="1"
                                       Style="{StaticResource SymbolOperatorButtonStyle}"
                                       FontSize="14"
                                       common:KeyboardShortcutManager.VirtualKey="{utils:ResourceVirtualKey Name=piButton/[using:CalculatorApp.Common]KeyboardShortcutManager/VirtualKey}"
                                       AutomationProperties.AutomationId="piButton"
                                       AutomationProperties.Name="{utils:ResourceString Name=piButton/[using:Windows.UI.Xaml.Automation]AutomationProperties/Name}"
                                       ButtonId="Pi"
                                       Click="Button_Clicked"
                                       Content="&#xf7cf;"
                                       IsTabStop="false"/>

            <controls:CalculatorButton x:Name="EulerButton"
                                       Grid.Row="1"
                                       Grid.Column="2"
                                       Style="{StaticResource OperatorButtonStyle}"
                                       FontSize="14"
                                       common:KeyboardShortcutManager.VirtualKeyShiftChord="{utils:ResourceVirtualKey Name=eulerButton/[using:CalculatorApp.Common]KeyboardShortcutManager/VirtualKeyShiftChord}"
                                       AutomationProperties.AutomationId="eulerButton"
                                       AutomationProperties.Name="{utils:ResourceString Name=eulerButton/[using:Windows.UI.Xaml.Automation]AutomationProperties/Name}"
                                       ButtonId="Euler"
                                       Click="Button_Clicked"
                                       Content="e"
                                       IsTabStop="false"/>
            <Grid x:Name="DisplayControls"
                  Grid.Row="1"
                  Grid.Column="2"
                  Grid.ColumnSpan="3"
                  AutomationProperties.HeadingLevel="Level1"
                  AutomationProperties.Name="{utils:ResourceString Name=DisplayControls/[using:Windows.UI.Xaml.Automation]AutomationProperties/Name}">
                <Grid.ColumnDefinitions>
                    <ColumnDefinition/>
                    <ColumnDefinition/>
                    <ColumnDefinition/>
                </Grid.ColumnDefinitions>

                <controls:CalculatorButton x:Name="ClearButton"
                                           Grid.Column="1"
                                           Style="{StaticResource OperatorButtonStyle}"
                                           FontSize="16"
                                           common:KeyboardShortcutManager.VirtualKey="{utils:ResourceVirtualKey Name=clearButton/[using:CalculatorApp.Common]KeyboardShortcutManager/VirtualKey}"
                                           AutomationProperties.AutomationId="clearButton"
                                           AutomationProperties.Name="{utils:ResourceString Name=clearButton/[using:Windows.UI.Xaml.Automation]AutomationProperties/Name}"
                                           ButtonId="Clear"
                                           Click="ClearButton_Clicked"
                                           Content="C"
                                           IsTabStop="false"/>

                <controls:CalculatorButton x:Name="BackSpaceButton"
                                           Grid.Column="2"
                                           Style="{StaticResource SymbolOperatorButtonStyle}"
                                           FontFamily="{StaticResource CalculatorFontFamily}"
                                           FontSize="16"
                                           common:KeyboardShortcutManager.VirtualKey="{utils:ResourceVirtualKey Name=backSpaceButton/[using:CalculatorApp.Common]KeyboardShortcutManager/VirtualKey}"
                                           AutomationProperties.AutomationId="backSpaceButton"
                                           AutomationProperties.Name="{utils:ResourceString Name=backSpaceButton/[using:Windows.UI.Xaml.Automation]AutomationProperties/Name}"
                                           ButtonId="Backspace"
                                           Click="BackSpaceButton_Clicked"
                                           Content="&#xE94F;"
                                           IsTabStop="false"/>
            </Grid>

            <Grid Grid.Row="2"
                  Grid.RowSpan="6"
                  AutomationProperties.HeadingLevel="Level1"
                  AutomationProperties.Name="{utils:ResourceString Name=ScientificFunctions/[using:Windows.UI.Xaml.Automation]AutomationProperties/Name}">
                <!-- Scientific Row 1 -->
                <Grid x:Name="Row1">
                    <Grid.RowDefinitions>
                        <RowDefinition/>
                        <RowDefinition/>
                        <RowDefinition/>
                        <RowDefinition/>
                        <RowDefinition/>
                        <RowDefinition/>
                    </Grid.RowDefinitions>

                    <controls:CalculatorButton x:Name="XPower2Button"
                                               Style="{StaticResource SymbolOperatorButtonStyle}"
                                               common:KeyboardShortcutManager.VirtualKey="{utils:ResourceVirtualKey Name=xpower2Button/[using:CalculatorApp.Common]KeyboardShortcutManager/VirtualKey}"
                                               AutomationProperties.AutomationId="xpower2Button"
                                               AutomationProperties.Name="{utils:ResourceString Name=xpower2Button/[using:Windows.UI.Xaml.Automation]AutomationProperties/Name}"
                                               ButtonId="XPower2"
                                               Click="Button_Clicked"
                                               Content="&#xf7c8;"
                                               IsTabStop="false"/>

                    <controls:CalculatorButton x:Name="SquareRootButton"
                                               Grid.Row="1"
                                               Style="{StaticResource SymbolOperatorButtonStyle}"
                                               common:KeyboardShortcutManager.Character="{utils:ResourceString Name=squareRootButton/[using:CalculatorApp.Common]KeyboardShortcutManager/Character}"
                                               AutomationProperties.AutomationId="squareRootButton"
                                               AutomationProperties.Name="{utils:ResourceString Name=squareRootButton/[using:Windows.UI.Xaml.Automation]AutomationProperties/Name}"
                                               ButtonId="Sqrt"
                                               Click="Button_Clicked"
                                               Content="&#xF899;"
                                               IsTabStop="false"/>

                    <controls:CalculatorButton x:Name="PowerButton"
                                               Grid.Row="2"
                                               Style="{StaticResource SymbolOperatorButtonStyle}"
                                               common:KeyboardShortcutManager.Character="{utils:ResourceString Name=powerButton/[using:CalculatorApp.Common]KeyboardShortcutManager/Character}"
                                               common:KeyboardShortcutManager.VirtualKey="{utils:ResourceVirtualKey Name=powerButton/[using:CalculatorApp.Common]KeyboardShortcutManager/VirtualKey}"
                                               AuditoryFeedback="{utils:ResourceString Name=powerButton/[using:CalculatorApp.Controls]CalculatorButton/AuditoryFeedback}"
                                               AutomationProperties.AutomationId="powerButton"
                                               AutomationProperties.Name="{utils:ResourceString Name=powerButton/[using:Windows.UI.Xaml.Automation]AutomationProperties/Name}"
                                               ButtonId="XPowerY"
                                               Click="Button_Clicked"
                                               Content="&#xf7ca;"
                                               IsTabStop="false"/>

                    <controls:CalculatorButton x:Name="PowerOf10Button"
                                               Grid.Row="3"
                                               Style="{StaticResource SymbolOperatorButtonStyle}"
                                               common:KeyboardShortcutManager.VirtualKeyControlChord="{utils:ResourceVirtualKey Name=powerOf10Button/[using:CalculatorApp.Common]KeyboardShortcutManager/VirtualKeyControlChord}"
                                               AutomationProperties.AutomationId="powerOf10Button"
                                               AutomationProperties.Name="{utils:ResourceString Name=powerOf10Button/[using:Windows.UI.Xaml.Automation]AutomationProperties/Name}"
                                               ButtonId="TenPowerX"
                                               Click="Button_Clicked"
                                               Content="&#xF7CC;"
                                               IsTabStop="false"/>

                    <controls:CalculatorButton x:Name="LogBase10Button"
                                               Grid.Row="4"
                                               Style="{StaticResource OperatorButtonStyle}"
                                               common:KeyboardShortcutManager.VirtualKey="{utils:ResourceVirtualKey Name=logBase10Button/[using:CalculatorApp.Common]KeyboardShortcutManager/VirtualKey}"
                                               AutomationProperties.AutomationId="logBase10Button"
                                               AutomationProperties.Name="{utils:ResourceString Name=logBase10Button/[using:Windows.UI.Xaml.Automation]AutomationProperties/Name}"
                                               ButtonId="LogBase10"
                                               Click="Button_Clicked"
                                               Content="log"
                                               IsTabStop="false"/>

                    <controls:CalculatorButton x:Name="LogBaseEButton"
                                               Grid.Row="5"
                                               Style="{StaticResource OperatorButtonStyle}"
                                               common:KeyboardShortcutManager.VirtualKey="{utils:ResourceVirtualKey Name=logBaseEButton/[using:CalculatorApp.Common]KeyboardShortcutManager/VirtualKey}"
                                               AutomationProperties.AutomationId="logBaseEButton"
                                               AutomationProperties.Name="{utils:ResourceString Name=logBaseEButton/[using:Windows.UI.Xaml.Automation]AutomationProperties/Name}"
                                               ButtonId="LogBaseE"
                                               Click="Button_Clicked"
                                               Content="ln"
                                               IsTabStop="false"/>
                </Grid>

                <!-- Scientific INV Row 1 -->
                <Grid x:Name="InvRow1" Visibility="Collapsed">
                    <Grid.RowDefinitions>
                        <RowDefinition/>
                        <RowDefinition/>
                        <RowDefinition/>
                        <RowDefinition/>
                        <RowDefinition/>
                        <RowDefinition/>
                    </Grid.RowDefinitions>

                    <controls:CalculatorButton x:Name="XPower3Button"
                                               Style="{StaticResource EmphasizedCalcButtonStyle}"
                                               common:KeyboardShortcutManager.Character="{utils:ResourceString Name=xpower3Button/[using:CalculatorApp.Common]KeyboardShortcutManager/Character}"
                                               AutomationProperties.AutomationId="xpower3Button"
                                               AutomationProperties.Name="{utils:ResourceString Name=xpower3Button/[using:Windows.UI.Xaml.Automation]AutomationProperties/Name}"
                                               ButtonId="Cube"
                                               Click="ShiftButton_Uncheck"
                                               Content="&#xf7cb;"
                                               IsTabStop="false"/>

                    <controls:CalculatorButton x:Name="CubeRootButton"
                                               Grid.Row="1"
                                               Style="{StaticResource EmphasizedCalcButtonStyle}"
                                               common:KeyboardShortcutManager.VirtualKey="{utils:ResourceVirtualKey Name=cubeRootButton/[using:CalculatorApp.Common]KeyboardShortcutManager/VirtualKey}"
                                               AutomationProperties.AutomationId="cubeRootButton"
                                               AutomationProperties.Name="{utils:ResourceString Name=cubeRootButton/[using:Windows.UI.Xaml.Automation]AutomationProperties/Name}"
                                               ButtonId="CubeRoot"
                                               Click="ShiftButton_Uncheck"
                                               Content="&#xF881;"
                                               IsTabStop="false"/>

                    <controls:CalculatorButton x:Name="YSquareRootButton"
                                               Grid.Row="2"
                                               Style="{StaticResource EmphasizedCalcButtonStyle}"
                                               common:KeyboardShortcutManager.VirtualKeyControlChord="{utils:ResourceVirtualKey Name=ySquareRootButton/[using:CalculatorApp.Common]KeyboardShortcutManager/VirtualKeyControlChord}"
                                               AuditoryFeedback="{utils:ResourceString Name=ySquareRootButton/[using:CalculatorApp.Controls]CalculatorButton/AuditoryFeedback}"
                                               AutomationProperties.AutomationId="ySquareRootButton"
                                               AutomationProperties.Name="{utils:ResourceString Name=ySquareRootButton/[using:Windows.UI.Xaml.Automation]AutomationProperties/Name}"
                                               ButtonId="YRootX"
                                               Click="ShiftButton_Uncheck"
                                               Content="&#xf7cd;"
                                               IsTabStop="false"/>

                    <controls:CalculatorButton x:Name="TwoPowerXButton"
                                               Grid.Row="3"
                                               Style="{StaticResource EmphasizedCalcButtonStyle}"
                                               common:KeyboardShortcutManager.VirtualKey="{utils:ResourceVirtualKey Name=twoPowerXButton/[using:CalculatorApp.Common]KeyboardShortcutManager/VirtualKey}"
                                               AutomationProperties.AutomationId="twoPowerXButton"
                                               AutomationProperties.Name="{utils:ResourceString Name=twoPowerXButton/[using:Windows.UI.Xaml.Automation]AutomationProperties/Name}"
                                               ButtonId="TwoPowerX"
                                               Click="ShiftButton_Uncheck"
                                               Content="&#xF882;"
                                               IsTabStop="false"/>

                    <controls:CalculatorButton x:Name="LogBaseY"
                                               Grid.Row="4"
                                               Style="{StaticResource EmphasizedCalcButtonStyle}"
                                               common:KeyboardShortcutManager.VirtualKeyShiftChord="{utils:ResourceVirtualKey Name=logBaseY/[using:CalculatorApp.Common]KeyboardShortcutManager/VirtualKeyShiftChord}"
                                               AutomationProperties.AutomationId="logBaseY"
                                               AutomationProperties.Name="{utils:ResourceString Name=logBaseY/[using:Windows.UI.Xaml.Automation]AutomationProperties/Name}"
                                               ButtonId="LogBaseY"
                                               Click="ShiftButton_Uncheck"
                                               Content="&#xF883;"
                                               IsTabStop="false"/>

                    <controls:CalculatorButton x:Name="PowerOfEButton"
                                               Grid.Row="5"
                                               Style="{StaticResource EmphasizedCalcButtonStyle}"
                                               common:KeyboardShortcutManager.VirtualKeyControlChord="{utils:ResourceVirtualKey Name=powerOfEButton/[using:CalculatorApp.Common]KeyboardShortcutManager/VirtualKeyControlChord}"
                                               AutomationProperties.AutomationId="powerOfEButton"
                                               AutomationProperties.Name="{utils:ResourceString Name=powerOfEButton/[using:Windows.UI.Xaml.Automation]AutomationProperties/Name}"
                                               ButtonId="EPowerX"
                                               Click="ShiftButton_Uncheck"
                                               Content="&#xf7ce;"
                                               IsTabStop="false"/>
                </Grid>
            </Grid>
            <!-- Scientific Row 2 -->
            <Grid x:Name="Row2"
                  Grid.Row="2"
                  Grid.Column="1"
                  Grid.ColumnSpan="4">
                <Grid.ColumnDefinitions>
                    <ColumnDefinition Width="1*"/>
                    <ColumnDefinition Width="1*"/>
                    <ColumnDefinition Width="1*"/>
                    <ColumnDefinition Width="1*"/>
                </Grid.ColumnDefinitions>

                <controls:CalculatorButton x:Name="InvertButton"
                                           Style="{StaticResource SymbolOperatorButtonStyle}"
                                           common:KeyboardShortcutManager.VirtualKey="{utils:ResourceVirtualKey Name=invertButton/[using:CalculatorApp.Common]KeyboardShortcutManager/VirtualKey}"
                                           AutomationProperties.AutomationId="invertButton"
                                           AutomationProperties.Name="{utils:ResourceString Name=invertButton/[using:Windows.UI.Xaml.Automation]AutomationProperties/Name}"
                                           ButtonId="Invert"
                                           Click="Button_Clicked"
                                           Content="&#xf7c9;"
                                           IsTabStop="false"/>

                <controls:CalculatorButton x:Name="AbsButton"
                                           Grid.Column="1"
                                           Style="{StaticResource SymbolOperatorButtonStyle}"
                                           common:KeyboardShortcutManager.Character="{utils:ResourceString Name=absButton/[using:CalculatorApp.Common]KeyboardShortcutManager/Character}"
                                           AutomationProperties.AutomationId="absButton"
                                           AutomationProperties.Name="{utils:ResourceString Name=absButton/[using:Windows.UI.Xaml.Automation]AutomationProperties/Name}"
                                           ButtonId="Abs"
                                           Click="Button_Clicked"
                                           Content="&#xF884;"
                                           IsTabStop="false"/>

                <controls:CalculatorButton x:Name="XButton"
                                           Grid.Column="2"
                                           Style="{StaticResource OperatorButtonStyle}"
                                           FontSize="16"
                                           common:KeyboardShortcutManager.VirtualKey="{utils:ResourceVirtualKey Name=xButton/[using:CalculatorApp.Common]KeyboardShortcutManager/VirtualKey}"
                                           AutomationProperties.AutomationId="xButton"
                                           AutomationProperties.Name="{utils:ResourceString Name=xButton/[using:Windows.UI.Xaml.Automation]AutomationProperties/Name}"
                                           ButtonId="X"
                                           Click="Button_Clicked"
                                           Content="𝑥"
                                           IsTabStop="false"/>

                <controls:CalculatorButton x:Name="YButton"
                                           Grid.Column="3"
                                           Style="{StaticResource OperatorButtonStyle}"
                                           FontSize="16"
                                           common:KeyboardShortcutManager.VirtualKey="{utils:ResourceVirtualKey Name=yButton/[using:CalculatorApp.Common]KeyboardShortcutManager/VirtualKey}"
                                           AutomationProperties.AutomationId="yButton"
                                           AutomationProperties.Name="{utils:ResourceString Name=yButton/[using:Windows.UI.Xaml.Automation]AutomationProperties/Name}"
                                           ButtonId="Y"
                                           Click="Button_Clicked"
                                           Content="𝑦"
                                           IsTabStop="false"/>
            </Grid>

            <controls:CalculatorButton x:Name="OpenParenthesisButton"
                                       Grid.Row="3"
                                       Grid.Column="1"
                                       Style="{StaticResource ParenthesisCalcButtonStyle}"
                                       FontSize="19"
                                       common:KeyboardShortcutManager.Character="{utils:ResourceString Name=openParenthesisButton/[using:CalculatorApp.Common]KeyboardShortcutManager/Character}"
                                       AutomationProperties.AutomationId="openParenthesisButton"
                                       AutomationProperties.Name="{utils:ResourceString Name=openParenthesisButton/[using:Windows.UI.Xaml.Automation]AutomationProperties/Name}"
                                       ButtonId="OpenParenthesis"
                                       Click="Button_Clicked"
                                       Content="("
                                       IsTabStop="false"/>

            <controls:CalculatorButton x:Name="CloseParenthesisButton"
                                       Grid.Row="3"
                                       Grid.Column="2"
                                       Style="{StaticResource OperatorButtonStyle}"
                                       FontSize="19"
                                       common:KeyboardShortcutManager.Character="{utils:ResourceString Name=closeParenthesisButton/[using:CalculatorApp.Common]KeyboardShortcutManager/Character}"
                                       AutomationProperties.AutomationId="closeParenthesisButton"
                                       AutomationProperties.Name="{utils:ResourceString Name=closeParenthesisButton/[using:Windows.UI.Xaml.Automation]AutomationProperties/Name}"
                                       ButtonId="CloseParenthesis"
                                       Click="Button_Clicked"
                                       Content=")"
                                       IsTabStop="false"/>

            <controls:CalculatorButton x:Name="EqualButton"
                                       Grid.Row="3"
                                       Grid.Column="3"
                                       Style="{StaticResource SymbolOperatorButtonStyle}"
                                       common:KeyboardShortcutManager.Character="{utils:ResourceString Name=equalButton/[using:CalculatorApp.Common]KeyboardShortcutManager/Character}"
                                       common:KeyboardShortcutManager.VirtualKey="{utils:ResourceVirtualKey Name=equalButton/[using:CalculatorApp.Common]KeyboardShortcutManager/VirtualKey}"
                                       AutomationProperties.AutomationId="equalButton"
                                       AutomationProperties.Name="{utils:ResourceString Name=equalButton/[using:Windows.UI.Xaml.Automation]AutomationProperties/Name}"
                                       ButtonId="Equals"
                                       Click="Button_Clicked"
                                       Content="&#xE94E;"
                                       IsTabStop="false"/>

            <Grid x:Name="StandardOperators"
                  Grid.Row="3"
                  Grid.RowSpan="5"
                  Grid.Column="4"
                  AutomationProperties.HeadingLevel="Level1"
                  AutomationProperties.Name="{utils:ResourceString Name=StandardOperators/[using:Windows.UI.Xaml.Automation]AutomationProperties/Name}">
                <Grid.RowDefinitions>
                    <RowDefinition/>
                    <RowDefinition/>
                    <RowDefinition/>
                    <RowDefinition/>
                    <RowDefinition/>
                </Grid.RowDefinitions>
                <controls:CalculatorButton x:Name="DivideButton"
                                           Grid.Row="0"
                                           Style="{StaticResource SymbolOperatorButtonStyle}"
                                           common:KeyboardShortcutManager.Character="{utils:ResourceString Name=divideButton/[using:CalculatorApp.Common]KeyboardShortcutManager/Character}"
                                           AuditoryFeedback="{utils:ResourceString Name=divideButton/[using:CalculatorApp.Controls]CalculatorButton/AuditoryFeedback}"
                                           AutomationProperties.AutomationId="divideButton"
                                           AutomationProperties.Name="{utils:ResourceString Name=divideButton/[using:Windows.UI.Xaml.Automation]AutomationProperties/Name}"
                                           ButtonId="Divide"
                                           Click="Button_Clicked"
                                           Content="&#xE94A;"
                                           IsTabStop="false"/>

                <controls:CalculatorButton x:Name="MultiplyButton"
                                           Grid.Row="1"
                                           Style="{StaticResource SymbolOperatorButtonStyle}"
                                           common:KeyboardShortcutManager.Character="{utils:ResourceString Name=multiplyButton/[using:CalculatorApp.Common]KeyboardShortcutManager/Character}"
                                           AuditoryFeedback="{utils:ResourceString Name=multiplyButton/[using:CalculatorApp.Controls]CalculatorButton/AuditoryFeedback}"
                                           AutomationProperties.AutomationId="multiplyButton"
                                           AutomationProperties.Name="{utils:ResourceString Name=multiplyButton/[using:Windows.UI.Xaml.Automation]AutomationProperties/Name}"
                                           ButtonId="Multiply"
                                           Click="Button_Clicked"
                                           Content="&#xE947;"
                                           IsTabStop="false"/>

                <controls:CalculatorButton x:Name="MinusButton"
                                           Grid.Row="2"
                                           Style="{StaticResource SymbolOperatorButtonStyle}"
                                           common:KeyboardShortcutManager.Character="{utils:ResourceString Name=minusButton/[using:CalculatorApp.Common]KeyboardShortcutManager/Character}"
                                           AuditoryFeedback="{utils:ResourceString Name=minusButton/[using:CalculatorApp.Controls]CalculatorButton/AuditoryFeedback}"
                                           AutomationProperties.AutomationId="minusButton"
                                           AutomationProperties.Name="{utils:ResourceString Name=minusButton/[using:Windows.UI.Xaml.Automation]AutomationProperties/Name}"
                                           ButtonId="Subtract"
                                           Click="Button_Clicked"
                                           Content="&#xE949;"
                                           IsTabStop="false"/>

                <controls:CalculatorButton x:Name="PlusButton"
                                           Grid.Row="3"
                                           Style="{StaticResource SymbolOperatorButtonStyle}"
                                           common:KeyboardShortcutManager.Character="{utils:ResourceString Name=plusButton/[using:CalculatorApp.Common]KeyboardShortcutManager/Character}"
                                           AuditoryFeedback="{utils:ResourceString Name=plusButton/[using:CalculatorApp.Controls]CalculatorButton/AuditoryFeedback}"
                                           AutomationProperties.AutomationId="plusButton"
                                           AutomationProperties.Name="{utils:ResourceString Name=plusButton/[using:Windows.UI.Xaml.Automation]AutomationProperties/Name}"
                                           ButtonId="Add"
                                           Click="Button_Clicked"
                                           Content="&#xE948;"
                                           IsTabStop="false"/>

                <controls:CalculatorButton x:Name="SubmitButton"
                                           Grid.Row="4"
                                           Style="{StaticResource AccentEmphasizedCalcButtonStyle}"
                                           FontFamily="{StaticResource SymbolThemeFontFamily}"
                                           AutomationProperties.AutomationId="submitButton"
                                           AutomationProperties.Name="{utils:ResourceString Name=submitButton/[using:Windows.UI.Xaml.Automation]AutomationProperties/Name}"
                                           Click="SubmitButton_Clicked"
                                           Content="&#xE751;"
                                           IsTabStop="false"/>
            </Grid>

            <controls:CalculatorButton x:Name="NegateButton"
                                       Grid.Row="7"
                                       Grid.Column="1"
                                       Style="{StaticResource SymbolOperatorKeypadButtonStyle}"
                                       FontSize="16"
                                       FontWeight="Normal"
                                       common:KeyboardShortcutManager.VirtualKey="{utils:ResourceVirtualKey Name=negateButton/[using:CalculatorApp.Common]KeyboardShortcutManager/VirtualKey}"
                                       AutomationProperties.AutomationId="negateButton"
                                       AutomationProperties.Name="{utils:ResourceString Name=negateButton/[using:Windows.UI.Xaml.Automation]AutomationProperties/Name}"
                                       ButtonId="Negate"
                                       Click="Button_Clicked"
                                       Content="&#xE3B5;"
                                       IsTabStop="false"/>

            <controls:CalculatorButton x:Name="Num0Button"
                                       Grid.Row="7"
                                       Grid.Column="2"
                                       Style="{StaticResource NumericButtonStyle24}"
                                       common:KeyboardShortcutManager.Character="{utils:ResourceString Name=num0Button/[using:CalculatorApp.Common]KeyboardShortcutManager/Character}"
                                       AutomationProperties.AutomationId="num0Button"
                                       AutomationProperties.Name="{utils:ResourceString Name=num0Button/[using:Windows.UI.Xaml.Automation]AutomationProperties/Name}"
                                       ButtonId="Zero"
                                       Click="Button_Clicked"
                                       IsTabStop="false"/>
            <controls:CalculatorButton x:Name="Num1Button"
                                       Grid.Row="6"
                                       Grid.Column="1"
                                       Style="{StaticResource NumericButtonStyle24}"
                                       common:KeyboardShortcutManager.Character="{utils:ResourceString Name=num1Button/[using:CalculatorApp.Common]KeyboardShortcutManager/Character}"
                                       AutomationProperties.AutomationId="num1Button"
                                       AutomationProperties.Name="{utils:ResourceString Name=num1Button/[using:Windows.UI.Xaml.Automation]AutomationProperties/Name}"
                                       ButtonId="One"
                                       Click="Button_Clicked"
                                       IsTabStop="false"/>
            <controls:CalculatorButton x:Name="Num2Button"
                                       Grid.Row="6"
                                       Grid.Column="2"
                                       Style="{StaticResource NumericButtonStyle24}"
                                       common:KeyboardShortcutManager.Character="{utils:ResourceString Name=num2Button/[using:CalculatorApp.Common]KeyboardShortcutManager/Character}"
                                       AutomationProperties.AutomationId="num2Button"
                                       AutomationProperties.Name="{utils:ResourceString Name=num2Button/[using:Windows.UI.Xaml.Automation]AutomationProperties/Name}"
                                       ButtonId="Two"
                                       Click="Button_Clicked"
                                       IsTabStop="false"/>
            <controls:CalculatorButton x:Name="Num3Button"
                                       Grid.Row="6"
                                       Grid.Column="3"
                                       Style="{StaticResource NumericButtonStyle24}"
                                       common:KeyboardShortcutManager.Character="{utils:ResourceString Name=num3Button/[using:CalculatorApp.Common]KeyboardShortcutManager/Character}"
                                       AutomationProperties.AutomationId="num3Button"
                                       AutomationProperties.Name="{utils:ResourceString Name=num3Button/[using:Windows.UI.Xaml.Automation]AutomationProperties/Name}"
                                       ButtonId="Three"
                                       Click="Button_Clicked"
                                       IsTabStop="false"/>
            <controls:CalculatorButton x:Name="Num4Button"
                                       Grid.Row="5"
                                       Grid.Column="1"
                                       Style="{StaticResource NumericButtonStyle24}"
                                       common:KeyboardShortcutManager.Character="{utils:ResourceString Name=num4Button/[using:CalculatorApp.Common]KeyboardShortcutManager/Character}"
                                       AutomationProperties.AutomationId="num4Button"
                                       AutomationProperties.Name="{utils:ResourceString Name=num4Button/[using:Windows.UI.Xaml.Automation]AutomationProperties/Name}"
                                       ButtonId="Four"
                                       Click="Button_Clicked"
                                       IsTabStop="false"/>
            <controls:CalculatorButton x:Name="Num5Button"
                                       Grid.Row="5"
                                       Grid.Column="2"
                                       Style="{StaticResource NumericButtonStyle24}"
                                       common:KeyboardShortcutManager.Character="{utils:ResourceString Name=num5Button/[using:CalculatorApp.Common]KeyboardShortcutManager/Character}"
                                       AutomationProperties.AutomationId="num5Button"
                                       AutomationProperties.Name="{utils:ResourceString Name=num5Button/[using:Windows.UI.Xaml.Automation]AutomationProperties/Name}"
                                       ButtonId="Five"
                                       Click="Button_Clicked"
                                       IsTabStop="false"/>
            <controls:CalculatorButton x:Name="Num6Button"
                                       Grid.Row="5"
                                       Grid.Column="3"
                                       Style="{StaticResource NumericButtonStyle24}"
                                       common:KeyboardShortcutManager.Character="{utils:ResourceString Name=num6Button/[using:CalculatorApp.Common]KeyboardShortcutManager/Character}"
                                       AutomationProperties.AutomationId="num6Button"
                                       AutomationProperties.Name="{utils:ResourceString Name=num6Button/[using:Windows.UI.Xaml.Automation]AutomationProperties/Name}"
                                       ButtonId="Six"
                                       Click="Button_Clicked"
                                       IsTabStop="false"/>
            <controls:CalculatorButton x:Name="Num7Button"
                                       Grid.Row="4"
                                       Grid.Column="1"
                                       Style="{StaticResource NumericButtonStyle24}"
                                       common:KeyboardShortcutManager.Character="{utils:ResourceString Name=num7Button/[using:CalculatorApp.Common]KeyboardShortcutManager/Character}"
                                       AutomationProperties.AutomationId="num7Button"
                                       AutomationProperties.Name="{utils:ResourceString Name=num7Button/[using:Windows.UI.Xaml.Automation]AutomationProperties/Name}"
                                       ButtonId="Seven"
                                       Click="Button_Clicked"
                                       IsTabStop="false"/>
            <controls:CalculatorButton x:Name="Num8Button"
                                       Grid.Row="4"
                                       Grid.Column="2"
                                       Style="{StaticResource NumericButtonStyle24}"
                                       common:KeyboardShortcutManager.Character="{utils:ResourceString Name=num8Button/[using:CalculatorApp.Common]KeyboardShortcutManager/Character}"
                                       AutomationProperties.AutomationId="num8Button"
                                       AutomationProperties.Name="{utils:ResourceString Name=num8Button/[using:Windows.UI.Xaml.Automation]AutomationProperties/Name}"
                                       ButtonId="Eight"
                                       Click="Button_Clicked"
                                       IsTabStop="false"/>
            <controls:CalculatorButton x:Name="Num9Button"
                                       Grid.Row="4"
                                       Grid.Column="3"
                                       Style="{StaticResource NumericButtonStyle24}"
                                       common:KeyboardShortcutManager.Character="{utils:ResourceString Name=num9Button/[using:CalculatorApp.Common]KeyboardShortcutManager/Character}"
                                       AutomationProperties.AutomationId="num9Button"
                                       AutomationProperties.Name="{utils:ResourceString Name=num9Button/[using:Windows.UI.Xaml.Automation]AutomationProperties/Name}"
                                       ButtonId="Nine"
                                       Click="Button_Clicked"
                                       IsTabStop="false"/>
            <controls:CalculatorButton x:Name="DecimalSeparatorButton"
                                       Grid.Row="7"
                                       Grid.Column="3"
                                       Style="{StaticResource NumericButtonStyle24}"
                                       common:KeyboardShortcutManager.VirtualKey="{utils:ResourceVirtualKey Name=decimalSeparatorButton/[using:CalculatorApp.Common]KeyboardShortcutManager/VirtualKey}"
                                       AutomationProperties.AutomationId="decimalSeparatorButton"
                                       AutomationProperties.Name="{utils:ResourceString Name=decimalSeparatorButton/[using:Windows.UI.Xaml.Automation]AutomationProperties/Name}"
                                       ButtonId="Decimal"
                                       Click="Button_Clicked"
                                       IsTabStop="false"/>
        </Grid>
    </Border>
</UserControl>
