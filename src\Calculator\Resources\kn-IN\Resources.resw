﻿<?xml version="1.0" encoding="utf-8"?>
<root>
  <!-- 
    Microsoft ResX Schema 
    
    Version 2.0
    
    The primary goals of this format is to allow a simple XML format 
    that is mostly human readable. The generation and parsing of the 
    various data types are done through the TypeConverter classes 
    associated with the data types.
    
    Example:
    
    ... ado.net/XML headers & schema ...
    <resheader name="resmimetype">text/microsoft-resx</resheader>
    <resheader name="version">2.0</resheader>
    <resheader name="reader">System.Resources.ResXResourceReader, System.Windows.Forms, ...</resheader>
    <resheader name="writer">System.Resources.ResXResourceWriter, System.Windows.Forms, ...</resheader>
    <data name="Name1"><value>this is my long string</value><comment>this is a comment</comment></data>
    <data name="Color1" type="System.Drawing.Color, System.Drawing">Blue</data>
    <data name="Bitmap1" mimetype="application/x-microsoft.net.object.binary.base64">
        <value>[base64 mime encoded serialized .NET Framework object]</value>
    </data>
    <data name="Icon1" type="System.Drawing.Icon, System.Drawing" mimetype="application/x-microsoft.net.object.bytearray.base64">
        <value>[base64 mime encoded string representing a byte array form of the .NET Framework object]</value>
        <comment>This is a comment</comment>
    </data>
                
    There are any number of "resheader" rows that contain simple 
    name/value pairs.
    
    Each data row contains a name, and value. The row also contains a 
    type or mimetype. Type corresponds to a .NET class that support 
    text/value conversion through the TypeConverter architecture. 
    Classes that don't support this are serialized and stored with the 
    mimetype set.
    
    The mimetype is used for serialized objects, and tells the 
    ResXResourceReader how to depersist the object. This is currently not 
    extensible. For a given mimetype the value must be set accordingly:
    
    Note - application/x-microsoft.net.object.binary.base64 is the format 
    that the ResXResourceWriter will generate, however the reader can 
    read any of the formats listed below.
    
    mimetype: application/x-microsoft.net.object.binary.base64
    value   : The object must be serialized with 
            : System.Runtime.Serialization.Formatters.Binary.BinaryFormatter
            : and then encoded with base64 encoding.
    
    mimetype: application/x-microsoft.net.object.soap.base64
    value   : The object must be serialized with 
            : System.Runtime.Serialization.Formatters.Soap.SoapFormatter
            : and then encoded with base64 encoding.

    mimetype: application/x-microsoft.net.object.bytearray.base64
    value   : The object must be serialized into a byte array 
            : using a System.ComponentModel.TypeConverter
            : and then encoded with base64 encoding.
    -->
  <xsd:schema id="root" xmlns="" xmlns:xsd="http://www.w3.org/2001/XMLSchema" xmlns:msdata="urn:schemas-microsoft-com:xml-msdata">
    <xsd:import namespace="http://www.w3.org/XML/1998/namespace" />
    <xsd:element name="root" msdata:IsDataSet="true">
      <xsd:complexType>
        <xsd:choice maxOccurs="unbounded">
          <xsd:element name="metadata">
            <xsd:complexType>
              <xsd:sequence>
                <xsd:element name="value" type="xsd:string" minOccurs="0" />
              </xsd:sequence>
              <xsd:attribute name="name" use="required" type="xsd:string" />
              <xsd:attribute name="type" type="xsd:string" />
              <xsd:attribute name="mimetype" type="xsd:string" />
              <xsd:attribute ref="xml:space" />
            </xsd:complexType>
          </xsd:element>
          <xsd:element name="assembly">
            <xsd:complexType>
              <xsd:attribute name="alias" type="xsd:string" />
              <xsd:attribute name="name" type="xsd:string" />
            </xsd:complexType>
          </xsd:element>
          <xsd:element name="data">
            <xsd:complexType>
              <xsd:sequence>
                <xsd:element name="value" type="xsd:string" minOccurs="0" msdata:Ordinal="1" />
                <xsd:element name="comment" type="xsd:string" minOccurs="0" msdata:Ordinal="2" />
              </xsd:sequence>
              <xsd:attribute name="name" type="xsd:string" use="required" msdata:Ordinal="1" />
              <xsd:attribute name="type" type="xsd:string" msdata:Ordinal="3" />
              <xsd:attribute name="mimetype" type="xsd:string" msdata:Ordinal="4" />
              <xsd:attribute ref="xml:space" />
            </xsd:complexType>
          </xsd:element>
          <xsd:element name="resheader">
            <xsd:complexType>
              <xsd:sequence>
                <xsd:element name="value" type="xsd:string" minOccurs="0" msdata:Ordinal="1" />
              </xsd:sequence>
              <xsd:attribute name="name" type="xsd:string" use="required" />
            </xsd:complexType>
          </xsd:element>
        </xsd:choice>
      </xsd:complexType>
    </xsd:element>
  </xsd:schema>
  <resheader name="resmimetype">
    <value>text/microsoft-resx</value>
  </resheader>
  <resheader name="version">
    <value>2.0</value>
  </resheader>
  <resheader name="reader">
    <value>System.Resources.ResXResourceReader, System.Windows.Forms, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </resheader>
  <resheader name="writer">
    <value>System.Resources.ResXResourceWriter, System.Windows.Forms, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </resheader>
  <data name="AppName" xml:space="preserve">
    <value>ಕ್ಯಾಲ್ಕುಲೇಟರ್</value>
    <comment>{@Appx_ShortDisplayName@}{StringCategory="Feature Title"} This is the title of the official application when published through Windows Store.</comment>
  </data>
  <data name="DevAppName" xml:space="preserve">
    <value>ಕ್ಯಾಲ್ಕುಲೇಟರ್ [Dev]</value>
    <comment>{@Appx_ShortDisplayName@}{StringCategory="Feature Title"} This is the name of the application when built by a user via GitHub. We use a different name to make it easier for users to distinguish the apps when both this version and the Store version are installed on the same device.</comment>
  </data>
  <data name="AppStoreName" xml:space="preserve">
    <value>Windows ಕ್ಯಾಲ್ಕುಲೇಟರ್</value>
    <comment>{@Appx_DisplayName@}{StringCategory="Feature Title"} Name that shows up in the app store. It contains "Windows" to distinguish it from 3rd party calculator apps.</comment>
  </data>
  <data name="DevAppStoreName" xml:space="preserve">
    <value>Windows ಕ್ಯಾಲ್ಕುಲೇಟರ್ [Dev]</value>
    <comment>{@Appx_DisplayName@}{StringCategory="Feature Title"} Name that shows up in the app store. It contains "Windows" to distinguish it from 3rd party calculator apps. This is the the version of the name used when the app is built by a user via GitHub.</comment>
  </data>
  <data name="AppDescription" xml:space="preserve">
    <value>ಕ್ಯಾಲ್ಕುಲೇಟರ್</value>
    <comment>{@Appx_Description@} This description is used for the official application when published through Windows Store.</comment>
  </data>
  <data name="DevAppDescription" xml:space="preserve">
    <value>ಕ್ಯಾಲ್ಕುಲೇಟರ್ [Dev]</value>
    <comment>{@Appx_Description@} This is the description of the application when built by a user via GitHub. We use a different description to make it easier for users to distinguish the apps when both this version and the Store version are installed on the same device.</comment>
  </data>
  <data name="copyMenuItem" xml:space="preserve">
    <value>ನಕಲಿಸು</value>
    <comment>Copy context menu string</comment>
  </data>
  <data name="pasteMenuItem" xml:space="preserve">
    <value>ಅಂಟಿಸು</value>
    <comment>Paste context menu string</comment>
  </data>
  <data name="SupplementaryResultsHeader.Text" xml:space="preserve">
    <value>ಸುಮಾರು ಇದಕ್ಕೆ ಸಮಾನ</value>
    <comment>The text that shows at the bottom of the converter to head the supplementary results. Indicates that the main result is approximately equal to the supplementary results.</comment>
  </data>
  <data name="BitFlipItemAutomationName" xml:space="preserve">
    <value>%1, ಮೌಲ್ಯ %2</value>
    <comment>{Locked="%1","%2"}. String used in automation name for each bit in bit flip. %1 will be replaced by the position of the bit (1st bit, 3rd bit), %2 by a binary value (1 or 0)</comment>
  </data>
  <data name="BitPosition" xml:space="preserve">
    <value>%1 ಬಿಟ್</value>
    <comment>{Locked="%1"}. Sub-string used to indicate the position of a bit (e.g. 1st bit, 2nd bit...)</comment>
  </data>
  <data name="63" xml:space="preserve">
    <value>63 ನೆಯ</value>
    <comment>Sub-string used in automation name for 63 bit in bit flip</comment>
  </data>
  <data name="62" xml:space="preserve">
    <value>62 ನೆಯ</value>
    <comment>Sub-string used in automation name for 62 bit in bit flip</comment>
  </data>
  <data name="61" xml:space="preserve">
    <value>61 ನೆಯ</value>
    <comment>Sub-string used in automation name for 61 bit in bit flip</comment>
  </data>
  <data name="60" xml:space="preserve">
    <value>60 ನೆಯ</value>
    <comment>Sub-string used in automation name for 60 bit in bit flip</comment>
  </data>
  <data name="59" xml:space="preserve">
    <value>59 ನೆಯ</value>
    <comment>Sub-string used in automation name for 59 bit in bit flip</comment>
  </data>
  <data name="58" xml:space="preserve">
    <value>58 ನೆಯ</value>
    <comment>Sub-string used in automation name for 58 bit in bit flip</comment>
  </data>
  <data name="57" xml:space="preserve">
    <value>57 ನೆಯ</value>
    <comment>Sub-string used in automation name for 57 bit in bit flip</comment>
  </data>
  <data name="56" xml:space="preserve">
    <value>56 ನೆಯ</value>
    <comment>Sub-string used in automation name for 56 bit in bit flip</comment>
  </data>
  <data name="55" xml:space="preserve">
    <value>55 ನೆಯ</value>
    <comment>Sub-string used in automation name for 55 bit in bit flip</comment>
  </data>
  <data name="54" xml:space="preserve">
    <value>54 ನೆಯ</value>
    <comment>Sub-string used in automation name for 54 bit in bit flip</comment>
  </data>
  <data name="53" xml:space="preserve">
    <value>53 ನೆಯ</value>
    <comment>Sub-string used in automation name for 53 bit in bit flip</comment>
  </data>
  <data name="52" xml:space="preserve">
    <value>52 ನೆಯ</value>
    <comment>Sub-string used in automation name for 52 bit in bit flip</comment>
  </data>
  <data name="51" xml:space="preserve">
    <value>51 ನೆಯ</value>
    <comment>Sub-string used in automation name for 51 bit in bit flip</comment>
  </data>
  <data name="50" xml:space="preserve">
    <value>50 ನೆಯ</value>
    <comment>Sub-string used in automation name for 50 bit in bit flip</comment>
  </data>
  <data name="49" xml:space="preserve">
    <value>49 ನೆಯ</value>
    <comment>Sub-string used in automation name for 49 bit in bit flip</comment>
  </data>
  <data name="48" xml:space="preserve">
    <value>48 ನೆಯ</value>
    <comment>Sub-string used in automation name for 48 bit in bit flip</comment>
  </data>
  <data name="47" xml:space="preserve">
    <value>47 ನೆಯ</value>
    <comment>Sub-string used in automation name for 47 bit in bit flip</comment>
  </data>
  <data name="46" xml:space="preserve">
    <value>46 ನೆಯ</value>
    <comment>Sub-string used in automation name for 46 bit in bit flip</comment>
  </data>
  <data name="45" xml:space="preserve">
    <value>45 ನೆಯ</value>
    <comment>Sub-string used in automation name for 45 bit in bit flip</comment>
  </data>
  <data name="44" xml:space="preserve">
    <value>44 ನೆಯ</value>
    <comment>Sub-string used in automation name for 44 bit in bit flip</comment>
  </data>
  <data name="43" xml:space="preserve">
    <value>43 ನೆಯ</value>
    <comment>Sub-string used in automation name for 43 bit in bit flip</comment>
  </data>
  <data name="42" xml:space="preserve">
    <value>42 ನೆಯ</value>
    <comment>Sub-string used in automation name for 42 bit in bit flip</comment>
  </data>
  <data name="41" xml:space="preserve">
    <value>41 ನೆಯ</value>
    <comment>Sub-string used in automation name for 41 bit in bit flip</comment>
  </data>
  <data name="40" xml:space="preserve">
    <value>40 ನೆಯ</value>
    <comment>Sub-string used in automation name for 40 bit in bit flip</comment>
  </data>
  <data name="39" xml:space="preserve">
    <value>39 ನೆಯ</value>
    <comment>Sub-string used in automation name for 39 bit in bit flip</comment>
  </data>
  <data name="38" xml:space="preserve">
    <value>38 ನೆಯ</value>
    <comment>Sub-string used in automation name for 38 bit in bit flip</comment>
  </data>
  <data name="37" xml:space="preserve">
    <value>37 ನೆಯ</value>
    <comment>Sub-string used in automation name for 37 bit in bit flip</comment>
  </data>
  <data name="36" xml:space="preserve">
    <value>36 ನೆಯ</value>
    <comment>Sub-string used in automation name for 36 bit in bit flip</comment>
  </data>
  <data name="35" xml:space="preserve">
    <value>35 ನೆಯ</value>
    <comment>Sub-string used in automation name for 35 bit in bit flip</comment>
  </data>
  <data name="34" xml:space="preserve">
    <value>34 ನೆಯ</value>
    <comment>Sub-string used in automation name for 34 bit in bit flip</comment>
  </data>
  <data name="33" xml:space="preserve">
    <value>33 ನೆಯ</value>
    <comment>Sub-string used in automation name for 33 bit in bit flip</comment>
  </data>
  <data name="32" xml:space="preserve">
    <value>32 ನೆಯ</value>
    <comment>Sub-string used in automation name for 32 bit in bit flip</comment>
  </data>
  <data name="31" xml:space="preserve">
    <value>31 ನೆಯ</value>
    <comment>Sub-string used in automation name for 31 bit in bit flip</comment>
  </data>
  <data name="30" xml:space="preserve">
    <value>30 ನೆಯ</value>
    <comment>Sub-string used in automation name for 30 bit in bit flip</comment>
  </data>
  <data name="29" xml:space="preserve">
    <value>29 ನೆಯ</value>
    <comment>Sub-string used in automation name for 29 bit in bit flip</comment>
  </data>
  <data name="28" xml:space="preserve">
    <value>28 ನೆಯ</value>
    <comment>Sub-string used in automation name for 28 bit in bit flip</comment>
  </data>
  <data name="27" xml:space="preserve">
    <value>27 ನೆಯ</value>
    <comment>Sub-string used in automation name for 27 bit in bit flip</comment>
  </data>
  <data name="26" xml:space="preserve">
    <value>26 ನೆಯ</value>
    <comment>Sub-string used in automation name for 26 bit in bit flip</comment>
  </data>
  <data name="25" xml:space="preserve">
    <value>25 ನೆಯ</value>
    <comment>Sub-string used in automation name for 25 bit in bit flip</comment>
  </data>
  <data name="24" xml:space="preserve">
    <value>24 ನೆಯ</value>
    <comment>Sub-string used in automation name for 24 bit in bit flip</comment>
  </data>
  <data name="23" xml:space="preserve">
    <value>23 ನೆಯ</value>
    <comment>Sub-string used in automation name for 23 bit in bit flip</comment>
  </data>
  <data name="22" xml:space="preserve">
    <value>22 ನೆಯ</value>
    <comment>Sub-string used in automation name for 22 bit in bit flip</comment>
  </data>
  <data name="21" xml:space="preserve">
    <value>21 ನೆಯ</value>
    <comment>Sub-string used in automation name for 21 bit in bit flip</comment>
  </data>
  <data name="20" xml:space="preserve">
    <value>20 ನೆಯ</value>
    <comment>Sub-string used in automation name for 20 bit in bit flip</comment>
  </data>
  <data name="19" xml:space="preserve">
    <value>19 ನೆಯ</value>
    <comment>Sub-string used in automation name for 19 bit in bit flip</comment>
  </data>
  <data name="18" xml:space="preserve">
    <value>18 ನೆಯ</value>
    <comment>Sub-string used in automation name for 18 bit in bit flip</comment>
  </data>
  <data name="17" xml:space="preserve">
    <value>17 ನೆಯ</value>
    <comment>Sub-string used in automation name for 17 bit in bit flip</comment>
  </data>
  <data name="16" xml:space="preserve">
    <value>16 ನೆಯ</value>
    <comment>Sub-string used in automation name for 16 bit in bit flip</comment>
  </data>
  <data name="15" xml:space="preserve">
    <value>15 ನೆಯ</value>
    <comment>Sub-string used in automation name for 15 bit in bit flip</comment>
  </data>
  <data name="14" xml:space="preserve">
    <value>14 ನೆಯ</value>
    <comment>Sub-string used in automation name for 14 bit in bit flip</comment>
  </data>
  <data name="13" xml:space="preserve">
    <value>13 ನೆಯ</value>
    <comment>Sub-string used in automation name for 13 bit in bit flip</comment>
  </data>
  <data name="12" xml:space="preserve">
    <value>12 ನೆಯ</value>
    <comment>Sub-string used in automation name for 12 bit in bit flip</comment>
  </data>
  <data name="11" xml:space="preserve">
    <value>11 ನೆಯ</value>
    <comment>Sub-string used in automation name for 11 bit in bit flip</comment>
  </data>
  <data name="10" xml:space="preserve">
    <value>10 ನೆಯ</value>
    <comment>Sub-string used in automation name for 10 bit in bit flip</comment>
  </data>
  <data name="9" xml:space="preserve">
    <value>9 ನೆಯ</value>
    <comment>Sub-string used in automation name for 9 bit in bit flip</comment>
  </data>
  <data name="8" xml:space="preserve">
    <value>8 ನೆಯ</value>
    <comment>Sub-string used in automation name for 8 bit in bit flip</comment>
  </data>
  <data name="7" xml:space="preserve">
    <value>7 ನೆಯ</value>
    <comment>Sub-string used in automation name for 7 bit in bit flip</comment>
  </data>
  <data name="6" xml:space="preserve">
    <value>6 ನೆಯ</value>
    <comment>Sub-string used in automation name for 6 bit in bit flip</comment>
  </data>
  <data name="5" xml:space="preserve">
    <value>5 ನೆಯ</value>
    <comment>Sub-string used in automation name for 5 bit in bit flip</comment>
  </data>
  <data name="4" xml:space="preserve">
    <value>4 ನೆಯ</value>
    <comment>Sub-string used in automation name for 4 bit in bit flip</comment>
  </data>
  <data name="3" xml:space="preserve">
    <value>3 ನೆಯ</value>
    <comment>Sub-string used in automation name for 3 bit in bit flip</comment>
  </data>
  <data name="2" xml:space="preserve">
    <value>2 ನೆಯ</value>
    <comment>Sub-string used in automation name for 2 bit in bit flip</comment>
  </data>
  <data name="1" xml:space="preserve">
    <value>1 ನೆಯ</value>
    <comment>Sub-string used in automation name for 1 bit in bit flip</comment>
  </data>
  <data name="LeastSignificantBit" xml:space="preserve">
    <value>ಕನಿಷ್ಠ ಮಹತ್ವದ ಬಿಟ್</value>
    <comment>Used to describe the first bit of a binary number. Used in bit flip</comment>
  </data>
  <data name="MemoryButton_Open" xml:space="preserve">
    <value>ಸ್ಮರಣೆ ಫ್ಲೈಔಟ್ ತೆರೆಯಿರಿ</value>
    <comment>This is the automation name and label for the memory button when the memory flyout is closed.</comment>
  </data>
  <data name="MemoryButton_Close" xml:space="preserve">
    <value>ಸ್ಮರಣೆ ಫ್ಲೈಔಟ್ ಮುಚ್ಚು</value>
    <comment>This is the automation name and label for the memory button when the memory flyout is open.</comment>
  </data>
  <data name="AlwaysOnTop_Enter" xml:space="preserve">
    <value>ಮೇಲೆ ಇರಿಸಿ</value>
    <comment>This is the tool tip automation name for the always-on-top button when out of always-on-top mode.</comment>
  </data>
  <data name="AlwaysOnTop_Exit" xml:space="preserve">
    <value>ಪೂರ್ಣ ವೀಕ್ಷಣೆಗೆ ಹಿಂತಿರುಗಿ</value>
    <comment>This is the tool tip automation name for the always-on-top button when in always-on-top mode.</comment>
  </data>
  <data name="MemoryButton.[using:Windows.UI.Xaml.Controls]ToolTipService.ToolTip" xml:space="preserve">
    <value>ಸ್ಮರಣೆ</value>
    <comment>This is the tool tip automation name for the memory button.</comment>
  </data>
  <data name="HistoryButton.[using:Windows.UI.Xaml.Controls]ToolTipService.ToolTip" xml:space="preserve">
    <value>ಇತಿಹಾಸ (Ctrl+H)</value>
    <comment>This is the tool tip automation name for the history button.</comment>
  </data>
  <data name="bitFlip.[using:Windows.UI.Xaml.Controls]ToolTipService.ToolTip" xml:space="preserve">
    <value>ಬಿಟ್ ಟಾಗ್ಲಿಂಗ್ ಕೀಪ್ಯಾಡ್</value>
    <comment>This is the tool tip automation name for the bitFlip button.</comment>
  </data>
  <data name="fullKeypad.[using:Windows.UI.Xaml.Controls]ToolTipService.ToolTip" xml:space="preserve">
    <value>ಪೂರ್ಣ ಕೀಪ್ಯಾಡ್</value>
    <comment>This is the tool tip automation name for the numberPad button.</comment>
  </data>
  <data name="ClearMemoryButton.[using:Windows.UI.Xaml.Controls]ToolTipService.ToolTip" xml:space="preserve">
    <value>ಎಲ್ಲಾ ಸ್ಮರಣೆಯನ್ನು ತೆರವುಗೊಳಿಸಿ (Ctrl+L)</value>
    <comment>This is the tool tip automation name for the Clear Memory (MC) button.</comment>
  </data>
  <data name="MemoryLabel.Text" xml:space="preserve">
    <value>ಸ್ಮರಣೆ</value>
    <comment>The text that shows as the header for the memory list</comment>
  </data>
  <data name="MemoryPivotItem.[using:Windows.UI.Xaml.Automation]AutomationProperties.Name" xml:space="preserve">
    <value>ಸ್ಮರಣೆ</value>
    <comment>The automation name for the Memory pivot item that is shown when Calculator is in wide layout.</comment>
  </data>
  <data name="HistoryLabel.Text" xml:space="preserve">
    <value>ಇತಿಹಾಸ</value>
    <comment>The text that shows as the header for the history list</comment>
  </data>
  <data name="HistoryPivotItem.[using:Windows.UI.Xaml.Automation]AutomationProperties.Name" xml:space="preserve">
    <value>ಇತಿಹಾಸ</value>
    <comment>The automation name for the History pivot item that is shown when Calculator is in wide layout.</comment>
  </data>
  <data name="converterModeButton.Content" xml:space="preserve">
    <value>ಕನ್ವರ್ಟರ್</value>
    <comment>Label for a control that activates the unit converter mode.</comment>
  </data>
  <data name="scientificModeButton.Content" xml:space="preserve">
    <value>ವೈಜ್ಞಾನಿಕ</value>
    <comment>Label for a control that activates scientific mode calculator layout</comment>
  </data>
  <data name="standardModeButton.Content" xml:space="preserve">
    <value>ಪ್ರಮಾಣಿತ</value>
    <comment>Label for a control that activates standard mode calculator layout.</comment>
  </data>
  <data name="converterModeButton.[using:Windows.UI.Xaml.Automation]AutomationProperties.Name" xml:space="preserve">
    <value>ಕನ್ವರ್ಟರ್ ಮೋಡ್</value>
    <comment>Screen reader prompt for a control that activates the unit converter mode.</comment>
  </data>
  <data name="scientificModeButton.[using:Windows.UI.Xaml.Automation]AutomationProperties.Name" xml:space="preserve">
    <value>ವೈಜ್ಞಾನಿಕ ಮೋಡ್</value>
    <comment>Screen reader prompt for a control that activates scientific mode calculator layout</comment>
  </data>
  <data name="standardModeButton.[using:Windows.UI.Xaml.Automation]AutomationProperties.Name" xml:space="preserve">
    <value>ಪ್ರಮಾಣಿತ ಮೋಡ್</value>
    <comment>Screen reader prompt for a control that activates standard mode calculator layout.</comment>
  </data>
  <data name="ClearHistory.Name" xml:space="preserve">
    <value>ಎಲ್ಲಾ ಇತಿಹಾಸ ತೆರವುಗೊಳಿಸಿ</value>
    <comment>"ClearHistory" used on the calculator history pane that stores the calculation history.</comment>
  </data>
  <data name="ClearHistory.[using:Windows.UI.Xaml.Controls]ToolTipService.ToolTip" xml:space="preserve">
    <value>ಎಲ್ಲಾ ಇತಿಹಾಸ ತೆರವುಗೊಳಿಸಿ</value>
    <comment>This is the tool tip automation name for the Clear History button.</comment>
  </data>
  <data name="HideHistory.Name" xml:space="preserve">
    <value>ಮರೆಮಾಡಿ</value>
    <comment>"HideHistory" used on the calculator history pane that stores the calculation history.</comment>
  </data>
  <data name="StandardModeText" xml:space="preserve">
    <value>ಪ್ರಮಾಣಿತ</value>
    <comment>The text that shows in the dropdown navigation control in snapped mode when standard calculator mode is selected.</comment>
  </data>
  <data name="ScientificModeText" xml:space="preserve">
    <value>ವೈಜ್ಞಾನಿಕ</value>
    <comment>The text that shows in the dropdown navigation control in snapped mode when scientific calculator mode is selected.</comment>
  </data>
  <data name="ProgrammerModeText" xml:space="preserve">
    <value>ಪ್ರೋಗ್ರಾಮರ್</value>
    <comment>The text that shows in the dropdown navigation control in snapped mode when programmer calculator mode is selected.</comment>
  </data>
  <data name="ConverterModeText" xml:space="preserve">
    <value>ಕನ್ವರ್ಟರ್</value>
    <comment>The text that shows in the dropdown navigation control for the converter group. The previous key for this was "ConverterMode.Text".</comment>
  </data>
  <data name="CalculatorModeText" xml:space="preserve">
    <value>ಕ್ಯಾಲ್ಕುಲೇಟರ್</value>
    <comment>The text that shows in the dropdown navigation control for the calculator group.</comment>
  </data>
  <data name="ConverterModeTextCaps" xml:space="preserve">
    <value>ಕನ್ವರ್ಟರ್</value>
    <comment>The text that shows in the dropdown navigation control for the converter group in upper case. The previous key for this was "ConverterMode.Text".</comment>
  </data>
  <data name="CalculatorModeTextCaps" xml:space="preserve">
    <value>ಕ್ಯಾಲ್ಕುಲೇಟರ್</value>
    <comment>The text that shows in the dropdown navigation control for the calculator group in upper case.</comment>
  </data>
  <data name="ConverterModePluralText" xml:space="preserve">
    <value>ಪರಿವರ್ತಕಗಳು</value>
    <comment>Pluralized version of the converter group text, used for the screen reader prompt.</comment>
  </data>
  <data name="CalculatorModePluralText" xml:space="preserve">
    <value>ಕ್ಯಾಲ್ಕುಲೇಟರ್‌‍ಗಳು</value>
    <comment>Pluralized version of the calculator group text, used for the screen reader prompt.</comment>
  </data>
  <data name="Format_CalculatorResults" xml:space="preserve">
    <value>ಡಿಸ್‍ಪ್ಲೇ %1 ಆಗಿದೆ</value>
    <comment>{Locked="%1"}. Screen reader prompt for the Calculator results text block. %1 = Localized display value, e.g. "50".</comment>
  </data>
  <data name="Format_CalculatorAlwaysOnTopResults" xml:space="preserve">
    <value>ಅಭಿವ್ಯಕ್ತಿ %1, ಪ್ರಸ್ತುತ ಇನ್‌ಪುಟ್ %2 ಆಗಿದೆ</value>
    <comment>{Locked="%1","%2"}. Screen reader prompt for the Calculator always-on-top expression. %1 = Expression, e.g. "50 + 2 - 60 +", %2 = Localized display value, e.g. "50".</comment>
  </data>
  <data name="Format_CalculatorResults_Decimal" xml:space="preserve">
    <value>ಪ್ರದರ್ಶನವು %1 ಬಿಂದುವಾಗಿದೆ</value>
    <comment>{Locked="%1"}. Automation label for the calculator display in the specific case where the user has just pressed the decimal separator button. For example, the user wants to input "7.5".  When they have input "7." they will hear "Display is 7 point". "point" should be localized to the locale's appropriate decimal separator.</comment>
  </data>
  <data name="Format_CalculatorExpression" xml:space="preserve">
    <value>ಪದವಿನ್ಯಾಸ %1</value>
    <comment>{Locked="%1"}. Screen reader prompt for the Calculator expression results. %1 = Localized display value, e.g. "50 + 2 - 60 +".</comment>
  </data>
  <data name="Display_Copied" xml:space="preserve">
    <value>ಪ್ರದರ್ಶನ ಮೌಲ್ಯವನ್ನು ಕ್ಲಿಪ್‍ಬೋರ್ಡ್‍ಗೆ ನಕಲಿಸಲಾಗಿದೆ</value>
    <comment>Screen reader prompt for the Calculator display copy button, when the button is invoked.</comment>
  </data>
  <data name="HistoryPane" xml:space="preserve">
    <value>ಇತಿಹಾಸ</value>
    <comment>Screen reader prompt for the history flyout</comment>
  </data>
  <data name="MemoryPane" xml:space="preserve">
    <value>ಸ್ಮರಣೆ</value>
    <comment>Screen reader prompt for the memory flyout</comment>
  </data>
  <data name="Format_HexButtonValue" xml:space="preserve">
    <value>ಹೆಕ್ಸಾಡೆಸಿಮಲ್ %1</value>
    <comment>{Locked="%1"}. Screen reader prompt for the hexadecimal value in Programmer mode. %1 = the localized hexadecimal value, e.g. "21B4 8F73".</comment>
  </data>
  <data name="Format_DecButtonValue" xml:space="preserve">
    <value>ದಶಮಾಂಶ %1</value>
    <comment>{Locked="%1"}. Screen reader prompt for the decimal value in Programmer mode. %1 = the localized decimal value, e.g. "5,732".</comment>
  </data>
  <data name="Format_OctButtonValue" xml:space="preserve">
    <value>ಓಕ್ಟಲ್ %1</value>
    <comment>{Locked="%1"}. Screen reader prompt for the octal value in Programmer mode. %1 = the localized octal value, e.g. "155 174".</comment>
  </data>
  <data name="Format_BinButtonValue" xml:space="preserve">
    <value>ಬೈನರಿ %1</value>
    <comment>{Locked="%1"}. Screen reader prompt for the binary value in Programmer mode. %1 = the localized binary value, e.g. "0010 1011".</comment>
  </data>
  <data name="ClearHistory.[using:Windows.UI.Xaml.Automation]AutomationProperties.Name" xml:space="preserve">
    <value>ಎಲ್ಲಾ ಇತಿಹಾಸ ತೆರವುಗೊಳಿಸಿ</value>
    <comment>Screen reader prompt for the Calculator History Clear button</comment>
  </data>
  <data name="HistoryList_Cleared" xml:space="preserve">
    <value>ಇತಿಹಾಸ ಖಾಲಿ ಮಾಡಲಾಗಿದೆ</value>
    <comment>Screen reader prompt for the Calculator History Clear button, when the button is invoked.</comment>
  </data>
  <data name="HideHistory.[using:Windows.UI.Xaml.Automation]AutomationProperties.Name" xml:space="preserve">
    <value>ಇತಿಹಾಸ ಮರೆಮಾಡಿ</value>
    <comment>Screen reader prompt for the Calculator History Hide button</comment>
  </data>
  <data name="HistoryButton_Open" xml:space="preserve">
    <value>ಇತಿಹಾಸದ ಫ್ಲೈಔಟ್ ತೆರೆಯಿರಿ</value>
    <comment>Screen reader prompt for the Calculator History button, when the flyout is closed.</comment>
  </data>
  <data name="HistoryButton_Close" xml:space="preserve">
    <value>ಇತಿಹಾಸದ ಫ್ಲೈಔಟ್ ಮುಚ್ಚು</value>
    <comment>Screen reader prompt for the Calculator History button, when the flyout is open.</comment>
  </data>
  <data name="memButton.[using:Windows.UI.Xaml.Automation]AutomationProperties.Name" xml:space="preserve">
    <value>ಸ್ಮರಣೆ ಸಂಗ್ರಹ</value>
    <comment>Screen reader prompt for the Calculator Memory button</comment>
  </data>
  <data name="memButton.[using:Windows.UI.Xaml.Controls]ToolTipService.ToolTip" xml:space="preserve">
    <value>ಸ್ಮರಣೆ ಸಂಗ್ರಹಣೆ (Ctrl+M)</value>
    <comment>This is the tool tip automation name for the Memory Store (MS) button.</comment>
  </data>
  <data name="ClearMemoryButton.[using:Windows.UI.Xaml.Automation]AutomationProperties.Name" xml:space="preserve">
    <value>ಎಲ್ಲಾ ಸ್ಮರಣೆಯನ್ನು ತೆರವುಗೊಳಿಸಿ</value>
    <comment>Screen reader prompt for the Calculator Clear Memory button</comment>
  </data>
  <data name="Memory_Cleared" xml:space="preserve">
    <value>ಸ್ಮರಣೆ ತೆರವುಗೊಳಿಸಲಾಗಿದೆ</value>
    <comment>Screen reader prompt for the Calculator Clear Memory button, when the button is invoked.</comment>
  </data>
  <data name="MemRecall.[using:Windows.UI.Xaml.Automation]AutomationProperties.Name" xml:space="preserve">
    <value>ಸ್ಮರಣೆ ಮರುಪಡೆ</value>
    <comment>Screen reader prompt for the Calculator Memory Recall button</comment>
  </data>
  <data name="MemRecall.[using:Windows.UI.Xaml.Controls]ToolTipService.ToolTip" xml:space="preserve">
    <value>ಸ್ಮರಣೆ ಮರುಪಡೆಯಿರಿ (Ctrl+R)</value>
    <comment>This is the tool tip automation name for the Memory Recall (MR) button.</comment>
  </data>
  <data name="MemPlus.[using:Windows.UI.Xaml.Automation]AutomationProperties.Name" xml:space="preserve">
    <value>ಸ್ಮರಣೆ ಸೇರಿಸಿ</value>
    <comment>Screen reader prompt for the Calculator Memory Add button</comment>
  </data>
  <data name="MemPlus.[using:Windows.UI.Xaml.Controls]ToolTipService.ToolTip" xml:space="preserve">
    <value>ಸ್ಮರಣೆ ಸಂಕಲನ (Ctrl+P)</value>
    <comment>This is the tool tip automation name for the Memory Add (M+) button.</comment>
  </data>
  <data name="MemMinus.[using:Windows.UI.Xaml.Automation]AutomationProperties.Name" xml:space="preserve">
    <value>ಸ್ಮರಣೆ ಕಳೆಯಿರಿ</value>
    <comment>Screen reader prompt for the Calculator Memory Subtract button</comment>
  </data>
  <data name="MemMinus.[using:Windows.UI.Xaml.Controls]ToolTipService.ToolTip" xml:space="preserve">
    <value>ಸ್ಮರಣೆ ವ್ಯವಕಲನ (Ctrl+Q)</value>
    <comment>This is the tool tip automation name for the Memory Subtract (M-) button.</comment>
  </data>
  <data name="ClearMemoryItemButton.[using:Windows.UI.Xaml.Automation]AutomationProperties.Name" xml:space="preserve">
    <value>ಸ್ಮರಣೆ ಅಂಶ ತೆರವುಗೊಳಿಸಿ</value>
    <comment>Screen reader prompt for the Calculator Clear Memory button</comment>
  </data>
  <data name="ClearMemoryItemButton.[using:Windows.UI.Xaml.Controls]ToolTipService.ToolTip" xml:space="preserve">
    <value>ಸ್ಮರಣೆ ಅಂಶ ತೆರವುಗೊಳಿಸಿ</value>
    <comment>This is the tool tip automation name for the Clear Memory Item (MC) button in the Memory list.</comment>
  </data>
  <data name="MemPlusItem.[using:Windows.UI.Xaml.Automation]AutomationProperties.Name" xml:space="preserve">
    <value>ಸ್ಮರಣೆ ಅಂಶಕ್ಕೆ ಸೇರಿಸಿ</value>
    <comment>Screen reader prompt for the Calculator Memory Add button in the Memory list</comment>
  </data>
  <data name="MemPlusItem.[using:Windows.UI.Xaml.Controls]ToolTipService.ToolTip" xml:space="preserve">
    <value>ಸ್ಮರಣೆ ಅಂಶಕ್ಕೆ ಸೇರಿಸಿ</value>
    <comment>This is the tool tip automation name for the Calculator Memory Add button in the Memory list</comment>
  </data>
  <data name="MemMinusItem.[using:Windows.UI.Xaml.Automation]AutomationProperties.Name" xml:space="preserve">
    <value>ಸ್ಮರಣೆ ಐಟಂನಿಂದ ಕಳೆಯಿರಿ</value>
    <comment>Screen reader prompt for the Calculator Memory Subtract button in the Memory list</comment>
  </data>
  <data name="MemMinusItem.[using:Windows.UI.Xaml.Controls]ToolTipService.ToolTip" xml:space="preserve">
    <value>ಸ್ಮರಣೆ ಐಟಂನಿಂದ ಕಳೆಯಿರಿ</value>
    <comment>This is the tool tip automation name for the Calculator Memory Subtract button in the Memory list</comment>
  </data>
  <data name="ClearMemorySwipeItem.[using:Windows.UI.Xaml.Automation]AutomationProperties.Name" xml:space="preserve">
    <value>ಸ್ಮರಣೆ ಅಂಶ ತೆರವುಗೊಳಿಸಿ</value>
    <comment>Screen reader prompt for the Calculator Clear Memory button</comment>
  </data>
  <data name="ClearMemoryMenuItem.Text" xml:space="preserve">
    <value>ಸ್ಮರಣೆ ಅಂಶ ತೆರವುಗೊಳಿಸಿ</value>
    <comment>Text string for the Calculator Clear Memory option in the Memory list context menu</comment>
  </data>
  <data name="MemPlusSwipeItem.[using:Windows.UI.Xaml.Automation]AutomationProperties.Name" xml:space="preserve">
    <value>ಸ್ಮರಣೆ ಅಂಶಕ್ಕೆ ಸೇರಿಸಿ</value>
    <comment>Screen reader prompt for the Calculator Memory Add swipe button in the Memory list</comment>
  </data>
  <data name="MemPlusMenuItem.Text" xml:space="preserve">
    <value>ಸ್ಮರಣೆ ಅಂಶಕ್ಕೆ ಸೇರಿಸಿ</value>
    <comment>Text string for the Calculator Memory Add option in the Memory list context menu</comment>
  </data>
  <data name="MemMinusSwipeItem.[using:Windows.UI.Xaml.Automation]AutomationProperties.Name" xml:space="preserve">
    <value>ಸ್ಮರಣೆ ಐಟಂನಿಂದ ಕಳೆಯಿರಿ</value>
    <comment>Screen reader prompt for the Calculator Memory Subtract swipe button in the Memory list</comment>
  </data>
  <data name="MemMinusMenuItem.Text" xml:space="preserve">
    <value>ಸ್ಮರಣೆ ಐಟಂನಿಂದ ಕಳೆಯಿರಿ</value>
    <comment>Text string for the Calculator Memory Subtract option in the Memory list context menu</comment>
  </data>
  <data name="DeleteHistorySwipeItem.Text" xml:space="preserve">
    <value>ಅಳಿಸಿ</value>
    <comment>Text string for the Calculator Delete swipe button in the History list</comment>
  </data>
  <data name="CopyHistoryMenuItem.Text" xml:space="preserve">
    <value>ನಕಲಿಸಿ</value>
    <comment>Text string for the Calculator Copy option in the History list context menu</comment>
  </data>
  <data name="DeleteHistoryMenuItem.Text" xml:space="preserve">
    <value>ಅಳಿಸಿ</value>
    <comment>Text string for the Calculator Delete option in the History list context menu</comment>
  </data>
  <data name="DeleteHistorySwipeItem.[using:Windows.UI.Xaml.Automation]AutomationProperties.Name" xml:space="preserve">
    <value>ಇತಿಹಾಸ ಐಟಂ ಅಳಿಸಿ</value>
    <comment>Screen reader prompt for the Calculator Delete swipe button in the History list</comment>
  </data>
  <data name="DeleteHistoryMenuItem.[using:Windows.UI.Xaml.Automation]AutomationProperties.Name" xml:space="preserve">
    <value>ಇತಿಹಾಸ ಐಟಂ ಅಳಿಸಿ</value>
    <comment>Screen reader prompt for the Calculator Delete option in the History list context menu</comment>
  </data>
  <data name="backSpaceButton.[using:Windows.UI.Xaml.Automation]AutomationProperties.Name" xml:space="preserve">
    <value>ಬ್ಯಾಕ್ ಸ್ಪೇಸ್</value>
    <comment>Screen reader prompt for the Calculator Backspace button</comment>
  </data>
  <data name="BinaryZero.Text" xml:space="preserve">
    <value>1</value>
    <comment>Screen reader prompt for the Calculator number "0" button</comment>
  </data>
  <data name="BinaryOne.Text" xml:space="preserve">
    <value>1</value>
    <comment>Screen reader prompt for the Calculator number "1" button</comment>
  </data>
  <data name="num0Button.[using:Windows.UI.Xaml.Automation]AutomationProperties.Name" xml:space="preserve">
    <value>ಸೊನ್ನೆ</value>
    <comment>Screen reader prompt for the Calculator number "0" button</comment>
  </data>
  <data name="num1Button.[using:Windows.UI.Xaml.Automation]AutomationProperties.Name" xml:space="preserve">
    <value>ಒಂದು</value>
    <comment>Screen reader prompt for the Calculator number "1" button</comment>
  </data>
  <data name="num2Button.[using:Windows.UI.Xaml.Automation]AutomationProperties.Name" xml:space="preserve">
    <value>ಎರಡು</value>
    <comment>Screen reader prompt for the Calculator number "2" button</comment>
  </data>
  <data name="num3Button.[using:Windows.UI.Xaml.Automation]AutomationProperties.Name" xml:space="preserve">
    <value>ಮೂರು</value>
    <comment>Screen reader prompt for the Calculator number "3" button</comment>
  </data>
  <data name="num4Button.[using:Windows.UI.Xaml.Automation]AutomationProperties.Name" xml:space="preserve">
    <value>ನಾಲ್ಕು</value>
    <comment>Screen reader prompt for the Calculator number "4" button</comment>
  </data>
  <data name="num5Button.[using:Windows.UI.Xaml.Automation]AutomationProperties.Name" xml:space="preserve">
    <value>ಐದು</value>
    <comment>Screen reader prompt for the Calculator number "5" button</comment>
  </data>
  <data name="num6Button.[using:Windows.UI.Xaml.Automation]AutomationProperties.Name" xml:space="preserve">
    <value>ಆರು</value>
    <comment>Screen reader prompt for the Calculator number "6" button</comment>
  </data>
  <data name="num7Button.[using:Windows.UI.Xaml.Automation]AutomationProperties.Name" xml:space="preserve">
    <value>ಏಳು</value>
    <comment>Screen reader prompt for the Calculator number "7" button</comment>
  </data>
  <data name="num8Button.[using:Windows.UI.Xaml.Automation]AutomationProperties.Name" xml:space="preserve">
    <value>ಎಂಟು</value>
    <comment>Screen reader prompt for the Calculator number "8" button</comment>
  </data>
  <data name="num9Button.[using:Windows.UI.Xaml.Automation]AutomationProperties.Name" xml:space="preserve">
    <value>ಒಂಬತ್ತು</value>
    <comment>Screen reader prompt for the Calculator number "9" button</comment>
  </data>
  <data name="aButton.[using:Windows.UI.Xaml.Automation]AutomationProperties.Name" xml:space="preserve">
    <value>A</value>
    <comment>Screen reader prompt for the Calculator number "A" button</comment>
  </data>
  <data name="bButton.[using:Windows.UI.Xaml.Automation]AutomationProperties.Name" xml:space="preserve">
    <value>ಬಿ</value>
    <comment>Screen reader prompt for the Calculator number "B" button</comment>
  </data>
  <data name="cButton.[using:Windows.UI.Xaml.Automation]AutomationProperties.Name" xml:space="preserve">
    <value>C</value>
    <comment>Screen reader prompt for the Calculator number "C" button</comment>
  </data>
  <data name="dButton.[using:Windows.UI.Xaml.Automation]AutomationProperties.Name" xml:space="preserve">
    <value>D</value>
    <comment>Screen reader prompt for the Calculator number "D" button</comment>
  </data>
  <data name="eButton.[using:Windows.UI.Xaml.Automation]AutomationProperties.Name" xml:space="preserve">
    <value>E</value>
    <comment>Screen reader prompt for the Calculator number "E" button</comment>
  </data>
  <data name="fButton.[using:Windows.UI.Xaml.Automation]AutomationProperties.Name" xml:space="preserve">
    <value>F</value>
    <comment>Screen reader prompt for the Calculator number "F" button</comment>
  </data>
  <data name="andButton.[using:Windows.UI.Xaml.Automation]AutomationProperties.Name" xml:space="preserve">
    <value>ಮತ್ತು</value>
    <comment>Screen reader prompt for the Calculator And button</comment>
  </data>
  <data name="orButton.[using:Windows.UI.Xaml.Automation]AutomationProperties.Name" xml:space="preserve">
    <value>ಅಥವಾ</value>
    <comment>Screen reader prompt for the Calculator Or button</comment>
  </data>
  <data name="notButton.[using:Windows.UI.Xaml.Automation]AutomationProperties.Name" xml:space="preserve">
    <value>ಅಥವಾ</value>
    <comment>Screen reader prompt for the Calculator Not button</comment>
  </data>
  <data name="rolButton.[using:Windows.UI.Xaml.Automation]AutomationProperties.Name" xml:space="preserve">
    <value>ಎಡದಲ್ಲಿ ತಿರುಗಿಸು</value>
    <comment>Screen reader prompt for the Calculator ROL button</comment>
  </data>
  <data name="rorButton.[using:Windows.UI.Xaml.Automation]AutomationProperties.Name" xml:space="preserve">
    <value>ಬಲದಲ್ಲಿ ತಿರುಗಿಸು</value>
    <comment>Screen reader prompt for the Calculator ROR button</comment>
  </data>
  <data name="lshButton.[using:Windows.UI.Xaml.Automation]AutomationProperties.Name" xml:space="preserve">
    <value>ಎಡ ಶಿಫ್ಟ್</value>
    <comment>Screen reader prompt for the Calculator LSH button</comment>
  </data>
  <data name="rshButton.[using:Windows.UI.Xaml.Automation]AutomationProperties.Name" xml:space="preserve">
    <value>ಬಲ ಶಿಫ್ಟ್</value>
    <comment>Screen reader prompt for the Calculator RSH button</comment>
  </data>
  <data name="xorButton.[using:Windows.UI.Xaml.Automation]AutomationProperties.Name" xml:space="preserve">
    <value>ಏಕೈಕ ಅಥವಾ</value>
    <comment>Screen reader prompt for the Calculator XOR button</comment>
  </data>
  <data name="qwordButton.[using:Windows.UI.Xaml.Automation]AutomationProperties.Name" xml:space="preserve">
    <value>ನಾಲ್ಮಡಿ ಪದ ಟಾಗಲ್</value>
    <comment>Screen reader prompt for the Calculator qword button. Should read as "Quadruple word toggle button".</comment>
  </data>
  <data name="dwordButton.[using:Windows.UI.Xaml.Automation]AutomationProperties.Name" xml:space="preserve">
    <value>ದ್ವಿ ಪದ ಟಾಗಲ್</value>
    <comment>Screen reader prompt for the Calculator dword button. Should read as "Double word toggle button".</comment>
  </data>
  <data name="wordButton.[using:Windows.UI.Xaml.Automation]AutomationProperties.Name" xml:space="preserve">
    <value>ಪದ ಟಾಗಲ್</value>
    <comment>Screen reader prompt for the Calculator word button. Should read as "Word toggle button".</comment>
  </data>
  <data name="byteButton.[using:Windows.UI.Xaml.Automation]AutomationProperties.Name" xml:space="preserve">
    <value>ಬೈಟ್ ಟಾಗಲ್</value>
    <comment>Screen reader prompt for the Calculator byte button. Should read as "Byte toggle button".</comment>
  </data>
  <data name="bitFlip.[using:Windows.UI.Xaml.Automation]AutomationProperties.Name" xml:space="preserve">
    <value>ಬಿಟ್ ಟಾಗ್ಲಿಂಗ್ ಕೀಪ್ಯಾಡ್</value>
    <comment>Screen reader prompt for the Calculator bitFlip button</comment>
  </data>
  <data name="fullKeypad.[using:Windows.UI.Xaml.Automation]AutomationProperties.Name" xml:space="preserve">
    <value>ಪೂರ್ಣ ಕೀಪ್ಯಾಡ್</value>
    <comment>Screen reader prompt for the Calculator numberPad button</comment>
  </data>
  <data name="decimalSeparatorButton.[using:Windows.UI.Xaml.Automation]AutomationProperties.Name" xml:space="preserve">
    <value>ದಶಮಾಂಶ ವಿಭಾಜಕ</value>
    <comment>Screen reader prompt for the "." button</comment>
  </data>
  <data name="clearEntryButton.[using:Windows.UI.Xaml.Automation]AutomationProperties.Name" xml:space="preserve">
    <value>ನಮೂದು ತೆರವುಗೊಳಿಸಿ</value>
    <comment>Screen reader prompt for the "CE" button</comment>
  </data>
  <data name="clearButton.[using:Windows.UI.Xaml.Automation]AutomationProperties.Name" xml:space="preserve">
    <value>ತೆರವುಗೊಳಿಸು</value>
    <comment>Screen reader prompt for the "C" button</comment>
  </data>
  <data name="divideButton.[using:Windows.UI.Xaml.Automation]AutomationProperties.Name" xml:space="preserve">
    <value>ಇದರಿಂದ ವಿಭಾಗಿಸಿ</value>
    <comment>Screen reader prompt for the divide button on the number pad</comment>
  </data>
  <data name="multiplyButton.[using:Windows.UI.Xaml.Automation]AutomationProperties.Name" xml:space="preserve">
    <value>ಇದರಿಂದ ಗುಣಿಸಿ</value>
    <comment>Screen reader prompt for the multiply button on the number pad</comment>
  </data>
  <data name="equalButton.[using:Windows.UI.Xaml.Automation]AutomationProperties.Name" xml:space="preserve">
    <value>ಸಮ</value>
    <comment>Screen reader prompt for the equals button on the scientific operator keypad</comment>
  </data>
  <data name="shiftButton.[using:Windows.UI.Xaml.Automation]AutomationProperties.Name" xml:space="preserve">
    <value>ವಿಲೋಮ ಕಾರ್ಯ</value>
    <comment>Screen reader prompt for the shift button on the number pad in scientific mode.</comment>
  </data>
  <data name="minusButton.[using:Windows.UI.Xaml.Automation]AutomationProperties.Name" xml:space="preserve">
    <value>ಕಳೆ</value>
    <comment>Screen reader prompt for the minus button on the number pad</comment>
  </data>
  <data name="minus" xml:space="preserve">
    <value>ಕಳೆ</value>
    <comment>We use this resource to replace "-" sign for accessibility. So expression like, 1 - 3 = -2 becomes 1 minus 3 = minus 2</comment>
  </data>
  <data name="plusButton.[using:Windows.UI.Xaml.Automation]AutomationProperties.Name" xml:space="preserve">
    <value>ಕೂಡು</value>
    <comment>Screen reader prompt for the plus button on the number pad</comment>
  </data>
  <data name="squareRootButton.[using:Windows.UI.Xaml.Automation]AutomationProperties.Name" xml:space="preserve">
    <value>ವರ್ಗಮೂಲ</value>
    <comment>Screen reader prompt for the square root button on the scientific operator keypad</comment>
  </data>
  <data name="percentButton.[using:Windows.UI.Xaml.Automation]AutomationProperties.Name" xml:space="preserve">
    <value>ಶೇಕಡಾ</value>
    <comment>Screen reader prompt for the percent button on the scientific operator keypad</comment>
  </data>
  <data name="negateButton.[using:Windows.UI.Xaml.Automation]AutomationProperties.Name" xml:space="preserve">
    <value>ಧನಾತ್ಮಕ ಋಣಾತ್ಮಕ</value>
    <comment>Screen reader prompt for the negate button on the scientific operator keypad</comment>
  </data>
  <data name="converterNegateButton.[using:Windows.UI.Xaml.Automation]AutomationProperties.Name" xml:space="preserve">
    <value>ಧನಾತ್ಮಕ ಋಣಾತ್ಮಕ</value>
    <comment>Screen reader prompt for the negate button on the converter operator keypad</comment>
  </data>
  <data name="invertButton.[using:Windows.UI.Xaml.Automation]AutomationProperties.Name" xml:space="preserve">
    <value>ವ್ಯುತ್ಕ್ರಮ</value>
    <comment>Screen reader prompt for the invert button on the scientific operator keypad</comment>
  </data>
  <data name="openParenthesisButton.[using:Windows.UI.Xaml.Automation]AutomationProperties.Name" xml:space="preserve">
    <value>ಎಡ ಆವರಣ ಚಿಹ್ನೆ</value>
    <comment>Screen reader prompt for the Calculator "(" button on the scientific operator keypad</comment>
  </data>
  <data name="Format_OpenParenthesisAutomationNamePrefix" xml:space="preserve">
    <value>ಎಡ ಆವರಣ, ಬಲ ಆವರಣ ಎಣಿಕೆ %1</value>
    <comment>{Locked="%1"} Screen reader prompt for the Calculator "(" button on the scientific operator keypad. %1 is the localized count of open parenthesis, e.g. "2".</comment>
  </data>
  <data name="closeParenthesisButton.[using:Windows.UI.Xaml.Automation]AutomationProperties.Name" xml:space="preserve">
    <value>ಬಲ ಆವರಣ ಚಿಹ್ನೆ</value>
    <comment>Screen reader prompt for the Calculator ")" button on the scientific operator keypad</comment>
  </data>
  <data name="Format_OpenParenthesisCountAutomationNamePrefix" xml:space="preserve">
    <value>ತೆರೆದ ಆವರಣ ಎಣಿಕೆ %1</value>
    <comment>{Locked="%1"} Screen reader prompt for the Calculator "(" button on the scientific and programmer operator keypad. %1 is the localized count of open parenthesis, e.g. "2".</comment>
  </data>
  <data name="NoRightParenthesisAdded_Announcement" xml:space="preserve">
    <value>ಮುಚ್ಚಲು ಯಾವುದೇ ತೆರೆದ ಆವರಣ ಇಲ್ಲ.</value>
    <comment>{Locked="%1"} Screen reader prompt for the Calculator when the ")" button on the scientific and programmer operator keypad cannot be added to the equation. e.g. "1+)".</comment>
  </data>
  <data name="ftoeButton.[using:Windows.UI.Xaml.Automation]AutomationProperties.Name" xml:space="preserve">
    <value>ವೈಜ್ಞಾನಿಕ ಸಂಕೇತನ</value>
    <comment>Screen reader prompt for the Calculator F-E the scientific operator keypad</comment>
  </data>
  <data name="hyperbolicButton.[using:Windows.UI.Xaml.Automation]AutomationProperties.Name" xml:space="preserve">
    <value>ಹೈಪರ್‌ಬೋಲಿಕ್ ಕಾರ್ಯ</value>
    <comment>Screen reader prompt for the Calculator button HYP in the scientific operator keypad</comment>
  </data>
  <data name="piButton.[using:Windows.UI.Xaml.Automation]AutomationProperties.Name" xml:space="preserve">
    <value>Pi</value>
    <comment>Screen reader prompt for the Calculator pi button  on the scientific operator keypad</comment>
  </data>
  <data name="sinButton.[using:Windows.UI.Xaml.Automation]AutomationProperties.Name" xml:space="preserve">
    <value>ಸೈನ್</value>
    <comment>Screen reader prompt for the Calculator sin button  on the scientific operator keypad</comment>
  </data>
  <data name="cosButton.[using:Windows.UI.Xaml.Automation]AutomationProperties.Name" xml:space="preserve">
    <value>ಕೋಸೈನ್</value>
    <comment>Screen reader prompt for the Calculator cos button  on the scientific operator keypad</comment>
  </data>
  <data name="tanButton.[using:Windows.UI.Xaml.Automation]AutomationProperties.Name" xml:space="preserve">
    <value>ಟ್ಯಾಂಜೆಂಟ್</value>
    <comment>Screen reader prompt for the Calculator tan button  on the scientific operator keypad</comment>
  </data>
  <data name="sinhButton.[using:Windows.UI.Xaml.Automation]AutomationProperties.Name" xml:space="preserve">
    <value>ಹೈಪರ್‌ಬೋಲಿಕ್ ಸೈನ್</value>
    <comment>Screen reader prompt for the Calculator sinh button  on the scientific operator keypad</comment>
  </data>
  <data name="coshButton.[using:Windows.UI.Xaml.Automation]AutomationProperties.Name" xml:space="preserve">
    <value>ಹೈಪರ್‌ಬೋಲಿಕ್ ಕೋಸೈನ್</value>
    <comment>Screen reader prompt for the Calculator cosh button  on the scientific operator keypad</comment>
  </data>
  <data name="tanhButton.[using:Windows.UI.Xaml.Automation]AutomationProperties.Name" xml:space="preserve">
    <value>ಹೈಪರ್‌ಬೋಲಿಕ್ ಟ್ಯಾಂಜೆಂಟ್</value>
    <comment>Screen reader prompt for the Calculator tanh button  on the scientific operator keypad</comment>
  </data>
  <data name="xpower2Button.[using:Windows.UI.Xaml.Automation]AutomationProperties.Name" xml:space="preserve">
    <value>ಚೌಕ</value>
    <comment>Screen reader prompt for the x squared on the scientific operator keypad. </comment>
  </data>
  <data name="xpower3Button.[using:Windows.UI.Xaml.Automation]AutomationProperties.Name" xml:space="preserve">
    <value>ಘನಾಕೃತಿ</value>
    <comment>Screen reader prompt for the x cubed on the scientific operator keypad. </comment>
  </data>
  <data name="invsinButton.[using:Windows.UI.Xaml.Automation]AutomationProperties.Name" xml:space="preserve">
    <value>ಆರ್ಕ್ ಸೈನ್</value>
    <comment>Screen reader prompt for the inverted sin on the scientific operator keypad.</comment>
  </data>
  <data name="invcosButton.[using:Windows.UI.Xaml.Automation]AutomationProperties.Name" xml:space="preserve">
    <value>ಆರ್ಕ್ ಕೊಸೈನ್</value>
    <comment>Screen reader prompt for the inverted cos on the scientific operator keypad.</comment>
  </data>
  <data name="invtanButton.[using:Windows.UI.Xaml.Automation]AutomationProperties.Name" xml:space="preserve">
    <value>ಆರ್ಕ್ ಟ್ಯಾಂಜೆಂಟ್</value>
    <comment>Screen reader prompt for the inverted tan on the scientific operator keypad.</comment>
  </data>
  <data name="invsinhButton.[using:Windows.UI.Xaml.Automation]AutomationProperties.Name" xml:space="preserve">
    <value>ಹೈಪರ್‌ಬೋಲಿಕ್ ಆರ್ಕ್ ಸೈನ್</value>
    <comment>Screen reader prompt for the inverted sinh on the scientific operator keypad.</comment>
  </data>
  <data name="invcoshButton.[using:Windows.UI.Xaml.Automation]AutomationProperties.Name" xml:space="preserve">
    <value>ಹೈಪರ್‌ಬೋಲಿಕ್ ಆರ್ಕ್ ಕೋಸೈನ್</value>
    <comment>Screen reader prompt for the inverted cosh on the scientific operator keypad.</comment>
  </data>
  <data name="invtanhButton.[using:Windows.UI.Xaml.Automation]AutomationProperties.Name" xml:space="preserve">
    <value>ಹೈಪರ್‌ಬೋಲಿಕ್ ಆರ್ಕ್ ಟ್ಯಾಂಜೆಂಟ್</value>
    <comment>Screen reader prompt for the inverted tanh on the scientific operator keypad.</comment>
  </data>
  <data name="powerButton.[using:Windows.UI.Xaml.Automation]AutomationProperties.Name" xml:space="preserve">
    <value>’X’ ಘಾತ</value>
    <comment>Screen reader prompt for x power y button on the scientific operator keypad. </comment>
  </data>
  <data name="powerOf10Button.[using:Windows.UI.Xaml.Automation]AutomationProperties.Name" xml:space="preserve">
    <value>ಹತ್ತು ಘಾತ</value>
    <comment>Screen reader prompt for the 10 power x button on the scientific operator keypad.</comment>
  </data>
  <data name="powerOfEButton.[using:Windows.UI.Xaml.Automation]AutomationProperties.Name" xml:space="preserve">
    <value>’e’ ಘಾತ</value>
    <comment>Screen reader for the e power x on the scientific operator keypad.</comment>
  </data>
  <data name="ySquareRootButton.[using:Windows.UI.Xaml.Automation]AutomationProperties.Name" xml:space="preserve">
    <value>’x’ ನ ವರ್ಗಮೂಲ ’y’</value>
    <comment>Screen reader for the yth root of x on the scientific operator keypad. Note: String is meant to read out whatever the "Yth root" mathematical operator sounds like.</comment>
  </data>
  <data name="logBase10Button.[using:Windows.UI.Xaml.Automation]AutomationProperties.Name" xml:space="preserve">
    <value>ಲಾಗ್</value>
    <comment>Screen reader for the log base 10  on the scientific operator keypad</comment>
  </data>
  <data name="logBaseEButton.[using:Windows.UI.Xaml.Automation]AutomationProperties.Name" xml:space="preserve">
    <value>ನ್ಯಾಚುರಲ್ ಲಾಗ್</value>
    <comment>Screen reader for the log base e on the scientific operator keypad</comment>
  </data>
  <data name="modButton.[using:Windows.UI.Xaml.Automation]AutomationProperties.Name" xml:space="preserve">
    <value>ಮಾಡ್ಯುಲೋ</value>
    <comment>Screen reader for the mod button on the scientific operator keypad</comment>
  </data>
  <data name="expButton.[using:Windows.UI.Xaml.Automation]AutomationProperties.Name" xml:space="preserve">
    <value>ಘಾತೀಯ</value>
    <comment>Screen reader for the exp button on the scientific operator keypad</comment>
  </data>
  <data name="dmsButton.[using:Windows.UI.Xaml.Automation]AutomationProperties.Name" xml:space="preserve">
    <value>ಡಿಗ್ರಿ ನಿಮಿಷ ಸೆಕೆಂಡ್</value>
    <comment>Screen reader for the exp button on the scientific operator keypad</comment>
  </data>
  <data name="degreesButton.[using:Windows.UI.Xaml.Automation]AutomationProperties.Name" xml:space="preserve">
    <value>ಡಿಗ್ರಿಗಳು</value>
    <comment>Screen reader for the exp button on the scientific operator keypad</comment>
  </data>
  <data name="intButton.[using:Windows.UI.Xaml.Automation]AutomationProperties.Name" xml:space="preserve">
    <value>ಪೂರ್ಣಸಂಖ್ಯೆಯ ಭಾಗ</value>
    <comment>Screen reader for the int button on the scientific operator keypad</comment>
  </data>
  <data name="fractButton.[using:Windows.UI.Xaml.Automation]AutomationProperties.Name" xml:space="preserve">
    <value>ಭಿನ್ನಾಂಕ ಭಾಗ</value>
    <comment>Screen reader for the frac button on the scientific operator keypad</comment>
  </data>
  <data name="factorialButton.[using:Windows.UI.Xaml.Automation]AutomationProperties.Name" xml:space="preserve">
    <value>ಗುಣಲಬ್ಧ</value>
    <comment>Screen reader for the factorial button on the basic operator keypad</comment>
  </data>
  <data name="degButton.[using:Windows.UI.Xaml.Automation]AutomationProperties.Name" xml:space="preserve">
    <value>ಕೋನಗಳ ಟಾಗಲ್</value>
    <comment>This the Deg button's Degree mode automation nameon the scientific operator keypad. Should read as "Degrees toggle button".</comment>
  </data>
  <data name="gradButton.[using:Windows.UI.Xaml.Automation]AutomationProperties.Name" xml:space="preserve">
    <value>ಗ್ರೇಡಿಯನ್ಸ್ ಟಾಗಲ್</value>
    <comment>This is the Deg button's Grad mode automation name on the scientific operator keypad. Should read as "Gradians toggle button".</comment>
  </data>
  <data name="radButton.[using:Windows.UI.Xaml.Automation]AutomationProperties.Name" xml:space="preserve">
    <value>ರೇಡಿಯನ್ಸ್ ಟಾಗಲ್</value>
    <comment>This is the Deg button's Rad mode automation name on the scientific operator keypad. Should read as "Radians toggle button".</comment>
  </data>
  <data name="FlyoutNav.[using:Windows.UI.Xaml.Automation]AutomationProperties.Name" xml:space="preserve">
    <value>ಮೋಡ್ ಡ್ರಾಪ್‍ಡೌನ್</value>
    <comment>Screen reader prompt for the Mode dropdown field in Snapped and Portrait modes.</comment>
  </data>
  <data name="Categories.[using:Windows.UI.Xaml.Automation]AutomationProperties.Name" xml:space="preserve">
    <value>ವರ್ಗಗಳ ಡ್ರಾಪ್‍ಡೌನ್</value>
    <comment>Screen reader prompt for the Categories dropdown field.</comment>
  </data>
  <data name="EnterAlwaysOnTopButton.[using:Windows.UI.Xaml.Automation]AutomationProperties.Name" xml:space="preserve">
    <value>ಮೇಲೆ ಇರಿಸಿ</value>
    <comment>Screen reader prompt for the Always-on-Top button when in normal mode.</comment>
  </data>
  <data name="ExitAlwaysOnTopButton.[using:Windows.UI.Xaml.Automation]AutomationProperties.Name" xml:space="preserve">
    <value>ಪೂರ್ಣ ವೀಕ್ಷಣೆಗೆ ಹಿಂತಿರುಗಿ</value>
    <comment>Screen reader prompt for the Always-on-Top button when in Always-on-Top mode.</comment>
  </data>
  <data name="EnterAlwaysOnTopButton.[using:Windows.UI.Xaml.Controls]ToolTipService.ToolTip" xml:space="preserve">
    <value>ಮೇಲೆ ಇರಿಸಿ (Alt+Up)</value>
    <comment>This is the tool tip automation name for the Always-on-Top button when in normal mode.</comment>
  </data>
  <data name="ExitAlwaysOnTopButton.[using:Windows.UI.Xaml.Controls]ToolTipService.ToolTip" xml:space="preserve">
    <value>ಪೂರ್ಣ ವೀಕ್ಷಣೆಗೆ ಹಿಂತಿರುಗಿ (Alt+Down)</value>
    <comment>This is the tool tip automation name for the Always-on-Top button when in Always-on-Top mode.</comment>
  </data>
  <data name="Format_ValueFrom" xml:space="preserve">
    <value>%1 %2 ನಿಂದ ಪರಿವರ್ತಿಸಿ</value>
    <comment>Screen reader prompt for the Unit Converter Value1 i.e. top number field. %1 = DisplayValue, %2 = Unit field localized name.</comment>
  </data>
  <data name="Format_ValueFrom_Decimal" xml:space="preserve">
    <value>%1 ಪಾಯಿಂಟ‍ನಿಂದ %2 ಗೆ ಪರಿವರ್ತಿಸಿ</value>
    <comment>{Locked="%1"}. Automation label for the calculator display in the specific case where the user has just pressed the decimal separator button. For example, the user wants to input "7.5".  When they have input "7." they will hear "Convert from 7 point _current_unit_". "point" should be localized to the locale's appropriate decimal separator.</comment>
  </data>
  <data name="Format_ValueTo" xml:space="preserve">
    <value>%1 %2 ಗೆ ಪರಿವರ್ತಿಸುತ್ತದೆ</value>
    <comment>Screen reader prompt for the Unit Converter Value2 i.e. bottom number field. %1 = DisplayValue, %2 = Unit field localized name.</comment>
  </data>
  <data name="Format_ConversionResult" xml:space="preserve">
    <value>%1 %2 ಇದು %3 %4 ಆಗಿದೆ</value>
    <comment>Screen reader prompt for a conversion result, ie "2 liters is 2,000 milliliters" . %1 = From unit display value, %2 = From unit, %3 = To unit display value, %4 = To unit.</comment>
  </data>
  <data name="InputUnit_Name" xml:space="preserve">
    <value>ಇನ್‌ಪುಟ್ ಯುನಿಟ್</value>
    <comment>Screen reader prompt for the Unit Converter Units1 i.e. top units field.</comment>
  </data>
  <data name="OutputUnit_Name" xml:space="preserve">
    <value>ಔಟ್‍ಪುಟ್ ಯುನಿಟ್</value>
    <comment>Screen reader prompt for the Unit Converter Units2 i.e. bottom units field.</comment>
  </data>
  <data name="CategoryName_AreaText" xml:space="preserve">
    <value>ವಿಸ್ತೀರ್ಣ</value>
    <comment>Unit conversion category name called Area (eg. area of a sports field in square meters)</comment>
  </data>
  <data name="CategoryName_DataText" xml:space="preserve">
    <value>ಡೇಟಾ</value>
    <comment>Unit conversion category name called Data</comment>
  </data>
  <data name="CategoryName_EnergyText" xml:space="preserve">
    <value>ಶಕ್ತಿ</value>
    <comment>Unit conversion category name called Energy. (eg. the energy in a battery or in food)</comment>
  </data>
  <data name="CategoryName_LengthText" xml:space="preserve">
    <value>ಉದ್ದ</value>
    <comment>Unit conversion category name called Length</comment>
  </data>
  <data name="CategoryName_PowerText" xml:space="preserve">
    <value>ಪವರ್</value>
    <comment>Unit conversion category name called Power (eg. the power of an engine or a light bulb)</comment>
  </data>
  <data name="CategoryName_SpeedText" xml:space="preserve">
    <value>ವೇಗ</value>
    <comment>Unit conversion category name called Speed</comment>
  </data>
  <data name="CategoryName_TimeText" xml:space="preserve">
    <value>ಸಮಯ</value>
    <comment>Unit conversion category name called Time</comment>
  </data>
  <data name="CategoryName_VolumeText" xml:space="preserve">
    <value>ಪ್ರಮಾಣ</value>
    <comment>Unit conversion category name called Volume (eg. cups, teaspoons, milliliters)</comment>
  </data>
  <data name="CategoryName_TemperatureText" xml:space="preserve">
    <value>ಉಷ್ಣತೆ</value>
    <comment>Unit conversion category name called Temperature</comment>
  </data>
  <data name="CategoryName_WeightText" xml:space="preserve">
    <value>ತೂಕ</value>
    <comment>Unit conversion category name called Weight and mass. Note that this category includes units of both mass and weight. People use the word "weight" in everyday life for measuring things such as food and people. In case a language has same word for "weight" and "mass" please use one word only.</comment>
  </data>
  <data name="CategoryName_PressureText" xml:space="preserve">
    <value>ಒತ್ತಡ</value>
    <comment>Unit conversion category name called Pressure</comment>
  </data>
  <data name="CategoryName_AngleText" xml:space="preserve">
    <value>ಕೋನ</value>
    <comment>Unit conversion category name called Angle</comment>
  </data>
  <data name="CategoryName_CurrencyText" xml:space="preserve">
    <value>ಕರೆನ್ಸಿ</value>
    <comment>Unit conversion category name called Currency</comment>
  </data>
  <data name="UnitName_FluidOunceUK" xml:space="preserve">
    <value>ಫ್ಲೂಯಿಡ್ ಔನ್ಸ್‌ಗಳು (UK)</value>
    <comment>A measurement unit for volume, in plural. (Please choose the most appropriate plural form to fit any number between 0 and 999,999,999,999,999)</comment>
  </data>
  <data name="UnitAbbreviation_FluidOunceUK" xml:space="preserve">
    <value>fl oz (UK)</value>
    <comment>An abbreviation for a measurement unit of volume</comment>
  </data>
  <data name="UnitName_FluidOunceUS" xml:space="preserve">
    <value>ಫ್ಲೂಯಿಡ್ ಔನ್ಸ್‌ಗಳು (US)</value>
    <comment>A measurement unit for volume. (Please choose the most appropriate plural form to fit any number between 0 and 999,999,999,999,999)</comment>
  </data>
  <data name="UnitAbbreviation_FluidOunceUS" xml:space="preserve">
    <value>fl oz (US)</value>
    <comment>An abbreviation for a measurement unit of volume</comment>
  </data>
  <data name="UnitName_GallonUK" xml:space="preserve">
    <value>ಗ್ಯಾಲನ್‍ಗಳು (UK)</value>
    <comment>A measurement unit for volume. (Please choose the most appropriate plural form to fit any number between 0 and 999,999,999,999,999)</comment>
  </data>
  <data name="UnitAbbreviation_GallonUK" xml:space="preserve">
    <value>ಗ್ಯಾಲನ್ (UK)</value>
    <comment>An abbreviation for a measurement unit of volume</comment>
  </data>
  <data name="UnitName_GallonUS" xml:space="preserve">
    <value>ಗ್ಯಾಲನ್‍ಗಳು (US)</value>
    <comment>A measurement unit for volume. (Please choose the most appropriate plural form to fit any number between 0 and 999,999,999,999,999)</comment>
  </data>
  <data name="UnitAbbreviation_GallonUS" xml:space="preserve">
    <value>ಗ್ಯಾಲನ್ (US)</value>
    <comment>An abbreviation for a measurement unit of volume</comment>
  </data>
  <data name="UnitName_Liter" xml:space="preserve">
    <value>ಲೀಟರ್‌‍ಗಳು</value>
    <comment>A measurement unit for volume. (Please choose the most appropriate plural form to fit any number between 0 and 999,999,999,999,999)</comment>
  </data>
  <data name="UnitAbbreviation_Liter" xml:space="preserve">
    <value>ಲೀ</value>
    <comment>An abbreviation for a measurement unit of volume</comment>
  </data>
  <data name="UnitName_Milliliter" xml:space="preserve">
    <value>ಮಿಲಿಲೀಟರ್‌‍ಗಳು</value>
    <comment>A measurement unit for volume. (Please choose the most appropriate plural form to fit any number between 0 and 999,999,999,999,999)</comment>
  </data>
  <data name="UnitAbbreviation_Milliliter" xml:space="preserve">
    <value>ಮಿಲೀ</value>
    <comment>An abbreviation for a measurement unit of volume</comment>
  </data>
  <data name="UnitName_PintUK" xml:space="preserve">
    <value>ಪಿಂಟ್ಸ್ (UK)</value>
    <comment>A measurement unit for volume. (Please choose the most appropriate plural form to fit any number between 0 and 999,999,999,999,999)</comment>
  </data>
  <data name="UnitAbbreviation_PintUK" xml:space="preserve">
    <value>pt (UK)</value>
    <comment>An abbreviation for a measurement unit of volume</comment>
  </data>
  <data name="UnitName_PintUS" xml:space="preserve">
    <value>ಪಿಂಟ್ಸ್ (US)</value>
    <comment>A measurement unit for volume. (Please choose the most appropriate plural form to fit any number between 0 and 999,999,999,999,999)</comment>
  </data>
  <data name="UnitAbbreviation_PintUS" xml:space="preserve">
    <value>pt (US)</value>
    <comment>An abbreviation for a measurement unit of volume</comment>
  </data>
  <data name="UnitName_TablespoonUS" xml:space="preserve">
    <value>ಟೇಬಲ್‍ಸ್ಪೂನ್‍ಗಳು (US)</value>
    <comment>A measurement unit for volume. (Please choose the most appropriate plural form to fit any number between 0 and 999,999,999,999,999)</comment>
  </data>
  <data name="UnitAbbreviation_TablespoonUS" xml:space="preserve">
    <value>ಟೇಬಲ್ ಸ್ಪೂನ್ (US)</value>
    <comment>An abbreviation for a measurement unit of volume</comment>
  </data>
  <data name="UnitName_TeaspoonUS" xml:space="preserve">
    <value>ಟೀಸ್ಪೂನ್‍ಗಳು (US)</value>
    <comment>A measurement unit for volume. (Please choose the most appropriate plural form to fit any number between 0 and 999,999,999,999,999)</comment>
  </data>
  <data name="UnitAbbreviation_TeaspoonUS" xml:space="preserve">
    <value>ಟೀ ಸ್ಪೂನ್ (US)</value>
    <comment>An abbreviation for a measurement unit of volume</comment>
  </data>
  <data name="UnitName_TablespoonUK" xml:space="preserve">
    <value>ಟೇಬಲ್‍ಸ್ಪೂನ್‍ಗಳು (UK)</value>
    <comment>A measurement unit for volume. (Please choose the most appropriate plural form to fit any number between 0 and 999,999,999,999,999)</comment>
  </data>
  <data name="UnitAbbreviation_TablespoonUK" xml:space="preserve">
    <value>ಟೇಬಲ್ ಸ್ಪೂನ್ (UK)</value>
    <comment>An abbreviation for a measurement unit of volume</comment>
  </data>
  <data name="UnitName_TeaspoonUK" xml:space="preserve">
    <value>ಟೀಸ್ಪೂನ್‍ಗಳು (UK)</value>
    <comment>A measurement unit for volume. (Please choose the most appropriate plural form to fit any number between 0 and 999,999,999,999,999)</comment>
  </data>
  <data name="UnitAbbreviation_TeaspoonUK" xml:space="preserve">
    <value>ಟೀ ಸ್ಪೂನ್ (UK)</value>
    <comment>An abbreviation for a measurement unit of volume</comment>
  </data>
  <data name="UnitName_QuartUK" xml:space="preserve">
    <value>ಕ್ವಾರ್ಟ್ಸ್ (UK)</value>
    <comment>A measurement unit for volume. (Please choose the most appropriate plural form to fit any number between 0 and 999,999,999,999,999)</comment>
  </data>
  <data name="UnitAbbreviation_QuartUK" xml:space="preserve">
    <value>ಕ್ಯೂಟಿ (UK)</value>
    <comment>An abbreviation for a measurement unit of volume</comment>
  </data>
  <data name="UnitName_QuartUS" xml:space="preserve">
    <value>ಕ್ವಾರ್ಟ್ಸ್ (US)</value>
    <comment>A measurement unit for volume. (Please choose the most appropriate plural form to fit any number between 0 and 999,999,999,999,999)</comment>
  </data>
  <data name="UnitAbbreviation_QuartUS" xml:space="preserve">
    <value>qt (US)</value>
    <comment>An abbreviation for a measurement unit of volume</comment>
  </data>
  <data name="UnitName_CupUS" xml:space="preserve">
    <value>ಕಪ್‍ಗಳು (US)</value>
    <comment>A measurement unit for volume. (Please choose the most appropriate plural form to fit any number between 0 and 999,999,999,999,999)</comment>
  </data>
  <data name="UnitAbbreviation_CupUS" xml:space="preserve">
    <value>ಕಪ್ (US)</value>
    <comment>An abbreviation for a measurement unit of volume</comment>
  </data>
  <data name="UnitAbbreviation_Angstrom" xml:space="preserve">
    <value>A</value>
    <comment>An abbreviation for a measurement unit of length</comment>
  </data>
  <data name="UnitAbbreviation_Acre" xml:space="preserve">
    <value>ac</value>
    <comment>An abbreviation for a measurement unit of volume</comment>
  </data>
  <data name="UnitAbbreviation_Bit" xml:space="preserve">
    <value>b</value>
    <comment>An abbreviation for a measurement unit of data</comment>
  </data>
  <data name="UnitAbbreviation_BritishThermalUnit" xml:space="preserve">
    <value>BTU</value>
    <comment>An abbreviation for a measurement unit of volume</comment>
  </data>
  <data name="UnitAbbreviation_BTUPerMinute" xml:space="preserve">
    <value>BTU/ನಿಮಿಷ</value>
    <comment>An abbreviation for a measurement unit of power</comment>
  </data>
  <data name="UnitAbbreviation_Byte" xml:space="preserve">
    <value>ಬಿ</value>
    <comment>An abbreviation for a measurement unit of data</comment>
  </data>
  <data name="UnitAbbreviation_Calorie" xml:space="preserve">
    <value>ಕ್ಯಾಲೊರಿ</value>
    <comment>An abbreviation for a measurement unit of energy</comment>
  </data>
  <data name="UnitAbbreviation_Centimeter" xml:space="preserve">
    <value>ಸೆಂಮಿ</value>
    <comment>An abbreviation for a measurement unit of length</comment>
  </data>
  <data name="UnitAbbreviation_CentimetersPerSecond" xml:space="preserve">
    <value>ಸೆಂಮೀ/ಸೆ</value>
    <comment>An abbreviation for a measurement unit of speed</comment>
  </data>
  <data name="UnitAbbreviation_CubicCentimeter" xml:space="preserve">
    <value>ಸೆಂಮೀ³</value>
    <comment>An abbreviation for a measurement unit of volume</comment>
  </data>
  <data name="UnitAbbreviation_CubicFoot" xml:space="preserve">
    <value>ft³</value>
    <comment>An abbreviation for a measurement unit of volume</comment>
  </data>
  <data name="UnitAbbreviation_CubicInch" xml:space="preserve">
    <value>in³</value>
    <comment>An abbreviation for a measurement unit of volume</comment>
  </data>
  <data name="UnitAbbreviation_CubicMeter" xml:space="preserve">
    <value>ಮೀ³</value>
    <comment>An abbreviation for a measurement unit of volume</comment>
  </data>
  <data name="UnitAbbreviation_CubicYard" xml:space="preserve">
    <value>ಯಾರ್ಡ್³</value>
    <comment>An abbreviation for a measurement unit of volume</comment>
  </data>
  <data name="UnitAbbreviation_Day" xml:space="preserve">
    <value>d</value>
    <comment>An abbreviation for a measurement unit of time</comment>
  </data>
  <data name="UnitAbbreviation_DegreesCelsius" xml:space="preserve">
    <value>°C</value>
    <comment>An abbreviation for "degrees Celsius"</comment>
  </data>
  <data name="UnitAbbreviation_DegreesFahrenheit" xml:space="preserve">
    <value>°F</value>
    <comment>An abbreviation for a "degrees Fahrenheit"</comment>
  </data>
  <data name="UnitAbbreviation_Electron-Volt" xml:space="preserve">
    <value>ಇವಿ</value>
    <comment>An abbreviation for a measurement unit of energy</comment>
  </data>
  <data name="UnitAbbreviation_Foot" xml:space="preserve">
    <value>ಅಡಿ</value>
    <comment>An abbreviation for a measurement unit of length</comment>
  </data>
  <data name="UnitAbbreviation_FeetPerSecond" xml:space="preserve">
    <value>ಅಡಿ/ಸೆ</value>
    <comment>An abbreviation for a measurement unit of speed</comment>
  </data>
  <data name="UnitAbbreviation_Foot-Pound" xml:space="preserve">
    <value>ft•lb</value>
    <comment>An abbreviation for a measurement unit of energy</comment>
  </data>
  <data name="UnitAbbreviation_Gigabit" xml:space="preserve">
    <value>Gb</value>
    <comment>An abbreviation for a measurement unit of data</comment>
  </data>
  <data name="UnitAbbreviation_Gigabyte" xml:space="preserve">
    <value>GB</value>
    <comment>An abbreviation for a measurement unit of data</comment>
  </data>
  <data name="UnitAbbreviation_Hectare" xml:space="preserve">
    <value>ha</value>
    <comment>An abbreviation for a measurement unit of area</comment>
  </data>
  <data name="UnitAbbreviation_Horsepower" xml:space="preserve">
    <value>hp (US)</value>
    <comment>An abbreviation for a measurement unit of power</comment>
  </data>
  <data name="UnitAbbreviation_Hour" xml:space="preserve">
    <value>ಗಂಟೆ</value>
    <comment>An abbreviation for a measurement unit of time</comment>
  </data>
  <data name="UnitAbbreviation_Inch" xml:space="preserve">
    <value>in</value>
    <comment>An abbreviation for a measurement unit of length</comment>
  </data>
  <data name="UnitAbbreviation_Joule" xml:space="preserve">
    <value>J</value>
    <comment>An abbreviation for a measurement unit of energy</comment>
  </data>
  <data name="UnitAbbreviation_Kilowatthour" xml:space="preserve">
    <value>kWh</value>
    <comment>An abbreviation for a measurement unit of electricity consumption</comment>
  </data>
  <data name="UnitAbbreviation_Kelvin" xml:space="preserve">
    <value>K</value>
    <comment>An abbreviation for the temperature system "Kelvin" (eg. 0 degrees Celsius = 273 Kelvin)</comment>
  </data>
  <data name="UnitAbbreviation_Kilobit" xml:space="preserve">
    <value>Kb</value>
    <comment>An abbreviation for a measurement unit of data</comment>
  </data>
  <data name="UnitAbbreviation_Kilobyte" xml:space="preserve">
    <value>KB</value>
    <comment>An abbreviation for a measurement unit of data</comment>
  </data>
  <data name="UnitAbbreviation_Kilocalorie" xml:space="preserve">
    <value>ಕಿಲೋಕ್ಯಾಲೋರಿ</value>
    <comment>An abbreviation for a measurement unit of energy</comment>
  </data>
  <data name="UnitAbbreviation_Kilojoule" xml:space="preserve">
    <value>ಕಿಲೋಜೌಲ್</value>
    <comment>An abbreviation for a measurement unit of energy</comment>
  </data>
  <data name="UnitAbbreviation_Kilometer" xml:space="preserve">
    <value>ಕಿಮೀ</value>
    <comment>An abbreviation for a measurement unit of length</comment>
  </data>
  <data name="UnitAbbreviation_KilometersPerHour" xml:space="preserve">
    <value>ಕಿಮೀ/ಗಂ</value>
    <comment>An abbreviation for a measurement unit of speed</comment>
  </data>
  <data name="UnitAbbreviation_Kilowatt" xml:space="preserve">
    <value>ಕಿಲೋವ್ಯಾಟ್</value>
    <comment>An abbreviation for a measurement unit of power</comment>
  </data>
  <data name="UnitAbbreviation_Knot" xml:space="preserve">
    <value>kn</value>
    <comment>An abbreviation for a measurement unit of speed</comment>
  </data>
  <data name="UnitAbbreviation_Mach" xml:space="preserve">
    <value>M</value>
    <comment>An abbreviation for a measurement of speed (Mach is the speed of sound, Mach 2 is 2 times the speed of sound)</comment>
  </data>
  <data name="UnitAbbreviation_Megabit" xml:space="preserve">
    <value>Mb</value>
    <comment>An abbreviation for a measurement unit of data</comment>
  </data>
  <data name="UnitAbbreviation_Megabyte" xml:space="preserve">
    <value>MB</value>
    <comment>An abbreviation for a measurement unit of data</comment>
  </data>
  <data name="UnitAbbreviation_Meter" xml:space="preserve">
    <value>m</value>
    <comment>An abbreviation for a measurement unit of length</comment>
  </data>
  <data name="UnitAbbreviation_MetersPerSecond" xml:space="preserve">
    <value>m/s</value>
    <comment>An abbreviation for a measurement unit of speed</comment>
  </data>
  <data name="UnitAbbreviation_Micron" xml:space="preserve">
    <value>µm</value>
    <comment>An abbreviation for a measurement unit of length</comment>
  </data>
  <data name="UnitAbbreviation_Microsecond" xml:space="preserve">
    <value>µs</value>
    <comment>An abbreviation for a measurement unit of time</comment>
  </data>
  <data name="UnitAbbreviation_Mile" xml:space="preserve">
    <value>mi</value>
    <comment>An abbreviation for a measurement unit of length</comment>
  </data>
  <data name="UnitAbbreviation_MilesPerHour" xml:space="preserve">
    <value>mph</value>
    <comment>An abbreviation for a measurement unit of speed</comment>
  </data>
  <data name="UnitAbbreviation_Millimeter" xml:space="preserve">
    <value>ಮಿಮೀ</value>
    <comment>An abbreviation for a measurement unit of length</comment>
  </data>
  <data name="UnitAbbreviation_Millisecond" xml:space="preserve">
    <value>ms</value>
    <comment>An abbreviation for a measurement unit of time</comment>
  </data>
  <data name="UnitAbbreviation_Minute" xml:space="preserve">
    <value>ನಿಮಿಷ</value>
    <comment>An abbreviation for a measurement unit of time</comment>
  </data>
  <data name="UnitAbbreviation_Nanometer" xml:space="preserve">
    <value>nm</value>
    <comment>An abbreviation for a measurement unit of length</comment>
  </data>
  <data name="UnitAbbreviation_NauticalMile" xml:space="preserve">
    <value>nmi</value>
    <comment>An abbreviation for a measurement unit of length</comment>
  </data>
  <data name="UnitAbbreviation_Petabit" xml:space="preserve">
    <value>Pb</value>
    <comment>An abbreviation for a measurement unit of data</comment>
  </data>
  <data name="UnitAbbreviation_Petabyte" xml:space="preserve">
    <value>PB</value>
    <comment>An abbreviation for a measurement unit of data</comment>
  </data>
  <data name="UnitAbbreviation_Foot-PoundPerMinute" xml:space="preserve">
    <value>ft•lb/min</value>
    <comment>An abbreviation for a measurement unit of power</comment>
  </data>
  <data name="UnitAbbreviation_Second" xml:space="preserve">
    <value>ಸೆ</value>
    <comment>An abbreviation for a measurement unit of time</comment>
  </data>
  <data name="UnitAbbreviation_SquareCentimeter" xml:space="preserve">
    <value>ಸೆಂಮೀ²</value>
    <comment>An abbreviation for a measurement unit of area</comment>
  </data>
  <data name="UnitAbbreviation_SquareFoot" xml:space="preserve">
    <value>ಅಡಿ²</value>
    <comment>An abbreviation for a measurement unit of area</comment>
  </data>
  <data name="UnitAbbreviation_SquareInch" xml:space="preserve">
    <value>in²</value>
    <comment>An abbreviation for a measurement unit of area</comment>
  </data>
  <data name="UnitAbbreviation_SquareKilometer" xml:space="preserve">
    <value>ಕಿಮೀ²</value>
    <comment>An abbreviation for a measurement unit of area</comment>
  </data>
  <data name="UnitAbbreviation_SquareMeter" xml:space="preserve">
    <value>ಮೀ²</value>
    <comment>An abbreviation for a measurement unit of area</comment>
  </data>
  <data name="UnitAbbreviation_SquareMile" xml:space="preserve">
    <value>ಮಿಲಿ²</value>
    <comment>An abbreviation for a measurement unit of area</comment>
  </data>
  <data name="UnitAbbreviation_SquareMillimeter" xml:space="preserve">
    <value>ಮಿಮಿ²</value>
    <comment>An abbreviation for a measurement unit of area</comment>
  </data>
  <data name="UnitAbbreviation_SquareYard" xml:space="preserve">
    <value>ಯಾರ್ಡ್²</value>
    <comment>An abbreviation for a measurement unit of area</comment>
  </data>
  <data name="UnitAbbreviation_Terabit" xml:space="preserve">
    <value>Tb</value>
    <comment>An abbreviation for a measurement unit of data</comment>
  </data>
  <data name="UnitAbbreviation_Terabyte" xml:space="preserve">
    <value>TB</value>
    <comment>An abbreviation for a measurement unit of data</comment>
  </data>
  <data name="UnitAbbreviation_Watt" xml:space="preserve">
    <value>W</value>
    <comment>An abbreviation for a measurement unit of power</comment>
  </data>
  <data name="UnitAbbreviation_Week" xml:space="preserve">
    <value>ವಾರ</value>
    <comment>An abbreviation for a measurement unit of time</comment>
  </data>
  <data name="UnitAbbreviation_Yard" xml:space="preserve">
    <value>ಯಾರ್ಡ್</value>
    <comment>An abbreviation for a measurement unit of length</comment>
  </data>
  <data name="UnitAbbreviation_Year" xml:space="preserve">
    <value>ವರ್ಷ</value>
    <comment>An abbreviation for a measurement unit of time</comment>
  </data>
  <data name="UnitAbbreviation_Gibibits" xml:space="preserve">
    <value>Gi</value>
    <comment>An abbreviation for a measurement unit of data</comment>
  </data>
  <data name="UnitAbbreviation_Gibibytes" xml:space="preserve">
    <value>GiB</value>
    <comment>An abbreviation for a measurement unit of data</comment>
  </data>
  <data name="UnitAbbreviation_Kibibits" xml:space="preserve">
    <value>Ki</value>
    <comment>An abbreviation for a measurement unit of data</comment>
  </data>
  <data name="UnitAbbreviation_Kibibytes" xml:space="preserve">
    <value>KiB</value>
    <comment>An abbreviation for a measurement unit of data</comment>
  </data>
  <data name="UnitAbbreviation_Mebibits" xml:space="preserve">
    <value>Mi</value>
    <comment>An abbreviation for a measurement unit of data</comment>
  </data>
  <data name="UnitAbbreviation_Mebibytes" xml:space="preserve">
    <value>MiB</value>
    <comment>An abbreviation for a measurement unit of data</comment>
  </data>
  <data name="UnitAbbreviation_Nibble" xml:space="preserve">
    <value>nybl</value>
    <comment>An abbreviation for a measurement unit of data</comment>
  </data>
  <data name="UnitAbbreviation_Pebibits" xml:space="preserve">
    <value>Pi</value>
    <comment>An abbreviation for a measurement unit of data</comment>
  </data>
  <data name="UnitAbbreviation_Pebibytes" xml:space="preserve">
    <value>PiB</value>
    <comment>An abbreviation for a measurement unit of data</comment>
  </data>
  <data name="UnitAbbreviation_Tebibits" xml:space="preserve">
    <value>Ti</value>
    <comment>An abbreviation for a measurement unit of data</comment>
  </data>
  <data name="UnitAbbreviation_Tebibytes" xml:space="preserve">
    <value>TiB</value>
    <comment>An abbreviation for a measurement unit of data</comment>
  </data>
  <data name="UnitAbbreviation_Exabits" xml:space="preserve">
    <value>E</value>
    <comment>An abbreviation for a measurement unit of data</comment>
  </data>
  <data name="UnitAbbreviation_Exabytes" xml:space="preserve">
    <value>EB</value>
    <comment>An abbreviation for a measurement unit of data</comment>
  </data>
  <data name="UnitAbbreviation_Exbibits" xml:space="preserve">
    <value>Ei</value>
    <comment>An abbreviation for a measurement unit of data</comment>
  </data>
  <data name="UnitAbbreviation_Exbibytes" xml:space="preserve">
    <value>EiB</value>
    <comment>An abbreviation for a measurement unit of data</comment>
  </data>
  <data name="UnitAbbreviation_Zetabits" xml:space="preserve">
    <value>Z</value>
    <comment>An abbreviation for a measurement unit of data</comment>
  </data>
  <data name="UnitAbbreviation_Zetabytes" xml:space="preserve">
    <value>ZB</value>
    <comment>An abbreviation for a measurement unit of data</comment>
  </data>
  <data name="UnitAbbreviation_Zebibits" xml:space="preserve">
    <value>Zi</value>
    <comment>An abbreviation for a measurement unit of data</comment>
  </data>
  <data name="UnitAbbreviation_Zebibytes" xml:space="preserve">
    <value>ZiB</value>
    <comment>An abbreviation for a measurement unit of data</comment>
  </data>
  <data name="UnitAbbreviation_Yottabit" xml:space="preserve">
    <value>Y</value>
    <comment>An abbreviation for a measurement unit of data</comment>
  </data>
  <data name="UnitAbbreviation_Yottabyte" xml:space="preserve">
    <value>YB</value>
    <comment>An abbreviation for a measurement unit of data</comment>
  </data>
  <data name="UnitAbbreviation_Yobibits" xml:space="preserve">
    <value>Yi</value>
    <comment>An abbreviation for a measurement unit of data</comment>
  </data>
  <data name="UnitAbbreviation_Yobibytes" xml:space="preserve">
    <value>YiB</value>
    <comment>An abbreviation for a measurement unit of data</comment>
  </data>
  <data name="UnitName_Acre" xml:space="preserve">
    <value>ಎಕರೆಗಳು</value>
    <comment>A measurement unit for area. (Please choose the most appropriate plural form to fit any number between 0 and 999,999,999,999,999)</comment>
  </data>
  <data name="UnitName_Bit" xml:space="preserve">
    <value>ಬಿಟ್ಸ್</value>
    <comment>A measurement unit for data. (Please choose the most appropriate plural form to fit any number between 0 and 999,999,999,999,999)</comment>
  </data>
  <data name="UnitName_BritishThermalUnit" xml:space="preserve">
    <value>ಬ್ರಿಟಿಷ್ ಥರ್ಮಲ್ ಘಟಕಗಳು</value>
    <comment>A measurement unit for energy. (Please choose the most appropriate plural form to fit any number between 0 and 999,999,999,999,999)</comment>
  </data>
  <data name="UnitName_BTUPerMinute" xml:space="preserve">
    <value>BTU ಗಳು/ನಿಮಿಷ</value>
    <comment>A measurement unit for power: British Thermal Units per minute. (Please choose the most appropriate plural form to fit any number between 0 and 999,999,999,999,999)</comment>
  </data>
  <data name="UnitName_Byte" xml:space="preserve">
    <value>ಬೈಟ್‍ಗಳು</value>
    <comment>A measurement unit for data. (Please choose the most appropriate plural form to fit any number between 0 and 999,999,999,999,999)</comment>
  </data>
  <data name="UnitName_Calorie" xml:space="preserve">
    <value>ಥರ್ಮಲ್ ಕ್ಯಾಲೋರಿಗಳು</value>
    <comment>A measurement unit for energy. Please note that this is the "small calorie" used in science for measuring heat energy, not the "large calorie" commonly used for measuring food energy. If there is a simple term to distinguish this one from the large "Food calorie", please use that. (Please choose the most appropriate plural form to fit any number between 0 and 999,999,999,999,999)</comment>
  </data>
  <data name="UnitName_Centimeter" xml:space="preserve">
    <value>ಸೆಂಟಿಮೀಟರುಗಳು</value>
    <comment>A measurement unit for length. (Please choose the most appropriate plural form to fit any number between 0 and 999,999,999,999,999)</comment>
  </data>
  <data name="UnitName_CentimetersPerSecond" xml:space="preserve">
    <value>ಪ್ರತಿ ಸೆಕೆಂಡಿಗೆ ಸೆಂಟಿಮೀಟರ್‌‍ಗಳು</value>
    <comment>A measurement unit for speed. (Please choose the most appropriate plural form to fit any number between 0 and 999,999,999,999,999)</comment>
  </data>
  <data name="UnitName_CubicCentimeter" xml:space="preserve">
    <value>ಘನ ಸೆಂಟಿಮೀಟರ್‌‍ಗಳು</value>
    <comment>A measurement unit for volume. (Please choose the most appropriate plural form to fit any number between 0 and 999,999,999,999,999)</comment>
  </data>
  <data name="UnitName_CubicFoot" xml:space="preserve">
    <value>ಘನ ಅಡಿ</value>
    <comment>A measurement unit for volume. (Please choose the most appropriate plural form to fit any number between 0 and 999,999,999,999,999)</comment>
  </data>
  <data name="UnitName_CubicInch" xml:space="preserve">
    <value>ಘನ ಇಂಚುಗಳು</value>
    <comment>A measurement unit for volume. (Please choose the most appropriate plural form to fit any number between 0 and 999,999,999,999,999)</comment>
  </data>
  <data name="UnitName_CubicMeter" xml:space="preserve">
    <value>ಘನ ಮೀಟರ್‌‍ಗಳು</value>
    <comment>A measurement unit for volume. (Please choose the most appropriate plural form to fit any number between 0 and 999,999,999,999,999)</comment>
  </data>
  <data name="UnitName_CubicYard" xml:space="preserve">
    <value>ಘನ ಯಾರ್ಡ್‍ಗಳು</value>
    <comment>A measurement unit for volume. (Please choose the most appropriate plural form to fit any number between 0 and 999,999,999,999,999)</comment>
  </data>
  <data name="UnitName_Day" xml:space="preserve">
    <value>ದಿನಗಳು</value>
    <comment>A measurement unit for time. (Please choose the most appropriate plural form to fit any number between 0 and 999,999,999,999,999)</comment>
  </data>
  <data name="UnitName_DegreesCelsius" xml:space="preserve">
    <value>ಸೆಲ್ಸಿಯಸ್</value>
    <comment>An option in the unit converter to select degrees Celsius</comment>
  </data>
  <data name="UnitName_DegreesFahrenheit" xml:space="preserve">
    <value>ಫ್ಯಾರೆನ್‍ಹೀಟ್</value>
    <comment>An option in the unit converter to select degrees Fahrenheit</comment>
  </data>
  <data name="UnitName_Electron-Volt" xml:space="preserve">
    <value>ಎಲೆಕ್ಟ್ರಾನ್ ವೋಲ್ಟ್‌ಗಳು</value>
    <comment>A measurement unit for energy. (Please choose the most appropriate plural form to fit any number between 0 and 999,999,999,999,999)</comment>
  </data>
  <data name="UnitName_Foot" xml:space="preserve">
    <value>ಅಡಿ</value>
    <comment>A measurement unit for length. (Please choose the most appropriate plural form to fit any number between 0 and 999,999,999,999,999)</comment>
  </data>
  <data name="UnitName_FeetPerSecond" xml:space="preserve">
    <value>ಪ್ರತಿ ಸೆಕೆಂಡಿಗೆ ಅಡಿ</value>
    <comment>A measurement unit for speed. (Please choose the most appropriate plural form to fit any number between 0 and 999,999,999,999,999)</comment>
  </data>
  <data name="UnitName_Foot-Pound" xml:space="preserve">
    <value>ಫೂಟ್-ಪೌಂಡ್‍ಗಳು</value>
    <comment>A measurement unit for energy. (Please choose the most appropriate plural form to fit any number between 0 and 999,999,999,999,999)</comment>
  </data>
  <data name="UnitName_Foot-PoundPerMinute" xml:space="preserve">
    <value>ಫೂಟ್-ಪೌಂಡ್‍ಗಳು/ನಿಮಿಷಕ್ಕೆ</value>
    <comment>A measurement unit for power. (Please choose the most appropriate plural form to fit any number between 0 and 999,999,999,999,999)</comment>
  </data>
  <data name="UnitName_Gigabit" xml:space="preserve">
    <value>ಗಿಗಾಬಿಟ್ಸ್</value>
    <comment>A measurement unit for data. (Please choose the most appropriate plural form to fit any number between 0 and 999,999,999,999,999)</comment>
  </data>
  <data name="UnitName_Gigabyte" xml:space="preserve">
    <value>ಗಿಗಾಬೈಟ್ಸ್</value>
    <comment>A measurement unit for data. (Please choose the most appropriate plural form to fit any number between 0 and 999,999,999,999,999)</comment>
  </data>
  <data name="UnitName_Hectare" xml:space="preserve">
    <value>ಹೆಕ್ಟೇರ್‌‍ಗಳು</value>
    <comment>A measurement unit for area. (Please choose the most appropriate plural form to fit any number between 0 and 999,999,999,999,999)</comment>
  </data>
  <data name="UnitName_Horsepower" xml:space="preserve">
    <value>ಹಾರ್ಸ್ ಪವರ್ (US)</value>
    <comment>A measurement unit for power</comment>
  </data>
  <data name="UnitName_Hour" xml:space="preserve">
    <value>ಗಂಟೆಗಳು</value>
    <comment>A measurement unit for time. (Please choose the most appropriate plural form to fit any number between 0 and 999,999,999,999,999)</comment>
  </data>
  <data name="UnitName_Inch" xml:space="preserve">
    <value>ಇಂಚುಗಳು</value>
    <comment>A measurement unit for length. (Please choose the most appropriate plural form to fit any number between 0 and 999,999,999,999,999)</comment>
  </data>
  <data name="UnitName_Joule" xml:space="preserve">
    <value>ಜೌಲ್‍ಗಳು</value>
    <comment>A measurement unit for energy. (Please choose the most appropriate plural form to fit any number between 0 and 999,999,999,999,999)</comment>
  </data>
  <data name="UnitName_Kilowatthour" xml:space="preserve">
    <value>ಕಿಲೋವ್ಯಾಟ್-ಸಮಯ</value>
    <comment>A measurement unit for electricity consumption. (Please choose the most appropriate plural form to fit any number between 0 and 999,999,999,999,999)</comment>
  </data>
  <data name="UnitName_Kelvin" xml:space="preserve">
    <value>ಕೆಲ್ವಿನ್</value>
    <comment>An option in the unit converter to select the temperature system "Kelvin" (eg. 0 degrees Celsius = 273 Kelvin). At least in English, Kelvin does not use "degrees". A measurement is just stated as "273 Kelvin".</comment>
  </data>
  <data name="UnitName_Kilobit" xml:space="preserve">
    <value>ಕಿಲೋಬಿಟ್ಸ್</value>
    <comment>A measurement unit for data. (Please choose the most appropriate plural form to fit any number between 0 and 999,999,999,999,999)</comment>
  </data>
  <data name="UnitName_Kilobyte" xml:space="preserve">
    <value>ಕಿಲೋಬೈಟ್ಸ್</value>
    <comment>A measurement unit for data. (Please choose the most appropriate plural form to fit any number between 0 and 999,999,999,999,999)</comment>
  </data>
  <data name="UnitName_Kilocalorie" xml:space="preserve">
    <value>ಆಹಾರ ಕ್ಯಾಲೋರಿಗಳು</value>
    <comment>A measurement unit for energy. The scientific name is kilocalorie, but this is what is commonly referred to as a "calorie" or "large calorie" when talking about food. Please use the everyday-use word for food energy calories if there is one. (Please choose the most appropriate plural form to fit any number between 0 and 999,999,999,999,999)</comment>
  </data>
  <data name="UnitName_Kilojoule" xml:space="preserve">
    <value>ಕಿಲೋಜೌಲ್‍ಗಳು</value>
    <comment>A measurement unit for energy. (Please choose the most appropriate plural form to fit any number between 0 and 999,999,999,999,999)</comment>
  </data>
  <data name="UnitName_Kilometer" xml:space="preserve">
    <value>ಕಿಲೋಮೀಟರ್‌‍ಗಳು</value>
    <comment>A measurement unit for length. (Please choose the most appropriate plural form to fit any number between 0 and 999,999,999,999,999)</comment>
  </data>
  <data name="UnitName_KilometersPerHour" xml:space="preserve">
    <value>ಪ್ರತಿ ಗಂಟೆಗೆ ಕಿಲೋಮೀಟರ್‌‍ಗಳು</value>
    <comment>A measurement unit for speed. (Please choose the most appropriate plural form to fit any number between 0 and 999,999,999,999,999)</comment>
  </data>
  <data name="UnitName_Kilowatt" xml:space="preserve">
    <value>ಕಿಲೋವ್ಯಾಟ್ಸ್</value>
    <comment>A measurement unit for power. (Please choose the most appropriate plural form to fit any number between 0 and 999,999,999,999,999)</comment>
  </data>
  <data name="UnitName_Knot" xml:space="preserve">
    <value>ನಾಟ್ಸ್</value>
    <comment>A nautical/aerial measurement unit for speed. (Please choose the most appropriate plural form to fit any number between 0 and 999,999,999,999,999)</comment>
  </data>
  <data name="UnitName_Mach" xml:space="preserve">
    <value>ಮ್ಯಾಕ್</value>
    <comment>A measurement of speed (Mach is the speed of sound, Mach 2 is 2 times the speed of sound)</comment>
  </data>
  <data name="UnitName_Megabit" xml:space="preserve">
    <value>ಮೆಗಾಬಿಟ್ಸ್</value>
    <comment>A measurement unit for data. (Please choose the most appropriate plural form to fit any number between 0 and 999,999,999,999,999)</comment>
  </data>
  <data name="UnitName_Megabyte" xml:space="preserve">
    <value>ಮೆಗಾಬೈಟ್ಸ್</value>
    <comment>A measurement unit for data. (Please choose the most appropriate plural form to fit any number between 0 and 999,999,999,999,999)</comment>
  </data>
  <data name="UnitName_Meter" xml:space="preserve">
    <value>ಮೀಟರ್‌ಗಳು</value>
    <comment>A measurement unit for length. (Please choose the most appropriate plural form to fit any number between 0 and 999,999,999,999,999)</comment>
  </data>
  <data name="UnitName_MetersPerSecond" xml:space="preserve">
    <value>ಪ್ರತಿ ಸೆಕೆಂಡಿಗೆ ಮೀಟರ್‌‍ಗಳು</value>
    <comment>A measurement unit for speed. (Please choose the most appropriate plural form to fit any number between 0 and 999,999,999,999,999)</comment>
  </data>
  <data name="UnitName_Micron" xml:space="preserve">
    <value>ಮೈಕ್ರಾನ್‍ಗಳು</value>
    <comment>A measurement unit for length. (Please choose the most appropriate plural form to fit any number between 0 and 999,999,999,999,999)</comment>
  </data>
  <data name="UnitName_Microsecond" xml:space="preserve">
    <value>ಮೈಕ್ರೋಸೆಕೆಂಡುಗಳು</value>
    <comment>A measurement unit for time. (Please choose the most appropriate plural form to fit any number between 0 and 999,999,999,999,999)</comment>
  </data>
  <data name="UnitName_Mile" xml:space="preserve">
    <value>ಮೈಲುಗಳು</value>
    <comment>A measurement unit for length. (Please choose the most appropriate plural form to fit any number between 0 and 999,999,999,999,999)</comment>
  </data>
  <data name="UnitName_MilesPerHour" xml:space="preserve">
    <value>ಪ್ರತಿ ಗಂಟೆಗೆ ಮೈಲುಗಳು</value>
    <comment>A measurement unit for speed. (Please choose the most appropriate plural form to fit any number between 0 and 999,999,999,999,999)</comment>
  </data>
  <data name="UnitName_Millimeter" xml:space="preserve">
    <value>ಮಿಲಿಮೀಟರ್‌‍ಗಳು</value>
    <comment>A measurement unit for length. (Please choose the most appropriate plural form to fit any number between 0 and 999,999,999,999,999)</comment>
  </data>
  <data name="UnitName_Millisecond" xml:space="preserve">
    <value>ಮಿಲಿಸೆಕೆಂಡುಗಳು</value>
    <comment>A measurement unit for time. (Please choose the most appropriate plural form to fit any number between 0 and 999,999,999,999,999)</comment>
  </data>
  <data name="UnitName_Minute" xml:space="preserve">
    <value>ನಿಮಿಷಗಳು</value>
    <comment>A measurement unit for time. (Please choose the most appropriate plural form to fit any number between 0 and 999,999,999,999,999)</comment>
  </data>
  <data name="UnitName_Nibble" xml:space="preserve">
    <value>ನಿಬ್ಬಲ್</value>
    <comment>A measurement unit for data. (Please choose the most appropriate plural form to fit any number between 0 and 999,999,999,999,999)</comment>
  </data>
  <data name="UnitName_Nanometer" xml:space="preserve">
    <value>ನ್ಯಾನೋಮೀಟರ್‌‍ಗಳು</value>
    <comment>A measurement unit for length. (Please choose the most appropriate plural form to fit any number between 0 and 999,999,999,999,999)</comment>
  </data>
  <data name="UnitName_Angstrom" xml:space="preserve">
    <value>ಆಂಗ್‌ಸ್ಟ್ರೋಮ್ಸ್</value>
    <comment>A measurement unit for length. (Please choose the most appropriate plural form to fit any number between 0 and 999,999,999,999,999)</comment>
  </data>
  <data name="UnitName_NauticalMile" xml:space="preserve">
    <value>ನಾಟಿಕಲ್ ಮೈಲುಗಳು</value>
    <comment>A measurement unit for length. (Please choose the most appropriate plural form to fit any number between 0 and 999,999,999,999,999)</comment>
  </data>
  <data name="UnitName_Petabit" xml:space="preserve">
    <value>ಪೆಟಾಬಿಟ್ಸ್</value>
    <comment>A measurement unit for data. (Please choose the most appropriate plural form to fit any number between 0 and 999,999,999,999,999)</comment>
  </data>
  <data name="UnitName_Petabyte" xml:space="preserve">
    <value>ಪೆಟಾಬೈಟ್ಸ್</value>
    <comment>A measurement unit for data. (Please choose the most appropriate plural form to fit any number between 0 and 999,999,999,999,999)</comment>
  </data>
  <data name="UnitName_Second" xml:space="preserve">
    <value>ಸೆಕೆಂಡ್‍ಗಳು</value>
    <comment>A measurement unit for time. (Please choose the most appropriate plural form to fit any number between 0 and 999,999,999,999,999)</comment>
  </data>
  <data name="UnitName_SquareCentimeter" xml:space="preserve">
    <value>ಚದರ ಸೆಂಟಿಮೀಟರುಗಳು</value>
    <comment>A measurement unit for area. (Please choose the most appropriate plural form to fit any number between 0 and 999,999,999,999,999)</comment>
  </data>
  <data name="UnitName_SquareFoot" xml:space="preserve">
    <value>ಚದರ ಅಡಿ</value>
    <comment>A measurement unit for area. (Please choose the most appropriate plural form to fit any number between 0 and 999,999,999,999,999)</comment>
  </data>
  <data name="UnitName_SquareInch" xml:space="preserve">
    <value>ಚದರ ಇಂಚುಗಳು</value>
    <comment>A measurement unit for area. (Please choose the most appropriate plural form to fit any number between 0 and 999,999,999,999,999)</comment>
  </data>
  <data name="UnitName_SquareKilometer" xml:space="preserve">
    <value>ಚದರ ಕಿಲೋಮೀಟರುಗಳು</value>
    <comment>A measurement unit for area. (Please choose the most appropriate plural form to fit any number between 0 and 999,999,999,999,999)</comment>
  </data>
  <data name="UnitName_SquareMeter" xml:space="preserve">
    <value>ಚದರ ಮೀಟರುಗಳು</value>
    <comment>A measurement unit for area. (Please choose the most appropriate plural form to fit any number between 0 and 999,999,999,999,999)</comment>
  </data>
  <data name="UnitName_SquareMile" xml:space="preserve">
    <value>ಚದರ ಮೈಲುಗಳು</value>
    <comment>A measurement unit for area. (Please choose the most appropriate plural form to fit any number between 0 and 999,999,999,999,999)</comment>
  </data>
  <data name="UnitName_SquareMillimeter" xml:space="preserve">
    <value>ಚದರ ಮಿಲಿಮೀಟರುಗಳು</value>
    <comment>A measurement unit for area. (Please choose the most appropriate plural form to fit any number between 0 and 999,999,999,999,999)</comment>
  </data>
  <data name="UnitName_SquareYard" xml:space="preserve">
    <value>ಚದರ ಯಾರ್ಡುಗಳು</value>
    <comment>A measurement unit for area. (Please choose the most appropriate plural form to fit any number between 0 and 999,999,999,999,999)</comment>
  </data>
  <data name="UnitName_Terabit" xml:space="preserve">
    <value>ಟೆರಾಬಿಟ್ಸ್</value>
    <comment>A measurement unit for data. (Please choose the most appropriate plural form to fit any number between 0 and 999,999,999,999,999)</comment>
  </data>
  <data name="UnitName_Terabyte" xml:space="preserve">
    <value>ಟೆರಾಬೈಟ್ಸ್</value>
    <comment>A measurement unit for data. (Please choose the most appropriate plural form to fit any number between 0 and 999,999,999,999,999)</comment>
  </data>
  <data name="UnitName_Watt" xml:space="preserve">
    <value>ವ್ಯಾಟ್‍ಗಳು</value>
    <comment>A measurement unit for power. (Please choose the most appropriate plural form to fit any number between 0 and 999,999,999,999,999)</comment>
  </data>
  <data name="UnitName_Week" xml:space="preserve">
    <value>ವಾರಗಳು</value>
    <comment>A measurement unit for time. (Please choose the most appropriate plural form to fit any number between 0 and 999,999,999,999,999)</comment>
  </data>
  <data name="UnitName_Yard" xml:space="preserve">
    <value>ಯಾರ್ಡುಗಳು</value>
    <comment>A measurement unit for length. (Please choose the most appropriate plural form to fit any number between 0 and 999,999,999,999,999)</comment>
  </data>
  <data name="UnitName_Year" xml:space="preserve">
    <value>ವರ್ಷಗಳು</value>
    <comment>A measurement unit for time. (Please choose the most appropriate plural form to fit any number between 0 and 999,999,999,999,999)</comment>
  </data>
  <data name="UnitAbbreviation_Carat" xml:space="preserve">
    <value>CD</value>
    <comment>An abbreviation for a measurement unit of weight</comment>
  </data>
  <data name="UnitAbbreviation_Degree" xml:space="preserve">
    <value>deg</value>
    <comment>An abbreviation for a measurement unit of Angle</comment>
  </data>
  <data name="UnitAbbreviation_Radian" xml:space="preserve">
    <value>rad</value>
    <comment>An abbreviation for a measurement unit of Angle</comment>
  </data>
  <data name="UnitAbbreviation_Gradian" xml:space="preserve">
    <value>grad</value>
    <comment>An abbreviation for a measurement unit of Angle</comment>
  </data>
  <data name="UnitAbbreviation_Atmosphere" xml:space="preserve">
    <value>atm</value>
    <comment>An abbreviation for a measurement unit of Pressure</comment>
  </data>
  <data name="UnitAbbreviation_Bar" xml:space="preserve">
    <value>ba</value>
    <comment>An abbreviation for a measurement unit of Pressure</comment>
  </data>
  <data name="UnitAbbreviation_KiloPascal" xml:space="preserve">
    <value>kPa</value>
    <comment>An abbreviation for a measurement unit of Pressure</comment>
  </data>
  <data name="UnitAbbreviation_MillimeterOfMercury" xml:space="preserve">
    <value>mmHg</value>
    <comment>An abbreviation for a measurement unit of Pressure</comment>
  </data>
  <data name="UnitAbbreviation_Pascal" xml:space="preserve">
    <value>Pa</value>
    <comment>An abbreviation for a measurement unit of Pressure</comment>
  </data>
  <data name="UnitAbbreviation_PSI" xml:space="preserve">
    <value>psi</value>
    <comment>An abbreviation for a measurement unit of Pressure</comment>
  </data>
  <data name="UnitAbbreviation_Centigram" xml:space="preserve">
    <value>cg</value>
    <comment>An abbreviation for a measurement unit of weight</comment>
  </data>
  <data name="UnitAbbreviation_Decagram" xml:space="preserve">
    <value>ಡ್ಯಾಗ್</value>
    <comment>An abbreviation for a measurement unit of weight</comment>
  </data>
  <data name="UnitAbbreviation_Decigram" xml:space="preserve">
    <value>dg</value>
    <comment>An abbreviation for a measurement unit of weight</comment>
  </data>
  <data name="UnitAbbreviation_Gram" xml:space="preserve">
    <value>ಜಿ</value>
    <comment>An abbreviation for a measurement unit of weight</comment>
  </data>
  <data name="UnitAbbreviation_Hectogram" xml:space="preserve">
    <value>hg</value>
    <comment>An abbreviation for a measurement unit of weight</comment>
  </data>
  <data name="UnitAbbreviation_Kilogram" xml:space="preserve">
    <value>kg</value>
    <comment>An abbreviation for a measurement unit of weight</comment>
  </data>
  <data name="UnitAbbreviation_LongTon" xml:space="preserve">
    <value>ಟನ್ (UK)</value>
    <comment>An abbreviation for a measurement unit of weight</comment>
  </data>
  <data name="UnitAbbreviation_Milligram" xml:space="preserve">
    <value>ಮಿಗ್ರಾಂ</value>
    <comment>An abbreviation for a measurement unit of weight</comment>
  </data>
  <data name="UnitAbbreviation_Ounce" xml:space="preserve">
    <value>ಔನ್ಸ್</value>
    <comment>An abbreviation for a measurement unit of weight</comment>
  </data>
  <data name="UnitAbbreviation_Pound" xml:space="preserve">
    <value>lb</value>
    <comment>An abbreviation for a measurement unit of weight</comment>
  </data>
  <data name="UnitAbbreviation_ShortTon" xml:space="preserve">
    <value>ಟನ್ (US)</value>
    <comment>An abbreviation for a measurement unit of weight</comment>
  </data>
  <data name="UnitAbbreviation_Stone" xml:space="preserve">
    <value>st</value>
    <comment>An abbreviation for a measurement unit of weight</comment>
  </data>
  <data name="UnitAbbreviation_Tonne" xml:space="preserve">
    <value>t</value>
    <comment>An abbreviation for a measurement unit of weight</comment>
  </data>
  <data name="UnitName_Carat" xml:space="preserve">
    <value>ಕ್ಯಾರಟ್‍ಗಳು</value>
    <comment>A measurement unit for weight. (Please choose the most appropriate plural form to fit any number between 0 and 999,999,999,999,999)</comment>
  </data>
  <data name="UnitName_Degree" xml:space="preserve">
    <value>ಡಿಗ್ರಿಗಳು</value>
    <comment>A measurement unit for Angle.</comment>
  </data>
  <data name="UnitName_Radian" xml:space="preserve">
    <value>ರೇಡಿಯನ್‍ಗಳು</value>
    <comment>A measurement unit for Angle.</comment>
  </data>
  <data name="UnitName_Gradian" xml:space="preserve">
    <value>ಗ್ರೇಡಿಯನ್‌ಗಳು</value>
    <comment>A measurement unit for Angle.</comment>
  </data>
  <data name="UnitName_Atmosphere" xml:space="preserve">
    <value>ವಾತಾವರಣಗಳು</value>
    <comment>A measurement unit for Pressure.</comment>
  </data>
  <data name="UnitName_Bar" xml:space="preserve">
    <value>ಪಟ್ಟಿಗಳು</value>
    <comment>A measurement unit for Pressure.</comment>
  </data>
  <data name="UnitName_KiloPascal" xml:space="preserve">
    <value>ಕಿಲೋಪ್ಯಾಸ್ಕಲ್‍ಗಳು</value>
    <comment>A measurement unit for Pressure.</comment>
  </data>
  <data name="UnitName_MillimeterOfMercury" xml:space="preserve">
    <value>ಪಾದರಸದ ಮಿಲಿಮೀಟರ್‌‍ಗಳು</value>
    <comment>A measurement unit for Pressure.</comment>
  </data>
  <data name="UnitName_Pascal" xml:space="preserve">
    <value>ಪ್ಯಾಸ್ಕಲ್</value>
    <comment>A measurement unit for Pressure.</comment>
  </data>
  <data name="UnitName_PSI" xml:space="preserve">
    <value>ಪ್ರತಿ ಚದರ ಇಂಚಿಗೆ ಪೌಂಡ್‍ಗಳು</value>
    <comment>A measurement unit for Pressure.</comment>
  </data>
  <data name="UnitName_Centigram" xml:space="preserve">
    <value>ಸೆಂಟಿಗ್ರಾಂಗಳು</value>
    <comment>A measurement unit for weight. (Please choose the most appropriate plural form to fit any number between 0 and 999,999,999,999,999)</comment>
  </data>
  <data name="UnitName_Decagram" xml:space="preserve">
    <value>ಡೆಕಾಗ್ರಾಂಗಳು</value>
    <comment>A measurement unit for weight. Note: Dekagram is spelled "decagram" everywhere except where US English is used. (EN-US dekagram, elsewhere decagram). (Please choose the most appropriate plural form to fit any number between 0 and 999,999,999,999,999)</comment>
  </data>
  <data name="UnitName_Decigram" xml:space="preserve">
    <value>ಡೆಸಿಗ್ರಾಂಗಳು</value>
    <comment>A measurement unit for weight. (Please choose the most appropriate plural form to fit any number between 0 and 999,999,999,999,999)</comment>
  </data>
  <data name="UnitName_Gram" xml:space="preserve">
    <value>ಗ್ರಾಂಗಳು</value>
    <comment>A measurement unit for weight. (Please choose the most appropriate plural form to fit any number between 0 and 999,999,999,999,999)</comment>
  </data>
  <data name="UnitName_Hectogram" xml:space="preserve">
    <value>ಹೆಕ್ಟೋಗ್ರಾಂಗಳು</value>
    <comment>A measurement unit for weight. (Please choose the most appropriate plural form to fit any number between 0 and 999,999,999,999,999)</comment>
  </data>
  <data name="UnitName_Kilogram" xml:space="preserve">
    <value>ಕಿಲೋಗ್ರಾಂಗಳು</value>
    <comment>A measurement unit for weight. (Please choose the most appropriate plural form to fit any number between 0 and 999,999,999,999,999)</comment>
  </data>
  <data name="UnitName_LongTon" xml:space="preserve">
    <value>ಲಾಂಗ್ ಟನ್‍ಗಳು (UK)</value>
    <comment>A measurement unit for weight. This is the UK version of ton. (Please choose the most appropriate plural form to fit any number between 0 and 999,999,999,999,999)</comment>
  </data>
  <data name="UnitName_Milligram" xml:space="preserve">
    <value>ಮಿಲಿಗ್ರಾಂಗಳು</value>
    <comment>A measurement unit for weight. (Please choose the most appropriate plural form to fit any number between 0 and 999,999,999,999,999)</comment>
  </data>
  <data name="UnitName_Ounce" xml:space="preserve">
    <value>ಔನ್ಸ್‌ಗಳು</value>
    <comment>A measurement unit for weight. (Please choose the most appropriate plural form to fit any number between 0 and 999,999,999,999,999)</comment>
  </data>
  <data name="UnitName_Pound" xml:space="preserve">
    <value>ಪೌಂಡ್ಸ್</value>
    <comment>A measurement unit for weight. (Please choose the most appropriate plural form to fit any number between 0 and 999,999,999,999,999)</comment>
  </data>
  <data name="UnitName_ShortTon" xml:space="preserve">
    <value>ಶಾರ್ಟ್ ಟನ್‍ಗಳು (US)</value>
    <comment>A measurement unit for weight. This is the US version of ton. (Please choose the most appropriate plural form to fit any number between 0 and 999,999,999,999,999)</comment>
  </data>
  <data name="UnitName_Stone" xml:space="preserve">
    <value>ಸ್ಟೋನ್</value>
    <comment>A measurement unit for weight. Equal to 14 pounds. Note that this is the plural form of the word in English (eg. "This man weighs 11 stone."). (Please choose the most appropriate plural form to fit any number between 0 and 999,999,999,999,999)</comment>
  </data>
  <data name="UnitName_Tonne" xml:space="preserve">
    <value>ಮೆಟ್ರಿಕ್ ಟನ್‍ಗಳು</value>
    <comment>A measurement unit for weight. This is the metric version of tonne. (Please choose the most appropriate plural form to fit any number between 0 and 999,999,999,999,999)</comment>
  </data>
  <data name="UnitName_CD" xml:space="preserve">
    <value>CD ಗಳು</value>
    <comment>A compact disc, used as a comparison measurement unit for data storage. (Please choose the most appropriate plural form to fit any number between 0 and 999,999,999,999,999)</comment>
  </data>
  <data name="UnitAbbreviation_CD" xml:space="preserve">
    <value>CD ಗಳು</value>
    <comment>A compact disc, used as a comparison measurement unit for data storage. (Please choose the most appropriate plural form to fit any number between 0 and 999,999,999,999,999)</comment>
  </data>
  <data name="UnitName_SoccerField" xml:space="preserve">
    <value>ಸಾಕರ್ ಅಂಗಣಗಳು</value>
    <comment>A professional-sized soccer field, used as a comparison measurement unit for area. (Please choose the most appropriate plural form to fit any number between 0 and 999,999,999,999,999)</comment>
  </data>
  <data name="UnitAbbreviation_SoccerField" xml:space="preserve">
    <value>ಸಾಕರ್ ಅಂಗಣಗಳು</value>
    <comment>A professional-sized soccer field, used as a comparison measurement unit for area. (Please choose the most appropriate plural form to fit any number between 0 and 999,999,999,999,999)</comment>
  </data>
  <data name="UnitName_FloppyDisk" xml:space="preserve">
    <value>ಫ್ಲಾಪಿ ಡಿಸ್ಕ್‌ಗಳು</value>
    <comment>A 1.44 MB floppy disk, used as a comparison measurement unit for data storage. (Please choose the most appropriate plural form to fit any number between 0 and 999,999,999,999,999)</comment>
  </data>
  <data name="UnitAbbreviation_FloppyDisk" xml:space="preserve">
    <value>ಫ್ಲಾಪಿ ಡಿಸ್ಕ್‌ಗಳು</value>
    <comment>A 1.44 MB floppy disk, used as a comparison measurement unit for data storage. (Please choose the most appropriate plural form to fit any number between 0 and 999,999,999,999,999)</comment>
  </data>
  <data name="UnitName_DVD" xml:space="preserve">
    <value>DVD ಗಳು</value>
    <comment>A DVD, used as a comparison measurement unit for data storage. (Please choose the most appropriate plural form to fit any number between 0 and 999,999,999,999,999)</comment>
  </data>
  <data name="UnitAbbreviation_DVD" xml:space="preserve">
    <value>DVD ಗಳು</value>
    <comment>A DVD, used as a comparison measurement unit for data storage. (Please choose the most appropriate plural form to fit any number between 0 and 999,999,999,999,999)</comment>
  </data>
  <data name="UnitName_Battery" xml:space="preserve">
    <value>ಬ್ಯಾಟರಿಗಳು</value>
    <comment>AA-cell battery, used as a comparison measurement unit for data storage. (Please choose the most appropriate plural form to fit any number between 0 and 999,999,999,999,999)</comment>
  </data>
  <data name="UnitAbbreviation_Battery" xml:space="preserve">
    <value>ಬ್ಯಾಟರಿಗಳು</value>
    <comment>AA-cell battery, used as a comparison measurement unit for data storage. (Please choose the most appropriate plural form to fit any number between 0 and 999,999,999,999,999)</comment>
  </data>
  <data name="UnitName_Paperclip" xml:space="preserve">
    <value>ಪೇಪರ್‌ಕ್ಲಿಪ್‌ಗಳು</value>
    <comment>A standard paperclip, used as a comparison measurement unit for length. (Please choose the most appropriate plural form to fit any number between 0 and 999,999,999,999,999)</comment>
  </data>
  <data name="UnitAbbreviation_Paperclip" xml:space="preserve">
    <value>ಪೇಪರ್‌ಕ್ಲಿಪ್‌ಗಳು</value>
    <comment>A standard paperclip, used as a comparison measurement unit for length. (Please choose the most appropriate plural form to fit any number between 0 and 999,999,999,999,999)</comment>
  </data>
  <data name="UnitName_JumboJet" xml:space="preserve">
    <value>ಜಂಬೋ ಜೆಟ್‍ಗಳು</value>
    <comment>A jumbo jet (eg. Boeing 747), used as a comparison measurement unit for length. (Please choose the most appropriate plural form to fit any number between 0 and 999,999,999,999,999)</comment>
  </data>
  <data name="UnitAbbreviation_JumboJet" xml:space="preserve">
    <value>ಜಂಬೋ ಜೆಟ್‍ಗಳು</value>
    <comment>A jumbo jet (eg. Boeing 747), used as a comparison measurement unit for length. (Please choose the most appropriate plural form to fit any number between 0 and 999,999,999,999,999)</comment>
  </data>
  <data name="UnitName_LightBulb" xml:space="preserve">
    <value>ಲೈಟ್ ಬಲ್ಬ್‌ಗಳು</value>
    <comment>A light bulb, used as a comparison measurement unit for power (60 watts). (Please choose the most appropriate plural form to fit any number between 0 and 999,999,999,999,999)</comment>
  </data>
  <data name="UnitAbbreviation_LightBulb" xml:space="preserve">
    <value>ಲೈಟ್ ಬಲ್ಬ್‌ಗಳು</value>
    <comment>A light bulb, used as a comparison measurement unit for power (60 watts). (Please choose the most appropriate plural form to fit any number between 0 and 999,999,999,999,999)</comment>
  </data>
  <data name="UnitName_Horse" xml:space="preserve">
    <value>ಹಾರ್ಸ್‍ಗಳು</value>
    <comment>A horse, used as a comparison measurement unit for power (~1 horsepower) or speed. (Please choose the most appropriate plural form to fit any number between 0 and 999,999,999,999,999)</comment>
  </data>
  <data name="UnitAbbreviation_Horse" xml:space="preserve">
    <value>ಹಾರ್ಸ್‍ಗಳು</value>
    <comment>A horse, used as a comparison measurement unit for power (~1 horsepower) or speed. (Please choose the most appropriate plural form to fit any number between 0 and 999,999,999,999,999)</comment>
  </data>
  <data name="UnitName_Bathtub" xml:space="preserve">
    <value>ಬಾತ್‍ಟಬ್‍ಗಳು</value>
    <comment>A bathtub full of water, used as a comparison measurement unit for volume. (Please choose the most appropriate plural form to fit any number between 0 and 999,999,999,999,999)</comment>
  </data>
  <data name="UnitAbbreviation_Bathtub" xml:space="preserve">
    <value>ಬಾತ್‍ಟಬ್‍ಗಳು</value>
    <comment>A bathtub full of water, used as a comparison measurement unit for volume. (Please choose the most appropriate plural form to fit any number between 0 and 999,999,999,999,999)</comment>
  </data>
  <data name="UnitName_Snowflake" xml:space="preserve">
    <value>ಸ್ನೋಫ್ಲೇಕ್‍ಗಳು</value>
    <comment>A snowflake, used as a comparison measurement unit for weight. (Please choose the most appropriate plural form to fit any number between 0 and 999,999,999,999,999)</comment>
  </data>
  <data name="UnitAbbreviation_Snowflake" xml:space="preserve">
    <value>ಸ್ನೋಫ್ಲೇಕ್‍ಗಳು</value>
    <comment>A snowflake, used as a comparison measurement unit for weight. (Please choose the most appropriate plural form to fit any number between 0 and 999,999,999,999,999)</comment>
  </data>
  <data name="UnitName_Elephant" xml:space="preserve">
    <value>ಆನೆಗಳು</value>
    <comment>An elephant, used as a comparison measurement unit for weight. (Please choose the most appropriate plural form to fit any number between 0 and 999,999,999,999,999)</comment>
  </data>
  <data name="UnitAbbreviation_Elephant" xml:space="preserve">
    <value>ಆನೆಗಳು</value>
    <comment>An elephant, used as a comparison measurement unit for weight. (Please choose the most appropriate plural form to fit any number between 0 and 999,999,999,999,999)</comment>
  </data>
  <data name="UnitName_Turtle" xml:space="preserve">
    <value>ಆಮೆಗಳು</value>
    <comment>A turtle, used as a comparison measurement unit for speed. (Please choose the most appropriate plural form to fit any number between 0 and 999,999,999,999,999)</comment>
  </data>
  <data name="UnitAbbreviation_Turtle" xml:space="preserve">
    <value>ಆಮೆಗಳು</value>
    <comment>A turtle, used as a comparison measurement unit for speed. (Please choose the most appropriate plural form to fit any number between 0 and 999,999,999,999,999)</comment>
  </data>
  <data name="UnitName_Jet" xml:space="preserve">
    <value>ಜೆಟ್‍‍ಗಳು</value>
    <comment>A jet plane, used as a comparison measurement unit for speed. (Please choose the most appropriate plural form to fit any number between 0 and 999,999,999,999,999)</comment>
  </data>
  <data name="UnitAbbreviation_Jet" xml:space="preserve">
    <value>ಜೆಟ್‍‍ಗಳು</value>
    <comment>A jet plane, used as a comparison measurement unit for speed. (Please choose the most appropriate plural form to fit any number between 0 and 999,999,999,999,999)</comment>
  </data>
  <data name="UnitName_Whale" xml:space="preserve">
    <value>ತಿಮಿಂಗಿಲಗಳು</value>
    <comment>A blue whale, used as a comparison measurement unit for weight. (Please choose the most appropriate plural form to fit any number between 0 and 999,999,999,999,999)</comment>
  </data>
  <data name="UnitAbbreviation_Whale" xml:space="preserve">
    <value>ತಿಮಿಂಗಿಲಗಳು</value>
    <comment>A blue whale, used as a comparison measurement unit for weight. (Please choose the most appropriate plural form to fit any number between 0 and 999,999,999,999,999)</comment>
  </data>
  <data name="UnitName_CoffeeCup" xml:space="preserve">
    <value>ಕಾಫಿ ಕಪ್‍ಗಳು</value>
    <comment>A coffee cup, used as a comparison measurement unit for volume. (Please choose the most appropriate plural form to fit any number between 0 and 999,999,999,999,999)</comment>
  </data>
  <data name="UnitAbbreviation_CoffeeCup" xml:space="preserve">
    <value>ಕಾಫಿ ಕಪ್‍ಗಳು</value>
    <comment>A coffee cup, used as a comparison measurement unit for volume. (Please choose the most appropriate plural form to fit any number between 0 and 999,999,999,999,999)</comment>
  </data>
  <data name="UnitName_SwimmingPool" xml:space="preserve">
    <value>ಈಜುಕೊಳಗಳು</value>
    <comment>An Olympic-sized swimming pool, used as a comparison measurement unit for volume. (Please choose the most appropriate plural form to fit any number between 0 and 999,999,999,999,999)</comment>
  </data>
  <data name="UnitAbbreviation_SwimmingPool" xml:space="preserve">
    <value>ಈಜುಕೊಳಗಳು</value>
    <comment>An Olympic-sized swimming pool, used as a comparison measurement unit for volume. (Please choose the most appropriate plural form to fit any number between 0 and 999,999,999,999,999)</comment>
  </data>
  <data name="UnitName_Hand" xml:space="preserve">
    <value>ಕೈಗಳು</value>
    <comment>A human hand, used as a comparison measurement unit for length or area. (Please choose the most appropriate plural form to fit any number between 0 and 999,999,999,999,999)</comment>
  </data>
  <data name="UnitAbbreviation_Hand" xml:space="preserve">
    <value>ಕೈಗಳು</value>
    <comment>A human hand, used as a comparison measurement unit for length or area. (Please choose the most appropriate plural form to fit any number between 0 and 999,999,999,999,999)</comment>
  </data>
  <data name="UnitName_Paper" xml:space="preserve">
    <value>ಕಾಗದದ ಹಾಳೆಗಳು</value>
    <comment>A sheet of 8.5 x 11 inch paper, used as a comparison measurement unit for area. (Please choose the most appropriate plural form to fit any number between 0 and 999,999,999,999,999)</comment>
  </data>
  <data name="UnitAbbreviation_Paper" xml:space="preserve">
    <value>ಕಾಗದದ ಹಾಳೆಗಳು</value>
    <comment>A sheet of 8.5 x 11 inch paper, used as a comparison measurement unit for area. (Please choose the most appropriate plural form to fit any number between 0 and 999,999,999,999,999)</comment>
  </data>
  <data name="UnitName_Castle" xml:space="preserve">
    <value>ಕೋಟೆಗಳು</value>
    <comment>A castle, used as a comparison measurement unit for area (floorspace). (Please choose the most appropriate plural form to fit any number between 0 and 999,999,999,999,999)</comment>
  </data>
  <data name="UnitAbbreviation_Castle" xml:space="preserve">
    <value>ಕೋಟೆಗಳು</value>
    <comment>A castle, used as a comparison measurement unit for area (floorspace). (Please choose the most appropriate plural form to fit any number between 0 and 999,999,999,999,999)</comment>
  </data>
  <data name="UnitName_Banana" xml:space="preserve">
    <value>ಬಾಳೆಹಣ್ಣುಗಳು</value>
    <comment>A banana, used as a comparison measurement unit for food energy. (Please choose the most appropriate plural form to fit any number between 0 and 999,999,999,999,999)</comment>
  </data>
  <data name="UnitAbbreviation_Banana" xml:space="preserve">
    <value>ಬಾಳೆಹಣ್ಣುಗಳು</value>
    <comment>A banana, used as a comparison measurement unit for food energy. (Please choose the most appropriate plural form to fit any number between 0 and 999,999,999,999,999)</comment>
  </data>
  <data name="UnitName_SliceOfCake" xml:space="preserve">
    <value>ಕೇಕ್ ತುಣುಕುಗಳು</value>
    <comment>A slice of chocolate cake, used as a comparison measurement unit for food energy. (Please choose the most appropriate plural form to fit any number between 0 and 999,999,999,999,999)</comment>
  </data>
  <data name="UnitAbbreviation_SliceOfCake" xml:space="preserve">
    <value>ಕೇಕ್ ತುಣುಕುಗಳು</value>
    <comment>A slice of chocolate cake, used as a comparison measurement unit for food energy. (Please choose the most appropriate plural form to fit any number between 0 and 999,999,999,999,999)</comment>
  </data>
  <data name="UnitName_TrainEngine" xml:space="preserve">
    <value>ರೈಲು ಇಂಜಿನ್‍ಗಳು</value>
    <comment>A train engine, used as a comparison measurement unit for power. (Please choose the most appropriate plural form to fit any number between 0 and 999,999,999,999,999)</comment>
  </data>
  <data name="UnitAbbreviation_TrainEngine" xml:space="preserve">
    <value>ರೈಲು ಇಂಜಿನ್‍ಗಳು</value>
    <comment>A train engine, used as a comparison measurement unit for power. (Please choose the most appropriate plural form to fit any number between 0 and 999,999,999,999,999)</comment>
  </data>
  <data name="UnitName_SoccerBall" xml:space="preserve">
    <value>ಸಾಕರ್ ಚೆಂಡುಗಳು</value>
    <comment>A soccer ball, used as a comparison measurement unit for weight. (Please choose the most appropriate plural form to fit any number between 0 and 999,999,999,999,999)</comment>
  </data>
  <data name="UnitAbbreviation_SoccerBall" xml:space="preserve">
    <value>ಸಾಕರ್ ಚೆಂಡುಗಳು</value>
    <comment>A soccer ball, used as a comparison measurement unit for weight. (Please choose the most appropriate plural form to fit any number between 0 and 999,999,999,999,999)</comment>
  </data>
  <data name="MemoryItemHelpText" xml:space="preserve">
    <value>ಸ್ಮರಣೆ ಐಟಂ</value>
    <comment>Help text used by accessibility tools to indicate that an item in the list of memory values is a memory item.</comment>
  </data>
  <data name="SupplementaryUnit_AutomationName" xml:space="preserve">
    <value>%1 %2</value>
    <comment>This string is what is read by Narrator, and other screen readers, for the supplementary value at the bottom of the converter view, %1 = the value of the supplementary unit (i.e. 0.5), %2 = the unit itself (i.e. inches, meters, etc)</comment>
  </data>
  <data name="AboutControlBackButton.[using:Windows.UI.Xaml.Automation]AutomationProperties.Name" xml:space="preserve">
    <value>ಹಿಂದೆ</value>
    <comment>Screen reader prompt for the About panel back button</comment>
  </data>
  <data name="AboutControlBackButton.[using:Windows.UI.Xaml.Controls]ToolTipService.ToolTip" xml:space="preserve">
    <value>ಹಿಂದೆ</value>
    <comment>Content of tooltip being displayed on AboutControlBackButton</comment>
  </data>
  <data name="AboutEULA.Text" xml:space="preserve">
    <value>Microsoft ಸಾಫ್ಟ್‌ವೇರ್ ಪರವಾನಗಿ ನಿಯಮಗಳು</value>
    <comment>Displayed on a link to the Microsoft Software License Terms on the About panel</comment>
  </data>
  <data name="PreviewTag.Text" xml:space="preserve">
    <value>ಮುನ್ನೋಟ</value>
    <comment>Label displayed next to upcoming features</comment>
  </data>
  <data name="AboutControlPrivacyStatement.Text" xml:space="preserve">
    <value>Microsoft ಗೌಪ್ಯತೆ ಹೇಳಿಕೆ</value>
    <comment>Displayed on a link to the Microsoft Privacy Statement on the About panel</comment>
  </data>
  <data name="AboutControlCopyright" xml:space="preserve">
    <value>© %1 Microsoft. ಎಲ್ಲ ಹಕ್ಕುಗಳನ್ನು ಕಾಯ್ದಿರಿಸಲಾಗಿದೆ.</value>
    <comment>{Locked="%1"}. Copyright statement, displayed on the About panel. %1 = the current year (4 digits)</comment>
  </data>
  <data name="AboutControlContribute" xml:space="preserve">
    <value>ನೀವು Windows ಕ್ಯಾಲ್ಕುಲೇಟರ್‌ಗೆ ಹೇಗೆ ಕೊಡುಗೆ ನೀಡಬಹುದು ಎಂದು ತಿಳಿಯಲು, %HL%GitHub%HL% ನಲ್ಲಿ Project ಅನ್ನು ಪರಿಶೀಲಿಸಿ.</value>
    <comment>{Locked="%HL%GitHub%HL%"}. GitHub link, Displayed on the About panel</comment>
  </data>
  <data name="AboutGroupTitle.Text" xml:space="preserve">
    <value>ಬಗ್ಗೆ</value>
    <comment>Subtitle of about message on Settings page</comment>
  </data>
  <data name="FeedbackButton.Content" xml:space="preserve">
    <value>ಪ್ರತಿಕ್ರಿಯೆ ಕಳುಹಿಸಿ</value>
    <comment>The text that shows in the dropdown navigation control to give the user the option to send feedback about the app and it launches Windows Feedback app</comment>
  </data>
  <data name="HistoryEmpty.Text" xml:space="preserve">
    <value>ಇನ್ನೂ ಯಾವುದೇ ಇತಿಹಾಸವಿಲ್ಲ.</value>
    <comment>The text that shows as the header for the history list</comment>
  </data>
  <data name="MemoryPaneEmpty.Text" xml:space="preserve">
    <value>ಸ್ಮರಣೆಯಲ್ಲಿ ಏನನ್ನೂ ಉಳಿಸಲಾಗಿಲ್ಲ.</value>
    <comment>The text that shows as the header for the memory list</comment>
  </data>
  <data name="MemoryFlyout.[using:Windows.UI.Xaml.Automation]AutomationProperties.Name" xml:space="preserve">
    <value>ಸ್ಮರಣೆ</value>
    <comment>Screen reader prompt for the negate button on the converter operator keypad</comment>
  </data>
  <data name="CannotPaste" xml:space="preserve">
    <value>ಈ ಅಭಿವ್ಯಕ್ತಿಯನ್ನು ಅಂಟಿಸಲಾಗುವುದಿಲ್ಲ</value>
    <comment>The paste operation cannot be performed, if the expression is invalid.</comment>
  </data>
  <data name="UnitName_Gibibits" xml:space="preserve">
    <value>ಜಿಬಿಬಿಟ್‍ಗಳು</value>
    <comment>A measurement unit for data. (Please choose the most appropriate plural form to fit any number between 0 and 999,999,999,999,999)</comment>
  </data>
  <data name="UnitName_Gibibytes" xml:space="preserve">
    <value>ಜಿಬಿಬೈಟ್‍ಗಳು</value>
    <comment>A measurement unit for data. (Please choose the most appropriate plural form to fit any number between 0 and 999,999,999,999,999)</comment>
  </data>
  <data name="UnitName_Kibibits" xml:space="preserve">
    <value>ಕಿಬಿಬೈಟ್‍‌ಗಳು</value>
    <comment>A measurement unit for data. (Please choose the most appropriate plural form to fit any number between 0 and 999,999,999,999,999)</comment>
  </data>
  <data name="UnitName_Kibibytes" xml:space="preserve">
    <value>ಕಿಬಿಬೈಟ್‍ಗಳು</value>
    <comment>A measurement unit for data. (Please choose the most appropriate plural form to fit any number between 0 and 999,999,999,999,999)</comment>
  </data>
  <data name="UnitName_Mebibits" xml:space="preserve">
    <value>ಮೆಬಿಬಿಟ್‍ಗಳು</value>
    <comment>A measurement unit for data. (Please choose the most appropriate plural form to fit any number between 0 and 999,999,999,999,999)</comment>
  </data>
  <data name="UnitName_Mebibytes" xml:space="preserve">
    <value>ಮೆಬಿಬೈಟ್‍ಗಳು</value>
    <comment>A measurement unit for data. (Please choose the most appropriate plural form to fit any number between 0 and 999,999,999,999,999)</comment>
  </data>
  <data name="UnitName_Pebibits" xml:space="preserve">
    <value>ಪೆಬಿಬಿಟ್‍ಗಳು</value>
    <comment>A measurement unit for data. (Please choose the most appropriate plural form to fit any number between 0 and 999,999,999,999,999)</comment>
  </data>
  <data name="UnitName_Pebibytes" xml:space="preserve">
    <value>ಪೆಬಿಬೈಟ್‍ಗಳು</value>
    <comment>A measurement unit for data. (Please choose the most appropriate plural form to fit any number between 0 and 999,999,999,999,999)</comment>
  </data>
  <data name="UnitName_Tebibits" xml:space="preserve">
    <value>ಟೆಬಿಬಿಟ್‍ಗಳು</value>
    <comment>A measurement unit for data. (Please choose the most appropriate plural form to fit any number between 0 and 999,999,999,999,999)</comment>
  </data>
  <data name="UnitName_Tebibytes" xml:space="preserve">
    <value>ಟೆಬಿಬೈಟ್‍ಗಳು</value>
    <comment>A measurement unit for data. (Please choose the most appropriate plural form to fit any number between 0 and 999,999,999,999,999)</comment>
  </data>
  <data name="UnitName_Exabits" xml:space="preserve">
    <value>ಎಕ್ಸಾಬಿಟ್‍ಗಳು</value>
    <comment>A measurement unit for data. (Please choose the most appropriate plural form to fit any number between 0 and 999,999,999,999,999)</comment>
  </data>
  <data name="UnitName_Exabytes" xml:space="preserve">
    <value>ಎಕ್ಸಾಬೈಟ್‍ಗಳು</value>
    <comment>A measurement unit for data. (Please choose the most appropriate plural form to fit any number between 0 and 999,999,999,999,999)</comment>
  </data>
  <data name="UnitName_Exbibits" xml:space="preserve">
    <value>ಎಕ್ಸ್‌ಬಿ‌ಬೈಟ್‌ಗಳು</value>
    <comment>A measurement unit for data. (Please choose the most appropriate plural form to fit any number between 0 and 999,999,999,999,999)</comment>
  </data>
  <data name="UnitName_Exbibytes" xml:space="preserve">
    <value>ಎಕ್ಸ್‌ಬಿ‌ಬೈಟ್‌ಗಳು</value>
    <comment>A measurement unit for data. (Please choose the most appropriate plural form to fit any number between 0 and 999,999,999,999,999)</comment>
  </data>
  <data name="UnitName_Zetabits" xml:space="preserve">
    <value>ಝೆಟಾಬಿಟ್‍ಗಳು</value>
    <comment>A measurement unit for data. (Please choose the most appropriate plural form to fit any number between 0 and 999,999,999,999,999)</comment>
  </data>
  <data name="UnitName_Zetabytes" xml:space="preserve">
    <value>ಝೆಟಾಬೈಟ್‍ಗಳು</value>
    <comment>A measurement unit for data. (Please choose the most appropriate plural form to fit any number between 0 and 999,999,999,999,999)</comment>
  </data>
  <data name="UnitName_Zebibits" xml:space="preserve">
    <value>ಝೆಬಿಬಿಟ್‍ಗಳು</value>
    <comment>A measurement unit for data. (Please choose the most appropriate plural form to fit any number between 0 and 999,999,999,999,999)</comment>
  </data>
  <data name="UnitName_Zebibytes" xml:space="preserve">
    <value>ಝೆಬಿಬೈಟ್‍ಗಳು</value>
    <comment>A measurement unit for data. (Please choose the most appropriate plural form to fit any number between 0 and 999,999,999,999,999)</comment>
  </data>
  <data name="UnitName_Yottabit" xml:space="preserve">
    <value>ಯೋಟಾ ಬಿಟ್ಸ್</value>
    <comment>A measurement unit for data. (Please choose the most appropriate plural form to fit any number between 0 and 999,999,999,999,999)</comment>
  </data>
  <data name="UnitName_Yottabyte" xml:space="preserve">
    <value>ಯೊಟಾಬೈಟ್ಸ್</value>
    <comment>A measurement unit for data. (Please choose the most appropriate plural form to fit any number between 0 and 999,999,999,999,999)</comment>
  </data>
  <data name="UnitName_Yobibits" xml:space="preserve">
    <value>ಯೊಬಿಬಿಟ್‍ಗಳು</value>
    <comment>A measurement unit for data. (Please choose the most appropriate plural form to fit any number between 0 and 999,999,999,999,999)</comment>
  </data>
  <data name="UnitName_Yobibytes" xml:space="preserve">
    <value>ಯೊಬಿಬೈಟ್‍ಗಳು</value>
    <comment>A measurement unit for data. (Please choose the most appropriate plural form to fit any number between 0 and 999,999,999,999,999)</comment>
  </data>
  <data name="DateCalculationModeText" xml:space="preserve">
    <value>ದಿನಾಂಕ ಲೆಕ್ಕಾಚಾರ</value>
  </data>
  <data name="DateCalculationOption.[using:Windows.UI.Xaml.Automation]AutomationProperties.Name" xml:space="preserve">
    <value>ಲೆಕ್ಕಾಚಾರ ಮೋಡ್</value>
    <comment>Automation label for the Date calculation Mode combobox. Users will hear "Calculation mode combobox".</comment>
  </data>
  <data name="AddOption.Content" xml:space="preserve">
    <value>ಸೇರಿಸು</value>
    <comment>Add toggle button text</comment>
  </data>
  <data name="Date_AddSubtractOption.Content" xml:space="preserve">
    <value>ದಿನಗಳನ್ನು ಸಂಕಲಿಸಿ ಅಥವಾ ವ್ಯವಕಲಿಸಿ</value>
    <comment>Add or Subtract days option</comment>
  </data>
  <data name="DateLabel.Text" xml:space="preserve">
    <value>ದಿನಾಂಕ</value>
    <comment>Date result label</comment>
  </data>
  <data name="Date_DifferenceOption.Content" xml:space="preserve">
    <value>ದಿನಾಂಕಗಳ ನಡುವಿನ ವ್ಯತ್ಯಾಸ</value>
    <comment>Date difference option</comment>
  </data>
  <data name="DaysLabel.Text" xml:space="preserve">
    <value>ದಿನಗಳು</value>
    <comment>Add/Subtract Days label</comment>
  </data>
  <data name="Date_DifferenceLabel.Text" xml:space="preserve">
    <value>ವ್ಯತ್ಯಾಸ</value>
    <comment>Difference result label</comment>
  </data>
  <data name="DateDiff_FromHeader.Header" xml:space="preserve">
    <value>ಇಂದ</value>
    <comment>From Date Header for Difference Date Picker</comment>
  </data>
  <data name="MonthsLabel.Text" xml:space="preserve">
    <value>ತಿಂಗಳು</value>
    <comment>Add/Subtract Months label</comment>
  </data>
  <data name="SubtractOption.Content" xml:space="preserve">
    <value>ವ್ಯವಕಲನ</value>
    <comment>Subtract toggle button text</comment>
  </data>
  <data name="DateDiff_ToHeader.Header" xml:space="preserve">
    <value>ಇವರಿಗೆ</value>
    <comment>To Date Header for Difference Date Picker</comment>
  </data>
  <data name="YearsLabel.Text" xml:space="preserve">
    <value>ವರ್ಷಗಳು</value>
    <comment>Add/Subtract Years label</comment>
  </data>
  <data name="Date_OutOfBoundMessage" xml:space="preserve">
    <value>ದಿನಾಂಕ ವ್ಯಾಪ್ತಿ ಮೀರಿದೆ</value>
    <comment>Out of bound message shown as result when the date calculation exceeds the bounds</comment>
  </data>
  <data name="Date_Day" xml:space="preserve">
    <value>ದಿನ</value>
  </data>
  <data name="Date_Days" xml:space="preserve">
    <value>ದಿನಗಳು</value>
  </data>
  <data name="Date_Month" xml:space="preserve">
    <value>ತಿಂಗಳು</value>
  </data>
  <data name="Date_Months" xml:space="preserve">
    <value>ತಿಂಗಳು</value>
  </data>
  <data name="Date_SameDates" xml:space="preserve">
    <value>ಒಂದೇ ದಿನಾಂಕಗಳು</value>
  </data>
  <data name="Date_Week" xml:space="preserve">
    <value>ವಾರ</value>
  </data>
  <data name="Date_Weeks" xml:space="preserve">
    <value>ವಾರಗಳು</value>
  </data>
  <data name="Date_Year" xml:space="preserve">
    <value>ವರ್ಷ</value>
  </data>
  <data name="Date_Years" xml:space="preserve">
    <value>ವರ್ಷಗಳು</value>
  </data>
  <data name="Date_DifferenceResultAutomationName" xml:space="preserve">
    <value>ವ್ಯತ್ಯಾಸ %1</value>
    <comment>Automation name for reading out the date difference. %1 =  Date difference</comment>
  </data>
  <data name="Date_ResultingDateAutomationName" xml:space="preserve">
    <value>ಪರಿಣಾಮದ ದಿನಾಂಕ %1</value>
    <comment>Automation name for reading out the resulting date in Add/Subtract mode. %1 = Resulting date</comment>
  </data>
  <data name="HeaderAutomationName_Calculator" xml:space="preserve">
    <value>%1 ಕ್ಯಾಲ್ಕುಲೇಟರ್ ಮೋಡ್</value>
    <comment>{Locked='%1'} Automation name for when the mode header is focused. %1 = the current calculator mode: Scientific, Standard, or Programmer.</comment>
  </data>
  <data name="HeaderAutomationName_Converter" xml:space="preserve">
    <value>%1 ಕನ್ವರ್ಟರ್ ಮೋಡ್</value>
    <comment>{Locked='%1'} Automation name for when the mode header is focused. %1 = the current converter mode: "Weight and mass", "Energy", "Volume", etc.</comment>
  </data>
  <data name="HeaderAutomationName_Date" xml:space="preserve">
    <value>ದಿನಾಂಕ ಲೆಕ್ಕಾಚಾರ ಮೋಡ್</value>
    <comment>Automation name for when the mode header is focused and the current mode is Date calculation.</comment>
  </data>
  <data name="DockPanel.[using:Windows.UI.Xaml.Automation]AutomationProperties.Name" xml:space="preserve">
    <value>ಇತಿಹಾಸ ಮತ್ತು ಸ್ಮರಣೆ ಪಟ್ಟಿಗಳು</value>
    <comment>Automation name for the group of controls for history and memory lists.</comment>
  </data>
  <data name="MemoryPanel.[using:Windows.UI.Xaml.Automation]AutomationProperties.Name" xml:space="preserve">
    <value>ಸ್ಮರಣೆ ನಿಯಂತ್ರಣಗಳು</value>
    <comment>Automation name for the group of memory controls (Mem Clear, Mem Recall, Mem Add, Mem Subtract, Mem Store, Memory flyout toggle)</comment>
  </data>
  <data name="StandardFunctions.[using:Windows.UI.Xaml.Automation]AutomationProperties.Name" xml:space="preserve">
    <value>ಪ್ರಮಾಣಿತ ಕಾರ್ಯಗಳು</value>
    <comment>Automation name for the group of standard function buttons (Percent, Square Root, Square, Cube, Reciprocal)</comment>
  </data>
  <data name="DisplayControls.[using:Windows.UI.Xaml.Automation]AutomationProperties.Name" xml:space="preserve">
    <value>ಪ್ರದರ್ಶನ ನಿಯಂತ್ರಣಗಳು</value>
    <comment>Automation name for the group of display control buttons (Clear, Clear Entry, and Backspace)</comment>
  </data>
  <data name="StandardOperators.[using:Windows.UI.Xaml.Automation]AutomationProperties.Name" xml:space="preserve">
    <value>ಪ್ರಮಾಣಿತ ಆಪರೇಟರ್‌‍ಗಳು</value>
    <comment>Automation name for the group of standard operator buttons (Add, Subtract, Multiply, Divide, and Equals)</comment>
  </data>
  <data name="NumberPad.[using:Windows.UI.Xaml.Automation]AutomationProperties.Name" xml:space="preserve">
    <value>ಸಂಖ್ಯೆ ಪ್ಯಾಡ್</value>
    <comment>Automation name for the group of NumberPad buttons (0-9, A-F and Decimal button)</comment>
  </data>
  <data name="ScientificAngleOperators.[using:Windows.UI.Xaml.Automation]AutomationProperties.Name" xml:space="preserve">
    <value>ಆಪರೇಟರ್‌ಗಳು</value>
    <comment>Automation name for the group of Scientific angle operators (Degree mode, Hyperbolic toggle, and Precision button)</comment>
  </data>
  <data name="ScientificFunctions.[using:Windows.UI.Xaml.Automation]AutomationProperties.Name" xml:space="preserve">
    <value>ವೈಜ್ಞಾನಿಕ ಕಾರ್ಯಗಳು</value>
    <comment>Automation name for the group of Scientific functions.</comment>
  </data>
  <data name="RadixGroup.[using:Windows.UI.Xaml.Automation]AutomationProperties.Name" xml:space="preserve">
    <value>ರೇಡಿಕ್ಸ್ ಆಯ್ಕೆ</value>
    <comment>Automation name for the group of radices (Hexadecimal, Decimal, Octal, Binary). https://en.wikipedia.org/wiki/Radix </comment>
  </data>
  <data name="ProgrammerOperators.[using:Windows.UI.Xaml.Automation]AutomationProperties.Name" xml:space="preserve">
    <value>ಪ್ರೋಗ್ರಾಮರ್ ಆಪರೇಟರ್‌‍ಗಳು</value>
    <comment>Automation name for the group of programmer operators (RoL, RoR, Lsh, Rsh, OR, XOR, NOT, AND).</comment>
  </data>
  <data name="InputModeSelectionGroup.[using:Windows.UI.Xaml.Automation]AutomationProperties.Name" xml:space="preserve">
    <value>ಇನ್ಪುಟ್ ಮೋಡ್ ಆಯ್ಕೆ</value>
    <comment>Automation name for the group of input mode toggling buttons.</comment>
  </data>
  <data name="BitFlipPanel.[using:Windows.UI.Xaml.Automation]AutomationProperties.Name" xml:space="preserve">
    <value>ಬಿಟ್ ಟಾಗ್ಲಿಂಗ್ ಕೀಪ್ಯಾಡ್</value>
    <comment>Automation name for the group of bit toggling buttons.</comment>
  </data>
  <data name="scrollLeft.[using:Windows.UI.Xaml.Automation]AutomationProperties.Name" xml:space="preserve">
    <value>ಎಡಕ್ಕೆ ಅಭಿವ್ಯಕ್ತಿಯನ್ನು ಸ್ಕ್ರಾಲ್ ಮಾಡಿ</value>
    <comment>Automation label for the "scroll left" button that appears when an expression is too large to fit in the window.</comment>
  </data>
  <data name="scrollRight.[using:Windows.UI.Xaml.Automation]AutomationProperties.Name" xml:space="preserve">
    <value>ಬಲಕ್ಕೆ ಅಭಿವ್ಯಕ್ತಿಯನ್ನು ಸ್ಕ್ರಾಲ್ ಮಾಡಿ</value>
    <comment>Automation label for the "scroll right" button that appears when an expression is too large to fit in the window.</comment>
  </data>
  <data name="Format_MaxDigitsReached" xml:space="preserve">
    <value>ಗರಿಷ್ಠ ಅಂಕಿಗಳನ್ನು ತಲುಪಿದೆ. %1</value>
    <comment>{Locked='%1'} Formatting string for a Narrator announcement when user reaches max digits. The %1 is the automation name of the display. Users will hear "Max digits reached. Display is _current_value_".</comment>
  </data>
  <data name="Format_ButtonPressAuditoryFeedback" xml:space="preserve">
    <value>%1 %2</value>
    <comment>{Locked='%1','%2'} Formatting string for a Narrator announcement when user presses a button with auditory feedback. "%1" is the display value and "%2" is the button press feedback. Example, user presses "plus" and hears "Display is 7 plus".</comment>
  </data>
  <data name="Format_MemorySave" xml:space="preserve">
    <value>%1 ಅನ್ನು ಸ್ಮರಣೆಗೆ ಉಳಿಸಲಾಗಿದೆ</value>
    <comment>{Locked='%1'} Formatting string for a Narrator announcement when the user stores a number to memory. The %1 is the automation name of the display. Users will hear "_current_value_ saved to memory".</comment>
  </data>
  <data name="Format_MemorySlotChanged" xml:space="preserve">
    <value>ಮೆಮೊರಿ ಸ್ಲಾಟ್ %1 ಅಂದರೆ %2</value>
    <comment>{Locked='%1','%2'} Formatting string for a Narrator announcement when the user changes a memory slot. The %1 is the index of the memory slot and %2 is the new value. For example, users might hear "Memory slot 2 is 37".</comment>
  </data>
  <data name="Format_MemorySlotCleared" xml:space="preserve">
    <value>ಮೆಮೊರಿ ಸ್ಲಾಟ್ %1 ಖಾಲಿ ಮಾಡಲಾಗಿದೆ</value>
    <comment>{Locked='%1'} Formatting string for a Narrator announcement when the user clears a memory slot. The %1 is the index of the memory slot. For example, users might hear "Memory slot 2 cleared".</comment>
  </data>
  <data name="divideButton.[using:CalculatorApp.Controls]CalculatorButton.AuditoryFeedback" xml:space="preserve">
    <value>ಇದರಿಂದ ವಿಭಾಗಿಸಿ</value>
    <comment>Auditory feedback for screen reader users. Users will hear "Display is 7 divided by" when the button is pressed.</comment>
  </data>
  <data name="multiplyButton.[using:CalculatorApp.Controls]CalculatorButton.AuditoryFeedback" xml:space="preserve">
    <value>ಸಮಯಗಳು</value>
    <comment>Auditory feedback for screen reader users. Users will hear "Display is 7 times" when the button is pressed.</comment>
  </data>
  <data name="minusButton.[using:CalculatorApp.Controls]CalculatorButton.AuditoryFeedback" xml:space="preserve">
    <value>ಕಳೆ</value>
    <comment>Auditory feedback for screen reader users. Users will hear "Display is 7 minus" when the button is pressed.</comment>
  </data>
  <data name="plusButton.[using:CalculatorApp.Controls]CalculatorButton.AuditoryFeedback" xml:space="preserve">
    <value>ಕೂಡು</value>
    <comment>Auditory feedback for screen reader users. Users will hear "Display is 7 plus" when the button is pressed.</comment>
  </data>
  <data name="powerButton.[using:CalculatorApp.Controls]CalculatorButton.AuditoryFeedback" xml:space="preserve">
    <value>ಪವರ್</value>
    <comment>Auditory feedback for screen reader users. Users will hear "Display is 7 to the power of" when the button is pressed.</comment>
  </data>
  <data name="ySquareRootButton.[using:CalculatorApp.Controls]CalculatorButton.AuditoryFeedback" xml:space="preserve">
    <value>y ರೂಟ್</value>
    <comment>Auditory feedback for screen reader users. Users will hear "Display is 7 y root" when the button is pressed.</comment>
  </data>
  <data name="modButton.[using:CalculatorApp.Controls]CalculatorButton.AuditoryFeedback" xml:space="preserve">
    <value>ಮಾಡ್</value>
    <comment>Auditory feedback for screen reader users. Users will hear "Display is 7 mod" when the button is pressed.</comment>
  </data>
  <data name="lshButton.[using:CalculatorApp.Controls]CalculatorButton.AuditoryFeedback" xml:space="preserve">
    <value>ಎಡ ಶಿಫ್ಟ್</value>
    <comment>Auditory feedback for screen reader users. Users will hear "Display is 7 left shift" when the button is pressed.</comment>
  </data>
  <data name="rshButton.[using:CalculatorApp.Controls]CalculatorButton.AuditoryFeedback" xml:space="preserve">
    <value>ಬಲ ಶಿಫ್ಟ್</value>
    <comment>Auditory feedback for screen reader users. Users will hear "Display is 7 right shift" when the button is pressed.</comment>
  </data>
  <data name="orButton.[using:CalculatorApp.Controls]CalculatorButton.AuditoryFeedback" xml:space="preserve">
    <value>ಅಥವಾ</value>
    <comment>Auditory feedback for screen reader users. Users will hear "Display is 7 or" when the button is pressed. OR is a mathematical operation on two binary values.</comment>
  </data>
  <data name="xorButton.[using:CalculatorApp.Controls]CalculatorButton.AuditoryFeedback" xml:space="preserve">
    <value>x ಅಥವಾ</value>
    <comment>Auditory feedback for screen reader users. Users will hear "Display is 7 x or" when the button is pressed. XOR is a mathematical operation on two binary values. Here the feedback is "x or" in order to get the correct pronunciation.</comment>
  </data>
  <data name="andButton.[using:CalculatorApp.Controls]CalculatorButton.AuditoryFeedback" xml:space="preserve">
    <value>ಮತ್ತು</value>
    <comment>Auditory feedback for screen reader users. Users will hear "Display is 7 and" when the button is pressed. AND is a mathematical operation on two binary values.</comment>
  </data>
  <data name="CurrencyFromToRatioFormat" xml:space="preserve">
    <value>%1 %2 = %3 %4</value>
    <comment>The exact ratio between converted currencies, e.g. "1 USD = 0.8885 EUR". %1 will always be '1'. %2 is the From currency code. %3 is the formatted conversion ratio. %4 is the To currency code.</comment>
  </data>
  <data name="CurrencyTimestampFormat" xml:space="preserve">
    <value>%1 %2 ನವೀಕರಿಸಲಾಗಿದೆ</value>
    <comment>The timestamp of currency conversion ratios fetched from an online service. %1 is the date. %2 is the time. Example: "Updated Sep 28, 2016 5:42 PM"</comment>
  </data>
  <data name="RefreshButtonText.Content" xml:space="preserve">
    <value>ದರಗಳನ್ನು ಪರಿಷ್ಕರಿಸಿ</value>
    <comment>The text displayed for a hyperlink button that refreshes currency converter ratios.</comment>
  </data>
  <data name="DataChargesMayApply" xml:space="preserve">
    <value>ಡೇಟಾ ಶುಲ್ಕಗಳು ಅನ್ವಯವಾಗಬಹುದು.</value>
    <comment>The text displayed when users are on a metered connection and using currency converter.</comment>
  </data>
  <data name="FailedToRefresh" xml:space="preserve">
    <value>ಹೊಸ ದರಗಳನ್ನು ಪಡೆಯಲಾಗಲಿಲ್ಲ. ನಂತರ ಮತ್ತೊಮ್ಮೆ ಪ್ರಯತ್ನಿಸಿ.</value>
    <comment>The text displayed when currency ratio data fails to load.</comment>
  </data>
  <data name="OfflineStatusHyperlinkText" xml:space="preserve">
    <value>ಆಫ್‌ಲೈನ್. ದಯವಿಟ್ಟು ನಿಮ್ಮ %HL%ನೆಟ್‌ವರ್ಕ್ ಸೆಟ್ಟಿಂಗ್‌ಗಳು %HL% ಅನ್ನು ಪರಿಶೀಲಿಸಿ</value>
    <comment>Status text displayed when currency converter is disconnected from the internet. The text "Notification Settings" should be surrounded by %HL% since they are used to indicate that that text should be the hyperlink text. {Locked="%HL%"}</comment>
  </data>
  <data name="UpdatingCurrencyRates" xml:space="preserve">
    <value>ಕರೆನ್ಸಿ ದರಗಳನ್ನು ನವೀಕರಿಸಲಾಗುತ್ತಿದೆ</value>
    <comment>This string is what is read by Narrator, and other screen readers, when the "Update rates" button in the Currency Converter is clicked.</comment>
  </data>
  <data name="CurrencyRatesUpdated" xml:space="preserve">
    <value>ಕರೆನ್ಸಿ ದರಗಳು ನವೀಕರಿಸಲಾಗಿದೆ</value>
    <comment>This string is what is read by Narrator, and other screen readers, when the currency rates in Currency converter have successfully updated.</comment>
  </data>
  <data name="CurrencyRatesUpdateFailed" xml:space="preserve">
    <value>ದರಗಳನ್ನು ನವೀಕರಿಸಲು ಸಾಧ್ಯವಾಗಲಿಲ್ಲ</value>
    <comment>This string is what is read by Narrator, and other screen readers, when the currency rates in Currency converter have failed to update.</comment>
  </data>
  <data name="HistoryButton.AccessKey" xml:space="preserve">
    <value>I</value>
    <comment>Access key for the History button. {StringCategory="Accelerator"}</comment>
  </data>
  <data name="MemoryButton.AccessKey" xml:space="preserve">
    <value>M</value>
    <comment>Access key for the Memory button. {StringCategory="Accelerator"}</comment>
  </data>
  <data name="NavView.AccessKey" xml:space="preserve">
    <value>H</value>
    <comment>Access key for the Hamburger button. {StringCategory="Accelerator"}</comment>
  </data>
  <data name="CategoryName_AngleAccessKey" xml:space="preserve">
    <value>AN</value>
    <comment>AccessKey for the angle converter navbar item. {StringCategory="Accelerator"}</comment>
  </data>
  <data name="CategoryName_AreaAccessKey" xml:space="preserve">
    <value>AR</value>
    <comment>AccessKey for the area converter navbar item. {StringCategory="Accelerator"}</comment>
  </data>
  <data name="CategoryName_CurrencyAccessKey" xml:space="preserve">
    <value>C</value>
    <comment>AccessKey for the currency converter navbar item. {StringCategory="Accelerator"}</comment>
  </data>
  <data name="CategoryName_DataAccessKey" xml:space="preserve">
    <value>D</value>
    <comment>AccessKey for the data converter navbar item. {StringCategory="Accelerator"}</comment>
  </data>
  <data name="CategoryName_EnergyAccessKey" xml:space="preserve">
    <value>E</value>
    <comment>AccessKey for the energy converter navbar item. {StringCategory="Accelerator"}</comment>
  </data>
  <data name="CategoryName_LengthAccessKey" xml:space="preserve">
    <value>ಲೀ</value>
    <comment>AccessKey for the length converter navbar item. {StringCategory="Accelerator"}</comment>
  </data>
  <data name="CategoryName_PowerAccessKey" xml:space="preserve">
    <value>PO</value>
    <comment>AccessKey for the power converter navbar item. {StringCategory="Accelerator"}</comment>
  </data>
  <data name="CategoryName_PressureAccessKey" xml:space="preserve">
    <value>PR</value>
    <comment>AccessKey for the pressure converter navbar item. {StringCategory="Accelerator"}</comment>
  </data>
  <data name="CategoryName_SpeedAccessKey" xml:space="preserve">
    <value>S</value>
    <comment>AccessKey for the speed converter navbar item. {StringCategory="Accelerator"}</comment>
  </data>
  <data name="CategoryName_TimeAccessKey" xml:space="preserve">
    <value>TI</value>
    <comment>AccessKey for the time converter navbar item. {StringCategory="Accelerator"}</comment>
  </data>
  <data name="CategoryName_VolumeAccessKey" xml:space="preserve">
    <value>V</value>
    <comment>AccessKey for the volume converter navbar item. {StringCategory="Accelerator"}</comment>
  </data>
  <data name="CategoryName_WeightAccessKey" xml:space="preserve">
    <value>W</value>
    <comment>AccessKey for the weight converter navbar item. {StringCategory="Accelerator"}</comment>
  </data>
  <data name="CategoryName_TemperatureAccessKey" xml:space="preserve">
    <value>TE</value>
    <comment>AccessKey for the temperature converter navbar item. {StringCategory="Accelerator"}</comment>
  </data>
  <data name="ClearHistory.AccessKey" xml:space="preserve">
    <value>C</value>
    <comment>Access key for the Clear history button.{StringCategory="Accelerator"}</comment>
  </data>
  <data name="ClearMemory.AccessKey" xml:space="preserve">
    <value>C</value>
    <comment>Access key for the Clear memory button. {StringCategory="Accelerator"}</comment>
  </data>
  <data name="ClearMemory.[using:Windows.UI.Xaml.Controls]ToolTipService.ToolTip" xml:space="preserve">
    <value>ಎಲ್ಲಾ ಸ್ಮರಣೆಯನ್ನು ತೆರವುಗೊಳಿಸಿ (Ctrl+L)</value>
    <comment>This is the tool tip automation name for the Clear Memory button in the Memory Pane.</comment>
  </data>
  <data name="ClearMemory.[using:Windows.UI.Xaml.Automation]AutomationProperties.Name" xml:space="preserve">
    <value>ಎಲ್ಲಾ ಸ್ಮರಣೆಯನ್ನು ತೆರವುಗೊಳಿಸಿ</value>
    <comment>Screen reader prompt for the Calculator Clear Memory button in the Memory Pane</comment>
  </data>
  <data name="HistoryLabel.AccessKey" xml:space="preserve">
    <value>I</value>
    <comment>Access key for the History pivot item.{StringCategory="Accelerator"}</comment>
  </data>
  <data name="MemoryLabel.AccessKey" xml:space="preserve">
    <value>M</value>
    <comment>Access key for the Memory pivot item.{StringCategory="Accelerator"}</comment>
  </data>
  <data name="SineDegrees" xml:space="preserve">
    <value>ಸೈನ್ ಡಿಗ್ರಿಗಳು</value>
    <comment>Name for the sine function in degrees mode. Used by screen readers.</comment>
  </data>
  <data name="SineRadians" xml:space="preserve">
    <value>ಸೈನ್ ರೇಡಿಯನ್‍ಗಳು</value>
    <comment>Name for the sine function in radians mode. Used by screen readers.</comment>
  </data>
  <data name="SineGradians" xml:space="preserve">
    <value>ಸೈನ್ ಗ್ರೇಡಿಯನ್‍ಗಳು</value>
    <comment>Name for the sine function in gradians mode. Used by screen readers.</comment>
  </data>
  <data name="InverseSineDegrees" xml:space="preserve">
    <value>ವಿಲೋಮ ಸೈನ್ ಡಿಗ್ರಿಗಳು</value>
    <comment>Name for the inverse sine function in degrees mode. Used by screen readers.</comment>
  </data>
  <data name="InverseSineRadians" xml:space="preserve">
    <value>ವಿಲೋಮ ಸೈನ್ ರೇಡಿಯನ್‍ಗಳು</value>
    <comment>Name for the inverse sine function in radians mode. Used by screen readers.</comment>
  </data>
  <data name="InverseSineGradians" xml:space="preserve">
    <value>ವಿಲೋಮ ಸೈನ್ ಗ್ರೇಡಿಯನ್‍ಗಳು</value>
    <comment>Name for the inverse sine function in gradians mode. Used by screen readers.</comment>
  </data>
  <data name="HyperbolicSine" xml:space="preserve">
    <value>ಹೈಪರ್‌ಬೋಲಿಕ್ ಸೈನ್</value>
    <comment>Name for the hyperbolic sine function. Used by screen readers.</comment>
  </data>
  <data name="InverseHyperbolicSine" xml:space="preserve">
    <value>ವಿಲೋಮ ಹೈಪರ್‌ಬೋಲಿಕ್ ಸೈನ್</value>
    <comment>Name for the inverse hyperbolic sine function. Used by screen readers.</comment>
  </data>
  <data name="CosineDegrees" xml:space="preserve">
    <value>ಕೊಸೈನ್ ಡಿಗ್ರಿಗಳು</value>
    <comment>Name for the cosine function in degrees mode. Used by screen readers.</comment>
  </data>
  <data name="CosineRadians" xml:space="preserve">
    <value>ಕೊಸೈನ್ ರೇಡಿಯನ್‍ಗಳು</value>
    <comment>Name for the cosine function in radians mode. Used by screen readers.</comment>
  </data>
  <data name="CosineGradians" xml:space="preserve">
    <value>ಕೊಸೈನ್ ಗ್ರೇಡಿಯನ್‍ಗಳು</value>
    <comment>Name for the cosine function in gradians mode. Used by screen readers.</comment>
  </data>
  <data name="InverseCosineDegrees" xml:space="preserve">
    <value>ವಿಲೋಮ ಕೊಸೈನ್ ಡಿಗ್ರಿಗಳು</value>
    <comment>Name for the inverse cosine function in degrees mode. Used by screen readers.</comment>
  </data>
  <data name="InverseCosineRadians" xml:space="preserve">
    <value>ವಿಲೋಮ ಕೊಸೈನ್ ರೇಡಿಯನ್‍ಗಳು</value>
    <comment>Name for the inverse cosine function in radians mode. Used by screen readers.</comment>
  </data>
  <data name="InverseCosineGradians" xml:space="preserve">
    <value>ವಿಲೋಮ ಕೋಸೈನ್ ಗ್ರೇಡಿಯನ್‍ಗಳು</value>
    <comment>Name for the inverse cosine function in gradians mode. Used by screen readers.</comment>
  </data>
  <data name="HyperbolicCosine" xml:space="preserve">
    <value>ಹೈಪರ್‌ಬೋಲಿಕ್ ಕೋಸೈನ್</value>
    <comment>Name for the hyperbolic cosine function. Used by screen readers.</comment>
  </data>
  <data name="InverseHyperbolicCosine" xml:space="preserve">
    <value>ವಿಲೋಮ ಹೈಪರ್‌ಬೋಲಿಕ್ ಕೋಸೈನ್</value>
    <comment>Name for the inverse hyperbolic cosine function. Used by screen readers.</comment>
  </data>
  <data name="TangentDegrees" xml:space="preserve">
    <value>ಸ್ಪರ್ಶಕ ಡಿಗ್ರಿಗಳು</value>
    <comment>Name for the tangent function in degrees mode. Used by screen readers.</comment>
  </data>
  <data name="TangentRadians" xml:space="preserve">
    <value>ಟ್ಯಾಂಜೆಂಟ್ ರೇಡಿಯನ್‍ಗಳು</value>
    <comment>Name for the tangent function in radians mode. Used by screen readers.</comment>
  </data>
  <data name="TangentGradians" xml:space="preserve">
    <value>ಟ್ಯಾಂಜೆಂಟ್ ಗ್ರೇಡಿಯನ್‍ಗಳು</value>
    <comment>Name for the tangent function in gradians mode. Used by screen readers.</comment>
  </data>
  <data name="InverseTangentDegrees" xml:space="preserve">
    <value>ವಿಲೋಮ ಸ್ಪರ್ಶಕ ಡಿಗ್ರಿಗಳು</value>
    <comment>Name for the inverse tangent function in degrees mode. Used by screen readers.</comment>
  </data>
  <data name="InverseTangentRadians" xml:space="preserve">
    <value>ವಿಲೋಮ ಸ್ಪರ್ಶಕ ರೇಡಿಯನ್‍ಗಳು</value>
    <comment>Name for the inverse tangent function in radians mode. Used by screen readers.</comment>
  </data>
  <data name="InverseTangentGradians" xml:space="preserve">
    <value>ವಿಲೋಮ ಟ್ಯಾಂಜೆಂಟ್ ಗ್ರೇಡಿಯನ್‍ಗಳು</value>
    <comment>Name for the inverse tangent function in gradians mode. Used by screen readers.</comment>
  </data>
  <data name="HyperbolicTangent" xml:space="preserve">
    <value>ಹೈಪರ್‌ಬೋಲಿಕ್ ಟ್ಯಾಂಜೆಂಟ್</value>
    <comment>Name for the hyperbolic tangent function. Used by screen readers.</comment>
  </data>
  <data name="InverseHyperbolicTangent" xml:space="preserve">
    <value>ವಿಲೋಮ ಹೈಪರ್‌ಬೋಲಿಕ್ ಟ್ಯಾಂಜೆಂಟ್</value>
    <comment>Name for the inverse hyperbolic tangent function. Used by screen readers.</comment>
  </data>
  <data name="SecantDegrees" xml:space="preserve">
    <value>ಸೀಕಂಟ್ ಡಿಗ್ರಿಗಳು</value>
    <comment>Name for the secant function in degrees mode. Used by screen readers.</comment>
  </data>
  <data name="SecantRadians" xml:space="preserve">
    <value>ಸೀಕಂಟ್ ರೇಡಿಯನ್‌ಗಳು</value>
    <comment>Name for the secant function in radians mode. Used by screen readers.</comment>
  </data>
  <data name="SecantGradians" xml:space="preserve">
    <value>ಸೀಕಂಟ್ ಗ್ರೇಡಿಯನ್‌ಗಳು</value>
    <comment>Name for the secant function in gradians mode. Used by screen readers.</comment>
  </data>
  <data name="InverseSecantDegrees" xml:space="preserve">
    <value>ವಿಲೋಮ ಸೀಕಂಟ್‌ ಡಿಗ್ರಿಗಳು</value>
    <comment>Name for the inverse secant function in degrees mode. Used by screen readers.</comment>
  </data>
  <data name="InverseSecantRadians" xml:space="preserve">
    <value>ವಿಲೋಮ ಸೀಕಂಟ್‌ ರೇಡಿಯನ್‌ಗಳು</value>
    <comment>Name for the inverse secant function in radians mode. Used by screen readers.</comment>
  </data>
  <data name="InverseSecantGradians" xml:space="preserve">
    <value>ವಿಲೋಮ ಸೀಕಂಟ್‌ ಗ್ರೇಡಿಯನ್‌ಗಳು</value>
    <comment>Name for the inverse secant function in gradians mode. Used by screen readers.</comment>
  </data>
  <data name="HyperbolicSecant" xml:space="preserve">
    <value>ಹೈಪರ್‌ಬೋಲಿಕ್ ಸೀಕಂಟ್‌</value>
    <comment>Name for the hyperbolic secant function. Used by screen readers.</comment>
  </data>
  <data name="InverseHyperbolicSecant" xml:space="preserve">
    <value>ವಿಲೋಮ ಹೈಪರ್‌ಬೋಲಿಕ್ ಸೀಕಂಟ್‌</value>
    <comment>Name for the inverse hyperbolic secant function. Used by screen readers.</comment>
  </data>
  <data name="CosecantDegrees" xml:space="preserve">
    <value>ಕೊಸಿಕೆಂಟ್ ಡಿಗ್ರಿಗಳು</value>
    <comment>Name for the cosecant function in degrees mode. Used by screen readers.</comment>
  </data>
  <data name="CosecantRadians" xml:space="preserve">
    <value>ಕೊಸಿಕೆಂಟ್ ರೇಡಿಯನ್‌ಗಳು</value>
    <comment>Name for the cosecant function in radians mode. Used by screen readers.</comment>
  </data>
  <data name="CosecantGradians" xml:space="preserve">
    <value>ಕೊಸಿಕೆಂಟ್ ಗ್ರೇಡಿಯನ್‌ಗಳು</value>
    <comment>Name for the cosecant function in gradians mode. Used by screen readers.</comment>
  </data>
  <data name="InverseCosecantDegrees" xml:space="preserve">
    <value>ವಿಲೋಮ ಕೊಸಿಕೆಂಟ್ ಡಿಗ್ರಿಗಳು</value>
    <comment>Name for the inverse cosecant function in degrees mode. Used by screen readers.</comment>
  </data>
  <data name="InverseCosecantRadians" xml:space="preserve">
    <value>ವಿಲೋಮ ಕೊಸಿಕೆಂಟ್ ರೇಡಿಯನ್‌ಗಳು</value>
    <comment>Name for the inverse cosecant function in radians mode. Used by screen readers.</comment>
  </data>
  <data name="InverseCosecantGradians" xml:space="preserve">
    <value>ವಿಲೋಮ ಕೊಸಿಕೆಂಟ್ ಗ್ರೇಡಿಯನ್‌ಗಳು</value>
    <comment>Name for the inverse cosecant function in gradians mode. Used by screen readers.</comment>
  </data>
  <data name="HyperbolicCosecant" xml:space="preserve">
    <value>ಹೈಪರ್‌ಬೋಲಿಕ್ ಕೊಸಿಕೆಂಟ್</value>
    <comment>Name for the hyperbolic cosecant function. Used by screen readers.</comment>
  </data>
  <data name="InverseHyperbolicCosecant" xml:space="preserve">
    <value>ವಿಲೋಮ ಹೈಪರ್‌ಬೋಲಿಕ್ ಕೊಸಿಕೆಂಟ್</value>
    <comment>Name for the inverse hyperbolic cosecant function. Used by screen readers.</comment>
  </data>
  <data name="CotangentDegrees" xml:space="preserve">
    <value>ಕೊಟ್ಯಾಂಜೆಂಟ್ ಡಿಗ್ರಿಗಳು</value>
    <comment>Name for the cotangent function in degrees mode. Used by screen readers.</comment>
  </data>
  <data name="CotangentRadians" xml:space="preserve">
    <value>ಕೊಟ್ಯಾಂಜೆಂಟ್ ರೇಡಿಯನ್‌ಗಳು</value>
    <comment>Name for the cotangent function in radians mode. Used by screen readers.</comment>
  </data>
  <data name="CotangentGradians" xml:space="preserve">
    <value>ಕೊಟ್ಯಾಂಜೆಂಟ್ ಗ್ರೇಡಿಯನ್‌ಗಳು</value>
    <comment>Name for the cotangent function in gradians mode. Used by screen readers.</comment>
  </data>
  <data name="InverseCotangentDegrees" xml:space="preserve">
    <value>ವಿಲೋಮ ಕೊಟ್ಯಾಂಜೆಂಟ್ ಡಿಗ್ರಿಗಳು</value>
    <comment>Name for the inverse cotangent function in degrees mode. Used by screen readers.</comment>
  </data>
  <data name="InverseCotangentRadians" xml:space="preserve">
    <value>ವಿಲೋಮ ಕೊಟ್ಯಾಂಜೆಂಟ್ ರೇಡಿಯನ್‌ಗಳು</value>
    <comment>Name for the inverse cotangent function in radians mode. Used by screen readers.</comment>
  </data>
  <data name="InverseCotangentGradians" xml:space="preserve">
    <value>ವಿಲೋಮ ಕೊಟ್ಯಾಂಜೆಂಟ್ ಗ್ರೇಡಿಯನ್‌ಗಳು</value>
    <comment>Name for the inverse cotangent function in gradians mode. Used by screen readers.</comment>
  </data>
  <data name="HyperbolicCotangent" xml:space="preserve">
    <value>ಹೈಪರ್ ಬೋಲಿಕ್ ಕೊಟ್ಯಾಂಜೆಂಟ್</value>
    <comment>Name for the hyperbolic cotangent function. Used by screen readers.</comment>
  </data>
  <data name="InverseHyperbolicCotangent" xml:space="preserve">
    <value>ವಿಲೋಮ ಹೈಪರ್‌ಬೋಲಿಕ್ ಕೊಟ್ಯಾಂಜೆಂಟ್</value>
    <comment>Name for the inverse hyperbolic cotangent function. Used by screen readers.</comment>
  </data>
  <data name="CubeRoot" xml:space="preserve">
    <value>ಘನ ಮೂಲ</value>
    <comment>Name for the cube root function. Used by screen readers.</comment>
  </data>
  <data name="Logy" xml:space="preserve">
    <value>ಲಾಗ್ ಬೇಸ್</value>
    <comment>Name for the logbasey function. Used by screen readers.</comment>
  </data>
  <data name="AbsoluteValue" xml:space="preserve">
    <value>ಪರಿಪೂರ್ಣ ಮೌಲ್ಯ</value>
    <comment>Name for the absolute value function. Used by screen readers.</comment>
  </data>
  <data name="LeftShift" xml:space="preserve">
    <value>ಎಡ ಶಿಫ್ಟ್</value>
    <comment>Name for the programmer function that shifts bits to the left. Used by screen readers.</comment>
  </data>
  <data name="RightShift" xml:space="preserve">
    <value>ಬಲ ಶಿಫ್ಟ್</value>
    <comment>Name for the programmer function that shifts bits to the right. Used by screen readers.</comment>
  </data>
  <data name="Factorial" xml:space="preserve">
    <value>ಗುಣಲಬ್ಧ</value>
    <comment>Name for the factorial function. Used by screen readers.</comment>
  </data>
  <data name="DegreeMinuteSecond" xml:space="preserve">
    <value>ಡಿಗ್ರಿ ನಿಮಿಷ ಸೆಕೆಂಡ್</value>
    <comment>Name for the degree minute second (dms) function. Used by screen readers.</comment>
  </data>
  <data name="NaturalLog" xml:space="preserve">
    <value>ನ್ಯಾಚುರಲ್ ಲಾಗ್</value>
    <comment>Name for the natural log (ln) function. Used by screen readers.</comment>
  </data>
  <data name="Square" xml:space="preserve">
    <value>ಚೌಕ</value>
    <comment>Name for the square function. Used by screen readers.</comment>
  </data>
  <data name="YRoot" xml:space="preserve">
    <value>y ರೂಟ್</value>
    <comment>Name for the y root function. Used by screen readers.</comment>
  </data>
  <data name="NavCategoryItem_AutomationNameFormat" xml:space="preserve">
    <value>%1 %2</value>
    <comment>{Locked='%1','%2'}.  Format string for the accessible name of a Calculator menu item, used by screen readers. "%1" is the item name, e.g. Standard, Programmer, etc. %2 is the category name, e.g. Calculator, Converter. An example when formatted is "Standard Calculator" or "Currency Converter".</comment>
  </data>
  <data name="NavCategoryHeader_AutomationNameFormat" xml:space="preserve">
    <value>%1 ವರ್ಗ</value>
    <comment>{Locked='%1'} Format string for the accessible name of a Calculator menu category header, used by screen readers. "%1" is the pluralized category name, e.g. Calculators, Converters. An example when formatted is "Calculators category".</comment>
  </data>
  <data name="AboutControlServicesAgreement.Text" xml:space="preserve">
    <value>Microsoft ಸೇವೆಗಳ ಒಪ್ಪಂದ</value>
    <comment>Displayed on a link to the Microsoft Services Agreement in the about this app information</comment>
  </data>
  <data name="UnitAbbreviation_Pyeong" xml:space="preserve">
    <value>Pyeong</value>
    <comment>An abbreviation for a measurement unit of area.</comment>
  </data>
  <data name="UnitName_Pyeong" xml:space="preserve">
    <value>Pyeong</value>
    <comment>A measurement unit for area.</comment>
  </data>
  <data name="AddSubtract_Date_FromHeader.Header" xml:space="preserve">
    <value>ಇಂದ</value>
    <comment>From Date Header for AddSubtract Date Picker</comment>
  </data>
  <data name="CalculationResultScrollLeft.[using:Windows.UI.Xaml.Automation]AutomationProperties.Name" xml:space="preserve">
    <value>ಲೆಕ್ಕಾಚಾರ ಫಲಿತಾಂಶವನ್ನು ಎಡಕ್ಕೆ ಸ್ಕ್ರಾಲ್ ಮಾಡಿ</value>
    <comment>Automation label for the "Scroll Left" button that appears when a calculation result is too large to fit in calculation result text box.</comment>
  </data>
  <data name="CalculationResultScrollRight.[using:Windows.UI.Xaml.Automation]AutomationProperties.Name" xml:space="preserve">
    <value>ಲೆಕ್ಕಾಚಾರ ಫಲಿತಾಂಶವನ್ನು ಬಲಕ್ಕೆ ಸ್ಕ್ರಾಲ್ ಮಾಡಿ</value>
    <comment>Automation label for the "Scroll Right" button that appears when a calculation result is too large to fit in calculation result text box.</comment>
  </data>
  <data name="CalculationFailed" xml:space="preserve">
    <value>ಲೆಕ್ಕಾಚಾರ ವಿಫಲವಾಗಿದೆ</value>
    <comment>Text displayed when the application is not able to do a calculation</comment>
  </data>
  <data name="logBaseY.[using:Windows.UI.Xaml.Automation]AutomationProperties.Name" xml:space="preserve">
    <value>ಲಾಗ್ ಬೇಸ್ Y</value>
    <comment>Screen reader prompt for the logBaseY button</comment>
  </data>
  <data name="trigButton.Text" xml:space="preserve">
    <value>ಟ್ರಿಗ್ನೊಮೆಟ್ರಿ</value>
    <comment>Displayed on the button that contains a flyout for the trig functions in scientific mode.</comment>
  </data>
  <data name="funcButton.Text" xml:space="preserve">
    <value>ಕಾರ್ಯ</value>
    <comment>Displayed on the button that contains a flyout for the general functions in scientific mode.</comment>
  </data>
  <data name="inequalityButton.Text" xml:space="preserve">
    <value>ಅಸಮಾನತೆಗಳು</value>
    <comment>Displayed on the button that contains a flyout for the inequality functions.</comment>
  </data>
  <data name="inequalityButton.[using:Windows.UI.Xaml.Automation]AutomationProperties.Name" xml:space="preserve">
    <value>ಅಸಮಾನತೆಗಳು</value>
    <comment>Screen reader prompt for the Inequalities button</comment>
  </data>
  <data name="bitwiseButton.Text" xml:space="preserve">
    <value>ಬಿಟ್‌ವೈಸ್</value>
    <comment>Displayed on the button that contains a flyout for the bitwise functions in programmer mode.</comment>
  </data>
  <data name="bitShiftButton.Text" xml:space="preserve">
    <value>ಬಿಟ್ ಶಿಫ್ಟ್</value>
    <comment>Displayed on the button that contains a flyout for the bit shift functions in programmer mode.</comment>
  </data>
  <data name="trigShiftButton.[using:Windows.UI.Xaml.Automation]AutomationProperties.Name" xml:space="preserve">
    <value>ವಿಲೋಮ ಕಾರ್ಯ</value>
    <comment>Screen reader prompt for the shift button in the trig flyout in scientific mode.</comment>
  </data>
  <data name="hypButton.[using:Windows.UI.Xaml.Automation]AutomationProperties.Name" xml:space="preserve">
    <value>ಹೈಪರ್‌ಬೋಲಿಕ್ ಕಾರ್ಯ</value>
    <comment>Screen reader prompt for the Calculator button HYP in the scientific flyout keypad</comment>
  </data>
  <data name="secButton.[using:Windows.UI.Xaml.Automation]AutomationProperties.Name" xml:space="preserve">
    <value>ಸೀಕಂಟ್</value>
    <comment>Screen reader prompt for the Calculator button sec in the scientific flyout keypad</comment>
  </data>
  <data name="sechButton.[using:Windows.UI.Xaml.Automation]AutomationProperties.Name" xml:space="preserve">
    <value>ಹೈಪರ್‌ಬೋಲಿಕ್ ಸೀಕಂಟ್‌</value>
    <comment>Screen reader prompt for the Calculator button sech in the scientific flyout keypad</comment>
  </data>
  <data name="invsecButton.[using:Windows.UI.Xaml.Automation]AutomationProperties.Name" xml:space="preserve">
    <value>ಆರ್ಕ್ ಸೀಕಂಟ್</value>
    <comment>Screen reader prompt for the Calculator button arc sec in the scientific flyout keypad</comment>
  </data>
  <data name="invsechButton.[using:Windows.UI.Xaml.Automation]AutomationProperties.Name" xml:space="preserve">
    <value>ಹೈಪರ್‌ಬೋಲಿಕ್ ಆರ್ಕ್ ಸೀಕಂಟ್</value>
    <comment>Screen reader prompt for the Calculator button arc sec in the scientific flyout keypad</comment>
  </data>
  <data name="cscButton.[using:Windows.UI.Xaml.Automation]AutomationProperties.Name" xml:space="preserve">
    <value>ಕೊಸಿಕೆಂಟ್</value>
    <comment>Screen reader prompt for the Calculator button csc in the scientific flyout keypad</comment>
  </data>
  <data name="cschButton.[using:Windows.UI.Xaml.Automation]AutomationProperties.Name" xml:space="preserve">
    <value>ಹೈಪರ್‌ಬೋಲಿಕ್ ಕೊಸಿಕೆಂಟ್</value>
    <comment>Screen reader prompt for the Calculator button csch in the scientific flyout keypad</comment>
  </data>
  <data name="invcscButton.[using:Windows.UI.Xaml.Automation]AutomationProperties.Name" xml:space="preserve">
    <value>ಆರ್ಕ್ ಕೊಸಿಕೆಂಟ್</value>
    <comment>Screen reader prompt for the Calculator button arc csc in the scientific flyout keypad</comment>
  </data>
  <data name="invcschButton.[using:Windows.UI.Xaml.Automation]AutomationProperties.Name" xml:space="preserve">
    <value>ಹೈಪರ್‌ಬೋಲಿಕ್ ಆರ್ಕ್ ಕೊಸಿಕೆಂಟ್</value>
    <comment>Screen reader prompt for the Calculator button arc csc in the scientific flyout keypad</comment>
  </data>
  <data name="cotButton.[using:Windows.UI.Xaml.Automation]AutomationProperties.Name" xml:space="preserve">
    <value>ಕೊಟ್ಯಾಂಜೆಂಟ್</value>
    <comment>Screen reader prompt for the Calculator button cot in the scientific flyout keypad</comment>
  </data>
  <data name="cothButton.[using:Windows.UI.Xaml.Automation]AutomationProperties.Name" xml:space="preserve">
    <value>ಹೈಪರ್‌ಬೋಲಿಕ್ ಕೊಟ್ಯಾಂಜೆಂಟ್</value>
    <comment>Screen reader prompt for the Calculator button coth in the scientific flyout keypad</comment>
  </data>
  <data name="invcotButton.[using:Windows.UI.Xaml.Automation]AutomationProperties.Name" xml:space="preserve">
    <value>ಆರ್ಕ್ ಕೊಟ್ಯಾಂಜೆಂಟ್</value>
    <comment>Screen reader prompt for the Calculator button arc cot in the scientific flyout keypad</comment>
  </data>
  <data name="invcothButton.[using:Windows.UI.Xaml.Automation]AutomationProperties.Name" xml:space="preserve">
    <value>ಹೈಪರ್‌ಬೋಲಿಕ್ ಆರ್ಕ್ ಕೊಟ್ಯಾಂಜೆಂಟ್</value>
    <comment>Screen reader prompt for the Calculator button arc coth in the scientific flyout keypad</comment>
  </data>
  <data name="floorButton.[using:Windows.UI.Xaml.Automation]AutomationProperties.Name" xml:space="preserve">
    <value>ಫ್ಲೋರ್</value>
    <comment>Screen reader prompt for the Calculator button floor in the scientific flyout keypad</comment>
  </data>
  <data name="ceilButton.[using:Windows.UI.Xaml.Automation]AutomationProperties.Name" xml:space="preserve">
    <value>ಸೀಲಿಂಗ್</value>
    <comment>Screen reader prompt for the Calculator button ceiling in the scientific flyout keypad</comment>
  </data>
  <data name="randButton.[using:Windows.UI.Xaml.Automation]AutomationProperties.Name" xml:space="preserve">
    <value>ಯಾದೃಚ್ಛಿಕ</value>
    <comment>Screen reader prompt for the Calculator button random in the scientific flyout keypad</comment>
  </data>
  <data name="absButton.[using:Windows.UI.Xaml.Automation]AutomationProperties.Name" xml:space="preserve">
    <value>ಪರಿಪೂರ್ಣ ಮೌಲ್ಯ</value>
    <comment>Screen reader prompt for the Calculator button abs in the scientific flyout keypad</comment>
  </data>
  <data name="eulerButton.[using:Windows.UI.Xaml.Automation]AutomationProperties.Name" xml:space="preserve">
    <value>ಯೂಲರ್ಸ್ ನಂಬರ್</value>
    <comment>Screen reader prompt for the Calculator button e in the scientific flyout keypad</comment>
  </data>
  <data name="twoPowerXButton.[using:Windows.UI.Xaml.Automation]AutomationProperties.Name" xml:space="preserve">
    <value>ಘಾತಾಂಕಕ್ಕೆ ಎರಡು</value>
    <comment>Screen reader prompt for the Calculator button 2^x in the scientific flyout keypad</comment>
  </data>
  <data name="nandButton.[using:Windows.UI.Xaml.Automation]AutomationProperties.Name" xml:space="preserve">
    <value>ಇಲ್ಲ ಮತ್ತು</value>
    <comment>Screen reader prompt for the Calculator button nand in the scientific flyout keypad</comment>
  </data>
  <data name="nandButton.[using:CalculatorApp.Controls]CalculatorButton.AuditoryFeedback" xml:space="preserve">
    <value>ಇಲ್ಲ ಮತ್ತು</value>
    <comment>Auditory feedback for screen reader users. Users will hear "Display is 7 nand" when the button is pressed. NAND is a mathematical operation on two binary values.</comment>
  </data>
  <data name="norButton.[using:Windows.UI.Xaml.Automation]AutomationProperties.Name" xml:space="preserve">
    <value>ಅಥವಾ</value>
    <comment>Screen reader prompt for the Calculator button nor in the scientific flyout keypad</comment>
  </data>
  <data name="norButton.[using:CalculatorApp.Controls]CalculatorButton.AuditoryFeedback" xml:space="preserve">
    <value>ಅಥವಾ</value>
    <comment>Auditory feedback for screen reader users. Users will hear "Display is 7 nor" when the button is pressed. NAND is a mathematical operation on two binary values.</comment>
  </data>
  <data name="rolCarryButton.[using:Windows.UI.Xaml.Automation]AutomationProperties.Name" xml:space="preserve">
    <value>ಕ್ಯಾರಿಯೊಂದಿಗೆ ಎಡಗಡೆಗೆ ತಿರುಗಿಸಿ</value>
    <comment>Screen reader prompt for the Calculator button rol with carry in the scientific flyout keypad</comment>
  </data>
  <data name="rorCarryButton.[using:Windows.UI.Xaml.Automation]AutomationProperties.Name" xml:space="preserve">
    <value>ಕ್ಯಾರಿಯೊಂದಿಗೆ ಬಲಗಡೆಗೆ ತಿರುಗಿಸಿ</value>
    <comment>Screen reader prompt for the Calculator button ror with carry in the scientific flyout keypad</comment>
  </data>
  <data name="lshLogicalButton.[using:Windows.UI.Xaml.Automation]AutomationProperties.Name" xml:space="preserve">
    <value>ಎಡ ಶಿಫ್ಟ್</value>
    <comment>Screen reader prompt for the Calculator button lshLogical in the scientific flyout keypad</comment>
  </data>
  <data name="lshLogicalButton.[using:CalculatorApp.Controls]CalculatorButton.AuditoryFeedback" xml:space="preserve">
    <value>ಎಡ ಶಿಫ್ಟ್</value>
    <comment>Auditory feedback for screen reader users. Users will hear "Display is 7 left shift" when the button is pressed. NAND is a mathematical operation on two binary values.</comment>
  </data>
  <data name="rshLogicalButton.[using:Windows.UI.Xaml.Automation]AutomationProperties.Name" xml:space="preserve">
    <value>ಬಲ ಶಿಫ್ಟ್</value>
    <comment>Screen reader prompt for the Calculator button rshLogical in the scientific flyout keypad</comment>
  </data>
  <data name="rshLogicalButton.[using:CalculatorApp.Controls]CalculatorButton.AuditoryFeedback" xml:space="preserve">
    <value>ಬಲ ಶಿಫ್ಟ್</value>
    <comment>Auditory feedback for screen reader users. Users will hear "Display is 7 right shift" when the button is pressed. NAND is a mathematical operation on two binary values.</comment>
  </data>
  <data name="arithmeticShiftButton.Content" xml:space="preserve">
    <value>ಅಂಕಗಣಿತ ಶಿಫ್ಟ್</value>
    <comment>Label for a radio button that toggles arithmetic shift behavior for the shift operations.</comment>
  </data>
  <data name="logicalShiftButton.Content" xml:space="preserve">
    <value>ಲಾಜಿಕಲ್ ಶಿಫ್ಟ್</value>
    <comment>Label for a radio button that toggles logical shift behavior for the shift operations.</comment>
  </data>
  <data name="rotateCircularButton.Content" xml:space="preserve">
    <value>ವೃತ್ತಾಕಾರದ ಶಿಫ್ಟ್ ತಿರುಗಿಸಿ</value>
    <comment>Label for a radio button that toggles rotate circular behavior for the shift operations.</comment>
  </data>
  <data name="rotateCarryShiftButton.Content" xml:space="preserve">
    <value>ಕ್ಯಾರಿ ವೃತ್ತಾಕಾರದ ಶಿಫ್ಟ್ ಮೂಲಕ ತಿರುಗಿಸಿ</value>
    <comment>Label for a radio button that toggles rotate circular with carry behavior for the shift operations.</comment>
  </data>
  <data name="cubeRootButton.[using:Windows.UI.Xaml.Automation]AutomationProperties.Name" xml:space="preserve">
    <value>ಘನ ಮೂಲ</value>
    <comment>Screen reader prompt for the cube root button on the scientific operator keypad</comment>
  </data>
  <data name="trigButton.[using:Windows.UI.Xaml.Automation]AutomationProperties.Name" xml:space="preserve">
    <value>ಟ್ರಿಗ್ನೊಮೆಟ್ರಿ</value>
    <comment>Screen reader prompt for the square root button on the scientific operator keypad</comment>
  </data>
  <data name="funcButton.[using:Windows.UI.Xaml.Automation]AutomationProperties.Name" xml:space="preserve">
    <value>ಕಾರ್ಯಗಳು</value>
    <comment>Screen reader prompt for the square root button on the scientific operator keypad</comment>
  </data>
  <data name="bitwiseButton.[using:Windows.UI.Xaml.Automation]AutomationProperties.Name" xml:space="preserve">
    <value>ಬಿಟ್‌ವೈಸ್</value>
    <comment>Screen reader prompt for the square root button on the scientific operator keypad</comment>
  </data>
  <data name="bitShiftButton.[using:Windows.UI.Xaml.Automation]AutomationProperties.Name" xml:space="preserve">
    <value>ಬಿಟ್ಶಿಫ್ಟ್</value>
    <comment>Screen reader prompt for the square root button on the scientific operator keypad</comment>
  </data>
  <data name="ScientificOperatorPanel.[using:Windows.UI.Xaml.Automation]AutomationProperties.Name" xml:space="preserve">
    <value>ವೈಜ್ಞಾನಿಕ ಆಪರೇಟರ್ ಪ್ಯಾನೆಲ್‌ಗಳು</value>
    <comment>Screen reader prompt for the Scientific Operator Panels on the scientific operator keypad</comment>
  </data>
  <data name="ProgrammerOperatorPanel.[using:Windows.UI.Xaml.Automation]AutomationProperties.Name" xml:space="preserve">
    <value>ಪ್ರೋಗ್ರಾಮರ್ ಆಪರೇಟರ್ ಪ್ಯಾನೆಲ್‌ಗಳು</value>
    <comment>Screen reader prompt for the Programmer Operator Panels on the programmer operator keypad</comment>
  </data>
  <data name="MostSignificantBit" xml:space="preserve">
    <value>ಅತ್ಯಂತ ಮಹತ್ವದ ಬಿಟ್</value>
    <comment>Used to describe the last bit of a binary number. Used in bit flip</comment>
  </data>
  <data name="GraphingCalculatorModeText" xml:space="preserve">
    <value>ಗ್ರಾಫಿಂಗ್</value>
    <comment>Name of the Graphing mode of the Calculator app. Displayed in the navigation menu.</comment>
  </data>
  <data name="plotButton.[using:Windows.UI.Xaml.Automation]AutomationProperties.Name" xml:space="preserve">
    <value>ಪ್ಲಾಟ್</value>
    <comment>Screen reader prompt for the plot button on the graphing calculator operator keypad</comment>
  </data>
  <data name="graphViewButton.[using:Windows.UI.Xaml.Controls]ToolTipService.ToolTip" xml:space="preserve">
    <value>ಸ್ವಯಂಚಾಲಿತವಾಗಿ ನೋಟವನ್ನು ಪುನಶ್ಚೇತನಗೊಳಿಸು (Ctrl + 0)</value>
    <comment>This is the tool tip automation name for the Calculator graph view button.</comment>
  </data>
  <data name="graphViewButton.[using:Windows.UI.Xaml.Automation]AutomationProperties.Name" xml:space="preserve">
    <value>ಗ್ರಾಫ್ ವೀಕ್ಷಣೆ</value>
    <comment>Screen reader prompt for the graph view button.</comment>
  </data>
  <data name="GraphViewAutomaticBestFitAnnouncement" xml:space="preserve">
    <value>ಸ್ವಯಂಚಾಲಿತ ಅತ್ಯುತ್ತಮ ಹೊಂದಿಕೆ</value>
    <comment>Announcement used in Graphing Calculator when graph view button is clicked and automatic best fit is set</comment>
  </data>
  <data name="GraphViewManualAdjustmentAnnouncement" xml:space="preserve">
    <value>ಹಸ್ತಚಾಲಿತ ಹೊಂದಾಣಿಕೆ</value>
    <comment>Announcement used in Graphing Calculator when graph view button is clicked and manual adjustment is set</comment>
  </data>
  <data name="GridResetAnnouncement" xml:space="preserve">
    <value>ಗ್ರಾಫ್ ವೀಕ್ಷಣೆಯನ್ನು ಮರುಹೊಂದಿಸಲಾಗಿದೆ</value>
    <comment>Announcement used in Graphing Calculator when graph view button is clicked and automatic best fit is set, resetting the graph</comment>
  </data>
  <data name="zoomInButton.[using:Windows.UI.Xaml.Controls]ToolTipService.ToolTip" xml:space="preserve">
    <value>ಝೂಮ್ ಇನ್ (Ctrl + ಪ್ಲಸ್)</value>
    <comment>This is the tool tip automation name for the Calculator zoom in button.</comment>
  </data>
  <data name="zoomInButton.[using:Windows.UI.Xaml.Automation]AutomationProperties.Name" xml:space="preserve">
    <value>ಜೂಮ್ ಇನ್</value>
    <comment>Screen reader prompt for the zoom in button.</comment>
  </data>
  <data name="zoomOutButton.[using:Windows.UI.Xaml.Controls]ToolTipService.ToolTip" xml:space="preserve">
    <value>ಝೂಮ್ ಔಟ್ (Ctrl + ಮೈನಸ್)</value>
    <comment>This is the tool tip automation name for the Calculator zoom out button.</comment>
  </data>
  <data name="zoomOutButton.[using:Windows.UI.Xaml.Automation]AutomationProperties.Name" xml:space="preserve">
    <value>ಜೂಮ್ ಔಟ್</value>
    <comment>Screen reader prompt for the zoom out button.</comment>
  </data>
  <data name="EquationTextBoxAddPanel.[using:Windows.UI.Xaml.Controls]ToolTipService.ToolTip" xml:space="preserve">
    <value>ಸಮೀಕರಣ ಸೇರಿಸಿ</value>
    <comment>Placeholder text for the equation input button</comment>
  </data>
  <data name="ShareActionErrorMessage" xml:space="preserve">
    <value>ಈ ಸಮಯದಲ್ಲಿ ಹಂಚಿಕೊಳ್ಳಲು ಸಾಧ್ಯವಿಲ್ಲ.</value>
    <comment>If there is an error in the sharing action will display a dialog with this text.</comment>
  </data>
  <data name="ShareActionErrorOk" xml:space="preserve">
    <value>ಸರಿ</value>
    <comment>Used on the dismiss button of the share action error dialog.</comment>
  </data>
  <data name="ShareActionTitle" xml:space="preserve">
    <value>Windows ಕ್ಯಾಲ್ಕುಲೇಟರ್‌ನೊಂದಿಗೆ ನಾನು ಏನು ಗ್ರಹಿಸಿದ್ದೇನೆ ಎಂಬುದನ್ನು ನೋಡಿ</value>
    <comment>Sent as part of the shared content. The title for the share.</comment>
  </data>
  <data name="EquationsShareHeader" xml:space="preserve">
    <value>ಸಮೀಕರಣಗಳು</value>
    <comment>Header that appears over the equations section when sharing</comment>
  </data>
  <data name="VariablesShareHeader" xml:space="preserve">
    <value>ವೇರಿಯೇಬಲ್‌ಗಳು</value>
    <comment>Header that appears over the variables section when sharing</comment>
  </data>
  <data name="GraphImageAltText" xml:space="preserve">
    <value>ಸಮೀಕರಣಗಳೊಂದಿಗೆ ಗ್ರಾಫ್ ಚಿತ್ರ</value>
    <comment>Alt text for the graph image when output via Share</comment>
  </data>
  <data name="VaiablesHeader.Text" xml:space="preserve">
    <value>ವೇರಿಯೇಬಲ್‌ಗಳು</value>
    <comment>Header text for variables area</comment>
  </data>
  <data name="StepTextBlock.Text" xml:space="preserve">
    <value>ಹಂತ</value>
    <comment>Label text for the step text box</comment>
  </data>
  <data name="MinTextBlock.Text" xml:space="preserve">
    <value>ಕನಿಷ್ಠ</value>
    <comment>Label text for the min text box</comment>
  </data>
  <data name="MaxTextBlock.Text" xml:space="preserve">
    <value>ಗರಿಷ್ಠ</value>
    <comment>Label text for the max text box</comment>
  </data>
  <data name="LineColorText.Text" xml:space="preserve">
    <value>ಬಣ್ಣ</value>
    <comment>Label for the Line Color section of the style picker</comment>
  </data>
  <data name="StyleChooserBoxHeading.Text" xml:space="preserve">
    <value>ಶೈಲಿ</value>
    <comment>Label for the Line Style section of the style picker</comment>
  </data>
  <data name="KeyGraphFeaturesLabel.Text" xml:space="preserve">
    <value>ಕಾರ್ಯ ವಿಶ್ಲೇಷಣೆ</value>
    <comment>Title for KeyGraphFeatures Control</comment>
  </data>
  <data name="KGFHorizontalAsymptotesNone" xml:space="preserve">
    <value>ಫಂಕ್ಷನ್ ಯಾವುದೇ ಅಡ್ಡ ಅಸಂಪಾತಗಳನ್ನು ಹೊಂದಿಲ್ಲ.</value>
    <comment>Message displayed when the graph does not have any horizontal asymptotes</comment>
  </data>
  <data name="KGFInflectionPointsNone" xml:space="preserve">
    <value>ಫಂಕ್ಷನ್ ಯಾವುದೇ ಇನ್ಫ್ಲೆಕ್ಷನ್ ಪಾಯಿಂಟ್‌ಗಳನ್ನು ಹೊಂದಿಲ್ಲ.</value>
    <comment>Message displayed when the graph does not have any inflection points</comment>
  </data>
  <data name="KGFMaximaNone" xml:space="preserve">
    <value>ಫಂಕ್ಷನ್ ಯಾವುದೇ ಗರಿಷ್ಠ ಪಾಯಿಂಟ್‌ಗಳನ್ನು ಹೊಂದಿಲ್ಲ.</value>
    <comment>Message displayed when the graph does not have any maxima</comment>
  </data>
  <data name="KGFMinimaNone" xml:space="preserve">
    <value>ಫಂಕ್ಷನ್ ಯಾವುದೇ ಕನಿಷ್ಠ ಪಾಯಿಂಟ್‌ಗಳನ್ನು ಹೊಂದಿಲ್ಲ.</value>
    <comment>Message displayed when the graph does not have any minima</comment>
  </data>
  <data name="KGFMonotonicityConstant" xml:space="preserve">
    <value>ನಿರಂತರ</value>
    <comment>String describing constant monotonicity of a function</comment>
  </data>
  <data name="KGFMonotonicityDecreasing" xml:space="preserve">
    <value>ಕಡಿಮೆಯಾಗುತ್ತಿದೆ</value>
    <comment>String describing decreasing monotonicity of a function</comment>
  </data>
  <data name="KGFMonotonicityError" xml:space="preserve">
    <value>ಫಂಕ್ಷನ್‌ನ ಏಕತಾನತೆಯನ್ನು ನಿರ್ಧರಿಸಲು ಸಾಧ್ಯವಿಲ್ಲ.</value>
    <comment>Error displayed when monotonicity cannot be determined</comment>
  </data>
  <data name="KGFMonotonicityIncreasing" xml:space="preserve">
    <value>ಹೆಚ್ಚುತ್ತಿದೆ</value>
    <comment>String describing increasing monotonicity of a function</comment>
  </data>
  <data name="KGFMonotonicityUnknown" xml:space="preserve">
    <value>ಫಂಕ್ಷನ್‌ನ ಏಕತಾನತೆ ಅಜ್ಞಾತವಾಗಿದೆ.</value>
    <comment>Error displayed when monotonicity is unknown</comment>
  </data>
  <data name="KGFObliqueAsymptotesNone" xml:space="preserve">
    <value>ಫಂಕ್ಷನ್ ಯಾವುದೇ ಓರೆಯಾದ ಅಸಂಪಾತಗಳನ್ನು ಹೊಂದಿಲ್ಲ.</value>
    <comment>Message displayed when the graph does not have any oblique asymptotes</comment>
  </data>
  <data name="KGFParityError" xml:space="preserve">
    <value>ಫಂಕ್ಷನ್‌ನ ಸಮಾನತೆಯನ್ನು ನಿರ್ಧರಿಸಲು ಸಾಧ್ಯವಿಲ್ಲ.</value>
    <comment>Error displayed when parity is cannot be determined</comment>
  </data>
  <data name="KGFParityEven" xml:space="preserve">
    <value>ಫಂಕ್ಷನ್ ಸಮವಾಗಿದೆ.</value>
    <comment>Message displayed with the function parity is even</comment>
  </data>
  <data name="KGFParityNeither" xml:space="preserve">
    <value>ಫಂಕ್ಷನ್ ಸಮ ಅಥವಾ ಬೆಸ ಆಗಿಲ್ಲ.</value>
    <comment>Message displayed with the function parity is neither even nor odd</comment>
  </data>
  <data name="KGFParityOdd" xml:space="preserve">
    <value>ಫಂಕ್ಷನ್ ಬೆಸವಾಗಿದೆ.</value>
    <comment>Message displayed with the function parity is odd</comment>
  </data>
  <data name="KGFParityUnknown" xml:space="preserve">
    <value>ಫಂಕ್ಷನ್‌ನ ಸಮಾನತೆ ಅಜ್ಞಾತವಾಗಿದೆ.</value>
    <comment>Error displayed when parity is unknown</comment>
  </data>
  <data name="KGFPeriodicityError" xml:space="preserve">
    <value>ಈ ಫಂಕ್ಷನ್‌ಗಾಗಿ ಆವರ್ತಕತೆ ಬೆಂಬಲಿತವಾಗಿಲ್ಲ.</value>
    <comment>Error displayed when periodicity is not supported</comment>
  </data>
  <data name="KGFPeriodicityNotPeriodic" xml:space="preserve">
    <value>ಫಂಕ್ಷನ್ ಆವರ್ತಕವಲ್ಲ.</value>
    <comment>Message displayed with the function periodicity is not periodic</comment>
  </data>
  <data name="KGFPeriodicityUnknown" xml:space="preserve">
    <value>ಫಂಕ್ಷನ್ ಆವರ್ತಕತೆ ಅಜ್ಞಾತವಾಗಿದೆ.</value>
    <comment>Message displayed with the function periodicity is unknown</comment>
  </data>
  <data name="KGFTooComplexFeaturesError" xml:space="preserve">
    <value>ಈ ವೈಶಿಷ್ಟ್ಯಗಳು ಲೆಕ್ಕಾಚಾರ ಮಾಡಲು ಕ್ಯಾಲ್ಕುಲೇಟರ್‌ಗೆ ತುಂಬಾ ಸಂಕೀರ್ಣವಾಗಿವೆ:</value>
    <comment>Error displayed when analysis features cannot be calculated</comment>
  </data>
  <data name="KGFVerticalAsymptotesNone" xml:space="preserve">
    <value>ಫಂಕ್ಷನ್ ಯಾವುದೇ ಲಂಬ ಅಸಂಪಾತಗಳನ್ನು ಹೊಂದಿಲ್ಲ.</value>
    <comment>Message displayed when the graph does not have any vertical asymptotes</comment>
  </data>
  <data name="KGFXInterceptNone" xml:space="preserve">
    <value>ಫಂಕ್ಷನ್ ಯಾವುದೇ x-ಪ್ರತಿಬಂಧಗಳನ್ನು ಹೊಂದಿಲ್ಲ.</value>
    <comment>Message displayed when the graph does not have any x-intercepts</comment>
  </data>
  <data name="KGFYInterceptNone" xml:space="preserve">
    <value>ಫಂಕ್ಷನ್ ಯಾವುದೇ y-ಪ್ರತಿಬಂಧಗಳನ್ನು ಹೊಂದಿಲ್ಲ.</value>
    <comment>Message displayed when the graph does not have any y-intercepts</comment>
  </data>
  <data name="Domain" xml:space="preserve">
    <value>ಡೊಮೇನ್</value>
    <comment>Title for KeyGraphFeatures Domain Property</comment>
  </data>
  <data name="HorizontalAsymptotes" xml:space="preserve">
    <value>ಅಡ್ಡ ಅಸಂಪಾತಗಳು</value>
    <comment>Title for KeyGraphFeatures Horizontal aysmptotes Property</comment>
  </data>
  <data name="InflectionPoints" xml:space="preserve">
    <value>ರೂಪನಿಷ್ಪತ್ತಿ ಬಿಂದುಗಳು</value>
    <comment>Title for KeyGraphFeatures Inflection points Property</comment>
  </data>
  <data name="KGFAnalysisNotSupported" xml:space="preserve">
    <value>ಈ ಫಂಕ್ಷನ್‌ಗಾಗಿ ವಿಶ್ಲೇಷಣೆಯನ್ನು ಬೆಂಬಲಿತವಾಗಿಲ್ಲ.</value>
    <comment>Error displayed when graph analysis is not supported or had an error.</comment>
  </data>
  <data name="KGFVariableIsNotX" xml:space="preserve">
    <value>ವಿಶ್ಲೇಷಣೆಯನ್ನು f(x) ಸ್ವರೂಪದಲ್ಲಿ ಕಾರ್ಯಗಳಿಗೆ ಮಾತ್ರ ಬೆಂಬಲಿಸಲಾಗಿದೆ. ಉದಾಹರಣೆ: y=x</value>
    <comment>Error displayed when graph analysis detects the function format is not f(x).</comment>
  </data>
  <data name="Maxima" xml:space="preserve">
    <value>ಗರಿಷ್ಠ</value>
    <comment>Title for KeyGraphFeatures Maxima Property</comment>
  </data>
  <data name="Minima" xml:space="preserve">
    <value>ಕನಿಷ್ಠ</value>
    <comment>Title for KeyGraphFeatures Minima Property</comment>
  </data>
  <data name="Monotonicity" xml:space="preserve">
    <value>ಏಕತಾನತೆ</value>
    <comment>Title for KeyGraphFeatures Monotonicity Property</comment>
  </data>
  <data name="ObliqueAsymptotes" xml:space="preserve">
    <value>ಓರೆಯಾದ ಅಸಂಪಾತಗಳು</value>
    <comment>Title for KeyGraphFeatures Oblique asymptotes Property</comment>
  </data>
  <data name="Parity" xml:space="preserve">
    <value>ಸಮಾನತೆ</value>
    <comment>Title for KeyGraphFeatures Parity Property</comment>
  </data>
  <data name="Periodicity" xml:space="preserve">
    <value>ಅವಧಿ</value>
    <comment>Title for KeyGraphFeatures Periodicity Property. The period of a mathematical function is the smallest interval in its input values such that its output values repeat every such interval.</comment>
  </data>
  <data name="Range" xml:space="preserve">
    <value>ವ್ಯಾಪ್ತಿ</value>
    <comment>Title for KeyGraphFeatures Range Property</comment>
  </data>
  <data name="VerticalAsymptotes" xml:space="preserve">
    <value>ಲಂಬ ಅಸಂಪಾತಗಳು</value>
    <comment>Title for KeyGraphFeatures Vertical asymptotes Property</comment>
  </data>
  <data name="XIntercept" xml:space="preserve">
    <value>X-ಪ್ರತಿಬಂಧ</value>
    <comment>Title for KeyGraphFeatures XIntercept Property</comment>
  </data>
  <data name="YIntercept" xml:space="preserve">
    <value>Y-ಪ್ರತಿಬಂಧ</value>
    <comment>Title for KeyGraphFeatures YIntercept Property</comment>
  </data>
  <data name="KGFAnalysisCouldNotBePerformed" xml:space="preserve">
    <value>ಫಂಕ್ಷನ್‌ಗಾಗಿ ವಿಶ್ಲೇಷಣೆ ನಡೆಸಲು ಸಾಧ್ಯವಾಗಲಿಲ್ಲ.</value>
  </data>
  <data name="KGFDomainNone" xml:space="preserve">
    <value>ಈ ಫಂಕ್ಷನ್‌ಗಾಗಿ ಡೊಮೇನ್ ಅನ್ನು ಲೆಕ್ಕಹಾಕಲು ಸಾಧ್ಯವಿಲ್ಲ.</value>
    <comment>Error displayed when Domain is not returned from the analyzer.</comment>
  </data>
  <data name="KGFRangeNone" xml:space="preserve">
    <value>ಈ ಫಂಕ್ಷನ್‌ಗಾಗಿ ಶ್ರೇಣಿಯನ್ನು ಲೆಕ್ಕಹಾಕಲು ಸಾಧ್ಯವಿಲ್ಲ.</value>
    <comment>Error displayed when Range is not returned from the analyzer.</comment>
  </data>
  <data name="Overflow" xml:space="preserve">
    <value>ಅತ್ಯಾಧಿಕ (ಸಂಖ್ಯೆ ತುಂಬಾ ದೊಡ್ಡದಿದೆ)</value>
    <comment>Error that occurs during graphing when the number is too large. To see this error, assign a large number to variable a, then keep doing "a:=a*a" until it happens.</comment>
  </data>
  <data name="RequireRadiansMode" xml:space="preserve">
    <value>ಈ ಸಮೀಕರಣದ ಗ್ರಾಫ್‌ ಮಾಡಲು ರೇಡಿಯನ್ಸ್ ಮೋಡ್ ಅಗತ್ಯವಿದ್ದೆ.</value>
    <comment>Error that occurs during graphing when radians is required.</comment>
  </data>
  <data name="TooComplexToSolve" xml:space="preserve">
    <value>ಈ ಫಲನವು ಗ್ರಾಫ್ ರಚಿಸಲು ತುಂಬಾ ಸಂಕೀರ್ಣವಾಗಿದೆ</value>
    <comment>Error that occurs during graphing when the equation is too complex.</comment>
  </data>
  <data name="RequireDegreesMode" xml:space="preserve">
    <value>ಈ ಸಮೀಕರಣದ ಗ್ರಾಫ್‌ ತಯಾರಿಸಲು ಡಿಗ್ರೀ ಮೋಡ್‌ನ ಅಗತ್ಯವಿದ್ದೆ</value>
    <comment>Error that occurs during graphing when degrees is required</comment>
  </data>
  <data name="FactorialInvalidArgument" xml:space="preserve">
    <value>ಅಪವರ್ತನೀಯ ಫಲನವು ಒಂದು ಅಮಾನ್ಯ ಚರಪರಿಮಾಣವನ್ನು ಹೊಂದಿದೆ</value>
    <comment>Error that occurs during graphing when a factorial function has an invalid argument.</comment>
  </data>
  <data name="FactorialCannotPerformOnLargeNumber" xml:space="preserve">
    <value>ಅಪವರ್ತನೀಯ ಫಲನವು ಗ್ರಾಫ್ ರಚಿಸಲು ಅಗತ್ಯವಿರುವುದಕ್ಕಿಂತಲೂ ತುಂಬಾ ದೊಡ್ಡದಾದ ಒಂದು ಚರಪರಿಮಾಣವನ್ನು ಹೊಂದಿದೆ</value>
    <comment>Error that occurs during graphing when a factorial has a large n</comment>
  </data>
  <data name="ModuloCannotPerformOnFloat" xml:space="preserve">
    <value>ಮಾಡ್ಯುಲೋ ಅನ್ನು ಪೂರ್ಣ ಸಂಖ್ಯೆಗಳೊಂದಿಗೆ ಮಾತ್ರ ಬಳಸಬಹುದಾಗಿದೆ</value>
    <comment>Error that occurs during graphing when modulo is used with a float.</comment>
  </data>
  <data name="EquationHasNoSolution" xml:space="preserve">
    <value>ಸಮೀಕರಣವು ಯಾವುದೇ ಪರಿಹಾರವನ್ನು ಹೊಂದಿಲ್ಲ</value>
    <comment>Error that occurs during graphing when the equation has no solution.</comment>
  </data>
  <data name="DivideByZero" xml:space="preserve">
    <value>ಸೊನ್ನೆಯಿಂದ ವಿಭಾಗಿಸಲಾಗುವುದಿಲ್ಲ</value>
    <comment>Error that occurs during graphing when a divison by zero occurs.</comment>
  </data>
  <data name="MutuallyExclusiveConditions" xml:space="preserve">
    <value>ಈ ಸಮೀಕರಣವು ಪರಸ್ಪರ ವ್ಯಾವರ್ತಕವಾಗಿರುವ ತಾರ್ಕಿಕ ಪರಿಸ್ಥಿತಿಗಳನ್ನು ಹೊಂದಿದೆ</value>
    <comment>Error that occurs during graphing when mutually exclusive conditions are used.</comment>
  </data>
  <data name="OutOfDomain" xml:space="preserve">
    <value>ಸಮೀಕರಣ ಜ್ಞಾನ ನೆಲೆಯ ಹೊರಗಿನದ್ದಾಗಿದೆ</value>
    <comment>Error that occurs during graphing when the equation is out of domain.</comment>
  </data>
  <data name="GE_NotSupported" xml:space="preserve">
    <value>ಈ ಸಮೀಕರಣದ ಗ್ರಾಫಿಂಗ್‌ಗೆ ಬೆಂಬಲ ಇಲ್ಲ</value>
    <comment>Error that occurs during graphing when the equation is not supported.</comment>
  </data>
  <data name="ParenthesisMismatch" xml:space="preserve">
    <value>ಸಮೀಕರಣದಲ್ಲಿ ಆರಂಭಿಕ ಪ್ರಕ್ಷೇಪ ಚಿಹ್ನೆಯು ಕಾಣೆಯಾಗಿದೆ</value>
    <comment>Error that occurs during graphing when the equation is missing a (</comment>
  </data>
  <data name="UnmatchedParenthesis" xml:space="preserve">
    <value>ಸಮೀಕರಣದಲ್ಲಿ ಮುಚ್ಚುವ ಪ್ರಕ್ಷೇಪ ಚಿಹ್ನೆಯು ಕಾಣೆಯಾಗಿದೆ</value>
    <comment>Error that occurs during graphing when the equation is missing a )</comment>
  </data>
  <data name="TooManyDecimalPoints" xml:space="preserve">
    <value>ಒಂದು ಸಂಖ್ಯೆಯಲ್ಲಿ ಹಲವಾರು ದಶಮಾಂಶ ಬಿಂದುಗಳಿವೆ</value>
    <comment>Error that occurs during graphing when a number has too many decimals. Ex: 1.2.3</comment>
  </data>
  <data name="DecimalPointWithoutDigits" xml:space="preserve">
    <value>ಒಂದು ದಶಮಾಂಶ ಬಿಂದುವಿನಲ್ಲಿ ಅಂಕೆಗಳು ಕಾಣೆಯಾಗಿವೆ</value>
    <comment>Error that occurs during graphing with a decimal point without digits</comment>
  </data>
  <data name="UnexpectedEndOfExpression" xml:space="preserve">
    <value>ಅಭಿವ್ಯಕ್ತಿಯ ಅನಿರೀಕ್ಷಿತ ಅಂತ್ಯ</value>
    <comment>Error that occurs during graphing when the expression ends unexpectedly. Ex: 3-4*</comment>
  </data>
  <data name="UnexpectedToken" xml:space="preserve">
    <value>ಅಭಿವ್ಯಕ್ತಿಯಲ್ಲಿ ಅನಪೇಕ್ಷಿತ ಅಕ್ಷರಗಳಿರುವವು</value>
    <comment>Error that occurs during graphing when there is an unexpected token.</comment>
  </data>
  <data name="InvalidToken" xml:space="preserve">
    <value>ಅಭಿವ್ಯಕ್ತಿಯಲ್ಲಿ ಅಮಾನ್ಯವಾದ ಅಕ್ಷರಗಳಿರುವವು</value>
    <comment>Error that occurs during graphing when there is an invalid token.</comment>
  </data>
  <data name="TooManyEquals" xml:space="preserve">
    <value>ಹಲವಾರು ಸಮಾನ ಚಿಹ್ನೆಗಳು ಇರುವವು</value>
    <comment>Error that occurs during graphing when there are too many equals.</comment>
  </data>
  <data name="EqualWithoutGraphVariable" xml:space="preserve">
    <value>ಫಲನವು ಕನಿಷ್ಟ ಒಂದು x ಅಥವಾ y ಚರವನ್ನು ಹೊಂದಿರತಕ್ಕದ್ದು</value>
    <comment>Error that occurs during graphing when the equation is missing x or y.</comment>
  </data>
  <data name="InvalidEquationSyntax" xml:space="preserve">
    <value>ಅಮಾನ್ಯವಾದ ಅಭಿವ್ಯಕ್ತಿ</value>
    <comment>Error that occurs during graphing when an invalid syntax is used.</comment>
  </data>
  <data name="EmptyExpression" xml:space="preserve">
    <value>ಅಭಿವ್ಯಕ್ತಿಯು ಖಾಲಿ ಇದೆ</value>
    <comment>Error that occurs during graphing when the expression is empty</comment>
  </data>
  <data name="EqualWithoutEquation" xml:space="preserve">
    <value>ಸಮೀಕರಣವಿಲ್ಲದೆ ಸಮಾನ ಚಿಹ್ನೆಯನ್ನು ಬಳಸಲಾಗಿತ್ತು</value>
    <comment>Error that occurs during graphing when equal is used without an equation. Ex: sin(x=y)</comment>
  </data>
  <data name="ExpectParenthesisAfterFunctionName" xml:space="preserve">
    <value>ಫಲನದ ಹೆಸರಿನ ನಂತರ ಪ್ರಕ್ಷೇಪ ಚಿಹ್ನೆಗಳು ಕಾಣೆಯಾಗಿರುವವು</value>
    <comment>Error that occurs during graphing when parenthesis are missing after a function.</comment>
  </data>
  <data name="IncorrectNumParameter" xml:space="preserve">
    <value>ಗಣಿತೀಯ ಕಾರ್ಯಾಚರಣೆಯು ತಪ್ಪಾದ ಸಂಖ್ಯೆಯ ನಿಯತಾಂಕಗಳನ್ನು ಹೊಂದಿದೆ</value>
    <comment>Error that occurs during graphing when a function has the wrong number of parameters</comment>
  </data>
  <data name="InvalidVariableNameFormat" xml:space="preserve">
    <value>ಒಂದು ಚರದ ಹೆಸರು ಅಮಾನ್ಯವಾಗಿದೆ</value>
    <comment>Error that occurs during graphing when a variable name is invalid.</comment>
  </data>
  <data name="BracketMismatch" xml:space="preserve">
    <value>ಸಮೀಕರಣದಲ್ಲಿ ಆರಂಭಿಕ ಆವರಣವು ಕಾಣೆಯಾಗಿದೆ</value>
    <comment>Error that occurs during graphing when a { is missing</comment>
  </data>
  <data name="UnmatchedBracket" xml:space="preserve">
    <value>ಸಮೀಕರಣದಲ್ಲಿ ಮುಚ್ಚುವ ಆವರಣವು ಕಾಣೆಯಾಗಿದೆ</value>
    <comment>Error that occurs during graphing when a } is missing.</comment>
  </data>
  <data name="CannotUseIInReal" xml:space="preserve">
    <value>"i" ಮತ್ತು "I" ಗಳನ್ನು ಚರ ಹೆಸರುಗಳನ್ನಾಗಿ ಬಳಸುವಂತಿಲ್ಲ</value>
    <comment>Error that occurs during graphing when i or I is used.</comment>
  </data>
  <data name="GeneralError" xml:space="preserve">
    <value>ಈ ಸಮೀಕರಣದ ಗ್ರಾಫ್‌ ರಚಿಸಲು ಸಾಧ್ಯವಾಗಲಿಲ್ಲ</value>
    <comment>General error that occurs during graphing.</comment>
  </data>
  <data name="InvalidNumberDigit" xml:space="preserve">
    <value>ನೀಡಲಾದ ಮೂಲ ಸಂಖ್ಯೆಗೆ ಅಂಕೆಯನ್ನು ಪರಿಹರಿಸಲಾಗಲಿಲ್ಲ</value>
    <comment>Error that occurs during graphing when trying to use bases incorrect. Ex: base(2,1020).</comment>
  </data>
  <data name="InvalidNumberBase" xml:space="preserve">
    <value>ಮೂಲ ಸಂಖ್ಯೆಯು 2 ಕ್ಕಿಂತ ಹೆಚ್ಚಾಗಿರಬೇಕು ಮತ್ತು 36 ಕ್ಕಿಂತ ಕಡಿಮೆ ಇರಬೇಕು</value>
    <comment>Error that occurs during graphing when the base is out of range.</comment>
  </data>
  <data name="InvalidVariableSpecification" xml:space="preserve">
    <value>ಗಣಿತೀಯ ಕಾರ್ಯಾಚರಣೆಗಾಗಿ ಅದರ ಒಂದು ಪರಮಾಂಶವು ಒಂದು ವೇರಿಯಬಲ್ ಆಗಿರಬೇಕಾಗುತ್ತದೆ</value>
    <comment>Error that occurs during graphing when a function requires a variable in a particular position. Ex: 2nd argument of deriv.</comment>
  </data>
  <data name="ExpectingLogicalOperands" xml:space="preserve">
    <value>ಸಮೀಕರಣವು ತಾರ್ಕಿಕ ಮತ್ತು ಸದಿಶ ಪರಿಕರ್ಮ್ಯಗಳನ್ನು ಮಿಶ್ರಣ ಮಾಡುತ್ತಿದೆ</value>
    <comment>Error that occurs during graphing when operands are mixed. Such as true and 1.</comment>
  </data>
  <data name="CannotUseIndexVarInOpLimits" xml:space="preserve">
    <value>x ಅಥವಾ y ಅನ್ನು ಮೇಲಿನ ಅಥವಾ ಕೆಳಗಿನ ಮಿತಿಗಳಲ್ಲಿ ಬಳಸಲಾಗುವುದಿಲ್ಲ</value>
    <comment>Error that occurs during graphing when x or y is used in integral upper limits.</comment>
  </data>
  <data name="CannotUseIndexVarInLimPoint" xml:space="preserve">
    <value>x ಅಥವಾ y ಅನ್ನು ಮಿತಿಯ ಬಿಂದುವಿನಲ್ಲಿ ಬಳಸಲಾಗುವುದಿಲ್ಲ</value>
    <comment>Error that occurs during graphing when x or y is used in the limit point.</comment>
  </data>
  <data name="CannotUseComplexInfinityInReal" xml:space="preserve">
    <value>ಸಂಕೀರ್ಣ ಅನಂತತೆಯನ್ನು ಬಳಸುವಂತಿಲ್ಲ</value>
    <comment>Error that occurs during graphing when complex infinity is used</comment>
  </data>
  <data name="CannotUseIInInequalitySolving" xml:space="preserve">
    <value>ಅಸಮಾನತೆಗಳಲ್ಲಿ ಸಂಕೀರ್ಣ ಸಂಖ್ಯೆಗಳನ್ನು ಬಳಸುವಂತಿಲ್ಲ</value>
    <comment>Error that occurs during graphing when complex numbers are used in inequalities.</comment>
  </data>
  <data name="equationAnalysisBack.[using:Windows.UI.Xaml.Controls]ToolTipService.ToolTip" xml:space="preserve">
    <value>ಕಾರ್ಯದ ಪಟ್ಟಿಗೆ ಹಿಂತಿರುಗಿ</value>
    <comment>This is the tooltip for the back button in the equation analysis page in the graphing calculator</comment>
  </data>
  <data name="equationAnalysisBack.[using:Windows.UI.Xaml.Automation]AutomationProperties.Name" xml:space="preserve">
    <value>ಕಾರ್ಯದ ಪಟ್ಟಿಗೆ ಹಿಂತಿರುಗಿ</value>
    <comment>This is the automation name for the back button in the equation analysis page in the graphing calculator</comment>
  </data>
  <data name="functionAnalysisButton.[using:Windows.UI.Xaml.Controls]ToolTipService.ToolTip" xml:space="preserve">
    <value>ಕಾರ್ಯ ವಿಶ್ಲೇಷಿಸಿ</value>
    <comment>This is the tooltip for the analyze function button</comment>
  </data>
  <data name="functionAnalysisButton.[using:Windows.UI.Xaml.Automation]AutomationProperties.Name" xml:space="preserve">
    <value>ಕಾರ್ಯ ವಿಶ್ಲೇಷಿಸಿ</value>
    <comment>This is the automation name for the analyze function button</comment>
  </data>
  <data name="functionAnalysisMenuItem" xml:space="preserve">
    <value>ಕಾರ್ಯ ವಿಶ್ಲೇಷಿಸಿ</value>
    <comment>This is the text for the for the analyze function context menu command</comment>
  </data>
  <data name="removeButton.[using:Windows.UI.Xaml.Controls]ToolTipService.ToolTip" xml:space="preserve">
    <value>ಸಮೀಕರಣವನ್ನು ತೆಗೆದುಹಾಕಿ</value>
    <comment>This is the tooltip for the graphing calculator remove equation buttons</comment>
  </data>
  <data name="removeButton.[using:Windows.UI.Xaml.Automation]AutomationProperties.Name" xml:space="preserve">
    <value>ಸಮೀಕರಣವನ್ನು ತೆಗೆದುಹಾಕಿ</value>
    <comment>This is the automation name for the graphing calculator remove equation buttons</comment>
  </data>
  <data name="removeMenuItem" xml:space="preserve">
    <value>ಸಮೀಕರಣವನ್ನು ತೆಗೆದುಹಾಕಿ</value>
    <comment>This is the text for the for the remove equation context menu command</comment>
  </data>
  <data name="shareButton.[using:Windows.UI.Xaml.Controls]ToolTipService.ToolTip" xml:space="preserve">
    <value>ಹಂಚಿಕೊಳ್ಳಿ</value>
    <comment>This is the automation name for the graphing calculator share button.</comment>
  </data>
  <data name="shareButton.[using:Windows.UI.Xaml.Automation]AutomationProperties.Name" xml:space="preserve">
    <value>ಹಂಚಿಕೊಳ್ಳಿ</value>
    <comment>This is the tooltip for the graphing calculator share button.</comment>
  </data>
  <data name="colorChooserButton.[using:Windows.UI.Xaml.Controls]ToolTipService.ToolTip" xml:space="preserve">
    <value>ಸಮೀಕರಣದ ಶೈಲಿಯನ್ನು ಬದಲಾಯಿಸಿ</value>
    <comment>This is the tooltip for the graphing calculator equation style button</comment>
  </data>
  <data name="colorChooserButton.[using:Windows.UI.Xaml.Automation]AutomationProperties.Name" xml:space="preserve">
    <value>ಸಮೀಕರಣದ ಶೈಲಿಯನ್ನು ಬದಲಾಯಿಸಿ</value>
    <comment>This is the automation name for the graphing calculator equation style button</comment>
  </data>
  <data name="colorChooserMenuItem" xml:space="preserve">
    <value>ಸಮೀಕರಣದ ಶೈಲಿಯನ್ನು ಬದಲಾಯಿಸಿ</value>
    <comment>This is the text for the for the equation style context menu command</comment>
  </data>
  <data name="showEquationButtonToolTip" xml:space="preserve">
    <value>ಸಮೀಕರಣ ತೋರಿಸಿ</value>
    <comment>This is the tooltip/automation name shown when visibility is set to hidden in the graphing calculator.</comment>
  </data>
  <data name="hideEquationButtonToolTip" xml:space="preserve">
    <value>ಸಮೀಕರಣ ಮರೆಮಾಡಿ</value>
    <comment>This is the tooltip/automation name shown when visibility is set to visible in the graphing calculator.</comment>
  </data>
  <data name="showEquationButtonAutomationName" xml:space="preserve">
    <value>ಸಮೀಕರಣ ತೋರಿಸಿ %1</value>
    <comment>{Locked="%1"}, This is the tooltip/automation name shown when visibility is set to hidden in the graphing calculator. %1 is the equation number.</comment>
  </data>
  <data name="hideEquationButtonAutomationName" xml:space="preserve">
    <value>ಸಮೀಕರಣ ಮರೆಮಾಡಿ %1</value>
    <comment>{Locked="%1"}, This is the tooltip/automation name shown when visibility is set to visible in the graphing calculator. %1 is the equation number.</comment>
  </data>
  <data name="disableTracingButtonToolTip" xml:space="preserve">
    <value>ಪತ್ತೆಹಚ್ಚುತ್ತಿರುವುದನ್ನು ನಿಲ್ಲಿಸಿ</value>
    <comment>This is the tooltip/automation name for the graphing calculator stop tracing button</comment>
  </data>
  <data name="enableTracingButtonToolTip" xml:space="preserve">
    <value>ಪತ್ತೆಹಚ್ಚುತ್ತಿರುವುದನ್ನು ಪ್ರಾರಂಭಿಸಿ</value>
    <comment>This is the tooltip/automation name for the graphing calculator start tracing button</comment>
  </data>
  <data name="graphAutomationName" xml:space="preserve">
    <value>ಗ್ರಾಫ್ ವೀಕ್ಷಣೆ ವಿಂಡೋ, x- ಅಕ್ಷವು %1 ಮತ್ತು %2 ರಿಂದ ಸುತ್ತುವರಿಯಲ್ಪಟ್ಟಿದೆ, y- ಅಕ್ಷವು %3 ಮತ್ತು %4 ರಿಂದ ಸುತ್ತುವರೆದಿದೆ, %5 ಸಮೀಕರಣಗಳನ್ನು ಪ್ರದರ್ಶಿಸುತ್ತದೆ</value>
    <comment>{Locked="%1","%2", "%3", "%4", "%5"}. </comment>
  </data>
  <data name="sliderOptionsButton.[using:Windows.UI.Xaml.Controls]ToolTipService.ToolTip" xml:space="preserve">
    <value>ಸ್ಲೈಡರ್ ಅನ್ನು ಕಾನ್ಫಿಗರ್ ಮಾಡಿ</value>
    <comment>This is the tooltip text for the slider options button in Graphing Calculator</comment>
  </data>
  <data name="sliderOptionsButton.[using:Windows.UI.Xaml.Automation]AutomationProperties.Name" xml:space="preserve">
    <value>ಸ್ಲೈಡರ್ ಅನ್ನು ಕಾನ್ಫಿಗರ್ ಮಾಡಿ</value>
    <comment>This is the automation name text for the slider options button in Graphing Calculator</comment>
  </data>
  <data name="GraphSwitchToEquationMode" xml:space="preserve">
    <value>ಸಮೀಕರಣ ಮೋಡ್‌ಗೆ ಬದಲಿಸಿ</value>
    <comment>Used in Graphing Calculator to switch the view to the equation mode</comment>
  </data>
  <data name="GraphSwitchToGraphMode" xml:space="preserve">
    <value>ಗ್ರಾಫ್ ಮೋಡ್‌ಗೆ ಬದಲಿಸಿ</value>
    <comment>Used in Graphing Calculator to switch the view to the graph mode</comment>
  </data>
  <data name="SwitchModeToggleButton.[using:Windows.UI.Xaml.Automation]AutomationProperties.Name" xml:space="preserve">
    <value>ಸಮೀಕರಣ ಮೋಡ್‌ಗೆ ಬದಲಿಸಿ</value>
    <comment>Used in Graphing Calculator to switch the view to the equation mode</comment>
  </data>
  <data name="GraphSwitchedToEquationModeAnnouncement" xml:space="preserve">
    <value>ಪ್ರಸ್ತುತ ಮೋಡ್ ಸಮೀಕರಣ ಮೋಡ್ ಆಗಿದೆ</value>
    <comment>Announcement used in Graphing Calculator when switching to the equation mode</comment>
  </data>
  <data name="GraphSwitchedToGraphModeAnnouncement" xml:space="preserve">
    <value>ಪ್ರಸ್ತುತ ಮೋಡ್ ಗ್ರಾಫ್ ಮೋಡ್ ಆಗಿದೆ</value>
    <comment>Announcement used in Graphing Calculator when switching to the graph mode</comment>
  </data>
  <data name="GridHeading.Text" xml:space="preserve">
    <value>ವಿಂಡೋ</value>
    <comment>Heading for window extents on the settings </comment>
  </data>
  <data name="TrigModeDegrees.Content" xml:space="preserve">
    <value>ಡಿಗ್ರಿಗಳು</value>
    <comment>Degrees mode on settings page</comment>
  </data>
  <data name="TrigModeGradians.Content" xml:space="preserve">
    <value>ಗ್ರೇಡಿಯನ್‌ಗಳು</value>
    <comment>Gradian mode on settings page</comment>
  </data>
  <data name="TrigModeRadians.Content" xml:space="preserve">
    <value>ರೇಡಿಯನ್‌ಗಳು</value>
    <comment>Radians mode on settings page</comment>
  </data>
  <data name="UnitsHeading.Text" xml:space="preserve">
    <value>ಯುನಿಟ್‌ಗಳು</value>
    <comment>Heading for Unit's on the settings</comment>
  </data>
  <data name="ResetViewButton.Content" xml:space="preserve">
    <value>ಮರುಹೊಂದಿಸಿ ವೀಕ್ಷಣೆ</value>
    <comment>Hyperlink button to reset the view of the graph</comment>
  </data>
  <data name="GraphSettingsXMax.Header" xml:space="preserve">
    <value>X-ಗರಿಷ್ಠ</value>
    <comment>X maximum value header</comment>
  </data>
  <data name="GraphSettingsXMin.Header" xml:space="preserve">
    <value>X-ಕನಿಷ್ಠ</value>
    <comment>X minimum value header</comment>
  </data>
  <data name="GraphSettingsYMax.Header" xml:space="preserve">
    <value>Y-ಗರಿಷ್ಠ</value>
    <comment>Y Maximum value header</comment>
  </data>
  <data name="GraphSettingsYMin.Header" xml:space="preserve">
    <value>Y-ಕನಿಷ್ಠ</value>
    <comment>Y minimum value header</comment>
  </data>
  <data name="graphSettingsButton.[using:Windows.UI.Xaml.Controls]ToolTipService.ToolTip" xml:space="preserve">
    <value>ಗ್ರಾಫ್ ಆಯ್ಕೆಗಳು</value>
    <comment>This is the tooltip text for the graph options button in Graphing Calculator</comment>
  </data>
  <data name="graphSettingsButton.[using:Windows.UI.Xaml.Automation]AutomationProperties.Name" xml:space="preserve">
    <value>ಗ್ರಾಫ್ ಆಯ್ಕೆಗಳು</value>
    <comment>This is the automation name text for the graph options button in Graphing Calculator</comment>
  </data>
  <data name="GraphOptionsHeading.Text" xml:space="preserve">
    <value>ರೇಖಾನಕ್ಷೆ ಆಯ್ಕೆಗಳು</value>
    <comment>Heading for the Graph options flyout in Graphing mode.</comment>
  </data>
  <data name="VariableAreaSettings.[using:Windows.UI.Xaml.Automation]AutomationProperties.Name" xml:space="preserve">
    <value>ವೇರಿಯೇಬಲ್ ಆಯ್ಕೆಗಳು</value>
    <comment>Screen reader prompt for the variable settings toggle button</comment>
  </data>
  <data name="VariableAreaSettings.[using:Windows.UI.Xaml.Controls]ToolTipService.ToolTip" xml:space="preserve">
    <value>ವೇರಿಯಬಲ್ ಆಯ್ಕೆಗಳನ್ನು ಟಾಗಲ್ ಮಾಡಿ</value>
    <comment>Tool tip for the variable settings toggle button</comment>
  </data>
  <data name="LineThicknessBoxHeading.Text" xml:space="preserve">
    <value>ಸಾಲಿನ ದಪ್ಪ</value>
    <comment>Heading for the Graph options flyout in Graphing mode.</comment>
  </data>
  <data name="LineOptionsHeading.Text" xml:space="preserve">
    <value>ಸಾಲು ಆಯ್ಕೆಗಳು</value>
    <comment>Heading for the equation style flyout in Graphing mode.</comment>
  </data>
  <data name="SmallLineWidthAutomationName" xml:space="preserve">
    <value>ಚಿಕ್ಕ ಸಾಲಿನ ಅಗಲ</value>
    <comment>Automation name for line width setting</comment>
  </data>
  <data name="MediumLineWidthAutomationName" xml:space="preserve">
    <value>ಮಧ್ಯಮ ಸಾಲಿನ ಅಗಲ</value>
    <comment>Automation name for line width setting</comment>
  </data>
  <data name="LargeLineWidthAutomationName" xml:space="preserve">
    <value>ದೊಡ್ಡ ಸಾಲಿನ ಅಗಲ</value>
    <comment>Automation name for line width setting</comment>
  </data>
  <data name="ExtraLargeLineWidthAutomationName" xml:space="preserve">
    <value>ಹೆಚ್ಚು ದೊಡ್ಡ ಸಾಲಿನ ಅಗಲ</value>
    <comment>Automation name for line width setting</comment>
  </data>
  <data name="mathRichEditBox.PlaceholderText" xml:space="preserve">
    <value>ಅಭಿವ್ಯಕ್ತಿಯನ್ನು ನಮೂದಿಸಿ</value>
    <comment>this is the placeholder text used by the textbox to enter an equation</comment>
  </data>
  <data name="GraphCopyMenuItem.Text" xml:space="preserve">
    <value>ನಕಲಿಸಿ</value>
    <comment>Copy menu item for the graph context menu</comment>
  </data>
  <data name="cutEquationMenuItem.Text" xml:space="preserve">
    <value>ಕತ್ತರಿಸು</value>
    <comment>Cut menu item from the Equation TextBox</comment>
  </data>
  <data name="copyEquationMenuItem.Text" xml:space="preserve">
    <value>ನಕಲಿಸಿ</value>
    <comment>Copy menu item from the Equation TextBox</comment>
  </data>
  <data name="pasteEquationMenuItem.Text" xml:space="preserve">
    <value>ಅಂಟಿಸು</value>
    <comment>Paste menu item from the Equation TextBox</comment>
  </data>
  <data name="undoEquationMenuItem.Text" xml:space="preserve">
    <value>ರದ್ದುಮಾಡು</value>
    <comment>Undo menu item from the Equation TextBox</comment>
  </data>
  <data name="selectAllEquationMenuItem.Text" xml:space="preserve">
    <value>ಎಲ್ಲವನ್ನೂ ಆಯ್ಕೆಮಾಡಿ</value>
    <comment>Select all menu item from the Equation TextBox</comment>
  </data>
  <data name="EquationInputButton.[using:Windows.UI.Xaml.Automation]AutomationProperties.Name" xml:space="preserve">
    <value>ಕಾರ್ಯದ ಇನ್‌ಪುಟ್</value>
    <comment>The automation name for the Equation Input ListView item that is shown when Calculator is in graphing mode.</comment>
  </data>
  <data name="EquationInputList.[using:Windows.UI.Xaml.Automation]AutomationProperties.Name" xml:space="preserve">
    <value>ಕಾರ್ಯದ ಇನ್‌ಪುಟ್</value>
    <comment>The automation name for the Equation Input ListView that is shown when Calculator is in graphing mode.</comment>
  </data>
  <data name="EquationInputPanel.[using:Windows.UI.Xaml.Automation]AutomationProperties.Name" xml:space="preserve">
    <value>ಕಾರ್ಯದ ಇನ್‌ಪುಟ್ ಪ್ಯಾನಲ್</value>
    <comment>The automation name for the Equation Input StackPanel that is shown when Calculator is in graphing mode.</comment>
  </data>
  <data name="VariableStackPanel.[using:Windows.UI.Xaml.Automation]AutomationProperties.Name" xml:space="preserve">
    <value>ಬದಲಾಗಬಹುದಾದ ಪ್ಯಾನಲ್</value>
    <comment>The automation name for the Variable StackPanel that is shown when Calculator is in graphing mode.</comment>
  </data>
  <data name="VariableListView.[using:Windows.UI.Xaml.Automation]AutomationProperties.Name" xml:space="preserve">
    <value>ಬದಲಾಗಬಹುದಾದ ಪಟ್ಟಿ</value>
    <comment>The automation name for the Variable ListView that is shown when Calculator is in graphing mode.</comment>
  </data>
  <data name="VariableListViewItem" xml:space="preserve">
    <value>ಬದಲಾಗಬಹುದಾದ %1 ಪಟ್ಟಿಯ ಐಟಂ</value>
    <comment>The automation name for the Variable ListViewItem that is shown when Calculator is in graphing mode.</comment>
  </data>
  <data name="VariableValueTextBox.[using:Windows.UI.Xaml.Automation]AutomationProperties.Name" xml:space="preserve">
    <value>ಬದಲಾಗಬಹುದಾದ ಮೌಲ್ಯದ ಪಠ್ಯಪೆಟ್ಟಿಗೆ</value>
    <comment>The automation name for the Variable Value Textbox that is shown when Calculator is in graphing mode.</comment>
  </data>
  <data name="VariableValueSlider.[using:Windows.UI.Xaml.Automation]AutomationProperties.Name" xml:space="preserve">
    <value>ಬದಲಾಗಬಹುದಾದ ಮೌಲ್ಯದ ಸ್ಲೈಡರ್</value>
    <comment>The automation name for the Variable Value Slider that is shown when Calculator is in graphing mode.</comment>
  </data>
  <data name="VariableMinTextBox.[using:Windows.UI.Xaml.Automation]AutomationProperties.Name" xml:space="preserve">
    <value>ಬದಲಾಗಬಹುದಾದ ಕನಿಷ್ಠ ಮೌಲ್ಯದ ಪಠ್ಯಪೆಟ್ಟಿಗೆ</value>
    <comment>The automation name for the Variable Min Value Textbox that is shown when Calculator is in graphing mode.</comment>
  </data>
  <data name="VariableStepTextBox.[using:Windows.UI.Xaml.Automation]AutomationProperties.Name" xml:space="preserve">
    <value>ಬದಲಾಗಬಹುದಾದ ಹಂತ ಮೌಲ್ಯದ ಪಠ್ಯಪೆಟ್ಟಿಗೆ</value>
    <comment>The automation name for the Variable Step Textbox that is shown when Calculator is in graphing mode.</comment>
  </data>
  <data name="VariableMaxTextBox.[using:Windows.UI.Xaml.Automation]AutomationProperties.Name" xml:space="preserve">
    <value>ಬದಲಾಗಬಹುದಾದ ಗರಿಷ್ಠ ಮೌಲ್ಯದ ಪಠ್ಯಪೆಟ್ಟಿಗೆ</value>
    <comment>The automation name for the Variable Max Value Textbox that is shown when Calculator is in graphing mode.</comment>
  </data>
  <data name="solidLineStyleAutomationName" xml:space="preserve">
    <value>ಗಾಢಸಾಲಿನ ಶೈಲಿ</value>
    <comment>Name of the solid line style for a graphed equation</comment>
  </data>
  <data name="dotLineStyleAutomationName" xml:space="preserve">
    <value>ಬಿಂದು ಸಾಲಿನ ಶೈಲಿ</value>
    <comment>Name of the dotted line style for a graphed equation</comment>
  </data>
  <data name="dashLineStyleAutomationName" xml:space="preserve">
    <value>ಅಡ್ಡಸಾಲಿನ ಶೈಲಿ</value>
    <comment>Name of the dashed line style for a graphed equation</comment>
  </data>
  <data name="equationColor1AutomationName" xml:space="preserve">
    <value>ಗಾಢ ನೀಲಿ</value>
    <comment>Name of color in the color picker</comment>
  </data>
  <data name="equationColor2AutomationName" xml:space="preserve">
    <value>ಸೀಫೋಮ್</value>
    <comment>Name of color in the color picker</comment>
  </data>
  <data name="equationColor3AutomationName" xml:space="preserve">
    <value>ನೇರಿಳೆ</value>
    <comment>Name of color in the color picker</comment>
  </data>
  <data name="equationColor4AutomationName" xml:space="preserve">
    <value>ಹಸಿರು</value>
    <comment>Name of color in the color picker</comment>
  </data>
  <data name="equationColor5AutomationName" xml:space="preserve">
    <value>ಮಿಂಟ್ ಹಸಿರು</value>
    <comment>Name of color in the color picker</comment>
  </data>
  <data name="equationColor6AutomationName" xml:space="preserve">
    <value>ಗಾಢ ಹಸಿರು</value>
    <comment>Name of color in the color picker</comment>
  </data>
  <data name="equationColor7AutomationName" xml:space="preserve">
    <value>ಇದ್ದಿಲು</value>
    <comment>Name of color in the color picker</comment>
  </data>
  <data name="equationColor8AutomationName" xml:space="preserve">
    <value>ಕೆಂಪು</value>
    <comment>Name of color in the color picker</comment>
  </data>
  <data name="equationColor9AutomationName" xml:space="preserve">
    <value>ತಿಳಿ ಪ್ಲಮ್</value>
    <comment>Name of color in the color picker</comment>
  </data>
  <data name="equationColor10AutomationName" xml:space="preserve">
    <value>ಕೆನ್ನೇರಿಳೆ</value>
    <comment>Name of color in the color picker</comment>
  </data>
  <data name="equationColor11AutomationName" xml:space="preserve">
    <value>ಸ್ವರ್ಣ ಹಳದಿ </value>
    <comment>Name of color in the color picker</comment>
  </data>
  <data name="equationColor12AutomationName" xml:space="preserve">
    <value>ಪ್ರಕಾಶಮಾನವಾದ ಕಿತ್ತಳೆ ಬಣ್ಣ</value>
    <comment>Name of color in the color picker</comment>
  </data>
  <data name="equationColor13AutomationName" xml:space="preserve">
    <value>ಕಂದು</value>
    <comment>Name of color in the color picker</comment>
  </data>
  <data name="equationColor14BlackAutomationName" xml:space="preserve">
    <value>ಕಪ್ಪು</value>
    <comment>Name of color in the color picker</comment>
  </data>
  <data name="equationColor14WhiteAutomationName" xml:space="preserve">
    <value>ಬಿಳಿ</value>
    <comment>Name of color in the color picker</comment>
  </data>
  <data name="equationHighContrastColor1AutomationName" xml:space="preserve">
    <value>ಬಣ್ಣ 1</value>
    <comment>Name of color in the color picker</comment>
  </data>
  <data name="equationHighContrastColor2AutomationName" xml:space="preserve">
    <value>ಬಣ್ಣ 2</value>
    <comment>Name of color in the color picker</comment>
  </data>
  <data name="equationHighContrastColor3AutomationName" xml:space="preserve">
    <value>ಬಣ್ಣ 3</value>
    <comment>Name of color in the color picker</comment>
  </data>
  <data name="equationHighContrastColor4AutomationName" xml:space="preserve">
    <value>ಬಣ್ಣ 4</value>
    <comment>Name of color in the color picker</comment>
  </data>
  <data name="GraphThemeHeading.Text" xml:space="preserve">
    <value>ರೇಖಾನಕ್ಷೆಯ ಥೀಮ್</value>
    <comment>Graph settings heading for the theme options</comment>
  </data>
  <data name="AlwaysLightTheme.Content" xml:space="preserve">
    <value>ಸದಾ ಹಗುರ</value>
    <comment>Graph settings option to set graph to light theme</comment>
  </data>
  <data name="MatchAppTheme.Content" xml:space="preserve">
    <value>ಹೋಲಿಕೆ ಅಪ್ಲಿ ಥೀಮ್</value>
    <comment>Graph settings option to set graph to match the app theme</comment>
  </data>
  <data name="GraphThemeHeading.[using:Windows.UI.Xaml.Automation]AutomationProperties.Name" xml:space="preserve">
    <value>ಥೀಮ್</value>
    <comment>This is the automation name text for the Graph settings heading for the theme options</comment>
  </data>
  <data name="AlwaysLightTheme.[using:Windows.UI.Xaml.Automation]AutomationProperties.Name" xml:space="preserve">
    <value>ಸದಾ ಹಗುರ</value>
    <comment>This is the automation name text for the Graph settings option to set graph to light theme</comment>
  </data>
  <data name="MatchAppTheme.[using:Windows.UI.Xaml.Automation]AutomationProperties.Name" xml:space="preserve">
    <value>ಹೋಲಿಕೆ ಅಪ್ಲಿ ಥೀಮ್</value>
    <comment>This is the automation name text for the Graph settings option to set graph to match the app theme</comment>
  </data>
  <data name="FunctionRemovedAnnouncement" xml:space="preserve">
    <value>ಕಾರ್ಯವನ್ನು ತೆಗೆದುಹಾಕಲಾಗುತ್ತದೆ</value>
    <comment>Announcement used in Graphing Calculator when a function is removed from the function list</comment>
  </data>
  <data name="KGFEquationTextBox.[using:Windows.UI.Xaml.Automation]AutomationProperties.Name" xml:space="preserve">
    <value>ಕಾರ್ಯನಿರ್ವಹಣೆ ವಿಶ್ಲೇಷಣೆ ಸಮೀಕರಣ ಪೆಟ್ಟಿಗೆ</value>
    <comment>This is the automation name text for the equation box in the function analysis panel</comment>
  </data>
  <data name="graphingEqualButton.[using:Windows.UI.Xaml.Automation]AutomationProperties.Name" xml:space="preserve">
    <value>ಸಮ</value>
    <comment>Screen reader prompt for the equal button on the graphing calculator operator keypad</comment>
  </data>
  <data name="lessThanFlyoutButton.[using:Windows.UI.Xaml.Automation]AutomationProperties.Name" xml:space="preserve">
    <value>ಇದಕ್ಕೂ ಕಡಿಮೆ</value>
    <comment>Screen reader prompt for the Less than button</comment>
  </data>
  <data name="lessThanOrEqualFlyoutButton.[using:Windows.UI.Xaml.Automation]AutomationProperties.Name" xml:space="preserve">
    <value>ಇದಕ್ಕಿಂತ ಕಡಿಮೆ ಅಥವಾ ಸಮ</value>
    <comment>Screen reader prompt for the Less than or equal button</comment>
  </data>
  <data name="equalsFlyoutButton.[using:Windows.UI.Xaml.Automation]AutomationProperties.Name" xml:space="preserve">
    <value>ಸಮ</value>
    <comment>Screen reader prompt for the Equal button</comment>
  </data>
  <data name="greaterThanOrEqualFlyoutButton.[using:Windows.UI.Xaml.Automation]AutomationProperties.Name" xml:space="preserve">
    <value>ಇದಕ್ಕಿಂತ ಹೆಚ್ಚು ಅಥವಾ ಸಮ</value>
    <comment>Screen reader prompt for the Greater than or equal button</comment>
  </data>
  <data name="greaterThanFlyoutButton.[using:Windows.UI.Xaml.Automation]AutomationProperties.Name" xml:space="preserve">
    <value>ಇದಕ್ಕಿಂತ ಹೆಚ್ಚಿನದು</value>
    <comment>Screen reader prompt for the Greater than button</comment>
  </data>
  <data name="xButton.[using:Windows.UI.Xaml.Automation]AutomationProperties.Name" xml:space="preserve">
    <value>X</value>
    <comment>Screen reader prompt for the X button on the graphing calculator operator keypad</comment>
  </data>
  <data name="yButton.[using:Windows.UI.Xaml.Automation]AutomationProperties.Name" xml:space="preserve">
    <value>Y</value>
    <comment>Screen reader prompt for the Y button on the graphing calculator operator keypad</comment>
  </data>
  <data name="submitButton.[using:Windows.UI.Xaml.Automation]AutomationProperties.Name" xml:space="preserve">
    <value>ಸಲ್ಲಿಸು</value>
    <comment>Screen reader prompt for the submit button on the graphing calculator operator keypad</comment>
  </data>
  <data name="FunctionAnalysisGrid.[using:Windows.UI.Xaml.Automation]AutomationProperties.Name" xml:space="preserve">
    <value>ಕಾರ್ಯ ವಿಶ್ಲೇಷಣೆ</value>
    <comment>Screen reader prompt for the function analysis grid</comment>
  </data>
  <data name="GraphSettingsGrid.[using:Windows.UI.Xaml.Automation]AutomationProperties.Name" xml:space="preserve">
    <value>ರೇಖಾನಕ್ಷೆ ಆಯ್ಕೆಗಳು</value>
    <comment>Screen reader prompt for the graph options panel</comment>
  </data>
  <data name="DockPanel_HistoryMemoryLists" xml:space="preserve">
    <value>ಇತಿಹಾಸ ಮತ್ತು ಮೆಮೊರಿ ಪಟ್ಟಿಗಳು</value>
    <comment>Automation name for the group of controls for history and memory lists.</comment>
  </data>
  <data name="DockPanel_MemoryList" xml:space="preserve">
    <value>ಮೆಮೊರಿ ಪಟ್ಟಿ</value>
    <comment>Automation name for the group of controls for memory list.</comment>
  </data>
  <data name="Format_HistorySlotCleared" xml:space="preserve">
    <value>ಇತಿಹಾಸ ಸ್ಲಾಟ್ %1 ತೆರವುಗೊಳಿಸಲಾಗಿದೆ</value>
    <comment>{Locked='%1'} Formatting string for a Narrator announcement when the user clears a history slot. The %1 is the index of the history slot. For example, users might hear "History slot 2 cleared".</comment>
  </data>
  <data name="CalcAlwaysOnTop" xml:space="preserve">
    <value>ಯಾವಾಗಲೂ ಮೇಲ್ಭಾಗದಲ್ಲಿ ಕ್ಯಾಲ್ಕುಲೇಟರ್</value>
    <comment>Announcement to indicate calculator window is always shown on top.</comment>
  </data>
  <data name="CalcBackToFullView" xml:space="preserve">
    <value>ಕ್ಯಾಲ್ಕುಲೇಟರ್ ಮರಳಿ ಪೂರ್ಣ ವೀಕ್ಷಣೆಗೆ</value>
    <comment>Announcement to indicate calculator window is now back to full view.</comment>
  </data>
  <data name="arithmeticShiftButtonSelected" xml:space="preserve">
    <value>ಅಂಕಗಣಿತ ಶಿಫ್ಟ್ ಆಯ್ಕೆಮಾಡಲಾಗಿದೆ</value>
    <comment>Label for a radio button that toggles arithmetic shift behavior for the shift operations.</comment>
  </data>
  <data name="logicalShiftButtonSelected" xml:space="preserve">
    <value>ತಾರ್ಕಿಕ ಶಿಫ್ಟ್ ಆಯ್ಕೆಮಾಡಲಾಗಿದೆ</value>
    <comment>Label for a radio button that toggles logical shift behavior for the shift operations.</comment>
  </data>
  <data name="rotateCircularButtonSelected" xml:space="preserve">
    <value>ಆಯ್ಕೆಮಾಡಿದ ವೃತ್ತಾಕಾರದ ಶಿಫ್ಟ್ ತಿರುಗಿಸಿ</value>
    <comment>Label for a radio button that toggles rotate circular behavior for the shift operations.</comment>
  </data>
  <data name="rotateCarryShiftButtonSelected" xml:space="preserve">
    <value>ಆಯ್ಕೆಮಾಡಿದ ಕ್ಯಾರಿ ವೃತ್ತಾಕಾರದ ಶಿಫ್ಟ್ ಮೂಲಕ ತಿರುಗಿಸಿ</value>
    <comment>Label for a radio button that toggles rotate circular with carry behavior for the shift operations.</comment>
  </data>
  <data name="SettingsHeader.Text" xml:space="preserve">
    <value>ಸೆಟ್ಟಿಂಗ್‍ಗಳು</value>
    <comment>Header text of Settings page</comment>
  </data>
  <data name="SettingsAppearance.Text" xml:space="preserve">
    <value>ನೋಟ</value>
    <comment>Subtitle of appearance setting on Settings page</comment>
  </data>
  <data name="AppThemeExpander.Header" xml:space="preserve">
    <value>ಅಪ್ಲಿ ಥೀಮ್</value>
    <comment>Title of App theme expander</comment>
  </data>
  <data name="AppThemeExpander.Description" xml:space="preserve">
    <value>ಯಾವ ಅಪ್ಲಿ ಥೀಮ್ ಪ್ರದರ್ಶಿಸಬೇಕೆಂದು ಆಯ್ಕೆಮಾಡಿ</value>
    <comment>Description of App theme expander</comment>
  </data>
  <data name="LightThemeRadioButton.Content" xml:space="preserve">
    <value>ತಿಳಿ</value>
    <comment>Lable for light theme option</comment>
  </data>
  <data name="DarkThemeRadioButton.Content" xml:space="preserve">
    <value>ಗಾಢ</value>
    <comment>Lable for dark theme option</comment>
  </data>
  <data name="SystemThemeRadioButton.Content" xml:space="preserve">
    <value>ಸಿಸ್ಟಂ ಸೆಟ್ಟಿಂಗ್ ಅನ್ನು ಬಳಸಿ</value>
    <comment>Lable for the app theme option to use system setting</comment>
  </data>
  <data name="TitleBarBackButton.[using:Windows.UI.Xaml.Automation]AutomationProperties.Name" xml:space="preserve">
    <value>ಹಿಂದೆ</value>
    <comment>Screen reader prompt for the Back button in title bar to back to main page</comment>
  </data>
  <data name="SettingsPageOpenedAnnouncement" xml:space="preserve">
    <value>ಸೆಟ್ಟಿಂಗ್‌ಗಳ ಪುಟ</value>
    <comment>Announcement used when Settings page is opened</comment>
  </data>
  <data name="MathRichEditBox.[using:Windows.UI.Xaml.Automation]AutomationProperties.Name" xml:space="preserve">
    <value>ಲಭ್ಯವಿರುವ ಕ್ರಿಯೆಗಳಿಗೆ ಸಂದರ್ಭ ಮೆನು ತೆರೆಯಿರಿ</value>
    <comment>Screen reader prompt for the context menu of the expression box</comment>
  </data>
  <data name="ErrorButtonOk" xml:space="preserve">
    <value>ಸರಿ</value>
    <comment>The text of OK button to dismiss an error dialog.</comment>
  </data>
  <data name="SnapshotRestoreError" xml:space="preserve">
    <value>ಈ ಸ್ನ್ಯಾಪ್‌ಶಾಟ್ ಅನ್ನು ಪುನಃಸ್ಥಾಪಿಸಲು ಸಾಧ್ಯವಾಗಲಿಲ್ಲ.</value>
    <comment>The error message to notify user that restoring from snapshot has failed.</comment>
  </data>
</root>