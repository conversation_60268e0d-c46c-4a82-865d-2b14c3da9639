﻿<?xml version="1.0" encoding="utf-8"?>
<Project ToolsVersion="15.0" xmlns="http://schemas.microsoft.com/developer/msbuild/2003">
  <ItemGroup>
    <Filter Include="Common">
      <UniqueIdentifier>{05fb7833-4679-4430-bf21-808354e815bf}</UniqueIdentifier>
    </Filter>
    <Filter Include="Common\Automation">
      <UniqueIdentifier>{8f1ef587-e5ce-4fc2-b9b7-73326d5e779a}</UniqueIdentifier>
    </Filter>
    <Filter Include="DataLoaders">
      <UniqueIdentifier>{70216695-3d7b-451a-98e4-cacbea3ba0a6}</UniqueIdentifier>
    </Filter>
    <Filter Include="GraphingCalculator">
      <UniqueIdentifier>{9b94309f-6b9b-4cbb-8584-4273061cc432}</UniqueIdentifier>
    </Filter>
  </ItemGroup>
  <ItemGroup>
    <ClCompile Include="pch.cpp" />
    <ClCompile Include="Common\AppResourceProvider.cpp">
      <Filter>Common</Filter>
    </ClCompile>
    <ClCompile Include="Common\CalculatorButtonPressedEventArgs.cpp">
      <Filter>Common</Filter>
    </ClCompile>
    <ClCompile Include="Common\CalculatorDisplay.cpp">
      <Filter>Common</Filter>
    </ClCompile>
    <ClCompile Include="Common\CopyPasteManager.cpp">
      <Filter>Common</Filter>
    </ClCompile>
    <ClCompile Include="Common\DateCalculator.cpp">
      <Filter>Common</Filter>
    </ClCompile>
    <ClCompile Include="Common\EngineResourceProvider.cpp">
      <Filter>Common</Filter>
    </ClCompile>
    <ClCompile Include="Common\ExpressionCommandDeserializer.cpp">
      <Filter>Common</Filter>
    </ClCompile>
    <ClCompile Include="Common\ExpressionCommandSerializer.cpp">
      <Filter>Common</Filter>
    </ClCompile>
    <ClCompile Include="Common\LocalizationService.cpp">
      <Filter>Common</Filter>
    </ClCompile>
    <ClCompile Include="Common\NavCategory.cpp">
      <Filter>Common</Filter>
    </ClCompile>
    <ClCompile Include="Common\NetworkManager.cpp">
      <Filter>Common</Filter>
    </ClCompile>
    <ClCompile Include="Common\RadixType.cpp">
      <Filter>Common</Filter>
    </ClCompile>
    <ClCompile Include="Common\TraceLogger.cpp">
      <Filter>Common</Filter>
    </ClCompile>
    <ClCompile Include="Common\Utils.cpp">
      <Filter>Common</Filter>
    </ClCompile>
    <ClCompile Include="Common\Automation\NarratorAnnouncement.cpp">
      <Filter>Common\Automation</Filter>
    </ClCompile>
    <ClCompile Include="Common\Automation\NarratorNotifier.cpp">
      <Filter>Common\Automation</Filter>
    </ClCompile>
    <ClCompile Include="DataLoaders\CurrencyDataLoader.cpp">
      <Filter>DataLoaders</Filter>
    </ClCompile>
    <ClCompile Include="DataLoaders\CurrencyHttpClient.cpp">
      <Filter>DataLoaders</Filter>
    </ClCompile>
    <ClCompile Include="DataLoaders\UnitConverterDataLoader.cpp">
      <Filter>DataLoaders</Filter>
    </ClCompile>
    <ClCompile Include="GraphingCalculator\EquationViewModel.cpp">
      <Filter>GraphingCalculator</Filter>
    </ClCompile>
    <ClCompile Include="GraphingCalculator\GraphingCalculatorViewModel.cpp">
      <Filter>GraphingCalculator</Filter>
    </ClCompile>
    <ClCompile Include="GraphingCalculator\GraphingSettingsViewModel.cpp">
      <Filter>GraphingCalculator</Filter>
    </ClCompile>
    <ClCompile Include="DateCalculatorViewModel.cpp" />
    <ClCompile Include="HistoryItemViewModel.cpp" />
    <ClCompile Include="HistoryViewModel.cpp" />
    <ClCompile Include="MemoryItemViewModel.cpp" />
    <ClCompile Include="StandardCalculatorViewModel.cpp" />
    <ClCompile Include="UnitConverterViewModel.cpp" />
    <ClCompile Include="Snapshots.cpp" />
  </ItemGroup>
  <ItemGroup>
    <ClInclude Include="pch.h" />
    <ClInclude Include="Common\AppResourceProvider.h">
      <Filter>Common</Filter>
    </ClInclude>
    <ClInclude Include="Common\BitLength.h">
      <Filter>Common</Filter>
    </ClInclude>
    <ClInclude Include="Common\CalculatorButtonPressedEventArgs.h">
      <Filter>Common</Filter>
    </ClInclude>
    <ClInclude Include="Common\CalculatorButtonUser.h">
      <Filter>Common</Filter>
    </ClInclude>
    <ClInclude Include="Common\CalculatorDisplay.h">
      <Filter>Common</Filter>
    </ClInclude>
    <ClInclude Include="Common\CopyPasteManager.h">
      <Filter>Common</Filter>
    </ClInclude>
    <ClInclude Include="Common\DateCalculator.h">
      <Filter>Common</Filter>
    </ClInclude>
    <ClInclude Include="Common\DelegateCommand.h">
      <Filter>Common</Filter>
    </ClInclude>
    <ClInclude Include="Common\DisplayExpressionToken.h">
      <Filter>Common</Filter>
    </ClInclude>
    <ClInclude Include="Common\EngineResourceProvider.h">
      <Filter>Common</Filter>
    </ClInclude>
    <ClInclude Include="Common\ExpressionCommandDeserializer.h">
      <Filter>Common</Filter>
    </ClInclude>
    <ClInclude Include="Common\ExpressionCommandSerializer.h">
      <Filter>Common</Filter>
    </ClInclude>
    <ClInclude Include="Common\LocalizationService.h">
      <Filter>Common</Filter>
    </ClInclude>
    <ClInclude Include="Common\LocalizationSettings.h">
      <Filter>Common</Filter>
    </ClInclude>
    <ClInclude Include="Common\LocalizationStringUtil.h">
      <Filter>Common</Filter>
    </ClInclude>
    <ClInclude Include="Common\MyVirtualKey.h">
      <Filter>Common</Filter>
    </ClInclude>
    <ClInclude Include="Common\NavCategory.h">
      <Filter>Common</Filter>
    </ClInclude>
    <ClInclude Include="Common\NetworkManager.h">
      <Filter>Common</Filter>
    </ClInclude>
    <ClInclude Include="Common\NumberBase.h">
      <Filter>Common</Filter>
    </ClInclude>
    <ClInclude Include="Common\RadixType.h">
      <Filter>Common</Filter>
    </ClInclude>
    <ClInclude Include="Common\TraceLogger.h">
      <Filter>Common</Filter>
    </ClInclude>
    <ClInclude Include="Common\Utils.h">
      <Filter>Common</Filter>
    </ClInclude>
    <ClInclude Include="Common\Automation\NarratorAnnouncement.h">
      <Filter>Common\Automation</Filter>
    </ClInclude>
    <ClInclude Include="Common\Automation\NarratorNotifier.h">
      <Filter>Common\Automation</Filter>
    </ClInclude>
    <ClInclude Include="DataLoaders\CurrencyDataLoader.h">
      <Filter>DataLoaders</Filter>
    </ClInclude>
    <ClInclude Include="DataLoaders\CurrencyHttpClient.h">
      <Filter>DataLoaders</Filter>
    </ClInclude>
    <ClInclude Include="DataLoaders\UnitConverterDataConstants.h">
      <Filter>DataLoaders</Filter>
    </ClInclude>
    <ClInclude Include="DataLoaders\UnitConverterDataLoader.h">
      <Filter>DataLoaders</Filter>
    </ClInclude>
    <ClInclude Include="GraphingCalculator\EquationViewModel.h">
      <Filter>GraphingCalculator</Filter>
    </ClInclude>
    <ClInclude Include="GraphingCalculator\GraphingCalculatorViewModel.h">
      <Filter>GraphingCalculator</Filter>
    </ClInclude>
    <ClInclude Include="GraphingCalculator\GraphingSettingsViewModel.h">
      <Filter>GraphingCalculator</Filter>
    </ClInclude>
    <ClInclude Include="GraphingCalculator\VariableViewModel.h">
      <Filter>GraphingCalculator</Filter>
    </ClInclude>
    <ClInclude Include="DateCalculatorViewModel.h" />
    <ClInclude Include="GraphingCalculatorEnums.h" />
    <ClInclude Include="HistoryItemViewModel.h" />
    <ClInclude Include="HistoryViewModel.h" />
    <ClInclude Include="MemoryItemViewModel.h" />
    <ClInclude Include="StandardCalculatorViewModel.h" />
    <ClInclude Include="targetver.h" />
    <ClInclude Include="UnitConverterViewModel.h" />
    <ClInclude Include="Snapshots.h" />
  </ItemGroup>
  <ItemGroup>
    <None Include="DataLoaders\DefaultFromToCurrency.json">
      <Filter>DataLoaders</Filter>
    </None>
  </ItemGroup>
  <ItemGroup>
    <ResourceCompile Include="CalcViewModel.rc" />
  </ItemGroup>
</Project>