###############################################################################
# Set default behavior to automatically normalize line endings.
###############################################################################
* text=auto

###############################################################################
# Set default behavior for command prompt diff.
#
# This is need for earlier builds of msysgit that does not have it on by
# default for csharp files.
# Note: This is only used by command line
###############################################################################
*.cs     diff=csharp

###############################################################################
# behavior for image files
#
# image files are treated as binary by default.
###############################################################################
*.jpg   binary
*.png   binary
*.gif   binary

###############################################################################
# diff behavior for common document formats
# 
# Convert binary document formats to text before diffing them. This feature
# is only available from the command line. Turn it on by uncommenting the 
# entries below.
###############################################################################
*.doc   diff=astextplain
*.DOC   diff=astextplain
*.docx  diff=astextplain
*.DOCX  diff=astextplain
*.dot   diff=astextplain
*.DOT   diff=astextplain
*.pdf   diff=astextplain
*.PDF   diff=astextplain
*.rtf   diff=astextplain
*.RTF   diff=astextplain
