# Targeted Integer Overflow Test for Windows Calculator
# This script specifically targets the overflow conditions identified in the code analysis

Write-Host "Targeted Integer Overflow Test for Windows Calculator" -ForegroundColor Cyan
Write-Host "=" * 60 -ForegroundColor Cyan

Add-Type -AssemblyName System.Windows.Forms

function Start-Calculator {
    # Kill existing calculator processes
    Get-Process -Name "*calc*" -ErrorAction SilentlyContinue | Stop-Process -Force
    Start-Sleep -Seconds 1
    
    # Start calculator
    Start-Process "calc.exe"
    Start-Sleep -Seconds 3
    
    $calcProcess = Get-Process -Name "*calc*" -ErrorAction SilentlyContinue | Select-Object -First 1
    if ($calcProcess) {
        Write-Host "Calculator started (PID: $($calcProcess.Id))" -ForegroundColor Green
        return $calcProcess
    }
    return $null
}

function Send-Keys {
    param([string]$Keys, [int]$DelayMs = 50)
    
    [System.Windows.Forms.SendKeys]::SendWait("{ESC}")  # Clear first
    Start-Sleep -Milliseconds 200
    
    foreach ($char in $Keys.ToCharArray()) {
        [System.Windows.Forms.SendKeys]::SendWait($char)
        Start-Sleep -Milliseconds $DelayMs
    }
}

function Test-SpecificOverflowConditions {
    param($Process)
    
    Write-Host "`n=== Testing Specific Overflow Conditions ===" -ForegroundColor Yellow
    
    # Test Case 1: Maximum 32-bit values (MANTTYPE max)
    Write-Host "`nTest 1: Maximum 32-bit multiplication (0xFFFFFFFF * 0xFFFFFFFF)" -ForegroundColor Cyan
    $maxUint32 = [uint32]::MaxValue.ToString()  # 4294967295
    Write-Host "Testing: $maxUint32 * $maxUint32"
    
    $initialMem = $Process.WorkingSet64 / 1MB
    
    Send-Keys -Keys $maxUint32
    [System.Windows.Forms.SendKeys]::SendWait("*")
    Send-Keys -Keys $maxUint32
    [System.Windows.Forms.SendKeys]::SendWait("=")
    Start-Sleep -Seconds 2
    
    $Process.Refresh()
    $finalMem = $Process.WorkingSet64 / 1MB
    Write-Host "Memory change: $([math]::Round($finalMem - $initialMem, 2)) MB"
    
    if ($Process.HasExited) {
        Write-Host "🚨 CRASH: Calculator crashed on max uint32 multiplication!" -ForegroundColor Red
        return $false
    }
    
    # Test Case 2: BASEX value multiplication (0x80000000 * 0x80000000)
    Write-Host "`nTest 2: BASEX multiplication (2147483648 * 2147483648)" -ForegroundColor Cyan
    $basexValue = "2147483648"  # 0x80000000
    Write-Host "Testing: $basexValue * $basexValue"
    
    $initialMem = $Process.WorkingSet64 / 1MB
    
    Send-Keys -Keys $basexValue
    [System.Windows.Forms.SendKeys]::SendWait("*")
    Send-Keys -Keys $basexValue
    [System.Windows.Forms.SendKeys]::SendWait("=")
    Start-Sleep -Seconds 2
    
    $Process.Refresh()
    $finalMem = $Process.WorkingSet64 / 1MB
    Write-Host "Memory change: $([math]::Round($finalMem - $initialMem, 2)) MB"
    
    if ($Process.HasExited) {
        Write-Host "🚨 CRASH: Calculator crashed on BASEX multiplication!" -ForegroundColor Red
        return $false
    }
    
    # Test Case 3: Chain of operations that could cause carry overflow
    Write-Host "`nTest 3: Chain operations to trigger carry overflow" -ForegroundColor Cyan
    $largeNum = "999999999999999999"
    Write-Host "Testing chain: $largeNum + $largeNum + $largeNum"
    
    $initialMem = $Process.WorkingSet64 / 1MB
    
    Send-Keys -Keys $largeNum
    [System.Windows.Forms.SendKeys]::SendWait("+")
    Send-Keys -Keys $largeNum
    [System.Windows.Forms.SendKeys]::SendWait("+")
    Send-Keys -Keys $largeNum
    [System.Windows.Forms.SendKeys]::SendWait("=")
    Start-Sleep -Seconds 2
    
    $Process.Refresh()
    $finalMem = $Process.WorkingSet64 / 1MB
    Write-Host "Memory change: $([math]::Round($finalMem - $initialMem, 2)) MB"
    
    if ($Process.HasExited) {
        Write-Host "🚨 CRASH: Calculator crashed on chain addition!" -ForegroundColor Red
        return $false
    }
    
    return $true
}

function Test-ExtremeValues {
    param($Process)
    
    Write-Host "`n=== Testing Extreme Values ===" -ForegroundColor Yellow
    
    # Test with numbers that approach the limits of TWO_MANTTYPE (uint64_t)
    $extremeValues = @(
        "18446744073709551615",  # Max uint64_t
        "9223372036854775807",   # Max int64_t
        "1" + "0" * 19,          # 10^19
        "9" * 20                 # 20 nines
    )
    
    foreach ($value in $extremeValues) {
        Write-Host "`nTesting extreme value: $($value.Substring(0, [Math]::Min(20, $value.Length)))..." -ForegroundColor Cyan
        
        $initialMem = $Process.WorkingSet64 / 1MB
        
        try {
            Send-Keys -Keys $value -DelayMs 10
            [System.Windows.Forms.SendKeys]::SendWait("*")
            [System.Windows.Forms.SendKeys]::SendWait("2")
            [System.Windows.Forms.SendKeys]::SendWait("=")
            Start-Sleep -Seconds 3
            
            $Process.Refresh()
            $finalMem = $Process.WorkingSet64 / 1MB
            $memChange = $finalMem - $initialMem
            
            Write-Host "Memory change: $([math]::Round($memChange, 2)) MB"
            
            if ($memChange -gt 50) {
                Write-Host "🚨 LARGE MEMORY ALLOCATION: $([math]::Round($memChange, 2)) MB!" -ForegroundColor Red
            }
            
            if ($Process.HasExited) {
                Write-Host "🚨 CRASH: Calculator crashed on extreme value!" -ForegroundColor Red
                return $false
            }
        } catch {
            Write-Host "Error testing extreme value: $($_.Exception.Message)" -ForegroundColor Red
        }
    }
    
    return $true
}

function Test-ScientificNotationOverflow {
    param($Process)
    
    Write-Host "`n=== Testing Scientific Notation Overflow ===" -ForegroundColor Yellow
    
    $scientificValues = @(
        "9.999e+308",   # Close to double max
        "1.797e+308",   # Very close to double max
        "9e+999",       # Extreme exponent
        "1e+1000"       # Beyond normal limits
    )
    
    foreach ($value in $scientificValues) {
        Write-Host "`nTesting scientific notation: $value" -ForegroundColor Cyan
        
        $initialMem = $Process.WorkingSet64 / 1MB
        
        try {
            # Clear and input scientific notation
            [System.Windows.Forms.SendKeys]::SendWait("{ESC}")
            Start-Sleep -Milliseconds 200
            
            # Type the scientific notation
            [System.Windows.Forms.SendKeys]::SendWait($value)
            Start-Sleep -Seconds 1
            
            # Try to square it
            [System.Windows.Forms.SendKeys]::SendWait("*")
            [System.Windows.Forms.SendKeys]::SendWait($value)
            [System.Windows.Forms.SendKeys]::SendWait("=")
            Start-Sleep -Seconds 3
            
            $Process.Refresh()
            $finalMem = $Process.WorkingSet64 / 1MB
            Write-Host "Memory change: $([math]::Round($finalMem - $initialMem, 2)) MB"
            
            if ($Process.HasExited) {
                Write-Host "🚨 CRASH: Calculator crashed on scientific notation!" -ForegroundColor Red
                return $false
            }
        } catch {
            Write-Host "Error with scientific notation: $($_.Exception.Message)" -ForegroundColor Red
        }
    }
    
    return $true
}

function Test-RapidOverflowSequence {
    param($Process)
    
    Write-Host "`n=== Testing Rapid Overflow Sequence ===" -ForegroundColor Yellow
    
    # Rapidly perform operations that could trigger overflow conditions
    $initialMem = $Process.WorkingSet64 / 1MB
    
    for ($i = 1; $i -le 20; $i++) {
        try {
            # Each iteration: large number * large number
            [System.Windows.Forms.SendKeys]::SendWait("{ESC}")
            [System.Windows.Forms.SendKeys]::SendWait("999999999")
            [System.Windows.Forms.SendKeys]::SendWait("*")
            [System.Windows.Forms.SendKeys]::SendWait("999999999")
            [System.Windows.Forms.SendKeys]::SendWait("=")
            [System.Windows.Forms.SendKeys]::SendWait("*")
            [System.Windows.Forms.SendKeys]::SendWait("999999999")
            [System.Windows.Forms.SendKeys]::SendWait("=")
            
            if ($i % 5 -eq 0) {
                Write-Host "Completed $i rapid operations..."
                $Process.Refresh()
                if ($Process.HasExited) {
                    Write-Host "🚨 CRASH: Calculator crashed during rapid sequence at iteration $i!" -ForegroundColor Red
                    return $false
                }
            }
        } catch {
            Write-Host "Error in rapid sequence: $($_.Exception.Message)" -ForegroundColor Red
        }
    }
    
    $Process.Refresh()
    $finalMem = $Process.WorkingSet64 / 1MB
    Write-Host "Total memory change: $([math]::Round($finalMem - $initialMem, 2)) MB"
    
    return $true
}

# Main execution
function Main {
    $calcProcess = Start-Calculator
    if (-not $calcProcess) {
        Write-Host "Failed to start calculator" -ForegroundColor Red
        return
    }
    
    try {
        $success = $true
        
        if ($success) { $success = Test-SpecificOverflowConditions -Process $calcProcess }
        if ($success) { $success = Test-ExtremeValues -Process $calcProcess }
        if ($success) { $success = Test-ScientificNotationOverflow -Process $calcProcess }
        if ($success) { $success = Test-RapidOverflowSequence -Process $calcProcess }
        
        Write-Host "`n=== Final Results ===" -ForegroundColor Cyan
        if ($success) {
            Write-Host "✅ All targeted overflow tests completed without crashes" -ForegroundColor Green
            Write-Host "Note: This doesn't mean vulnerabilities don't exist, just that they weren't triggered by these specific tests." -ForegroundColor Yellow
        } else {
            Write-Host "🚨 Some tests caused crashes - potential vulnerabilities detected!" -ForegroundColor Red
        }
        
        if (-not $calcProcess.HasExited) {
            $calcProcess.Refresh()
            $finalMemory = $calcProcess.WorkingSet64 / 1MB
            Write-Host "Final memory usage: $([math]::Round($finalMemory, 2)) MB" -ForegroundColor Green
        }
        
    } catch {
        Write-Host "Unexpected error: $($_.Exception.Message)" -ForegroundColor Red
    }
}

Main
