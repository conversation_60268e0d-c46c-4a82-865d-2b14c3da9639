<?xml version="1.0" encoding="utf-8"?>
<Project DefaultTargets="Build" ToolsVersion="15.0" xmlns="http://schemas.microsoft.com/developer/msbuild/2003">
  <ItemGroup Label="ProjectConfigurations">
    <ProjectConfiguration Include="Debug|ARM">
      <Configuration>Debug</Configuration>
      <Platform>ARM</Platform>
    </ProjectConfiguration>
    <ProjectConfiguration Include="Debug|ARM64">
      <Configuration>Debug</Configuration>
      <Platform>ARM64</Platform>
    </ProjectConfiguration>
    <ProjectConfiguration Include="Debug|Win32">
      <Configuration>Debug</Configuration>
      <Platform>Win32</Platform>
    </ProjectConfiguration>
    <ProjectConfiguration Include="Debug|x64">
      <Configuration>Debug</Configuration>
      <Platform>x64</Platform>
    </ProjectConfiguration>
    <ProjectConfiguration Include="Release|ARM">
      <Configuration>Release</Configuration>
      <Platform>ARM</Platform>
    </ProjectConfiguration>
    <ProjectConfiguration Include="Release|ARM64">
      <Configuration>Release</Configuration>
      <Platform>ARM64</Platform>
    </ProjectConfiguration>
    <ProjectConfiguration Include="Release|Win32">
      <Configuration>Release</Configuration>
      <Platform>Win32</Platform>
    </ProjectConfiguration>
    <ProjectConfiguration Include="Release|x64">
      <Configuration>Release</Configuration>
      <Platform>x64</Platform>
    </ProjectConfiguration>
  </ItemGroup>
  <PropertyGroup Label="Globals">
    <ProjectGuid>{cc9b4fa7-d746-4f52-9401-0ad1b4d6b16d}</ProjectGuid>
    <Keyword>StaticLibrary</Keyword>
    <RootNamespace>CalcViewModelCopyForUT</RootNamespace>
    <DefaultLanguage>en-US</DefaultLanguage>
    <MinimumVisualStudioVersion>14.0</MinimumVisualStudioVersion>
    <AppContainerApplication>true</AppContainerApplication>
    <ApplicationType>Windows Store</ApplicationType>
    <WindowsTargetPlatformVersion>10.0.22621.0</WindowsTargetPlatformVersion>
    <WindowsTargetPlatformMinVersion>10.0.19041.0</WindowsTargetPlatformMinVersion>
    <ApplicationTypeRevision>10.0</ApplicationTypeRevision>
  </PropertyGroup>
  <Import Project="$(VCTargetsPath)\Microsoft.Cpp.Default.props" />
  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='Debug|Win32'" Label="Configuration">
    <ConfigurationType>StaticLibrary</ConfigurationType>
    <UseDebugLibraries>true</UseDebugLibraries>
    <PlatformToolset>v143</PlatformToolset>
  </PropertyGroup>
  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='Debug|ARM'" Label="Configuration">
    <ConfigurationType>StaticLibrary</ConfigurationType>
    <UseDebugLibraries>true</UseDebugLibraries>
    <PlatformToolset>v143</PlatformToolset>
  </PropertyGroup>
  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='Debug|ARM64'" Label="Configuration">
    <ConfigurationType>StaticLibrary</ConfigurationType>
    <UseDebugLibraries>true</UseDebugLibraries>
    <PlatformToolset>v143</PlatformToolset>
  </PropertyGroup>
  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='Debug|x64'" Label="Configuration">
    <ConfigurationType>StaticLibrary</ConfigurationType>
    <UseDebugLibraries>true</UseDebugLibraries>
    <PlatformToolset>v143</PlatformToolset>
  </PropertyGroup>
  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='Release|Win32'" Label="Configuration">
    <ConfigurationType>StaticLibrary</ConfigurationType>
    <UseDebugLibraries>false</UseDebugLibraries>
    <WholeProgramOptimization>true</WholeProgramOptimization>
    <PlatformToolset>v143</PlatformToolset>
  </PropertyGroup>
  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='Release|ARM'" Label="Configuration">
    <ConfigurationType>StaticLibrary</ConfigurationType>
    <UseDebugLibraries>false</UseDebugLibraries>
    <WholeProgramOptimization>true</WholeProgramOptimization>
    <PlatformToolset>v143</PlatformToolset>
  </PropertyGroup>
  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='Release|ARM64'" Label="Configuration">
    <ConfigurationType>StaticLibrary</ConfigurationType>
    <UseDebugLibraries>false</UseDebugLibraries>
    <WholeProgramOptimization>true</WholeProgramOptimization>
    <PlatformToolset>v143</PlatformToolset>
  </PropertyGroup>
  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='Release|x64'" Label="Configuration">
    <ConfigurationType>StaticLibrary</ConfigurationType>
    <UseDebugLibraries>false</UseDebugLibraries>
    <WholeProgramOptimization>true</WholeProgramOptimization>
    <PlatformToolset>v143</PlatformToolset>
  </PropertyGroup>
  <Import Project="$(VCTargetsPath)\Microsoft.Cpp.props" />
  <ImportGroup Label="ExtensionSettings">
  </ImportGroup>
  <ImportGroup Label="Shared">
  </ImportGroup>
  <ImportGroup Label="PropertySheets" Condition="'$(Configuration)|$(Platform)'=='Debug|Win32'">
    <Import Project="$(UserRootDir)\Microsoft.Cpp.$(Platform).user.props" Condition="exists('$(UserRootDir)\Microsoft.Cpp.$(Platform).user.props')" Label="LocalAppDataPlatform" />
  </ImportGroup>
  <ImportGroup Label="PropertySheets" Condition="'$(Configuration)|$(Platform)'=='Release|Win32'">
    <Import Project="$(UserRootDir)\Microsoft.Cpp.$(Platform).user.props" Condition="exists('$(UserRootDir)\Microsoft.Cpp.$(Platform).user.props')" Label="LocalAppDataPlatform" />
  </ImportGroup>
  <ImportGroup Label="PropertySheets" Condition="'$(Configuration)|$(Platform)'=='Debug|ARM'">
    <Import Project="$(UserRootDir)\Microsoft.Cpp.$(Platform).user.props" Condition="exists('$(UserRootDir)\Microsoft.Cpp.$(Platform).user.props')" Label="LocalAppDataPlatform" />
  </ImportGroup>
  <ImportGroup Condition="'$(Configuration)|$(Platform)'=='Debug|ARM64'" Label="PropertySheets">
    <Import Project="$(UserRootDir)\Microsoft.Cpp.$(Platform).user.props" Condition="exists('$(UserRootDir)\Microsoft.Cpp.$(Platform).user.props')" Label="LocalAppDataPlatform" />
  </ImportGroup>
  <ImportGroup Label="PropertySheets" Condition="'$(Configuration)|$(Platform)'=='Release|ARM'">
    <Import Project="$(UserRootDir)\Microsoft.Cpp.$(Platform).user.props" Condition="exists('$(UserRootDir)\Microsoft.Cpp.$(Platform).user.props')" Label="LocalAppDataPlatform" />
  </ImportGroup>
  <ImportGroup Condition="'$(Configuration)|$(Platform)'=='Release|ARM64'" Label="PropertySheets">
    <Import Project="$(UserRootDir)\Microsoft.Cpp.$(Platform).user.props" Condition="exists('$(UserRootDir)\Microsoft.Cpp.$(Platform).user.props')" Label="LocalAppDataPlatform" />
  </ImportGroup>
  <ImportGroup Label="PropertySheets" Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">
    <Import Project="$(UserRootDir)\Microsoft.Cpp.$(Platform).user.props" Condition="exists('$(UserRootDir)\Microsoft.Cpp.$(Platform).user.props')" Label="LocalAppDataPlatform" />
  </ImportGroup>
  <ImportGroup Label="PropertySheets" Condition="'$(Configuration)|$(Platform)'=='Release|x64'">
    <Import Project="$(UserRootDir)\Microsoft.Cpp.$(Platform).user.props" Condition="exists('$(UserRootDir)\Microsoft.Cpp.$(Platform).user.props')" Label="LocalAppDataPlatform" />
  </ImportGroup>
  <PropertyGroup Label="UserMacros" />
  <PropertyGroup />
  <PropertyGroup>
    <GenerateManifest>false</GenerateManifest>
    <GenerateProjectSpecificOutputFolder>true</GenerateProjectSpecificOutputFolder>
  </PropertyGroup>
  <ItemDefinitionGroup Condition="'$(Configuration)|$(Platform)'=='Debug|Win32'">
    <ClCompile>
      <PrecompiledHeader>Use</PrecompiledHeader>
      <CompileAsWinRT>true</CompileAsWinRT>
      <SDLCheck>true</SDLCheck>
      <AdditionalIncludeDirectories>$(SolutionDir)..\src\;$(SolutionDir)CalcViewModel\;%(AdditionalIncludeDirectories)</AdditionalIncludeDirectories>
      <DisableSpecificWarnings>4453</DisableSpecificWarnings>
      <AdditionalOptions>/bigobj /await /std:c++17 /utf-8 /w44242 %(AdditionalOptions)</AdditionalOptions>
      <WarningLevel>Level4</WarningLevel>
      <TreatWarningAsError>true</TreatWarningAsError>
      <PreprocessorDefinitions>VIEWMODEL_FOR_UT;%(PreprocessorDefinitions)</PreprocessorDefinitions>
    </ClCompile>
    <Link>
      <SubSystem>Console</SubSystem>
      <IgnoreAllDefaultLibraries>false</IgnoreAllDefaultLibraries>
      <GenerateWindowsMetadata>false</GenerateWindowsMetadata>
    </Link>
    <Lib>
      <AdditionalOptions>/ignore:4264 %(AdditionalOptions)</AdditionalOptions>
    </Lib>
  </ItemDefinitionGroup>
  <ItemDefinitionGroup Condition="'$(Configuration)|$(Platform)'=='Release|Win32'">
    <ClCompile>
      <PrecompiledHeader>Use</PrecompiledHeader>
      <CompileAsWinRT>true</CompileAsWinRT>
      <SDLCheck>true</SDLCheck>
      <AdditionalIncludeDirectories>$(SolutionDir)..\src\;$(SolutionDir)CalcViewModel\;%(AdditionalIncludeDirectories)</AdditionalIncludeDirectories>
      <DisableSpecificWarnings>4453</DisableSpecificWarnings>
      <AdditionalOptions>/bigobj /await /std:c++17 /utf-8 /w44242 %(AdditionalOptions)</AdditionalOptions>
      <WarningLevel>Level4</WarningLevel>
      <TreatWarningAsError>true</TreatWarningAsError>
      <PreprocessorDefinitions>VIEWMODEL_FOR_UT;%(PreprocessorDefinitions)</PreprocessorDefinitions>
    </ClCompile>
    <Link>
      <SubSystem>Console</SubSystem>
      <IgnoreAllDefaultLibraries>false</IgnoreAllDefaultLibraries>
      <GenerateWindowsMetadata>false</GenerateWindowsMetadata>
    </Link>
    <Lib>
      <AdditionalOptions>/ignore:4264 %(AdditionalOptions)</AdditionalOptions>
    </Lib>
  </ItemDefinitionGroup>
  <ItemDefinitionGroup Condition="'$(Configuration)|$(Platform)'=='Debug|arm'">
    <ClCompile>
      <PrecompiledHeader>Use</PrecompiledHeader>
      <CompileAsWinRT>true</CompileAsWinRT>
      <SDLCheck>true</SDLCheck>
      <AdditionalIncludeDirectories>$(SolutionDir)..\src\;$(SolutionDir)CalcViewModel\;%(AdditionalIncludeDirectories)</AdditionalIncludeDirectories>
      <DisableSpecificWarnings>4453</DisableSpecificWarnings>
      <AdditionalOptions>/bigobj /await /std:c++17 /utf-8 /w44242 %(AdditionalOptions)</AdditionalOptions>
      <WarningLevel>Level4</WarningLevel>
      <TreatWarningAsError>true</TreatWarningAsError>
      <PreprocessorDefinitions>VIEWMODEL_FOR_UT;%(PreprocessorDefinitions)</PreprocessorDefinitions>
    </ClCompile>
    <Link>
      <SubSystem>Console</SubSystem>
      <IgnoreAllDefaultLibraries>false</IgnoreAllDefaultLibraries>
      <GenerateWindowsMetadata>false</GenerateWindowsMetadata>
    </Link>
    <Lib>
      <AdditionalOptions>/ignore:4264 %(AdditionalOptions)</AdditionalOptions>
    </Lib>
  </ItemDefinitionGroup>
  <ItemDefinitionGroup Condition="'$(Configuration)|$(Platform)'=='Release|arm'">
    <ClCompile>
      <PrecompiledHeader>Use</PrecompiledHeader>
      <CompileAsWinRT>true</CompileAsWinRT>
      <SDLCheck>true</SDLCheck>
      <AdditionalIncludeDirectories>$(SolutionDir)..\src\;$(SolutionDir)CalcViewModel\;%(AdditionalIncludeDirectories)</AdditionalIncludeDirectories>
      <DisableSpecificWarnings>4453</DisableSpecificWarnings>
      <AdditionalOptions>/bigobj /await /std:c++17 /utf-8 /w44242 %(AdditionalOptions)</AdditionalOptions>
      <WarningLevel>Level4</WarningLevel>
      <TreatWarningAsError>true</TreatWarningAsError>
      <PreprocessorDefinitions>VIEWMODEL_FOR_UT;%(PreprocessorDefinitions)</PreprocessorDefinitions>
    </ClCompile>
    <Link>
      <SubSystem>Console</SubSystem>
      <IgnoreAllDefaultLibraries>false</IgnoreAllDefaultLibraries>
      <GenerateWindowsMetadata>false</GenerateWindowsMetadata>
    </Link>
    <Lib>
      <AdditionalOptions>/ignore:4264 %(AdditionalOptions)</AdditionalOptions>
    </Lib>
  </ItemDefinitionGroup>
  <ItemDefinitionGroup Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">
    <ClCompile>
      <PrecompiledHeader>Use</PrecompiledHeader>
      <CompileAsWinRT>true</CompileAsWinRT>
      <SDLCheck>true</SDLCheck>
      <AdditionalIncludeDirectories>$(SolutionDir);$(SolutionDir)CalcViewModel\;%(AdditionalIncludeDirectories)</AdditionalIncludeDirectories>
      <DisableSpecificWarnings>4453</DisableSpecificWarnings>
      <AdditionalOptions>/bigobj /await /std:c++17 /utf-8 /w44242 %(AdditionalOptions)</AdditionalOptions>
      <WarningLevel>Level4</WarningLevel>
      <TreatWarningAsError>true</TreatWarningAsError>
      <PreprocessorDefinitions>VIEWMODEL_FOR_UT;%(PreprocessorDefinitions)</PreprocessorDefinitions>
    </ClCompile>
    <Link>
      <SubSystem>Console</SubSystem>
      <IgnoreAllDefaultLibraries>false</IgnoreAllDefaultLibraries>
      <GenerateWindowsMetadata>false</GenerateWindowsMetadata>
    </Link>
    <Lib>
      <AdditionalOptions>/ignore:4264 %(AdditionalOptions)</AdditionalOptions>
    </Lib>
  </ItemDefinitionGroup>
  <ItemDefinitionGroup Condition="'$(Configuration)|$(Platform)'=='Release|x64'">
    <ClCompile>
      <PrecompiledHeader>Use</PrecompiledHeader>
      <CompileAsWinRT>true</CompileAsWinRT>
      <SDLCheck>true</SDLCheck>
      <AdditionalIncludeDirectories>$(SolutionDir)..\src\;$(SolutionDir)CalcViewModel\;%(AdditionalIncludeDirectories)</AdditionalIncludeDirectories>
      <DisableSpecificWarnings>4453</DisableSpecificWarnings>
      <AdditionalOptions>/bigobj /await /std:c++17 /utf-8 /w44242 %(AdditionalOptions)</AdditionalOptions>
      <WarningLevel>Level4</WarningLevel>
      <TreatWarningAsError>true</TreatWarningAsError>
      <PreprocessorDefinitions>VIEWMODEL_FOR_UT;%(PreprocessorDefinitions)</PreprocessorDefinitions>
    </ClCompile>
    <Link>
      <SubSystem>Console</SubSystem>
      <IgnoreAllDefaultLibraries>false</IgnoreAllDefaultLibraries>
      <GenerateWindowsMetadata>false</GenerateWindowsMetadata>
    </Link>
    <Lib>
      <AdditionalOptions>/ignore:4264 %(AdditionalOptions)</AdditionalOptions>
    </Lib>
  </ItemDefinitionGroup>
  <ItemDefinitionGroup Condition="'$(Configuration)|$(Platform)'=='Debug|arm64'">
    <ClCompile>
      <PrecompiledHeader>Use</PrecompiledHeader>
      <CompileAsWinRT>true</CompileAsWinRT>
      <SDLCheck>true</SDLCheck>
      <AdditionalIncludeDirectories>$(SolutionDir)..\src\;$(SolutionDir)CalcViewModel\;%(AdditionalIncludeDirectories)</AdditionalIncludeDirectories>
      <DisableSpecificWarnings>4453</DisableSpecificWarnings>
      <AdditionalOptions>/bigobj /await /std:c++17 /utf-8 /w44242 %(AdditionalOptions)</AdditionalOptions>
      <WarningLevel>Level4</WarningLevel>
      <TreatWarningAsError>true</TreatWarningAsError>
      <PreprocessorDefinitions>VIEWMODEL_FOR_UT;%(PreprocessorDefinitions)</PreprocessorDefinitions>
    </ClCompile>
    <Link>
      <SubSystem>Console</SubSystem>
      <IgnoreAllDefaultLibraries>false</IgnoreAllDefaultLibraries>
      <GenerateWindowsMetadata>false</GenerateWindowsMetadata>
    </Link>
    <Lib>
      <AdditionalOptions>/ignore:4264 %(AdditionalOptions)</AdditionalOptions>
    </Lib>
  </ItemDefinitionGroup>
  <ItemDefinitionGroup Condition="'$(Configuration)|$(Platform)'=='Release|arm64'">
    <ClCompile>
      <PrecompiledHeader>Use</PrecompiledHeader>
      <CompileAsWinRT>true</CompileAsWinRT>
      <SDLCheck>true</SDLCheck>
      <AdditionalIncludeDirectories>$(SolutionDir)..\src\;$(SolutionDir)CalcViewModel\;%(AdditionalIncludeDirectories)</AdditionalIncludeDirectories>
      <DisableSpecificWarnings>4453</DisableSpecificWarnings>
      <AdditionalOptions>/bigobj /await /std:c++17 /utf-8 /w44242 %(AdditionalOptions)</AdditionalOptions>
      <WarningLevel>Level4</WarningLevel>
      <TreatWarningAsError>true</TreatWarningAsError>
      <PreprocessorDefinitions>VIEWMODEL_FOR_UT;%(PreprocessorDefinitions)</PreprocessorDefinitions>
    </ClCompile>
    <Link>
      <SubSystem>Console</SubSystem>
      <IgnoreAllDefaultLibraries>false</IgnoreAllDefaultLibraries>
      <GenerateWindowsMetadata>false</GenerateWindowsMetadata>
    </Link>
    <Lib>
      <AdditionalOptions>/ignore:4264 %(AdditionalOptions)</AdditionalOptions>
    </Lib>
  </ItemDefinitionGroup>
  <ItemDefinitionGroup Condition="'$(IsStoreBuild)' == 'True'">
    <ClCompile>
      <AdditionalOptions>/DSEND_DIAGNOSTICS %(AdditionalOptions)</AdditionalOptions>
    </ClCompile>
  </ItemDefinitionGroup>
  <ItemGroup>
    <ClInclude Include="..\CalcViewModel\Common\AlwaysSelectedCollectionView.h" />
    <ClInclude Include="..\CalcViewModel\Common\AppResourceProvider.h" />
    <ClInclude Include="..\CalcViewModel\Common\Automation\NarratorAnnouncement.h" />
    <ClInclude Include="..\CalcViewModel\Common\Automation\NarratorNotifier.h" />
    <ClInclude Include="..\CalcViewModel\Common\BitLength.h" />
    <ClInclude Include="..\CalcViewModel\Common\CalculatorButtonPressedEventArgs.h" />
    <ClInclude Include="..\CalcViewModel\Common\CalculatorButtonUser.h" />
    <ClInclude Include="..\CalcViewModel\Common\CalculatorDisplay.h" />
    <ClInclude Include="..\CalcViewModel\Common\CopyPasteManager.h" />
    <ClInclude Include="..\CalcViewModel\Common\DateCalculator.h" />
    <ClInclude Include="..\CalcViewModel\Common\DelegateCommand.h" />
    <ClInclude Include="..\CalcViewModel\Common\DisplayExpressionToken.h" />
    <ClInclude Include="..\CalcViewModel\Common\EngineResourceProvider.h" />
    <ClInclude Include="..\CalcViewModel\Common\ExpressionCommandDeserializer.h" />
    <ClInclude Include="..\CalcViewModel\Common\ExpressionCommandSerializer.h" />
    <ClInclude Include="..\CalcViewModel\Common\LocalizationService.h" />
    <ClInclude Include="..\CalcViewModel\Common\LocalizationSettings.h" />
    <ClInclude Include="..\CalcViewModel\Common\LocalizationStringUtil.h" />
    <ClInclude Include="..\CalcViewModel\Common\MyVirtualKey.h" />
    <ClInclude Include="..\CalcViewModel\Common\NavCategory.h" />
    <ClInclude Include="..\CalcViewModel\Common\NetworkManager.h" />
    <ClInclude Include="..\CalcViewModel\Common\NumberBase.h" />
    <ClInclude Include="..\CalcViewModel\Common\RadixType.h" />
    <ClInclude Include="..\CalcViewModel\Common\TraceLogger.h" />
    <ClInclude Include="..\CalcViewModel\Common\Utils.h" />
    <ClInclude Include="..\CalcViewModel\DataLoaders\CurrencyDataLoader.h" />
    <ClInclude Include="..\CalcViewModel\DataLoaders\UnitConverterDataConstants.h" />
    <ClInclude Include="..\CalcViewModel\DataLoaders\UnitConverterDataLoader.h" />
    <ClInclude Include="..\CalcViewModel\DateCalculatorViewModel.h" />
    <ClInclude Include="..\CalcViewModel\GraphingCalculatorEnums.h" />
    <ClInclude Include="..\CalcViewModel\GraphingCalculator\EquationViewModel.h" />
    <ClInclude Include="..\CalcViewModel\GraphingCalculator\GraphingCalculatorViewModel.h" />
    <ClInclude Include="..\CalcViewModel\GraphingCalculator\VariableViewModel.h" />
    <ClInclude Include="..\CalcViewModel\GraphingCalculator\GraphingSettingsViewModel.h" />
    <ClInclude Include="..\CalcViewModel\HistoryItemViewModel.h" />
    <ClInclude Include="..\CalcViewModel\HistoryViewModel.h" />
    <ClInclude Include="..\CalcViewModel\MemoryItemViewModel.h" />
    <ClInclude Include="..\CalcViewModel\pch.h" />
    <ClInclude Include="..\CalcViewModel\Snapshots.h" />
    <ClInclude Include="..\CalcViewModel\StandardCalculatorViewModel.h" />
    <ClInclude Include="..\CalcViewModel\targetver.h" />
    <ClInclude Include="..\CalcViewModel\UnitConverterViewModel.h" />
  </ItemGroup>
  <ItemGroup>
    <ClCompile Include="..\CalcViewModel\Common\AppResourceProvider.cpp" />
    <ClCompile Include="..\CalcViewModel\Common\Automation\NarratorAnnouncement.cpp" />
    <ClCompile Include="..\CalcViewModel\Common\Automation\NarratorNotifier.cpp" />
    <ClCompile Include="..\CalcViewModel\Common\CalculatorButtonPressedEventArgs.cpp" />
    <ClCompile Include="..\CalcViewModel\Common\CalculatorDisplay.cpp" />
    <ClCompile Include="..\CalcViewModel\Common\CopyPasteManager.cpp" />
    <ClCompile Include="..\CalcViewModel\Common\DateCalculator.cpp" />
    <ClCompile Include="..\CalcViewModel\Common\EngineResourceProvider.cpp" />
    <ClCompile Include="..\CalcViewModel\Common\ExpressionCommandDeserializer.cpp" />
    <ClCompile Include="..\CalcViewModel\Common\ExpressionCommandSerializer.cpp" />
    <ClCompile Include="..\CalcViewModel\Common\LocalizationService.cpp" />
    <ClCompile Include="..\CalcViewModel\Common\NavCategory.cpp" />
    <ClCompile Include="..\CalcViewModel\Common\NetworkManager.cpp" />
    <ClCompile Include="..\CalcViewModel\Common\RadixType.cpp" />
    <ClCompile Include="..\CalcViewModel\Common\TraceLogger.cpp" />
    <ClCompile Include="..\CalcViewModel\Common\Utils.cpp" />
    <ClCompile Include="..\CalcViewModel\DataLoaders\CurrencyDataLoader.cpp" />
    <ClCompile Include="..\CalcViewModel\DataLoaders\UnitConverterDataLoader.cpp" />
    <ClCompile Include="..\CalcViewModel\DateCalculatorViewModel.cpp" />
    <ClCompile Include="..\CalcViewModel\GraphingCalculator\EquationViewModel.cpp" />
    <ClCompile Include="..\CalcViewModel\GraphingCalculator\GraphingCalculatorViewModel.cpp" />
    <ClCompile Include="..\CalcViewModel\GraphingCalculator\GraphingSettingsViewModel.cpp" />
    <ClCompile Include="..\CalcViewModel\HistoryItemViewModel.cpp" />
    <ClCompile Include="..\CalcViewModel\HistoryViewModel.cpp" />
    <ClCompile Include="..\CalcViewModel\MemoryItemViewModel.cpp" />
    <ClCompile Include="..\CalcViewModel\pch.cpp">
      <PrecompiledHeader Condition="'$(Configuration)|$(Platform)'=='Debug|Win32'">Create</PrecompiledHeader>
      <PrecompiledHeader Condition="'$(Configuration)|$(Platform)'=='Release|Win32'">Create</PrecompiledHeader>
      <PrecompiledHeader Condition="'$(Configuration)|$(Platform)'=='Debug|ARM'">Create</PrecompiledHeader>
      <PrecompiledHeader Condition="'$(Configuration)|$(Platform)'=='Debug|ARM64'">Create</PrecompiledHeader>
      <PrecompiledHeader Condition="'$(Configuration)|$(Platform)'=='Release|ARM'">Create</PrecompiledHeader>
      <PrecompiledHeader Condition="'$(Configuration)|$(Platform)'=='Release|ARM64'">Create</PrecompiledHeader>
      <PrecompiledHeader Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">Create</PrecompiledHeader>
      <PrecompiledHeader Condition="'$(Configuration)|$(Platform)'=='Release|x64'">Create</PrecompiledHeader>
    </ClCompile>
    <ClCompile Include="..\CalcViewModel\Snapshots.cpp" />
    <ClCompile Include="..\CalcViewModel\StandardCalculatorViewModel.cpp" />
    <ClCompile Include="..\CalcViewModel\UnitConverterViewModel.cpp" />
    <ClCompile Include="DataLoaders\CurrencyHttpClient.cpp" />
  </ItemGroup>
  <ItemGroup>
    <ProjectReference Include="..\CalcManager\CalcManager.vcxproj">
      <Project>{311e866d-8b93-4609-a691-265941fee101}</Project>
    </ProjectReference>
    <ProjectReference Include="..\GraphControl\GraphControl.vcxproj">
      <Project>{e727a92b-f149-492c-8117-c039a298719b}</Project>
    </ProjectReference>
    <ProjectReference Include="..\TraceLogging\TraceLogging.vcxproj">
      <Project>{fc81ff41-02cd-4cd9-9bc5-45a1e39ac6ed}</Project>
    </ProjectReference>
  </ItemGroup>
  <ItemGroup>
    <None Include="..\CalcViewModel\DataLoaders\DefaultFromToCurrency.json" />
  </ItemGroup>
  <Import Project="$(VCTargetsPath)\Microsoft.Cpp.targets" />
  <ImportGroup Label="ExtensionTargets" />
</Project>