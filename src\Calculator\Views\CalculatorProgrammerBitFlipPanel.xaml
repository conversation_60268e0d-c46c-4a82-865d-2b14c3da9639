<UserControl x:Class="CalculatorApp.CalculatorProgrammerBitFlipPanel"
             xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
             xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
             xmlns:common="using:CalculatorApp.Common"
             xmlns:controls="using:CalculatorApp.Controls"
             xmlns:d="http://schemas.microsoft.com/expression/blend/2008"
             xmlns:local="using:CalculatorApp"
             xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006"
             xmlns:utils="using:CalculatorApp.Utils"
             x:Name="BitFlip"
             d:DesignHeight="395"
             d:DesignWidth="315"
             Loaded="OnLoaded"
             Unloaded="OnUnloaded"
             mc:Ignorable="d">
    <UserControl.Resources>
        <Style x:Key="FlippingToggleButtonStyle" TargetType="ToggleButton">
            <Setter Property="FontSize" Value="20"/>
            <Setter Property="Foreground" Value="{ThemeResource SystemControlForegroundBaseHighBrush}"/>
            <Setter Property="Background" Value="{ThemeResource SystemControlBackgroundTransparentBrush}"/>
            <Setter Property="Padding" Value="0,0,0,0"/>
            <Setter Property="MinWidth" Value="12"/>
            <Setter Property="MinHeight" Value="20"/>
            <Setter Property="HorizontalAlignment" Value="Stretch"/>
            <Setter Property="VerticalAlignment" Value="Stretch"/>
            <Setter Property="HorizontalContentAlignment" Value="Center"/>
            <Setter Property="VerticalContentAlignment" Value="Center"/>
            <Setter Property="Template">
                <Setter.Value>
                    <ControlTemplate TargetType="ToggleButton">
                        <Grid>
                            <VisualStateManager.VisualStateGroups>
                                <VisualStateGroup x:Name="CommonStates">
                                    <VisualState x:Name="Normal"/>
                                    <VisualState x:Name="PointerOver">
                                        <VisualState.Setters>
                                            <Setter Target="BinaryZero.Foreground" Value="{ThemeResource SystemControlHighlightAltBaseMediumBrush}"/>
                                            <Setter Target="ContentPanel.Background" Value="{ThemeResource SystemControlHighlightTransparentBrush}"/>
                                        </VisualState.Setters>
                                    </VisualState>
                                    <VisualState x:Name="Pressed">
                                        <VisualState.Setters>
                                            <Setter Target="BinaryZero.Foreground" Value="{ThemeResource SystemControlHighlightAltBaseMediumLowBrush}"/>
                                            <Setter Target="ContentPanel.Background" Value="{ThemeResource SystemControlHighlightTransparentBrush}"/>
                                        </VisualState.Setters>
                                        <Storyboard>
                                            <PointerDownThemeAnimation Storyboard.TargetName="ContentPanel"/>
                                        </Storyboard>
                                    </VisualState>
                                    <VisualState x:Name="Disabled">
                                        <VisualState.Setters>
                                            <Setter Target="BinaryZero.Foreground" Value="{ThemeResource SystemControlDisabledBaseLowBrush}"/>
                                        </VisualState.Setters>
                                    </VisualState>
                                    <VisualState x:Name="Checked">
                                        <VisualState.Setters>
                                            <Setter Target="BinaryZero.Visibility" Value="Collapsed"/>
                                            <Setter Target="BinaryOne.Visibility" Value="Visible"/>
                                        </VisualState.Setters>
                                        <Storyboard>
                                            <ObjectAnimationUsingKeyFrames Storyboard.TargetName="ContentPresenter" Storyboard.TargetProperty="Content">
                                                <DiscreteObjectKeyFrame KeyTime="0" Value="{Binding Text, ElementName=BinaryOne, Mode=OneWay}"/>
                                            </ObjectAnimationUsingKeyFrames>
                                        </Storyboard>
                                    </VisualState>
                                    <VisualState x:Name="CheckedPointerOver">
                                        <VisualState.Setters>
                                            <Setter Target="BinaryZero.Visibility" Value="Collapsed"/>
                                            <Setter Target="BinaryOne.Visibility" Value="Visible"/>
                                            <Setter Target="BinaryOne.Foreground" Value="{ThemeResource SystemControlHighlightAltBaseHighBrush}"/>
                                            <Setter Target="ContentPanel.Background" Value="{ThemeResource SystemControlHighlightTransparentBrush}"/>
                                        </VisualState.Setters>
                                        <Storyboard>
                                            <ObjectAnimationUsingKeyFrames Storyboard.TargetName="ContentPresenter" Storyboard.TargetProperty="Content">
                                                <DiscreteObjectKeyFrame KeyTime="0" Value="{Binding Text, ElementName=BinaryOne, Mode=OneWay}"/>
                                            </ObjectAnimationUsingKeyFrames>
                                        </Storyboard>
                                    </VisualState>
                                    <VisualState x:Name="CheckedPressed">
                                        <VisualState.Setters>
                                            <Setter Target="BinaryZero.Visibility" Value="Collapsed"/>
                                            <Setter Target="BinaryOne.Visibility" Value="Visible"/>
                                            <Setter Target="BinaryOne.Foreground" Value="{ThemeResource SystemControlHighlightAltBaseMediumLowBrush}"/>
                                            <Setter Target="ContentPanel.Background" Value="{ThemeResource SystemControlHighlightTransparentBrush}"/>
                                        </VisualState.Setters>
                                        <Storyboard>
                                            <ObjectAnimationUsingKeyFrames Storyboard.TargetName="ContentPresenter" Storyboard.TargetProperty="Content">
                                                <DiscreteObjectKeyFrame KeyTime="0" Value="{Binding Text, ElementName=BinaryOne}"/>
                                            </ObjectAnimationUsingKeyFrames>
                                            <PointerDownThemeAnimation Storyboard.TargetName="ContentPanel"/>
                                        </Storyboard>
                                    </VisualState>
                                    <VisualState x:Name="CheckedDisabled"/>
                                </VisualStateGroup>
                            </VisualStateManager.VisualStateGroups>
                            <Grid x:Name="ContentPanel" Background="{TemplateBinding Background}">
                                <ContentPresenter x:Name="ContentPresenter"
                                                  Opacity="0"
                                                  AutomationProperties.AccessibilityView="Raw"
                                                  Content="{Binding Text, ElementName=BinaryZero, Mode=OneWay}"/>
                                <TextBlock x:Name="BinaryZero"
                                           Margin="{TemplateBinding Padding}"
                                           HorizontalAlignment="{TemplateBinding HorizontalContentAlignment}"
                                           VerticalAlignment="{TemplateBinding VerticalContentAlignment}"
                                           Foreground="{TemplateBinding Foreground}"
                                           FontSize="{TemplateBinding FontSize}"
                                           FontWeight="SemiBold"
                                           AutomationProperties.AccessibilityView="Raw"
                                           IsTextScaleFactorEnabled="False"
                                           Text="{utils:ResourceString Name=BinaryZero/Text}"/>
                                <TextBlock x:Name="BinaryOne"
                                           Margin="{TemplateBinding Padding}"
                                           HorizontalAlignment="{TemplateBinding HorizontalContentAlignment}"
                                           VerticalAlignment="{TemplateBinding VerticalContentAlignment}"
                                           Foreground="{ThemeResource AccentTextFillColorTertiaryBrush}"
                                           FontSize="{TemplateBinding FontSize}"
                                           FontWeight="SemiBold"
                                           AutomationProperties.AccessibilityView="Raw"
                                           IsTextScaleFactorEnabled="False"
                                           Text="{utils:ResourceString Name=BinaryOne/Text}"
                                           Visibility="Collapsed"/>
                            </Grid>
                        </Grid>
                    </ControlTemplate>
                </Setter.Value>
            </Setter>
        </Style>

        <Style x:Key="TextBlockStyle"
               BasedOn="{StaticResource CaptionTextBlockStyle}"
               TargetType="TextBlock">
            <Setter Property="VerticalAlignment" Value="Top"/>
            <Setter Property="HorizontalAlignment" Value="Center"/>
            <Setter Property="TextAlignment" Value="Center"/>
            <Setter Property="FontWeight" Value="SemiBold"/>
            <Setter Property="Foreground" Value="{ThemeResource AppControlPageTextBaseMediumHighBrush}"/>
            <Setter Property="AutomationProperties.AccessibilityView" Value="Raw"/>
        </Style>
    </UserControl.Resources>
    <Grid x:Name="BitFlipPanel"
          Margin="0,1,0,1"
          AutomationProperties.HeadingLevel="Level1"
          AutomationProperties.Name="{utils:ResourceString Name=BitFlipPanel/[using:Windows.UI.Xaml.Automation]AutomationProperties/Name}">
        <Grid.RowDefinitions>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="1*"/>
            <RowDefinition Height="1*"/>
            <RowDefinition Height="1*"/>
            <RowDefinition Height="1*"/>
            <RowDefinition Height="1*"/>
            <RowDefinition Height="1*"/>
            <RowDefinition Height="1*"/>
            <RowDefinition Height="1*"/>
        </Grid.RowDefinitions>
        <Grid.ColumnDefinitions>
            <ColumnDefinition x:Name="Gutter0"
                              Width="0.8*"
                              MinWidth="12"/>
            <ColumnDefinition Width="1*"/>
            <ColumnDefinition Width="1*"/>
            <ColumnDefinition Width="1*"/>
            <ColumnDefinition Width="1*"/>
            <ColumnDefinition x:Name="Gutter1" Width="0.8*"/>
            <ColumnDefinition Width="1*"/>
            <ColumnDefinition Width="1*"/>
            <ColumnDefinition Width="1*"/>
            <ColumnDefinition Width="1*"/>
            <ColumnDefinition x:Name="Gutter2" Width="0.8*"/>
            <ColumnDefinition Width="1*"/>
            <ColumnDefinition Width="1*"/>
            <ColumnDefinition Width="1*"/>
            <ColumnDefinition Width="1*"/>
            <ColumnDefinition x:Name="Gutter3" Width="0.8*"/>
            <ColumnDefinition Width="1*"/>
            <ColumnDefinition Width="1*"/>
            <ColumnDefinition Width="1*"/>
            <ColumnDefinition Width="1*"/>
            <ColumnDefinition x:Name="Gutter4"
                              Width="0.8*"
                              MinWidth="12"/>
        </Grid.ColumnDefinitions>
        <VisualStateManager.VisualStateGroups>
            <VisualStateGroup x:Name="Sizing">
                <VisualState x:Name="MinSizeLayout">
                    <VisualState.StateTriggers>
                        <AdaptiveTrigger MinWindowHeight="{StaticResource AppMinWindowHeight}" MinWindowWidth="{StaticResource AppMinWindowWidth}"/>
                    </VisualState.StateTriggers>
                </VisualState>
                <VisualState x:Name="DefaultLayout">
                    <VisualState.StateTriggers>
                        <AdaptiveTrigger MinWindowHeight="0" MinWindowWidth="0"/>
                    </VisualState.StateTriggers>
                    <VisualState.Setters>
                        <Setter Target="Label60.FontSize" Value="10"/>
                        <Setter Target="Label56.FontSize" Value="10"/>
                        <Setter Target="Label52.FontSize" Value="10"/>
                        <Setter Target="Label48.FontSize" Value="10"/>

                        <Setter Target="Label44.FontSize" Value="10"/>
                        <Setter Target="Label40.FontSize" Value="10"/>
                        <Setter Target="Label36.FontSize" Value="10"/>
                        <Setter Target="Label32.FontSize" Value="10"/>

                        <Setter Target="Label28.FontSize" Value="10"/>
                        <Setter Target="Label24.FontSize" Value="10"/>
                        <Setter Target="Label20.FontSize" Value="10"/>
                        <Setter Target="Label16.FontSize" Value="10"/>

                        <Setter Target="Label12.FontSize" Value="10"/>
                        <Setter Target="Label8.FontSize" Value="10"/>
                        <Setter Target="Label4.FontSize" Value="10"/>
                        <Setter Target="Label0.FontSize" Value="10"/>
                    </VisualState.Setters>
                </VisualState>
            </VisualStateGroup>
        </VisualStateManager.VisualStateGroups>
        <!-- Horizontal divider -->
        <Rectangle Grid.ColumnSpan="21"
                   Height="1"
                   Margin="-3,4,-3,1"
                   HorizontalAlignment="Stretch"
                   VerticalAlignment="Top"
                   Fill="{ThemeResource DividerStrokeColorDefaultBrush}"
                   RadiusX="0.5"
                   RadiusY="0.5"/>
        <TextBlock x:Name="Label60"
                   Grid.Row="2"
                   Grid.Column="4"
                   Style="{StaticResource TextBlockStyle}"
                   Text="60"/>
        <TextBlock x:Name="Label56"
                   Grid.Row="2"
                   Grid.Column="9"
                   Style="{StaticResource TextBlockStyle}"
                   Text="56"/>
        <TextBlock x:Name="Label52"
                   Grid.Row="2"
                   Grid.Column="14"
                   Style="{StaticResource TextBlockStyle}"
                   Text="52"/>
        <TextBlock x:Name="Label48"
                   Grid.Row="2"
                   Grid.Column="19"
                   Style="{StaticResource TextBlockStyle}"
                   Text="48"/>
        <TextBlock x:Name="Label44"
                   Grid.Row="4"
                   Grid.Column="4"
                   Style="{StaticResource TextBlockStyle}"
                   Text="44"/>
        <TextBlock x:Name="Label40"
                   Grid.Row="4"
                   Grid.Column="9"
                   Style="{StaticResource TextBlockStyle}"
                   Text="40"/>
        <TextBlock x:Name="Label36"
                   Grid.Row="4"
                   Grid.Column="14"
                   Style="{StaticResource TextBlockStyle}"
                   Text="36"/>
        <TextBlock x:Name="Label32"
                   Grid.Row="4"
                   Grid.Column="19"
                   Style="{StaticResource TextBlockStyle}"
                   Text="32"/>
        <TextBlock x:Name="Label28"
                   Grid.Row="6"
                   Grid.Column="4"
                   Style="{StaticResource TextBlockStyle}"
                   Text="28"/>
        <TextBlock x:Name="Label24"
                   Grid.Row="6"
                   Grid.Column="9"
                   Style="{StaticResource TextBlockStyle}"
                   Text="24"/>
        <TextBlock x:Name="Label20"
                   Grid.Row="6"
                   Grid.Column="14"
                   Style="{StaticResource TextBlockStyle}"
                   Text="20"/>
        <TextBlock x:Name="Label16"
                   Grid.Row="6"
                   Grid.Column="19"
                   Style="{StaticResource TextBlockStyle}"
                   Text="16"/>
        <TextBlock x:Name="Label12"
                   Grid.Row="8"
                   Grid.Column="4"
                   Style="{StaticResource TextBlockStyle}"
                   Text="12"/>
        <TextBlock x:Name="Label8"
                   Grid.Row="8"
                   Grid.Column="9"
                   Style="{StaticResource TextBlockStyle}"
                   Text="8"/>
        <TextBlock x:Name="Label4"
                   Grid.Row="8"
                   Grid.Column="14"
                   Style="{StaticResource TextBlockStyle}"
                   Text="4"/>
        <TextBlock x:Name="Label0"
                   Grid.Row="8"
                   Grid.Column="19"
                   Style="{StaticResource TextBlockStyle}"
                   Text="0"/>

        <controls:FlipButtons x:Name="Bit63"
                              Grid.Row="1"
                              Grid.Column="1"
                              Style="{StaticResource FlippingToggleButtonStyle}"
                              ButtonId="BINPOS63"
                              Checked="OnBitToggled"
                              Unchecked="OnBitToggled"
                              IsEnabled="{x:Bind ShouldEnableBit(Model.ValueBitLength, 63), Mode=OneWay}">
            <controls:FlipButtons.Tag>
                <x:Int32>63</x:Int32>
            </controls:FlipButtons.Tag>
        </controls:FlipButtons>
        <controls:FlipButtons x:Name="Bit62"
                              Grid.Row="1"
                              Grid.Column="2"
                              Style="{StaticResource FlippingToggleButtonStyle}"
                              ButtonId="BINPOS62"
                              Checked="OnBitToggled"
                              Unchecked="OnBitToggled"
                              IsEnabled="{x:Bind ShouldEnableBit(Model.ValueBitLength, 62), Mode=OneWay}">
            <controls:FlipButtons.Tag>
                <x:Int32>62</x:Int32>
            </controls:FlipButtons.Tag>
        </controls:FlipButtons>
        <controls:FlipButtons x:Name="Bit61"
                              Grid.Row="1"
                              Grid.Column="3"
                              Style="{StaticResource FlippingToggleButtonStyle}"
                              ButtonId="BINPOS61"
                              Checked="OnBitToggled"
                              Unchecked="OnBitToggled"
                              IsEnabled="{x:Bind ShouldEnableBit(Model.ValueBitLength, 61), Mode=OneWay}">
            <controls:FlipButtons.Tag>
                <x:Int32>61</x:Int32>
            </controls:FlipButtons.Tag>
        </controls:FlipButtons>
        <controls:FlipButtons x:Name="Bit60"
                              Grid.Row="1"
                              Grid.Column="4"
                              Style="{StaticResource FlippingToggleButtonStyle}"
                              ButtonId="BINPOS60"
                              Checked="OnBitToggled"
                              Unchecked="OnBitToggled"
                              IsEnabled="{x:Bind ShouldEnableBit(Model.ValueBitLength, 60), Mode=OneWay}">
            <controls:FlipButtons.Tag>
                <x:Int32>60</x:Int32>
            </controls:FlipButtons.Tag>
        </controls:FlipButtons>

        <controls:FlipButtons x:Name="Bit59"
                              Grid.Row="1"
                              Grid.Column="6"
                              Style="{StaticResource FlippingToggleButtonStyle}"
                              ButtonId="BINPOS59"
                              Checked="OnBitToggled"
                              Unchecked="OnBitToggled"
                              IsEnabled="{x:Bind ShouldEnableBit(Model.ValueBitLength, 59), Mode=OneWay}">
            <controls:FlipButtons.Tag>
                <x:Int32>59</x:Int32>
            </controls:FlipButtons.Tag>
        </controls:FlipButtons>
        <controls:FlipButtons x:Name="Bit58"
                              Grid.Row="1"
                              Grid.Column="7"
                              Style="{StaticResource FlippingToggleButtonStyle}"
                              ButtonId="BINPOS58"
                              Checked="OnBitToggled"
                              Unchecked="OnBitToggled"
                              IsEnabled="{x:Bind ShouldEnableBit(Model.ValueBitLength, 58), Mode=OneWay}">
            <controls:FlipButtons.Tag>
                <x:Int32>58</x:Int32>
            </controls:FlipButtons.Tag>
        </controls:FlipButtons>
        <controls:FlipButtons x:Name="Bit57"
                              Grid.Row="1"
                              Grid.Column="8"
                              Style="{StaticResource FlippingToggleButtonStyle}"
                              ButtonId="BINPOS57"
                              Checked="OnBitToggled"
                              Unchecked="OnBitToggled"
                              IsEnabled="{x:Bind ShouldEnableBit(Model.ValueBitLength, 57), Mode=OneWay}">
            <controls:FlipButtons.Tag>
                <x:Int32>57</x:Int32>
            </controls:FlipButtons.Tag>
        </controls:FlipButtons>
        <controls:FlipButtons x:Name="Bit56"
                              Grid.Row="1"
                              Grid.Column="9"
                              Style="{StaticResource FlippingToggleButtonStyle}"
                              ButtonId="BINPOS56"
                              Checked="OnBitToggled"
                              Unchecked="OnBitToggled"
                              IsEnabled="{x:Bind ShouldEnableBit(Model.ValueBitLength, 56), Mode=OneWay}">
            <controls:FlipButtons.Tag>
                <x:Int32>56</x:Int32>
            </controls:FlipButtons.Tag>
        </controls:FlipButtons>

        <controls:FlipButtons x:Name="Bit55"
                              Grid.Row="1"
                              Grid.Column="11"
                              Style="{StaticResource FlippingToggleButtonStyle}"
                              ButtonId="BINPOS55"
                              Checked="OnBitToggled"
                              Unchecked="OnBitToggled"
                              IsEnabled="{x:Bind ShouldEnableBit(Model.ValueBitLength, 55), Mode=OneWay}">
            <controls:FlipButtons.Tag>
                <x:Int32>55</x:Int32>
            </controls:FlipButtons.Tag>
        </controls:FlipButtons>
        <controls:FlipButtons x:Name="Bit54"
                              Grid.Row="1"
                              Grid.Column="12"
                              Style="{StaticResource FlippingToggleButtonStyle}"
                              ButtonId="BINPOS54"
                              Checked="OnBitToggled"
                              Unchecked="OnBitToggled"
                              IsEnabled="{x:Bind ShouldEnableBit(Model.ValueBitLength, 54), Mode=OneWay}">
            <controls:FlipButtons.Tag>
                <x:Int32>54</x:Int32>
            </controls:FlipButtons.Tag>
        </controls:FlipButtons>
        <controls:FlipButtons x:Name="Bit53"
                              Grid.Row="1"
                              Grid.Column="13"
                              Style="{StaticResource FlippingToggleButtonStyle}"
                              ButtonId="BINPOS53"
                              Checked="OnBitToggled"
                              Unchecked="OnBitToggled"
                              IsEnabled="{x:Bind ShouldEnableBit(Model.ValueBitLength, 53), Mode=OneWay}">
            <controls:FlipButtons.Tag>
                <x:Int32>53</x:Int32>
            </controls:FlipButtons.Tag>
        </controls:FlipButtons>
        <controls:FlipButtons x:Name="Bit52"
                              Grid.Row="1"
                              Grid.Column="14"
                              Style="{StaticResource FlippingToggleButtonStyle}"
                              ButtonId="BINPOS52"
                              Checked="OnBitToggled"
                              Unchecked="OnBitToggled"
                              IsEnabled="{x:Bind ShouldEnableBit(Model.ValueBitLength, 52), Mode=OneWay}">
            <controls:FlipButtons.Tag>
                <x:Int32>52</x:Int32>
            </controls:FlipButtons.Tag>
        </controls:FlipButtons>

        <controls:FlipButtons x:Name="Bit51"
                              Grid.Row="1"
                              Grid.Column="16"
                              Style="{StaticResource FlippingToggleButtonStyle}"
                              ButtonId="BINPOS51"
                              Checked="OnBitToggled"
                              Unchecked="OnBitToggled"
                              IsEnabled="{x:Bind ShouldEnableBit(Model.ValueBitLength, 51), Mode=OneWay}">
            <controls:FlipButtons.Tag>
                <x:Int32>51</x:Int32>
            </controls:FlipButtons.Tag>
        </controls:FlipButtons>
        <controls:FlipButtons x:Name="Bit50"
                              Grid.Row="1"
                              Grid.Column="17"
                              Style="{StaticResource FlippingToggleButtonStyle}"
                              ButtonId="BINPOS50"
                              Checked="OnBitToggled"
                              Unchecked="OnBitToggled"
                              IsEnabled="{x:Bind ShouldEnableBit(Model.ValueBitLength, 50), Mode=OneWay}">
            <controls:FlipButtons.Tag>
                <x:Int32>50</x:Int32>
            </controls:FlipButtons.Tag>
        </controls:FlipButtons>
        <controls:FlipButtons x:Name="Bit49"
                              Grid.Row="1"
                              Grid.Column="18"
                              Style="{StaticResource FlippingToggleButtonStyle}"
                              ButtonId="BINPOS49"
                              Checked="OnBitToggled"
                              Unchecked="OnBitToggled"
                              IsEnabled="{x:Bind ShouldEnableBit(Model.ValueBitLength, 49), Mode=OneWay}">
            <controls:FlipButtons.Tag>
                <x:Int32>49</x:Int32>
            </controls:FlipButtons.Tag>
        </controls:FlipButtons>
        <controls:FlipButtons x:Name="Bit48"
                              Grid.Row="1"
                              Grid.Column="19"
                              Style="{StaticResource FlippingToggleButtonStyle}"
                              ButtonId="BINPOS48"
                              Checked="OnBitToggled"
                              Unchecked="OnBitToggled"
                              IsEnabled="{x:Bind ShouldEnableBit(Model.ValueBitLength, 48), Mode=OneWay}">
            <controls:FlipButtons.Tag>
                <x:Int32>48</x:Int32>
            </controls:FlipButtons.Tag>
        </controls:FlipButtons>

        <controls:FlipButtons x:Name="Bit47"
                              Grid.Row="3"
                              Grid.Column="1"
                              Style="{StaticResource FlippingToggleButtonStyle}"
                              ButtonId="BINPOS47"
                              Checked="OnBitToggled"
                              Unchecked="OnBitToggled"
                              IsEnabled="{x:Bind ShouldEnableBit(Model.ValueBitLength, 47), Mode=OneWay}">
            <controls:FlipButtons.Tag>
                <x:Int32>47</x:Int32>
            </controls:FlipButtons.Tag>
        </controls:FlipButtons>
        <controls:FlipButtons x:Name="Bit46"
                              Grid.Row="3"
                              Grid.Column="2"
                              Style="{StaticResource FlippingToggleButtonStyle}"
                              ButtonId="BINPOS46"
                              Checked="OnBitToggled"
                              Unchecked="OnBitToggled"
                              IsEnabled="{x:Bind ShouldEnableBit(Model.ValueBitLength, 46), Mode=OneWay}">
            <controls:FlipButtons.Tag>
                <x:Int32>46</x:Int32>
            </controls:FlipButtons.Tag>
        </controls:FlipButtons>
        <controls:FlipButtons x:Name="Bit45"
                              Grid.Row="3"
                              Grid.Column="3"
                              Style="{StaticResource FlippingToggleButtonStyle}"
                              ButtonId="BINPOS45"
                              Checked="OnBitToggled"
                              Unchecked="OnBitToggled"
                              IsEnabled="{x:Bind ShouldEnableBit(Model.ValueBitLength, 45), Mode=OneWay}">
            <controls:FlipButtons.Tag>
                <x:Int32>45</x:Int32>
            </controls:FlipButtons.Tag>
        </controls:FlipButtons>
        <controls:FlipButtons x:Name="Bit44"
                              Grid.Row="3"
                              Grid.Column="4"
                              Style="{StaticResource FlippingToggleButtonStyle}"
                              ButtonId="BINPOS44"
                              Checked="OnBitToggled"
                              Unchecked="OnBitToggled"
                              IsEnabled="{x:Bind ShouldEnableBit(Model.ValueBitLength, 44), Mode=OneWay}">
            <controls:FlipButtons.Tag>
                <x:Int32>44</x:Int32>
            </controls:FlipButtons.Tag>
        </controls:FlipButtons>

        <controls:FlipButtons x:Name="Bit43"
                              Grid.Row="3"
                              Grid.Column="6"
                              Style="{StaticResource FlippingToggleButtonStyle}"
                              ButtonId="BINPOS43"
                              Checked="OnBitToggled"
                              Unchecked="OnBitToggled"
                              IsEnabled="{x:Bind ShouldEnableBit(Model.ValueBitLength, 43), Mode=OneWay}">
            <controls:FlipButtons.Tag>
                <x:Int32>43</x:Int32>
            </controls:FlipButtons.Tag>
        </controls:FlipButtons>
        <controls:FlipButtons x:Name="Bit42"
                              Grid.Row="3"
                              Grid.Column="7"
                              Style="{StaticResource FlippingToggleButtonStyle}"
                              ButtonId="BINPOS42"
                              Checked="OnBitToggled"
                              Unchecked="OnBitToggled"
                              IsEnabled="{x:Bind ShouldEnableBit(Model.ValueBitLength, 42), Mode=OneWay}">
            <controls:FlipButtons.Tag>
                <x:Int32>42</x:Int32>
            </controls:FlipButtons.Tag>
        </controls:FlipButtons>
        <controls:FlipButtons x:Name="Bit41"
                              Grid.Row="3"
                              Grid.Column="8"
                              Style="{StaticResource FlippingToggleButtonStyle}"
                              ButtonId="BINPOS41"
                              Checked="OnBitToggled"
                              Unchecked="OnBitToggled"
                              IsEnabled="{x:Bind ShouldEnableBit(Model.ValueBitLength, 41), Mode=OneWay}">
            <controls:FlipButtons.Tag>
                <x:Int32>41</x:Int32>
            </controls:FlipButtons.Tag>
        </controls:FlipButtons>
        <controls:FlipButtons x:Name="Bit40"
                              Grid.Row="3"
                              Grid.Column="9"
                              Style="{StaticResource FlippingToggleButtonStyle}"
                              ButtonId="BINPOS40"
                              Checked="OnBitToggled"
                              Unchecked="OnBitToggled"
                              IsEnabled="{x:Bind ShouldEnableBit(Model.ValueBitLength, 40), Mode=OneWay}">
            <controls:FlipButtons.Tag>
                <x:Int32>40</x:Int32>
            </controls:FlipButtons.Tag>
        </controls:FlipButtons>

        <controls:FlipButtons x:Name="Bit39"
                              Grid.Row="3"
                              Grid.Column="11"
                              Style="{StaticResource FlippingToggleButtonStyle}"
                              ButtonId="BINPOS39"
                              Checked="OnBitToggled"
                              Unchecked="OnBitToggled"
                              IsEnabled="{x:Bind ShouldEnableBit(Model.ValueBitLength, 39), Mode=OneWay}">
            <controls:FlipButtons.Tag>
                <x:Int32>39</x:Int32>
            </controls:FlipButtons.Tag>
        </controls:FlipButtons>
        <controls:FlipButtons x:Name="Bit38"
                              Grid.Row="3"
                              Grid.Column="12"
                              Style="{StaticResource FlippingToggleButtonStyle}"
                              ButtonId="BINPOS38"
                              Checked="OnBitToggled"
                              Unchecked="OnBitToggled"
                              IsEnabled="{x:Bind ShouldEnableBit(Model.ValueBitLength, 38), Mode=OneWay}">
            <controls:FlipButtons.Tag>
                <x:Int32>38</x:Int32>
            </controls:FlipButtons.Tag>
        </controls:FlipButtons>
        <controls:FlipButtons x:Name="Bit37"
                              Grid.Row="3"
                              Grid.Column="13"
                              Style="{StaticResource FlippingToggleButtonStyle}"
                              ButtonId="BINPOS37"
                              Checked="OnBitToggled"
                              Unchecked="OnBitToggled"
                              IsEnabled="{x:Bind ShouldEnableBit(Model.ValueBitLength, 37), Mode=OneWay}">
            <controls:FlipButtons.Tag>
                <x:Int32>37</x:Int32>
            </controls:FlipButtons.Tag>
        </controls:FlipButtons>
        <controls:FlipButtons x:Name="Bit36"
                              Grid.Row="3"
                              Grid.Column="14"
                              Style="{StaticResource FlippingToggleButtonStyle}"
                              ButtonId="BINPOS36"
                              Checked="OnBitToggled"
                              Unchecked="OnBitToggled"
                              IsEnabled="{x:Bind ShouldEnableBit(Model.ValueBitLength, 36), Mode=OneWay}">
            <controls:FlipButtons.Tag>
                <x:Int32>36</x:Int32>
            </controls:FlipButtons.Tag>
        </controls:FlipButtons>

        <controls:FlipButtons x:Name="Bit35"
                              Grid.Row="3"
                              Grid.Column="16"
                              Style="{StaticResource FlippingToggleButtonStyle}"
                              ButtonId="BINPOS35"
                              Checked="OnBitToggled"
                              Unchecked="OnBitToggled"
                              IsEnabled="{x:Bind ShouldEnableBit(Model.ValueBitLength, 35), Mode=OneWay}">
            <controls:FlipButtons.Tag>
                <x:Int32>35</x:Int32>
            </controls:FlipButtons.Tag>
        </controls:FlipButtons>
        <controls:FlipButtons x:Name="Bit34"
                              Grid.Row="3"
                              Grid.Column="17"
                              Style="{StaticResource FlippingToggleButtonStyle}"
                              ButtonId="BINPOS34"
                              Checked="OnBitToggled"
                              Unchecked="OnBitToggled"
                              IsEnabled="{x:Bind ShouldEnableBit(Model.ValueBitLength, 34), Mode=OneWay}">
            <controls:FlipButtons.Tag>
                <x:Int32>34</x:Int32>
            </controls:FlipButtons.Tag>
        </controls:FlipButtons>
        <controls:FlipButtons x:Name="Bit33"
                              Grid.Row="3"
                              Grid.Column="18"
                              Style="{StaticResource FlippingToggleButtonStyle}"
                              ButtonId="BINPOS33"
                              Checked="OnBitToggled"
                              Unchecked="OnBitToggled"
                              IsEnabled="{x:Bind ShouldEnableBit(Model.ValueBitLength, 33), Mode=OneWay}">
            <controls:FlipButtons.Tag>
                <x:Int32>33</x:Int32>
            </controls:FlipButtons.Tag>
        </controls:FlipButtons>
        <controls:FlipButtons x:Name="Bit32"
                              Grid.Row="3"
                              Grid.Column="19"
                              Style="{StaticResource FlippingToggleButtonStyle}"
                              ButtonId="BINPOS32"
                              Checked="OnBitToggled"
                              Unchecked="OnBitToggled"
                              IsEnabled="{x:Bind ShouldEnableBit(Model.ValueBitLength, 32), Mode=OneWay}">
            <controls:FlipButtons.Tag>
                <x:Int32>32</x:Int32>
            </controls:FlipButtons.Tag>
        </controls:FlipButtons>

        <controls:FlipButtons x:Name="Bit31"
                              Grid.Row="5"
                              Grid.Column="1"
                              Style="{StaticResource FlippingToggleButtonStyle}"
                              ButtonId="BINPOS31"
                              Checked="OnBitToggled"
                              Unchecked="OnBitToggled"
                              IsEnabled="{x:Bind ShouldEnableBit(Model.ValueBitLength, 31), Mode=OneWay}">
            <controls:FlipButtons.Tag>
                <x:Int32>31</x:Int32>
            </controls:FlipButtons.Tag>
        </controls:FlipButtons>
        <controls:FlipButtons x:Name="Bit30"
                              Grid.Row="5"
                              Grid.Column="2"
                              Style="{StaticResource FlippingToggleButtonStyle}"
                              ButtonId="BINPOS30"
                              Checked="OnBitToggled"
                              Unchecked="OnBitToggled"
                              IsEnabled="{x:Bind ShouldEnableBit(Model.ValueBitLength, 30), Mode=OneWay}">
            <controls:FlipButtons.Tag>
                <x:Int32>30</x:Int32>
            </controls:FlipButtons.Tag>
        </controls:FlipButtons>
        <controls:FlipButtons x:Name="Bit29"
                              Grid.Row="5"
                              Grid.Column="3"
                              Style="{StaticResource FlippingToggleButtonStyle}"
                              ButtonId="BINPOS29"
                              Checked="OnBitToggled"
                              Unchecked="OnBitToggled"
                              IsEnabled="{x:Bind ShouldEnableBit(Model.ValueBitLength, 29), Mode=OneWay}">
            <controls:FlipButtons.Tag>
                <x:Int32>29</x:Int32>
            </controls:FlipButtons.Tag>
        </controls:FlipButtons>
        <controls:FlipButtons x:Name="Bit28"
                              Grid.Row="5"
                              Grid.Column="4"
                              Style="{StaticResource FlippingToggleButtonStyle}"
                              ButtonId="BINPOS28"
                              Checked="OnBitToggled"
                              Unchecked="OnBitToggled"
                              IsEnabled="{x:Bind ShouldEnableBit(Model.ValueBitLength, 28), Mode=OneWay}">
            <controls:FlipButtons.Tag>
                <x:Int32>28</x:Int32>
            </controls:FlipButtons.Tag>
        </controls:FlipButtons>

        <controls:FlipButtons x:Name="Bit27"
                              Grid.Row="5"
                              Grid.Column="6"
                              Style="{StaticResource FlippingToggleButtonStyle}"
                              ButtonId="BINPOS27"
                              Checked="OnBitToggled"
                              Unchecked="OnBitToggled"
                              IsEnabled="{x:Bind ShouldEnableBit(Model.ValueBitLength, 27), Mode=OneWay}">
            <controls:FlipButtons.Tag>
                <x:Int32>27</x:Int32>
            </controls:FlipButtons.Tag>
        </controls:FlipButtons>
        <controls:FlipButtons x:Name="Bit26"
                              Grid.Row="5"
                              Grid.Column="7"
                              Style="{StaticResource FlippingToggleButtonStyle}"
                              ButtonId="BINPOS26"
                              Checked="OnBitToggled"
                              Unchecked="OnBitToggled"
                              IsEnabled="{x:Bind ShouldEnableBit(Model.ValueBitLength, 26), Mode=OneWay}">
            <controls:FlipButtons.Tag>
                <x:Int32>26</x:Int32>
            </controls:FlipButtons.Tag>
        </controls:FlipButtons>
        <controls:FlipButtons x:Name="Bit25"
                              Grid.Row="5"
                              Grid.Column="8"
                              Style="{StaticResource FlippingToggleButtonStyle}"
                              ButtonId="BINPOS25"
                              Checked="OnBitToggled"
                              Unchecked="OnBitToggled"
                              IsEnabled="{x:Bind ShouldEnableBit(Model.ValueBitLength, 25), Mode=OneWay}">
            <controls:FlipButtons.Tag>
                <x:Int32>25</x:Int32>
            </controls:FlipButtons.Tag>
        </controls:FlipButtons>
        <controls:FlipButtons x:Name="Bit24"
                              Grid.Row="5"
                              Grid.Column="9"
                              Style="{StaticResource FlippingToggleButtonStyle}"
                              ButtonId="BINPOS24"
                              Checked="OnBitToggled"
                              Unchecked="OnBitToggled"
                              IsEnabled="{x:Bind ShouldEnableBit(Model.ValueBitLength, 24), Mode=OneWay}">
            <controls:FlipButtons.Tag>
                <x:Int32>24</x:Int32>
            </controls:FlipButtons.Tag>
        </controls:FlipButtons>

        <controls:FlipButtons x:Name="Bit23"
                              Grid.Row="5"
                              Grid.Column="11"
                              Style="{StaticResource FlippingToggleButtonStyle}"
                              ButtonId="BINPOS23"
                              Checked="OnBitToggled"
                              Unchecked="OnBitToggled"
                              IsEnabled="{x:Bind ShouldEnableBit(Model.ValueBitLength, 23), Mode=OneWay}">
            <controls:FlipButtons.Tag>
                <x:Int32>23</x:Int32>
            </controls:FlipButtons.Tag>
        </controls:FlipButtons>
        <controls:FlipButtons x:Name="Bit22"
                              Grid.Row="5"
                              Grid.Column="12"
                              Style="{StaticResource FlippingToggleButtonStyle}"
                              ButtonId="BINPOS22"
                              Checked="OnBitToggled"
                              Unchecked="OnBitToggled"
                              IsEnabled="{x:Bind ShouldEnableBit(Model.ValueBitLength, 22), Mode=OneWay}">
            <controls:FlipButtons.Tag>
                <x:Int32>22</x:Int32>
            </controls:FlipButtons.Tag>
        </controls:FlipButtons>
        <controls:FlipButtons x:Name="Bit21"
                              Grid.Row="5"
                              Grid.Column="13"
                              Style="{StaticResource FlippingToggleButtonStyle}"
                              ButtonId="BINPOS21"
                              Checked="OnBitToggled"
                              Unchecked="OnBitToggled"
                              IsEnabled="{x:Bind ShouldEnableBit(Model.ValueBitLength, 21), Mode=OneWay}">
            <controls:FlipButtons.Tag>
                <x:Int32>21</x:Int32>
            </controls:FlipButtons.Tag>
        </controls:FlipButtons>
        <controls:FlipButtons x:Name="Bit20"
                              Grid.Row="5"
                              Grid.Column="14"
                              Style="{StaticResource FlippingToggleButtonStyle}"
                              ButtonId="BINPOS20"
                              Checked="OnBitToggled"
                              Unchecked="OnBitToggled"
                              IsEnabled="{x:Bind ShouldEnableBit(Model.ValueBitLength, 20), Mode=OneWay}">
            <controls:FlipButtons.Tag>
                <x:Int32>20</x:Int32>
            </controls:FlipButtons.Tag>
        </controls:FlipButtons>

        <controls:FlipButtons x:Name="Bit19"
                              Grid.Row="5"
                              Grid.Column="16"
                              Style="{StaticResource FlippingToggleButtonStyle}"
                              ButtonId="BINPOS19"
                              Checked="OnBitToggled"
                              Unchecked="OnBitToggled"
                              IsEnabled="{x:Bind ShouldEnableBit(Model.ValueBitLength, 19), Mode=OneWay}">
            <controls:FlipButtons.Tag>
                <x:Int32>19</x:Int32>
            </controls:FlipButtons.Tag>
        </controls:FlipButtons>
        <controls:FlipButtons x:Name="Bit18"
                              Grid.Row="5"
                              Grid.Column="17"
                              Style="{StaticResource FlippingToggleButtonStyle}"
                              ButtonId="BINPOS18"
                              Checked="OnBitToggled"
                              Unchecked="OnBitToggled"
                              IsEnabled="{x:Bind ShouldEnableBit(Model.ValueBitLength, 18), Mode=OneWay}">
            <controls:FlipButtons.Tag>
                <x:Int32>18</x:Int32>
            </controls:FlipButtons.Tag>
        </controls:FlipButtons>
        <controls:FlipButtons x:Name="Bit17"
                              Grid.Row="5"
                              Grid.Column="18"
                              Style="{StaticResource FlippingToggleButtonStyle}"
                              ButtonId="BINPOS17"
                              Checked="OnBitToggled"
                              Unchecked="OnBitToggled"
                              IsEnabled="{x:Bind ShouldEnableBit(Model.ValueBitLength, 17), Mode=OneWay}">
            <controls:FlipButtons.Tag>
                <x:Int32>17</x:Int32>
            </controls:FlipButtons.Tag>
        </controls:FlipButtons>
        <controls:FlipButtons x:Name="Bit16"
                              Grid.Row="5"
                              Grid.Column="19"
                              Style="{StaticResource FlippingToggleButtonStyle}"
                              ButtonId="BINPOS16"
                              Checked="OnBitToggled"
                              Unchecked="OnBitToggled"
                              IsEnabled="{x:Bind ShouldEnableBit(Model.ValueBitLength, 16), Mode=OneWay}">
            <controls:FlipButtons.Tag>
                <x:Int32>16</x:Int32>
            </controls:FlipButtons.Tag>
        </controls:FlipButtons>

        <controls:FlipButtons x:Name="Bit15"
                              Grid.Row="7"
                              Grid.Column="1"
                              Style="{StaticResource FlippingToggleButtonStyle}"
                              ButtonId="BINPOS15"
                              Checked="OnBitToggled"
                              Unchecked="OnBitToggled"
                              IsEnabled="{x:Bind ShouldEnableBit(Model.ValueBitLength, 15), Mode=OneWay}">
            <controls:FlipButtons.Tag>
                <x:Int32>15</x:Int32>
            </controls:FlipButtons.Tag>
        </controls:FlipButtons>
        <controls:FlipButtons x:Name="Bit14"
                              Grid.Row="7"
                              Grid.Column="2"
                              Style="{StaticResource FlippingToggleButtonStyle}"
                              ButtonId="BINPOS14"
                              Checked="OnBitToggled"
                              Unchecked="OnBitToggled"
                              IsEnabled="{x:Bind ShouldEnableBit(Model.ValueBitLength, 14), Mode=OneWay}">
            <controls:FlipButtons.Tag>
                <x:Int32>14</x:Int32>
            </controls:FlipButtons.Tag>
        </controls:FlipButtons>
        <controls:FlipButtons x:Name="Bit13"
                              Grid.Row="7"
                              Grid.Column="3"
                              Style="{StaticResource FlippingToggleButtonStyle}"
                              ButtonId="BINPOS13"
                              Checked="OnBitToggled"
                              Unchecked="OnBitToggled"
                              IsEnabled="{x:Bind ShouldEnableBit(Model.ValueBitLength, 13), Mode=OneWay}">
            <controls:FlipButtons.Tag>
                <x:Int32>13</x:Int32>
            </controls:FlipButtons.Tag>
        </controls:FlipButtons>
        <controls:FlipButtons x:Name="Bit12"
                              Grid.Row="7"
                              Grid.Column="4"
                              Style="{StaticResource FlippingToggleButtonStyle}"
                              ButtonId="BINPOS12"
                              Checked="OnBitToggled"
                              Unchecked="OnBitToggled"
                              IsEnabled="{x:Bind ShouldEnableBit(Model.ValueBitLength, 12), Mode=OneWay}">
            <controls:FlipButtons.Tag>
                <x:Int32>12</x:Int32>
            </controls:FlipButtons.Tag>
        </controls:FlipButtons>

        <controls:FlipButtons x:Name="Bit11"
                              Grid.Row="7"
                              Grid.Column="6"
                              Style="{StaticResource FlippingToggleButtonStyle}"
                              ButtonId="BINPOS11"
                              Checked="OnBitToggled"
                              Unchecked="OnBitToggled"
                              IsEnabled="{x:Bind ShouldEnableBit(Model.ValueBitLength, 11), Mode=OneWay}">
            <controls:FlipButtons.Tag>
                <x:Int32>11</x:Int32>
            </controls:FlipButtons.Tag>
        </controls:FlipButtons>
        <controls:FlipButtons x:Name="Bit10"
                              Grid.Row="7"
                              Grid.Column="7"
                              Style="{StaticResource FlippingToggleButtonStyle}"
                              ButtonId="BINPOS10"
                              Checked="OnBitToggled"
                              Unchecked="OnBitToggled"
                              IsEnabled="{x:Bind ShouldEnableBit(Model.ValueBitLength, 10), Mode=OneWay}">
            <controls:FlipButtons.Tag>
                <x:Int32>10</x:Int32>
            </controls:FlipButtons.Tag>
        </controls:FlipButtons>
        <controls:FlipButtons x:Name="Bit9"
                              Grid.Row="7"
                              Grid.Column="8"
                              Style="{StaticResource FlippingToggleButtonStyle}"
                              ButtonId="BINPOS9"
                              Checked="OnBitToggled"
                              Unchecked="OnBitToggled"
                              IsEnabled="{x:Bind ShouldEnableBit(Model.ValueBitLength, 9), Mode=OneWay}">
            <controls:FlipButtons.Tag>
                <x:Int32>9</x:Int32>
            </controls:FlipButtons.Tag>
        </controls:FlipButtons>
        <controls:FlipButtons x:Name="Bit8"
                              Grid.Row="7"
                              Grid.Column="9"
                              Style="{StaticResource FlippingToggleButtonStyle}"
                              ButtonId="BINPOS8"
                              Checked="OnBitToggled"
                              Unchecked="OnBitToggled"
                              IsEnabled="{x:Bind ShouldEnableBit(Model.ValueBitLength, 8), Mode=OneWay}">
            <controls:FlipButtons.Tag>
                <x:Int32>8</x:Int32>
            </controls:FlipButtons.Tag>
        </controls:FlipButtons>

        <controls:FlipButtons x:Name="Bit7"
                              Grid.Row="7"
                              Grid.Column="11"
                              Style="{StaticResource FlippingToggleButtonStyle}"
                              ButtonId="BINPOS7"
                              Checked="OnBitToggled"
                              Unchecked="OnBitToggled">
            <controls:FlipButtons.Tag>
                <x:Int32>7</x:Int32>
            </controls:FlipButtons.Tag>
        </controls:FlipButtons>
        <controls:FlipButtons x:Name="Bit6"
                              Grid.Row="7"
                              Grid.Column="12"
                              Style="{StaticResource FlippingToggleButtonStyle}"
                              ButtonId="BINPOS6"
                              Checked="OnBitToggled"
                              Unchecked="OnBitToggled">
            <controls:FlipButtons.Tag>
                <x:Int32>6</x:Int32>
            </controls:FlipButtons.Tag>
        </controls:FlipButtons>
        <controls:FlipButtons x:Name="Bit5"
                              Grid.Row="7"
                              Grid.Column="13"
                              Style="{StaticResource FlippingToggleButtonStyle}"
                              ButtonId="BINPOS5"
                              Checked="OnBitToggled"
                              Unchecked="OnBitToggled">
            <controls:FlipButtons.Tag>
                <x:Int32>5</x:Int32>
            </controls:FlipButtons.Tag>
        </controls:FlipButtons>
        <controls:FlipButtons x:Name="Bit4"
                              Grid.Row="7"
                              Grid.Column="14"
                              Style="{StaticResource FlippingToggleButtonStyle}"
                              ButtonId="BINPOS4"
                              Checked="OnBitToggled"
                              Unchecked="OnBitToggled">
            <controls:FlipButtons.Tag>
                <x:Int32>4</x:Int32>
            </controls:FlipButtons.Tag>
        </controls:FlipButtons>

        <controls:FlipButtons x:Name="Bit3"
                              Grid.Row="7"
                              Grid.Column="16"
                              Style="{StaticResource FlippingToggleButtonStyle}"
                              ButtonId="BINPOS3"
                              Checked="OnBitToggled"
                              Unchecked="OnBitToggled">
            <controls:FlipButtons.Tag>
                <x:Int32>3</x:Int32>
            </controls:FlipButtons.Tag>
        </controls:FlipButtons>
        <controls:FlipButtons x:Name="Bit2"
                              Grid.Row="7"
                              Grid.Column="17"
                              Style="{StaticResource FlippingToggleButtonStyle}"
                              ButtonId="BINPOS2"
                              Checked="OnBitToggled"
                              Unchecked="OnBitToggled">
            <controls:FlipButtons.Tag>
                <x:Int32>2</x:Int32>
            </controls:FlipButtons.Tag>
        </controls:FlipButtons>
        <controls:FlipButtons x:Name="Bit1"
                              Grid.Row="7"
                              Grid.Column="18"
                              Style="{StaticResource FlippingToggleButtonStyle}"
                              ButtonId="BINPOS1"
                              Checked="OnBitToggled"
                              Unchecked="OnBitToggled">
            <controls:FlipButtons.Tag>
                <x:Int32>1</x:Int32>
            </controls:FlipButtons.Tag>
        </controls:FlipButtons>
        <controls:FlipButtons x:Name="Bit0"
                              Grid.Row="7"
                              Grid.Column="19"
                              Style="{StaticResource FlippingToggleButtonStyle}"
                              ButtonId="BINPOS0"
                              Checked="OnBitToggled"
                              Unchecked="OnBitToggled">
            <controls:FlipButtons.Tag>
                <x:Int32>0</x:Int32>
            </controls:FlipButtons.Tag>
        </controls:FlipButtons>
    </Grid>
</UserControl>
