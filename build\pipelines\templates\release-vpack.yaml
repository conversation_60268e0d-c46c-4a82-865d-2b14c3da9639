# This template contains a job to create a VPack. The VPack is used to preinstall the app in a
# Windows OS build.

jobs:
- job: ReleaseVPack
  dependsOn: Package
  variables:
    skipComponentGovernanceDetection: true
  templateContext:
    outputs:
    - output: pipelineArtifact
      displayName: Publish vpack\app artifact with vpack manifest
      targetPath: $(XES_VPACKMANIFESTDIRECTORY)\$(XES_VPACKMANIFESTNAME)
      artifactName: vpackManifest
      sbomEnabled: false

  steps:
  - checkout: none

  - download: current
    displayName: Download msixBundleSigned artifact
    artifact: msixBundleSigned

  - task: CopyFiles@2
    displayName: Co<PERSON> signed Ms<PERSON>Bundle to vpack staging folder
    inputs:
      sourceFolder: $(Pipeline.Workspace)\msixBundleSigned
      contents: Microsoft.WindowsCalculator_8wekyb3d8bbwe.msixbundle
      targetFolder: $(Pipeline.Workspace)\vpack\msixBundle

  - task: AzureCLI@2
    displayName: Register SBOM sign service connection
    inputs:
      azureSubscription: Essential Experiences SBOMSign PME
      scriptType: ps
      scriptLocation: inlineScript
      inlineScript: Write-Host "Registering service connection for current run"
      visibleAzLogin: false

  - task: UniversalPackages@0
    displayName: Download internals package
    inputs:
      command: download
      downloadDirectory: $(Build.SourcesDirectory)
      vstsFeed: WindowsInboxApps
      vstsFeedPackage: calculator-internals
      vstsPackageVersion: 0.0.117

  - pwsh: |
      $configPath = "$(Build.SourcesDirectory)\Tools\Build\Signing\ESRP-auth.json"
      $auth = Get-Content -Raw $configPath | ConvertFrom-Json
      $sbomKeyCode = $auth._ExtraContext.SbomKeyCode
      echo $sbomKeyCode
      echo "##vso[task.setvariable variable=SbomKeyCode]$sbomKeyCode"
    displayName: Get SBOM Key Code

  - task: PkgESVPack@12
    displayName: Create and push vpack for app
    env:
      SYSTEM_ACCESSTOKEN: $(System.AccessToken)
    inputs:
      sourceDirectory: $(Pipeline.Workspace)\vpack\msixBundle
      description: VPack for the Calculator Application
      pushPkgName: calculator.app
      version: $(versionMajor).$(versionMinor).$(versionBuild)
      owner: paxeeapps
      provData: true
      taskLogVerbosity: Diagnostic
      coseUsageScenario: 'product'
      signSbom: true
      sbomKeyCode: $(SbomKeyCode)
      pathToEsrpAuthJson: '$(Build.SourcesDirectory)\Tools\Build\Signing\ESRP-auth.json'
