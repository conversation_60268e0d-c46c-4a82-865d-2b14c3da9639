# This template contains a job which takes .msix packages which were built separately for each
# architecture (arm, x86, etc.) and combines them into a single .msixbundle. In release builds,
# this job also signs the bundle and creates StoreBroker packages.

parameters:
  signBundle: false
  createStoreBrokerPackages: false

jobs:
- job: Package
  dependsOn:
    - Buildx64
    - Buildx86
    - BuildARM
    - BuildARM64
  condition: |
    and
    (
      in(dependencies.Buildx64.result, 'Succeeded', 'SucceededWithIssues', 'Skipped'),
      in(dependencies.Buildx86.result, 'Succeeded', 'SucceededWithIssues', 'Skipped'),
      in(dependencies.BuildARM.result, 'Succeeded', 'SucceededWithIssues', 'Skipped'),
      in(dependencies.BuildARM64.result, 'Succeeded', 'SucceededWithIssues', 'Skipped')
    )
  variables:
    skipComponentGovernanceDetection: true
    StoreBrokerMediaRootPath: $(TEMP)\SBMedia
    StoreBrokerPackagePath: $(Build.ArtifactStagingDirectory)\storeBrokerPayload
    PackageX86: $[in(dependencies.Buildx86.result, 'Succeeded', 'SucceededWithIssues')]
    PackageX64: $[in(dependencies.Buildx64.result, 'Succeeded', 'SucceededWithIssues')]
    PackageARM: $[in(dependencies.BuildARM.result, 'Succeeded', 'SucceededWithIssues')]
    PackageARM64: $[in(dependencies.BuildARM64.result, 'Succeeded', 'SucceededWithIssues')]
  templateContext:
    outputs:
    - ${{ if eq(parameters.signBundle, false) }}:
      - output: pipelineArtifact
        displayName: Publish MsixBundle artifact
        targetPath: $(Build.ArtifactStagingDirectory)\msixBundle
        artifactName: msixBundle
    - ${{ else }}:
      - output: pipelineArtifact
        displayName: Publish MsixBundleSigned artifact
        targetPath: $(Build.ArtifactStagingDirectory)\msixBundle
        artifactName: msixBundleSigned
      - ${{ if eq(parameters.createStoreBrokerPackages, true) }}:
        - output: pipelineArtifact
          displayName: Publish StoreBroker Payload artifact
          targetPath: $(StoreBrokerPackagePath)
          artifactName: storeBrokerPayload

  steps:
  - checkout: self
    fetchDepth: 1

  - task: DownloadPipelineArtifact@2
    displayName: Download all .msix artifacts (x86)
    condition: and(succeeded(), eq(variables.PackageX86, 'true'))
    inputs:
      artifactName: drop-x86
      itemPattern: '**/*.msix'
      targetPath: $(Build.ArtifactStagingDirectory)\drop\x86

  - task: DownloadPipelineArtifact@2
    displayName: Download all .msix artifacts (x64)
    condition: and(succeeded(), eq(variables.PackageX64, 'true'))
    inputs:
      artifactName: drop-x64
      itemPattern: '**/*.msix'
      targetPath: $(Build.ArtifactStagingDirectory)\drop\x64

  - task: DownloadPipelineArtifact@2
    displayName: Download all .msix artifacts (ARM)
    condition: and(succeeded(), eq(variables.PackageARM, 'true'))
    inputs:
      artifactName: drop-ARM
      itemPattern: '**/*.msix'
      targetPath: $(Build.ArtifactStagingDirectory)\drop\ARM

  - task: DownloadPipelineArtifact@2
    displayName: Download all .msix artifacts (ARM64)
    condition: and(succeeded(), eq(variables.PackageARM64, 'true'))
    inputs:
      artifactName: drop-ARM64
      itemPattern: '**/*.msix'
      targetPath: $(Build.ArtifactStagingDirectory)\drop\ARM64

  - ${{ if or(eq(parameters.createStoreBrokerPackages, true), eq(parameters.signBundle, true)) }}:
    - task: UniversalPackages@0
      displayName: Download internals package
      inputs:
        command: download
        downloadDirectory: $(Build.SourcesDirectory)
        vstsFeed: WindowsInboxApps
        vstsFeedPackage: calculator-internals
        vstsPackageVersion: 0.0.117

  - task: PowerShell@2
    displayName: Generate MsixBundle mapping
    inputs:
      filePath: $(Build.SourcesDirectory)\build\scripts\CreateMsixBundleMapping.ps1
      arguments: '-InputPath $(Build.ArtifactStagingDirectory)\drop -ProjectName Calculator -OutputFile $(Build.BinariesDirectory)\MsixBundleMapping.txt'

  - powershell: |
      $buildVersion = [version]$Env:BUILDVERSION
      $bundleVersion = "2021.$($buildVersion.Minor).$($buildVersion.Build).$($buildVersion.Revision)"
      & "C:\Program Files (x86)\Windows Kits\10\bin\10.0.22000.0\x64\MakeAppx.exe" bundle /v /bv $bundleVersion /f $Env:MAPPINGFILEPATH /p $Env:OUTPUTPATH
    displayName: Make MsixBundle
    env:
      BUILDVERSION: $(Build.BuildNumber)
      MAPPINGFILEPATH: $(Build.BinariesDirectory)\MsixBundleMapping.txt
      OUTPUTPATH: $(Build.BinariesDirectory)\Microsoft.WindowsCalculator_8wekyb3d8bbwe.msixbundle

  - task: CopyFiles@2
    displayName: Copy MsixBundle to staging directory
    inputs:
      sourceFolder: $(Build.BinariesDirectory)
      contents: Microsoft.WindowsCalculator_8wekyb3d8bbwe.msixbundle
      targetFolder: $(Build.ArtifactStagingDirectory)\msixBundle

  - ${{ if eq(parameters.signBundle, true) }}:
    - pwsh: |
        $configPath = "$(Build.SourcesDirectory)\Tools\Build\Signing\ESRP-codesign.json"
        $config = Get-Content -Raw $configPath | ConvertFrom-Json
        $esrpAppRegClientId = $config.AppRegistrationClientId
        $esrpAppRegTenantId = $config.AppRegistrationClientId
        $esrpClientId = $config.EsrpClientId
        echo AppRegistrationClientId:$esrpAppRegClientId, AppRegTenantId:$esrpAppRegTenantId, EsrpClientId:$esrpClientId
        echo "##vso[task.setvariable variable=EsrpAppRegClientId]$esrpAppRegClientId"
        echo "##vso[task.setvariable variable=EsrpAppRegTenantId]$esrpAppRegTenantId"
        echo "##vso[task.setvariable variable=EsrpClientId]$esrpClientId"
      displayName: Get ESRP config

    - task: EsrpCodeSigning@5
      displayName: Send msixbundle to code signing service
      inputs:
        ConnectedServiceName: Essential Experiences Codesign PME
        UseMSIAuthentication: true
        AppRegistrationClientId: $(EsrpAppRegClientId)
        AppRegistrationTenantId: $(EsrpAppRegTenantId)
        EsrpClientId: $(EsrpClientId)
        AuthAKVName: EE-Apps-CodeSign-KV
        AuthCertName: EE-Auth-Cert
        AuthSignCertName: EE-Codesign-Cert
        FolderPath: $(Build.ArtifactStagingDirectory)\msixBundle
        Pattern: Microsoft.WindowsCalculator_8wekyb3d8bbwe.msixbundle
        signConfigType: inlineSignParams
        inlineOperation: |
          [
            {
              "CertTemplateName": "WINMSAPP1ST",
              "CertSubjectName": "CN=Microsoft Corporation, O=Microsoft Corporation, L=Redmond, S=Washington, C=US",
              "KeyCode": "Dynamic",
              "OperationCode": "SigntoolvNextSign",
              "Parameters": {
                "OpusName": "Microsoft",
                "OpusInfo": "http://www.microsoft.com",
                "FileDigest": "/fd \"SHA256\"",
                "TimeStamp": "/tr \"http://rfc3161.gtm.corp.microsoft.com/TSS/HttpTspServer\" /td sha256"
              },
              "ToolName": "sign",
              "ToolVersion": "1.0"
            },
            {
              "CertTemplateName": "WINMSAPP1ST",
              "CertSubjectName": "CN=Microsoft Corporation, O=Microsoft Corporation, L=Redmond, S=Washington, C=US",
              "KeyCode": "Dynamic",
              "OperationCode": "SigntoolvNextVerify",
              "Parameters": {},
              "ToolName": "sign",
              "ToolVersion": "1.0"
            }
          ]

    - ${{ if eq(parameters.createStoreBrokerPackages, true) }}:
      - powershell: |
          # Just modify this line to indicate where your en-us PDP file is. Leave the other lines alone.
          $enUSPdpFilePath = "$(Build.SourcesDirectory)\PDP\en-US\PDP.xml"

          # This is going to save the release value from the PDP file to $(SBMediaReleaseVersion)
          # which you can then refer to in the UniversalPackages task.
          $release = ([xml](Get-Content $enUSPdpFilePath)).ProductDescription.Release.Trim()
          Write-Host "##vso[task.setvariable variable=SBMediaReleaseVersion;]$release"
        displayName: Determine the PDP Media release version from the en-us PDP file

      - task: UniversalPackages@0
        displayName: Download PDP media (screenshots, trailers) universal package
        inputs:
          command: download
          downloadDirectory: $(StoreBrokerMediaRootPath)/$(SBMediaReleaseVersion)
          vstsFeed: WindowsInboxApps
          vstsFeedPackage: calculator-pdp-media
          vstsPackageVersion: $(SBMediaReleaseVersion)

      - task: MS-RDX-MRO.windows-store-publish-dev.package-task.store-package@3
        displayName: Create StoreBroker Payload
        inputs:
          serviceEndpoint: Calculator StoreBroker FC
          sbConfigPath: Tools/Build/StoreBroker/SBCalculatorConfig.json
          sourceFolder: $(Build.ArtifactStagingDirectory)/msixBundle
          contents: Microsoft.WindowsCalculator_8wekyb3d8bbwe.msixbundle
          pdpPath: $(Build.SourcesDirectory)\PDP
          pdpInclude: PDP.xml
          pdpMediaPath: $(StoreBrokerMediaRootPath)
          outSBPackagePath: $(StoreBrokerPackagePath)
          outSBName: SBCalculator
