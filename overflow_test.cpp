#include <iostream>
#include <cstdint>
#include <limits>
#include <iomanip>

// Mimic the calculator's data types
typedef uint32_t MANTTYPE;
typedef uint64_t TWO_MANTTYPE;

static constexpr uint32_t BASEX = 0x80000000; // Internal radix from calculator

// Test function that mimics the vulnerable multiplication logic
void test_multiplication_overflow() {
    std::cout << "=== Testing Multiplication Overflow Scenarios ===" << std::endl;
    
    // Test Case 1: Maximum values that could cause overflow
    MANTTYPE da_max = std::numeric_limits<MANTTYPE>::max(); // 0xFFFFFFFF
    MANTTYPE pchb_max = std::numeric_limits<MANTTYPE>::max(); // 0xFFFFFFFF
    
    std::cout << "da_max = 0x" << std::hex << da_max << std::endl;
    std::cout << "pchb_max = 0x" << std::hex << pchb_max << std::endl;
    
    // This is the vulnerable line from num.cpp:250
    TWO_MANTTYPE mcy = (TWO_MANTTYPE)da_max * pchb_max;
    
    std::cout << "mcy = da_max * pchb_max = 0x" << std::hex << mcy << std::endl;
    std::cout << "mcy in decimal = " << std::dec << mcy << std::endl;
    
    // Check if this fits in TWO_MANTTYPE (uint64_t)
    std::cout << "TWO_MANTTYPE max = 0x" << std::hex << std::numeric_limits<TWO_MANTTYPE>::max() << std::endl;
    
    if (mcy == std::numeric_limits<TWO_MANTTYPE>::max()) {
        std::cout << "⚠️  POTENTIAL OVERFLOW: Result equals maximum value!" << std::endl;
    }
    
    std::cout << std::endl;
}

// Test the carry addition logic that could overflow
void test_carry_overflow() {
    std::cout << "=== Testing Carry Addition Overflow ===" << std::endl;
    
    // Simulate the vulnerable line from num.cpp:263
    TWO_MANTTYPE cy = std::numeric_limits<TWO_MANTTYPE>::max() - 1;
    TWO_MANTTYPE pchc_icdigit = std::numeric_limits<MANTTYPE>::max();
    TWO_MANTTYPE mcy_mod = std::numeric_limits<MANTTYPE>::max();
    
    std::cout << "cy = 0x" << std::hex << cy << std::endl;
    std::cout << "pchc[icdigit] = 0x" << std::hex << pchc_icdigit << std::endl;
    std::cout << "mcy % radix = 0x" << std::hex << mcy_mod << std::endl;
    
    // This is the vulnerable calculation
    TWO_MANTTYPE result = cy + pchc_icdigit + mcy_mod;
    
    std::cout << "Result = cy + pchc[icdigit] + (mcy % radix)" << std::endl;
    std::cout << "Result = 0x" << std::hex << result << std::endl;
    
    // Check for overflow
    if (result < cy) {
        std::cout << "🚨 OVERFLOW DETECTED: Result wrapped around!" << std::endl;
    } else {
        std::cout << "✅ No overflow in this case" << std::endl;
    }
    
    std::cout << std::endl;
}

// Test edge cases with BASEX radix
void test_basex_operations() {
    std::cout << "=== Testing BASEX Radix Operations ===" << std::endl;
    
    std::cout << "BASEX = 0x" << std::hex << BASEX << " (" << std::dec << BASEX << ")" << std::endl;
    
    // Test modulo operations with BASEX
    TWO_MANTTYPE large_value = std::numeric_limits<TWO_MANTTYPE>::max();
    TWO_MANTTYPE mod_result = large_value % (TWO_MANTTYPE)BASEX;
    
    std::cout << "Large value = 0x" << std::hex << large_value << std::endl;
    std::cout << "mod_result = large_value % BASEX = 0x" << std::hex << mod_result << std::endl;
    
    // Test division operations
    TWO_MANTTYPE div_result = large_value / (TWO_MANTTYPE)BASEX;
    std::cout << "div_result = large_value / BASEX = 0x" << std::hex << div_result << std::endl;
    
    std::cout << std::endl;
}

// Test specific values that could cause issues
void test_specific_attack_values() {
    std::cout << "=== Testing Specific Attack Values ===" << std::endl;
    
    // Values that are close to causing overflow
    MANTTYPE attack_val1 = 0xFFFFFFFF; // Maximum 32-bit value
    MANTTYPE attack_val2 = 0x80000000; // BASEX value
    MANTTYPE attack_val3 = 0x7FFFFFFF; // Maximum signed 32-bit value
    
    std::cout << "Testing multiplication combinations:" << std::endl;
    
    struct TestCase {
        MANTTYPE a, b;
        const char* description;
    };
    
    TestCase cases[] = {
        {attack_val1, attack_val1, "MAX * MAX"},
        {attack_val1, attack_val2, "MAX * BASEX"},
        {attack_val2, attack_val2, "BASEX * BASEX"},
        {attack_val3, attack_val1, "SIGNED_MAX * MAX"},
    };
    
    for (const auto& test : cases) {
        TWO_MANTTYPE result = (TWO_MANTTYPE)test.a * test.b;
        std::cout << test.description << ": 0x" << std::hex << test.a 
                  << " * 0x" << test.b << " = 0x" << result << std::endl;
        
        // Check if result would cause issues in subsequent operations
        if (result > (std::numeric_limits<TWO_MANTTYPE>::max() / 2)) {
            std::cout << "  ⚠️  High risk: Result is in upper half of range" << std::endl;
        }
    }
    
    std::cout << std::endl;
}

// Simulate the actual calculator multiplication algorithm
void simulate_calculator_multiply() {
    std::cout << "=== Simulating Calculator Multiplication Algorithm ===" << std::endl;
    
    // Create two large numbers to multiply
    MANTTYPE a_digits[] = {0xFFFFFFFF, 0xFFFFFFFF}; // Large number A
    MANTTYPE b_digits[] = {0xFFFFFFFF, 0xFFFFFFFF}; // Large number B
    
    int a_cdigit = 2;
    int b_cdigit = 2;
    
    std::cout << "Multiplying two large numbers..." << std::endl;
    std::cout << "A = [0x" << std::hex << a_digits[1] << ", 0x" << a_digits[0] << "]" << std::endl;
    std::cout << "B = [0x" << std::hex << b_digits[1] << ", 0x" << b_digits[0] << "]" << std::endl;
    
    // Simulate the nested loop from the vulnerable code
    for (int iadigit = 0; iadigit < a_cdigit; iadigit++) {
        MANTTYPE da = a_digits[iadigit];
        
        for (int ibdigit = 0; ibdigit < b_cdigit; ibdigit++) {
            MANTTYPE db = b_digits[ibdigit];
            
            // This is the critical multiplication
            TWO_MANTTYPE mcy = (TWO_MANTTYPE)da * db;
            
            std::cout << "  da[" << iadigit << "] * db[" << ibdigit << "] = 0x" 
                      << std::hex << da << " * 0x" << db << " = 0x" << mcy << std::endl;
            
            // Check for potential issues
            if (mcy > 0xFFFFFFFFFFFFFF00ULL) { // Close to max uint64_t
                std::cout << "    🚨 WARNING: Very large intermediate result!" << std::endl;
            }
        }
    }
}

int main() {
    std::cout << "Windows Calculator Integer Overflow Test Suite" << std::endl;
    std::cout << "===============================================" << std::endl << std::endl;
    
    test_multiplication_overflow();
    test_carry_overflow();
    test_basex_operations();
    test_specific_attack_values();
    simulate_calculator_multiply();
    
    std::cout << "Test completed. Review output for potential overflow conditions." << std::endl;
    
    return 0;
}
