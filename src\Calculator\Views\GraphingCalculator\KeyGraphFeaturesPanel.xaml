<UserControl x:Class="CalculatorApp.KeyGraphFeaturesPanel"
             xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
             xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
             xmlns:contract7Present="http://schemas.microsoft.com/winfx/2006/xaml/presentation?IsApiContractPresent(Windows.Foundation.UniversalApiContract,7)"
             xmlns:controls="using:CalculatorApp.Controls"
             xmlns:converters="using:CalculatorApp.Converters"
             xmlns:d="http://schemas.microsoft.com/expression/blend/2008"
             xmlns:local="using:CalculatorApp"
             xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006"
             xmlns:ts="using:CalculatorApp.TemplateSelectors"
             xmlns:utils="using:CalculatorApp.Utils"
             xmlns:vm="using:CalculatorApp.ViewModel"
             Background="{ThemeResource ApplicationPageBackgroundThemeBrush}"
             mc:Ignorable="d">
    <UserControl.Resources>
        <ResourceDictionary>
            <ResourceDictionary.ThemeDictionaries>
                <ResourceDictionary x:Key="Default">
                    <!-- Can't be #ffffff due to a bug in RichEditBox considering it as the default value -->
                    <!-- and replacing it by the system value (#000000) when dark theme is used -->
                    <SolidColorBrush x:Key="TitleMathRichEditBoxForegroundBrush" Color="#feffff"/>
                    <x:Double x:Key="EquationButtonOverlayPointerOverOpacity">0.3</x:Double>
                    <x:Double x:Key="EquationButtonOverlayPressedOpacity">0.5</x:Double>
                    <SolidColorBrush x:Key="EquationButtonOverlayBackgroundBrush" Color="White"/>
                    <Style x:Key="ThemedBackButtonStyle"
                           BasedOn="{StaticResource BackButtonStyle}"
                           TargetType="Button"/>
                </ResourceDictionary>
                <ResourceDictionary x:Key="Light">
                    <SolidColorBrush x:Key="TitleMathRichEditBoxForegroundBrush" Color="Black"/>
                    <x:Double x:Key="EquationButtonOverlayPointerOverOpacity">0.2</x:Double>
                    <x:Double x:Key="EquationButtonOverlayPressedOpacity">0.4</x:Double>
                    <SolidColorBrush x:Key="EquationButtonOverlayBackgroundBrush" Color="Black"/>
                    <Style x:Key="ThemedBackButtonStyle"
                           BasedOn="{StaticResource BackButtonStyle}"
                           TargetType="Button"/>
                </ResourceDictionary>
                <ResourceDictionary x:Key="HighContrast">
                    <SolidColorBrush x:Key="TitleMathRichEditBoxForegroundBrush" Color="{ThemeResource SystemColorWindowTextColor}"/>
                    <x:Double x:Key="EquationButtonOverlayPointerOverOpacity">0</x:Double>
                    <x:Double x:Key="EquationButtonOverlayPressedOpacity">0</x:Double>
                    <SolidColorBrush x:Key="EquationButtonOverlayBackgroundBrush" Color="Transparent"/>
                    <Style x:Key="ThemedBackButtonStyle" TargetType="Button"/>
                </ResourceDictionary>
            </ResourceDictionary.ThemeDictionaries>
            <!-- EquationButtonBackgroundBrush is only used in Dark and Light theme. -->
            <!-- The x:Name property is required for High Contrast theme so the brush is indexed properly. -->
            <SolidColorBrush x:Key="EquationButtonBackgroundBrush"
                             x:Name="EquationButtonBackgroundBrush"
                             Color="{x:Bind ViewModel.LineColor, Mode=OneWay}"/>
            <Style x:Key="BackButtonStyle" TargetType="Button">
                <Setter Property="Background" Value="{ThemeResource EquationButtonBackgroundBrush}"/>
                <Setter Property="Foreground" Value="{ThemeResource SystemChromeWhiteColor}"/>
                <Setter Property="Template">
                    <Setter.Value>
                        <ControlTemplate TargetType="Button">
                            <Grid x:Name="RootGrid"
                                  Background="{TemplateBinding Background}"
                                  BorderBrush="{TemplateBinding BorderBrush}"
                                  CornerRadius="{TemplateBinding CornerRadius}">

                                <VisualStateManager.VisualStateGroups>
                                    <VisualStateGroup x:Name="CommonStates">
                                        <VisualState x:Name="Normal">
                                            <VisualState.Setters>
                                                <Setter Target="Overlay.Opacity" Value="0.0"/>
                                            </VisualState.Setters>
                                        </VisualState>
                                        <VisualState x:Name="PointerOver">
                                            <VisualState.Setters>
                                                <Setter Target="Overlay.Opacity" Value="{StaticResource EquationButtonOverlayPointerOverOpacity}"/>
                                            </VisualState.Setters>
                                        </VisualState>
                                        <VisualState x:Name="Pressed">
                                            <VisualState.Setters>
                                                <Setter Target="Overlay.Opacity" Value="{StaticResource EquationButtonOverlayPressedOpacity}"/>
                                            </VisualState.Setters>
                                        </VisualState>
                                    </VisualStateGroup>
                                </VisualStateManager.VisualStateGroups>
                                <Rectangle x:Name="Overlay"
                                           Fill="{ThemeResource EquationButtonOverlayBackgroundBrush}"
                                           Opacity="0"
                                           IsHitTestVisible="False"/>
                                <ContentPresenter x:Name="ContentPresenter"
                                                  Padding="{TemplateBinding Padding}"
                                                  HorizontalContentAlignment="{TemplateBinding HorizontalContentAlignment}"
                                                  VerticalContentAlignment="{TemplateBinding VerticalContentAlignment}"
                                                  BorderBrush="{TemplateBinding BorderBrush}"
                                                  BorderThickness="{TemplateBinding BorderThickness}"
                                                  AutomationProperties.AccessibilityView="Raw"
                                                  Content="{TemplateBinding Content}"
                                                  ContentTemplate="{TemplateBinding ContentTemplate}"
                                                  ContentTransitions="{TemplateBinding ContentTransitions}"
                                                  CornerRadius="{TemplateBinding CornerRadius}"/>
                            </Grid>

                        </ControlTemplate>
                    </Setter.Value>
                </Setter>
            </Style>
            <Style x:Key="KGF_RichEditBoxStyle" TargetType="controls:MathRichEditBox">
                <Setter Property="FontSize" Value="14"/>
                <Setter Property="IsReadOnly" Value="True"/>
                <Setter Property="UseSystemFocusVisuals" Value="True"/>
                <Setter Property="HorizontalAlignment" Value="Left"/>
                <Setter Property="VerticalAlignment" Value="Center"/>
                <Setter Property="Typography.StylisticSet20" Value="True"/>
                <Setter Property="Padding" Value="0"/>
                <Setter Property="Template">
                    <Setter.Value>
                        <ControlTemplate TargetType="controls:MathRichEditBox">
                            <ScrollViewer x:Name="ContentElement"
                                          Grid.Row="1"
                                          Margin="{TemplateBinding BorderThickness}"
                                          Padding="{TemplateBinding Padding}"
                                          VerticalAlignment="Center"
                                          AutomationProperties.AccessibilityView="Raw"
                                          HorizontalScrollBarVisibility="{TemplateBinding ScrollViewer.HorizontalScrollBarVisibility}"
                                          HorizontalScrollMode="{TemplateBinding ScrollViewer.HorizontalScrollMode}"
                                          IsDeferredScrollingEnabled="{TemplateBinding ScrollViewer.IsDeferredScrollingEnabled}"
                                          IsHorizontalRailEnabled="{TemplateBinding ScrollViewer.IsHorizontalRailEnabled}"
                                          IsTabStop="False"
                                          IsVerticalRailEnabled="{TemplateBinding ScrollViewer.IsVerticalRailEnabled}"
                                          VerticalScrollBarVisibility="{TemplateBinding ScrollViewer.VerticalScrollBarVisibility}"
                                          VerticalScrollMode="{TemplateBinding ScrollViewer.VerticalScrollMode}"
                                          ZoomMode="Disabled"/>
                        </ControlTemplate>
                    </Setter.Value>
                </Setter>
            </Style>

            <Style x:Name="KGF_TitleTextBlockStyle" TargetType="TextBlock">
                <Setter Property="FontWeight" Value="Normal"/>
                <Setter Property="FontSize" Value="12"/>
                <Setter Property="TextWrapping" Value="WrapWholeWords"/>
                <Setter Property="AutomationProperties.HeadingLevel" Value="Level2"/>
            </Style>

            <Style x:Name="KGF_TextBlockStyle" TargetType="TextBlock">
                <Setter Property="FontSize" Value="14"/>
                <Setter Property="IsTextSelectionEnabled" Value="True"/>
                <Setter Property="TextWrapping" Value="WrapWholeWords"/>
            </Style>

            <Style x:Name="KGF_ListViewItemContainerStyle" TargetType="ListViewItem">
                <Setter Property="HorizontalContentAlignment" Value="Stretch"/>
                <Setter Property="Padding" Value="0,10"/>
            </Style>

            <DataTemplate x:Key="KGFRichEditDataTemplate" x:DataType="vm:KeyGraphFeaturesItem">
                <StackPanel AutomationProperties.Name="{x:Bind Title, Mode=OneWay}">
                    <TextBlock x:Name="TitleTextBlock"
                               Style="{StaticResource KGF_TitleTextBlockStyle}"
                               Foreground="{ThemeResource TextFillColorSecondaryBrush}"
                               Text="{x:Bind Title, Mode=OneWay}"/>
                    <ItemsControl IsTabStop="False" ItemsSource="{x:Bind DisplayItems, Mode=OneWay}">
                        <ItemsControl.ItemTemplate>
                            <DataTemplate x:DataType="x:String">
                                <controls:MathRichEditBox Style="{StaticResource KGF_RichEditBoxStyle}"
                                                          IsTabStop="False"
                                                          MathText="{x:Bind}"/>
                            </DataTemplate>
                        </ItemsControl.ItemTemplate>
                    </ItemsControl>
                </StackPanel>
            </DataTemplate>

            <DataTemplate x:Key="KGFGridDataTemplate" x:DataType="vm:KeyGraphFeaturesItem">
                <StackPanel>
                    <TextBlock x:Name="TitleTextBlock"
                               Style="{StaticResource KGF_TitleTextBlockStyle}"
                               Foreground="{ThemeResource TextFillColorSecondaryBrush}"
                               Text="{x:Bind Title, Mode=OneWay}"/>
                    <ItemsControl ItemsSource="{x:Bind GridItems, Mode=OneWay}" UseSystemFocusVisuals="True">
                        <ItemsControl.ItemTemplate>
                            <DataTemplate x:DataType="vm:GridDisplayItems">
                                <Grid VerticalAlignment="Center">
                                    <Grid.ColumnDefinitions>
                                        <ColumnDefinition Width="Auto" MinWidth="64"/>
                                        <ColumnDefinition Width="Auto"/>
                                    </Grid.ColumnDefinitions>
                                    <controls:MathRichEditBox Style="{StaticResource KGF_RichEditBoxStyle}" MathText="{x:Bind Expression}"/>
                                    <TextBlock Grid.Column="1"
                                               Margin="0,-2,0,0"
                                               HorizontalAlignment="Left"
                                               VerticalAlignment="Center"
                                               Style="{StaticResource KGF_TextBlockStyle}"
                                               Text="{x:Bind Direction}"/>
                                </Grid>
                            </DataTemplate>
                        </ItemsControl.ItemTemplate>
                    </ItemsControl>
                </StackPanel>
            </DataTemplate>

            <DataTemplate x:Key="KGFTextBlockDataTemplate" x:DataType="vm:KeyGraphFeaturesItem">
                <StackPanel AutomationProperties.Name="{x:Bind Title, Mode=OneWay}">
                    <TextBlock x:Name="TitleTextBlock"
                               Style="{StaticResource KGF_TitleTextBlockStyle}"
                               Foreground="{ThemeResource TextFillColorSecondaryBrush}"
                               Text="{x:Bind Title, Mode=OneWay}"/>
                    <ItemsControl IsTabStop="False" ItemsSource="{x:Bind DisplayItems, Mode=OneWay}">
                        <ItemsControl.ItemTemplate>
                            <DataTemplate x:DataType="x:String">
                                <TextBlock Style="{StaticResource KGF_TextBlockStyle}" Text="{x:Bind}"/>
                            </DataTemplate>
                        </ItemsControl.ItemTemplate>
                    </ItemsControl>
                </StackPanel>
            </DataTemplate>

            <ts:KeyGraphFeaturesTemplateSelector x:Key="KGFTemplateSelector"
                                                 GridTemplate="{StaticResource KGFGridDataTemplate}"
                                                 RichEditTemplate="{StaticResource KGFRichEditDataTemplate}"
                                                 TextBlockTemplate="{StaticResource KGFTextBlockDataTemplate}"/>

            <converters:BooleanToVisibilityNegationConverter x:Name="BooleanToVisibilityNegationConverter"/>

            <SolidColorBrush x:Key="TextControlBackgroundPointerOver" Color="Transparent"/>
            <SolidColorBrush x:Key="TextControlBackgroundFocused" Color="Transparent"/>
        </ResourceDictionary>
    </UserControl.Resources>

    <Grid AutomationProperties.Name="{utils:ResourceString Name=FunctionAnalysisGrid/[using:Windows.UI.Xaml.Automation]AutomationProperties/Name}">
        <Grid.RowDefinitions>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="*"/>
        </Grid.RowDefinitions>
        <Grid Margin="4,0"
              Background="{ThemeResource TextControlBackground}"
              CornerRadius="{ThemeResource ControlCornerRadius}">
            <Grid.ColumnDefinitions>
                <ColumnDefinition Width="Auto"/>
                <ColumnDefinition Width="*"/>
            </Grid.ColumnDefinitions>
            <Button x:Name="BackButton"
                    MinWidth="44"
                    VerticalAlignment="Stretch"
                    Style="{ThemeResource ThemedBackButtonStyle}"
                    contract7Present:CornerRadius="{ThemeResource ControlCornerRadius}"
                    AutomationProperties.Name="{utils:ResourceString Name=equationAnalysisBack/[using:Windows.UI.Xaml.Automation]AutomationProperties/Name}"
                    Click="BackButton_Click"
                    ToolTipService.ToolTip="{utils:ResourceString Name=equationAnalysisBack/[using:Windows.UI.Xaml.Controls]ToolTipService/ToolTip}">
                <StackPanel Margin="5,0"
                            HorizontalAlignment="Center"
                            VerticalAlignment="Center"
                            Orientation="Horizontal">
                    <FontIcon Margin="0,0,6,0"
                              VerticalAlignment="Center"
                              FontFamily="{StaticResource CalculatorFontFamily}"
                              FontSize="16"
                              Glyph="&#xE72B;"/>
                    <StackPanel FlowDirection="LeftToRight" Orientation="Horizontal">
                        <FontIcon FontFamily="{StaticResource CalculatorFontFamily}" Glyph="&#xF893;"/>
                        <TextBlock Margin="-5,19,0,0"
                                   FontSize="11"
                                   Text="{x:Bind ViewModel.FunctionLabelIndex}"/>
                    </StackPanel>
                </StackPanel>
            </Button>
            <controls:MathRichEditBox Grid.Column="1"
                                      Padding="10,0,6,0"
                                      VerticalAlignment="Stretch"
                                      VerticalContentAlignment="Center"
                                      Style="{StaticResource KGF_RichEditBoxStyle}"
                                      Foreground="{ThemeResource TitleMathRichEditBoxForegroundBrush}"
                                      AutomationProperties.Name="{utils:ResourceString Name=KGFEquationTextBox/[using:Windows.UI.Xaml.Automation]AutomationProperties/Name}"
                                      MathText="{x:Bind ViewModel.Expression, Mode=OneWay}"/>
        </Grid>
        <TextBlock Grid.Row="1"
                   Margin="12,12,10,0"
                   Style="{StaticResource KGF_TitleTextBlockStyle}"
                   FontSize="20"
                   FontWeight="Medium"
                   AutomationProperties.HeadingLevel="Level1"
                   Text="{utils:ResourceString Name=KeyGraphFeaturesLabel/Text}"/>

        <ListView x:Name="KeyGraphFeaturesListView"
                  Grid.Row="2"
                  Padding="12,10,10,12"
                  IsItemClickEnabled="False"
                  ItemContainerStyle="{StaticResource KGF_ListViewItemContainerStyle}"
                  ItemTemplateSelector="{StaticResource KGFTemplateSelector}"
                  ItemsSource="{x:Bind ViewModel.KeyGraphFeaturesItems}"
                  ScrollViewer.VerticalScrollBarVisibility="Auto"
                  ScrollViewer.VerticalScrollMode="Auto"
                  SelectionMode="None"
                  Visibility="{x:Bind ViewModel.AnalysisErrorVisible, Converter={StaticResource BooleanToVisibilityNegationConverter}, Mode=OneWay}"/>
        <TextBlock x:Name="AnalysisErrorTextBlock"
                   Grid.Row="2"
                   Margin="12,10,10,0"
                   Style="{StaticResource KGF_TextBlockStyle}"
                   FontWeight="Normal"
                   IsTextSelectionEnabled="False"
                   Text="{x:Bind ViewModel.AnalysisErrorString, Mode=OneWay}"
                   TextWrapping="Wrap"
                   Visibility="{x:Bind ViewModel.AnalysisErrorVisible, Mode=OneWay}"/>
    </Grid>
</UserControl>
