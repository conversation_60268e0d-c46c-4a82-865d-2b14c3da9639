<?xml version="1.0" encoding="utf-8"?>
<RunSettings>
    <DataCollectionRunSettings>
        <DataCollectors>
            <DataCollector uri="datacollector://microsoft/VideoRecorder/1.0" assemblyQualifiedName="Microsoft.VisualStudio.TestTools.DataCollection.VideoRecorder.VideoRecorderDataCollector, Microsoft.VisualStudio.TestTools.DataCollection.VideoRecorder, Version=15.0.0.0, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a" friendlyName="Screen and Voice Recorder">
            </DataCollector>
        </DataCollectors>
    </DataCollectionRunSettings>
    <TestRunParameters>
        <Parameter Name="AppId" Value="Microsoft.WindowsCalculator.Dev_8wekyb3d8bbwe!App" />
        <Parameter Name="CurrencyWith3FractionalDigits" Value="Jordan - Dinar" />
        <Parameter Name="CurrencyWithoutFractionalDigits" Value="Japan - Yen" />
    </TestRunParameters>
</RunSettings>
