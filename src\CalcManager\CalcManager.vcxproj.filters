﻿<?xml version="1.0" encoding="utf-8"?>
<Project ToolsVersion="14.0" xmlns="http://schemas.microsoft.com/developer/msbuild/2003">
  <ItemGroup>
    <Filter Include="CEngine">
      <UniqueIdentifier>{957a8e3c-00c7-48bc-b63c-83b2140a8251}</UniqueIdentifier>
    </Filter>
    <Filter Include="RatPack">
      <UniqueIdentifier>{a1bae6f0-0a01-447d-9a3a-5c65bcd384e6}</UniqueIdentifier>
    </Filter>
    <Filter Include="Header Files">
      <UniqueIdentifier>{5149465e-c5c9-48a2-b676-f11380b733a0}</UniqueIdentifier>
    </Filter>
  </ItemGroup>
  <ItemGroup>
    <ClCompile Include="pch.cpp" />
    <ClCompile Include="ExpressionCommand.cpp" />
    <ClCompile Include="CEngine\calc.cpp">
      <Filter>CEngine</Filter>
    </ClCompile>
    <ClCompile Include="CEngine\CalcUtils.cpp">
      <Filter>CEngine</Filter>
    </ClCompile>
    <ClCompile Include="CEngine\History.cpp">
      <Filter>CEngine</Filter>
    </ClCompile>
    <ClCompile Include="CEngine\scicomm.cpp">
      <Filter>CEngine</Filter>
    </ClCompile>
    <ClCompile Include="CEngine\scidisp.cpp">
      <Filter>CEngine</Filter>
    </ClCompile>
    <ClCompile Include="CEngine\scifunc.cpp">
      <Filter>CEngine</Filter>
    </ClCompile>
    <ClCompile Include="CEngine\scioper.cpp">
      <Filter>CEngine</Filter>
    </ClCompile>
    <ClCompile Include="CEngine\sciset.cpp">
      <Filter>CEngine</Filter>
    </ClCompile>
    <ClCompile Include="Ratpack\basex.cpp">
      <Filter>RatPack</Filter>
    </ClCompile>
    <ClCompile Include="Ratpack\conv.cpp">
      <Filter>RatPack</Filter>
    </ClCompile>
    <ClCompile Include="Ratpack\exp.cpp">
      <Filter>RatPack</Filter>
    </ClCompile>
    <ClCompile Include="Ratpack\fact.cpp">
      <Filter>RatPack</Filter>
    </ClCompile>
    <ClCompile Include="Ratpack\itrans.cpp">
      <Filter>RatPack</Filter>
    </ClCompile>
    <ClCompile Include="Ratpack\itransh.cpp">
      <Filter>RatPack</Filter>
    </ClCompile>
    <ClCompile Include="Ratpack\logic.cpp">
      <Filter>RatPack</Filter>
    </ClCompile>
    <ClCompile Include="Ratpack\num.cpp">
      <Filter>RatPack</Filter>
    </ClCompile>
    <ClCompile Include="Ratpack\rat.cpp">
      <Filter>RatPack</Filter>
    </ClCompile>
    <ClCompile Include="Ratpack\support.cpp">
      <Filter>RatPack</Filter>
    </ClCompile>
    <ClCompile Include="Ratpack\trans.cpp">
      <Filter>RatPack</Filter>
    </ClCompile>
    <ClCompile Include="Ratpack\transh.cpp">
      <Filter>RatPack</Filter>
    </ClCompile>
    <ClCompile Include="CalculatorHistory.cpp" />
    <ClCompile Include="CalculatorManager.cpp" />
    <ClCompile Include="UnitConverter.cpp" />
    <ClCompile Include="CEngine\CalcInput.cpp">
      <Filter>CEngine</Filter>
    </ClCompile>
    <ClCompile Include="CEngine\Number.cpp">
      <Filter>CEngine</Filter>
    </ClCompile>
    <ClCompile Include="CEngine\Rational.cpp">
      <Filter>CEngine</Filter>
    </ClCompile>
    <ClCompile Include="CEngine\RationalMath.cpp">
      <Filter>CEngine</Filter>
    </ClCompile>
    <ClCompile Include="NumberFormattingUtils.cpp" />
  </ItemGroup>
  <ItemGroup>
    <ClInclude Include="Command.h" />
    <ClInclude Include="ExpressionCommand.h" />
    <ClInclude Include="ExpressionCommandInterface.h" />
    <ClInclude Include="pch.h" />
    <ClInclude Include="Header Files\History.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="Header Files\CalcEngine.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="Header Files\CalcUtils.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="Header Files\CCommand.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="Header Files\EngineStrings.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="Ratpack\CalcErr.h">
      <Filter>RatPack</Filter>
    </ClInclude>
    <ClInclude Include="Ratpack\ratconst.h">
      <Filter>RatPack</Filter>
    </ClInclude>
    <ClInclude Include="Ratpack\ratpak.h">
      <Filter>RatPack</Filter>
    </ClInclude>
    <ClInclude Include="Header Files\CalcEngine.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="Header Files\CalcUtils.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="Header Files\CCommand.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="Header Files\EngineStrings.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="Header Files\History.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="UnitConverter.h" />
    <ClInclude Include="CalculatorHistory.h" />
    <ClInclude Include="CalculatorManager.h" />
    <ClInclude Include="CalculatorResource.h" />
    <ClInclude Include="Header Files\ICalcDisplay.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="Header Files\CalcInput.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="Header Files\IHistoryDisplay.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="Header Files\Number.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="Header Files\Rational.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="Header Files\RadixType.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="Header Files\RationalMath.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="NumberFormattingUtils.h" />
  </ItemGroup>
  <ItemGroup>
    <Natvis Include="ratpak.natvis">
      <Filter>RatPack</Filter>
    </Natvis>
  </ItemGroup>
</Project>