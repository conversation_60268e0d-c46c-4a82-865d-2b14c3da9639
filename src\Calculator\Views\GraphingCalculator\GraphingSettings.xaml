<UserControl x:Class="CalculatorApp.GraphingSettings"
             xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
             xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
             xmlns:contract7Present="http://schemas.microsoft.com/winfx/2006/xaml/presentation?IsApiContractPresent(Windows.Foundation.UniversalApiContract, 7)"
             xmlns:converters="using:CalculatorApp.Converters"
             xmlns:d="http://schemas.microsoft.com/expression/blend/2008"
             xmlns:local="using:CalculatorApp"
             xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006"
             xmlns:utils="using:CalculatorApp.Utils"
             Background="{ThemeResource ApplicationPageBackgroundThemeBrush}"
             mc:Ignorable="d">
    <UserControl.Resources>
        <ResourceDictionary>
            <ResourceDictionary.ThemeDictionaries>
                <ResourceDictionary x:Key="Default">
                    <SolidColorBrush x:Key="GraphSettingsErrorBackgroundBrush" Color="#33EB5757"/>
                    <SolidColorBrush x:Key="GraphSettingsErrorBorderBrush" Color="#FFEB5757"/>
                </ResourceDictionary>
                <ResourceDictionary x:Key="Light">
                    <SolidColorBrush x:Key="GraphSettingsErrorBackgroundBrush" Color="#33EB5757"/>
                    <SolidColorBrush x:Key="GraphSettingsErrorBorderBrush" Color="#FFEB5757"/>
                </ResourceDictionary>
                <ResourceDictionary x:Key="HighContrast">
                    <SolidColorBrush x:Key="GraphSettingsErrorBackgroundBrush" Color="{ThemeResource SystemColorButtonFaceColor}"/>
                    <SolidColorBrush x:Key="GraphSettingsErrorBorderBrush" Color="Red"/>
                </ResourceDictionary>
            </ResourceDictionary.ThemeDictionaries>

            <Style x:Key="TrigUnitsRadioButtonStyle" TargetType="RadioButton">
                <Setter Property="MinHeight" Value="38"/>
                <Setter Property="MinWidth" Value="90"/>
                <Setter Property="Padding" Value="4,0"/>
                <Setter Property="FocusVisualMargin" Value="0"/>
                <Setter Property="HorizontalAlignment" Value="Stretch"/>
                <Setter Property="Template">
                    <Setter.Value>
                        <ControlTemplate TargetType="RadioButton">
                            <Grid Name="LayoutRoot"
                                  Background="{ThemeResource ToggleButtonBackground}"
                                  contract7Present:CornerRadius="{TemplateBinding CornerRadius}">
                                <VisualStateManager.VisualStateGroups>
                                    <VisualStateGroup x:Name="CommonStates">
                                        <VisualState x:Name="Normal"/>
                                        <VisualState x:Name="PointerOver">
                                            <VisualState.Setters>
                                                <Setter Target="LayoutRoot.Background" Value="{ThemeResource ToggleButtonBackgroundPointerOver}"/>
                                                <Setter Target="AccessibilityBorder.BorderBrush" Value="{ThemeResource ToggleButtonBorderBrushPointerOver}"/>
                                            </VisualState.Setters>
                                        </VisualState>
                                        <VisualState x:Name="Pressed">
                                            <VisualState.Setters>
                                                <Setter Target="LayoutRoot.Background" Value="{ThemeResource ToggleButtonBackgroundPressed}"/>
                                                <Setter Target="AccessibilityBorder.BorderBrush" Value="{ThemeResource ToggleButtonBorderBrushPressed}"/>
                                            </VisualState.Setters>
                                        </VisualState>
                                    </VisualStateGroup>
                                    <VisualStateGroup x:Name="CheckStates">
                                        <VisualState x:Name="Checked">
                                            <VisualState.Setters>
                                                <Setter Target="SelectedBackgroundRectangle.Opacity" Value="1"/>
                                                <Setter Target="ContentPresenter.Foreground" Value="{ThemeResource ToggleButtonForegroundChecked}"/>
                                            </VisualState.Setters>
                                        </VisualState>
                                        <VisualState x:Name="Unchecked"/>
                                        <VisualState x:Name="Indeterminate"/>
                                    </VisualStateGroup>
                                </VisualStateManager.VisualStateGroups>
                                <Border Name="AccessibilityBorder"
                                        BorderBrush="{ThemeResource ToggleButtonBorderBrush}"
                                        BorderThickness="1"
                                        contract7Present:CornerRadius="{TemplateBinding CornerRadius}"/>
                                <Rectangle Name="SelectedBackgroundRectangle"
                                           Fill="{ThemeResource ToggleButtonBackgroundChecked}"
                                           Opacity="0"/>
                                <ContentPresenter Name="ContentPresenter"
                                                  Grid.Column="1"
                                                  Margin="{TemplateBinding Padding}"
                                                  HorizontalAlignment="Center"
                                                  VerticalAlignment="Center"
                                                  Foreground="{TemplateBinding Foreground}"
                                                  AutomationProperties.AccessibilityView="Raw"
                                                  Content="{TemplateBinding Content}"
                                                  ContentTemplate="{TemplateBinding ContentTemplate}"
                                                  ContentTransitions="{TemplateBinding ContentTransitions}"/>
                            </Grid>
                        </ControlTemplate>
                    </Setter.Value>
                </Setter>
            </Style>
            <Style x:Key="SubTitleTextBoxStyle" TargetType="TextBlock">
                <Setter Property="FontSize" Value="12"/>
                <Setter Property="FontWeight" Value="Normal"/>
                <Setter Property="Foreground" Value="{ThemeResource TextFillColorSecondaryBrush}"/>
            </Style>

            <Style x:Key="ErrorTextBoxStyle" TargetType="TextBox">
                <Setter Property="BorderBrush" Value="{ThemeResource GraphSettingsErrorBorderBrush}"/>
                <Setter Property="Background" Value="{ThemeResource GraphSettingsErrorBackgroundBrush}"/>
            </Style>

            <DataTemplate x:Key="XYTextBoxHeaderTemplate" x:DataType="x:String">
                <TextBlock Foreground="{ThemeResource TextFillColorSecondaryBrush}"
                           FontSize="12"
                           Text="{x:Bind}"/>
            </DataTemplate>

            <converters:BooleanNegationConverter x:Key="BooleanNegationConverter"/>
        </ResourceDictionary>
    </UserControl.Resources>

    <Grid>
        <StackPanel Margin="4,0,4,4">
            <TextBlock x:Name="GraphOptionsHeading"
                       FontSize="20"
                       FontWeight="Medium"
                       AutomationProperties.HeadingLevel="Level1"
                       Text="{utils:ResourceString Name=GraphOptionsHeading/Text}"/>
            <Grid Margin="0,16,0,4">
                <Grid.ColumnDefinitions>
                    <ColumnDefinition Width="*"/>
                    <ColumnDefinition Width="Auto"/>
                </Grid.ColumnDefinitions>
                <TextBlock x:Name="GridHeading"
                           Style="{StaticResource SubTitleTextBoxStyle}"
                           AutomationProperties.HeadingLevel="Level2"
                           Text="{utils:ResourceString Name=GridHeading/Text}"/>
                <HyperlinkButton Grid.Column="1"
                                 Padding="0"
                                 Foreground="{ThemeResource AccentTextFillColorPrimaryBrush}"
                                 BorderThickness="0"
                                 FontSize="12"
                                 Click="ResetViewButton_Clicked"
                                 Content="{utils:ResourceString Name=ResetViewButton/Content}">
                    <HyperlinkButton.Resources>
                        <ResourceDictionary>
                            <StaticResource x:Key="HyperlinkButtonBackgroundPointerOver" ResourceKey="SubtleFillColorTransparentBrush"/>
                            <StaticResource x:Key="HyperlinkButtonBackgroundPressed" ResourceKey="SubtleFillColorTransparentBrush"/>
                        </ResourceDictionary>
                    </HyperlinkButton.Resources>
                </HyperlinkButton>
            </Grid>
            <Grid>
                <Grid.RowDefinitions>
                    <RowDefinition Height="auto"/>
                    <RowDefinition Height="10"/>
                    <RowDefinition Height="auto"/>
                </Grid.RowDefinitions>
                <Grid.ColumnDefinitions>
                    <ColumnDefinition Width="*"/>
                    <ColumnDefinition Width="10"/>
                    <ColumnDefinition Width="*"/>
                </Grid.ColumnDefinitions>

                <TextBox x:Name="SettingsXMin"
                         MaxWidth="160"
                         Style="{x:Bind SelectTextBoxStyle(ViewModel.XError, ViewModel.XMinError), Mode=OneWay}"
                         contract7Present:CornerRadius="{ThemeResource ControlCornerRadius}"
                         Header="{utils:ResourceString Name=GraphSettingsXMin/Header}"
                         HeaderTemplate="{StaticResource XYTextBoxHeaderTemplate}"
                         PreviewKeyDown="GridSettingsTextBox_PreviewKeyDown"
                         Text="{x:Bind ViewModel.XMin, Mode=TwoWay}"/>
                <TextBox x:Name="SettingsXMax"
                         Grid.Column="2"
                         MaxWidth="160"
                         Style="{x:Bind SelectTextBoxStyle(ViewModel.XError, ViewModel.XMaxError), Mode=OneWay}"
                         contract7Present:CornerRadius="{ThemeResource ControlCornerRadius}"
                         Header="{utils:ResourceString Name=GraphSettingsXMax/Header}"
                         HeaderTemplate="{StaticResource XYTextBoxHeaderTemplate}"
                         PreviewKeyDown="GridSettingsTextBox_PreviewKeyDown"
                         Text="{x:Bind ViewModel.XMax, Mode=TwoWay}"/>
                <TextBox x:Name="SettingsYMin"
                         Grid.Row="2"
                         MaxWidth="160"
                         Style="{x:Bind SelectTextBoxStyle(ViewModel.YError, ViewModel.YMinError), Mode=OneWay}"
                         contract7Present:CornerRadius="{ThemeResource ControlCornerRadius}"
                         Header="{utils:ResourceString Name=GraphSettingsYMin/Header}"
                         HeaderTemplate="{StaticResource XYTextBoxHeaderTemplate}"
                         PreviewKeyDown="GridSettingsTextBox_PreviewKeyDown"
                         Text="{x:Bind ViewModel.YMin, Mode=TwoWay}"/>
                <TextBox x:Name="SettingsYMax"
                         Grid.Row="2"
                         Grid.Column="2"
                         MaxWidth="160"
                         Style="{x:Bind SelectTextBoxStyle(ViewModel.YError, ViewModel.YMaxError), Mode=OneWay}"
                         contract7Present:CornerRadius="{ThemeResource ControlCornerRadius}"
                         Header="{utils:ResourceString Name=GraphSettingsYMax/Header}"
                         HeaderTemplate="{StaticResource XYTextBoxHeaderTemplate}"
                         PreviewKeyDown="GridSettingsTextBox_PreviewKeyDown"
                         Text="{x:Bind ViewModel.YMax, Mode=TwoWay}"/>
            </Grid>

            <TextBlock x:Name="UnitsHeading"
                       Margin="0,16,0,6"
                       Style="{StaticResource SubTitleTextBoxStyle}"
                       AutomationProperties.HeadingLevel="Level2"
                       Text="{utils:ResourceString Name=UnitsHeading/Text}"/>

            <StackPanel AutomationProperties.LabeledBy="{Binding ElementName=UnitsHeading}" Orientation="Horizontal">
                <RadioButton x:Name="Radians"
                             Style="{StaticResource TrigUnitsRadioButtonStyle}"
                             contract7Present:CornerRadius="{ThemeResource ControlCornerRadius}"
                             Content="{utils:ResourceString Name=TrigModeRadians/Content}"
                             IsChecked="{x:Bind ViewModel.TrigModeRadians, Mode=TwoWay}"/>
                <RadioButton x:Name="Degrees"
                             Margin="4,0"
                             Style="{StaticResource TrigUnitsRadioButtonStyle}"
                             contract7Present:CornerRadius="{ThemeResource ControlCornerRadius}"
                             Content="{utils:ResourceString Name=TrigModeDegrees/Content}"
                             IsChecked="{x:Bind ViewModel.TrigModeDegrees, Mode=TwoWay}"/>
                <RadioButton x:Name="Gradians"
                             Style="{StaticResource TrigUnitsRadioButtonStyle}"
                             contract7Present:CornerRadius="{ThemeResource ControlCornerRadius}"
                             Content="{utils:ResourceString Name=TrigModeGradians/Content}"
                             IsChecked="{x:Bind ViewModel.TrigModeGradians, Mode=TwoWay}"/>
            </StackPanel>

            <TextBlock x:Name="LineThicknessBoxHeading"
                       Margin="0,16,0,6"
                       Style="{StaticResource SubTitleTextBoxStyle}"
                       AutomationProperties.HeadingLevel="Level2"
                       Text="{utils:ResourceString Name=LineThicknessBoxHeading/Text}"/>
            <ComboBox MinWidth="200"
                      HorizontalAlignment="Stretch"
                      AutomationProperties.LabeledBy="{Binding ElementName=LineThicknessBoxHeading}"
                      SelectedItem="{x:Bind ViewModel.Graph.LineWidth, Mode=TwoWay}">
                <ComboBox.Items>
                    <x:Double>1.0</x:Double>
                    <x:Double>2.0</x:Double>
                    <x:Double>3.0</x:Double>
                    <x:Double>4.0</x:Double>
                </ComboBox.Items>
                <ComboBox.ItemTemplate>
                    <DataTemplate x:DataType="x:Double">
                        <Grid x:Name="LineGrid"
                              Height="20"
                              MaxWidth="200"
                              AutomationProperties.Name="{x:Bind local:GraphingSettings.GetLineWidthAutomationName((x:Double))}">
                            <Line VerticalAlignment="Center"
                                  Stroke="{ThemeResource AppControlPageTextBaseHighColorBrush}"
                                  StrokeThickness="{x:Bind}"
                                  X1="0"
                                  X2="200"
                                  Y1="4"
                                  Y2="4"/>
                        </Grid>
                    </DataTemplate>
                </ComboBox.ItemTemplate>
            </ComboBox>
            <TextBlock x:Name="GraphThemeHeading"
                       Margin="0,16,0,6"
                       Style="{StaticResource SubTitleTextBoxStyle}"
                       AutomationProperties.HeadingLevel="Level2"
                       AutomationProperties.Name="{utils:ResourceString Name=GraphThemeHeading/[using:Windows.UI.Xaml.Automation]AutomationProperties/Name}"
                       Text="{utils:ResourceString Name=GraphThemeHeading/Text}"/>

            <StackPanel AutomationProperties.LabeledBy="{Binding ElementName=GraphThemeHeading}">
                <RadioButton AutomationProperties.Name="{utils:ResourceString Name=AlwaysLightTheme/[using:Windows.UI.Xaml.Automation]AutomationProperties/Name}"
                             Content="{utils:ResourceString Name=AlwaysLightTheme/Content}"
                             IsChecked="{x:Bind IsMatchAppTheme, Mode=TwoWay, Converter={StaticResource BooleanNegationConverter}}"/>
                <RadioButton Margin="1,0"
                             AutomationProperties.Name="{utils:ResourceString Name=MatchAppTheme/[using:Windows.UI.Xaml.Automation]AutomationProperties/Name}"
                             Content="{utils:ResourceString Name=MatchAppTheme/Content}"
                             IsChecked="{x:Bind IsMatchAppTheme, Mode=TwoWay}"/>
            </StackPanel>
        </StackPanel>
    </Grid>
</UserControl>
