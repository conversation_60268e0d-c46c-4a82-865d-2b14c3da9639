#include <iostream>
#include <string>
#include <vector>
#include <iomanip>

// Include calculator headers
#include "src/CalcManager/Ratpack/ratpak.h"
#include "src/CalcManager/Header Files/CalcEngine.h"

using namespace std;

class OverflowTester {
public:
    // Test 1: Large number string parsing
    void test_large_string_parsing() {
        cout << "=== Testing Large String Parsing ===" << endl;

        // Create progressively larger strings
        vector<size_t> test_sizes = {1000, 10000, 100000, 1000000};

        for (size_t size : test_sizes) {
            wstring large_number(size, L'9');
            cout << "Testing string of size: " << size << " characters" << endl;

            try {
                // This calls the vulnerable StringToNumber function
                PNUMBER result = StringToNumber(large_number, 10, 32);

                if (result != nullptr) {
                    cout << "  ✅ Successfully parsed " << size << " digit number" << endl;
                    cout << "  Memory allocated: ~" << (size * sizeof(MANTTYPE)) << " bytes" << endl;
                    destroynum(result);
                } else {
                    cout << "  ❌ Failed to parse " << size << " digit number" << endl;
                }
            } catch (const exception& e) {
                cout << "  🚨 Exception caught: " << e.what() << endl;
            } catch (uint32_t error) {
                cout << "  🚨 Calculator error: 0x" << hex << error << dec << endl;
            }
        }
        cout << endl;
    }

    // Test 2: Multiplication overflow scenarios
    void test_multiplication_overflow() {
        cout << "=== Testing Multiplication Overflow ===" << endl;

        try {
            // Create two large numbers
            PNUMBER num1 = nullptr;
            PNUMBER num2 = nullptr;

            // Create maximum value numbers
            createnum(num1, 10);
            createnum(num2, 10);

            // Fill with maximum values
            for (int i = 0; i < 10; i++) {
                num1->mant[i] = 0xFFFFFFFF;
                num2->mant[i] = 0xFFFFFFFF;
            }
            num1->cdigit = 10;
            num2->cdigit = 10;
            num1->sign = 1;
            num2->sign = 1;
            num1->exp = 0;
            num2->exp = 0;

            cout << "Created two large numbers with max values" << endl;
            cout << "Each number has " << num1->cdigit << " digits" << endl;

            // Attempt multiplication - this should trigger overflow conditions
            cout << "Attempting multiplication..." << endl;
            mulnum(&num1, num2, BASEX);

            cout << "  ✅ Multiplication completed" << endl;
            cout << "  Result has " << num1->cdigit << " digits" << endl;

            destroynum(num1);
            destroynum(num2);

        } catch (const exception& e) {
            cout << "  🚨 Exception: " << e.what() << endl;
        } catch (uint32_t error) {
            cout << "  🚨 Calculator error: 0x" << hex << error << dec << endl;
        }
        cout << endl;
    }

    // Test 3: Memory exhaustion attack
    void test_memory_exhaustion() {
        cout << "=== Testing Memory Exhaustion ===" << endl;

        // Try to allocate increasingly large numbers
        vector<uint32_t> sizes = {1000, 10000, 100000, 1000000};

        for (uint32_t size : sizes) {
            cout << "Attempting to allocate number with " << size << " digits" << endl;

            try {
                PNUMBER large_num = nullptr;
                createnum(large_num, size);

                if (large_num != nullptr) {
                    cout << "  ✅ Successfully allocated " << size << " digits" << endl;
                    cout << "  Memory used: ~" << (size * sizeof(MANTTYPE) + sizeof(NUMBER)) << " bytes" << endl;
                    destroynum(large_num);
                } else {
                    cout << "  ❌ Failed to allocate " << size << " digits" << endl;
                }

            } catch (const exception& e) {
                cout << "  🚨 Exception: " << e.what() << endl;
                break;
            } catch (uint32_t error) {
                cout << "  🚨 Calculator error: 0x" << hex << error << dec << endl;
                break;
            }
        }
        cout << endl;
    }

    // Test 4: Rational number overflow
    void test_rational_overflow() {
        cout << "=== Testing Rational Number Overflow ===" << endl;

        try {
            // Create large rational numbers
            PRAT rat1 = nullptr;
            PRAT rat2 = nullptr;

            createrat(rat1);
            createrat(rat2);

            // Create large numerators and denominators
            rat1->pp = StringToNumber(wstring(1000, L'9'), 10, 32);
            rat1->pq = StringToNumber(L"1", 10, 32);
            rat2->pp = StringToNumber(wstring(1000, L'9'), 10, 32);
            rat2->pq = StringToNumber(L"1", 10, 32);

            if (rat1->pp && rat1->pq && rat2->pp && rat2->pq) {
                cout << "Created large rational numbers" << endl;

                // Test multiplication of large rationals
                cout << "Testing rational multiplication..." << endl;
                mulrat(&rat1, rat2, 32);

                cout << "  ✅ Rational multiplication completed" << endl;
            }

            destroyrat(rat1);
            destroyrat(rat2);

        } catch (const exception& e) {
            cout << "  🚨 Exception: " << e.what() << endl;
        } catch (uint32_t error) {
            cout << "  🚨 Calculator error: 0x" << hex << error << dec << endl;
        }
        cout << endl;
    }

    // Test 5: Edge case calculations
    void test_edge_cases() {
        cout << "=== Testing Edge Cases ===" << endl;

        // Test maximum factorial
        try {
            cout << "Testing large factorial..." << endl;
            PRAT fact_input = nullptr;
            createrat(fact_input);
            fact_input->pp = StringToNumber(L"170", 10, 32); // Close to overflow limit
            fact_input->pq = StringToNumber(L"1", 10, 32);

            factrat(&fact_input, 10, 32);
            cout << "  ✅ Factorial calculation completed" << endl;

            destroyrat(fact_input);

        } catch (uint32_t error) {
            cout << "  🚨 Factorial overflow error: 0x" << hex << error << dec << endl;
        }

        // Test power operations
        try {
            cout << "Testing large power operations..." << endl;
            PRAT base = nullptr;
            createrat(base);
            base->pp = StringToNumber(L"999999999", 10, 32);
            base->pq = StringToNumber(L"1", 10, 32);

            // This should cause overflow
            ratpowi32(&base, 100, 32);
            cout << "  ✅ Power calculation completed" << endl;

            destroyrat(base);

        } catch (uint32_t error) {
            cout << "  🚨 Power overflow error: 0x" << hex << error << dec << endl;
        }

        cout << endl;
    }
};

// Function to run all tests
void run_overflow_tests() {
    cout << "Windows Calculator Overflow Vulnerability Tests" << endl;
    cout << "===============================================" << endl << endl;

    OverflowTester tester;

    tester.test_large_string_parsing();
    tester.test_multiplication_overflow();
    tester.test_memory_exhaustion();
    tester.test_rational_overflow();
    tester.test_edge_cases();

    cout << "All tests completed!" << endl;
}

int main() {
    try {
        run_overflow_tests();
    } catch (const exception& e) {
        cout << "Fatal error: " << e.what() << endl;
        return 1;
    }

    return 0;
}