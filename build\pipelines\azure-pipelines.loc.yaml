#
# Localization
# This pipeline uploads English strings files to the localization service, downloads any translated
# files which are available, and checks them in to git. This pipeline relies on Microsoft-internal
# resources to run.
#

schedules:
- cron: "0 5 * * *"
  displayName: Daily sync
  branches:
    include:
    - main
  always: true

trigger: none
pr: none

name: $(BuildDefinitionName)_$(date:yyMM).$(date:dd)$(rev:rrr)

variables:
  isMainBranch: ${{ eq(variables['Build.SourceBranch'], 'refs/heads/main') }}

jobs:
- job: Localize
  pool:
    name: EssentialExperiences-windows-2022
  variables:
    skipComponentGovernanceDetection: true
  steps:
  - checkout: self
    clean: true

  - task: MicrosoftTDBuild.tdbuild-task.tdbuild-task.TouchdownBuildTask@5
    displayName: Send resources to Touchdown Build
    inputs:
      teamId: 86
      authType: FederatedIdentity
      FederatedIdentityServiceConnection: EE-TDBuild-Localization-FC
      isPreview: false
      relativePathRoot: src/Calculator/Resources/en-US/
      resourceFilePath: '*.resw'
      outputDirectoryRoot: src/Calculator/Resources/
      ${{ if eq(variables['isMainBranch'], false) }}:
        localizationTarget: false

  - script: |
      cd $(Build.SourcesDirectory)
      git add -A
      git diff --cached --exit-code
      echo ##vso[task.setvariable variable=hasChanges]%errorlevel%
      git diff --cached > $(Build.ArtifactStagingDirectory)\LocalizedStrings.patch
    displayName: Check for changes and create patch file

  - task: PublishPipelineArtifact@0
    displayName: Publish patch file as artifact
    condition: and(eq(variables['hasChanges'], '1'), eq(variables['isMainBranch'], true))
    inputs:
      artifactName: Patch
      targetPath: $(Build.ArtifactStagingDirectory)
