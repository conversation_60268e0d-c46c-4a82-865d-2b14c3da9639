# This template contains jobs to run UI tests using WinAppDriver.

parameters:
  platform: ''
  runsettingsFileName: ''

jobs:
- job: UITests${{ parameters.platform }}
  displayName: UITests ${{ parameters.platform }}
  dependsOn: Build${{ parameters.platform }}
  condition: succeeded()
  pool:
    name: EssentialExperiences-Win11
    image: Win11Enterprise-BuildTools2022
  variables:
    skipComponentGovernanceDetection: true
    DropName: drop-${{ parameters.platform }}

  steps:
  - checkout: self
    fetchDepth: 1

  - task: PowerShell@2
    displayName: Turn off animation effects
    inputs:
      filePath: $(Build.SourcesDirectory)\build\scripts\TurnOffAnimationEffects.ps1

  - task: ScreenResolutionUtility@1
    displayName: Set resolution to 1920x1080
    inputs:
      displaySettings: 'specific'
      width: 1920
      height: 1080

  - download: current
    displayName: Download MsixBundle and CalculatorUITests
    artifact: $(DropName)
    patterns: |
      Calculator/AppPackages/**
      publish/**

  - powershell: |
      $(Build.SourcesDirectory)/build/scripts/SignTestApp.ps1 -AppToSign '$(Pipeline.Workspace)/$(DropName)/Calculator/AppPackages/Calculator_*_Test/Calculator_*.msixbundle'
      $(Pipeline.Workspace)/$(DropName)/Calculator/AppPackages/Calculator_*_Test/Add-AppDevPackage.ps1 -Force
    displayName: Install app

  - task: VSTest@2
    displayName: Run CalculatorUITests
    inputs:
      testAssemblyVer2: $(Pipeline.Workspace)/$(DropName)/publish/CalculatorUITests.dll
      runSettingsFile: $(Pipeline.Workspace)/$(DropName)/publish/${{ parameters.runsettingsFileName }}
      platform: ${{ parameters.platform }}
      configuration: Release
