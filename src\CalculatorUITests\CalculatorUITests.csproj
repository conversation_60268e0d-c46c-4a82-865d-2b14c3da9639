<Project Sdk="Microsoft.NET.Sdk">
  <PropertyGroup>
    <TargetFramework>net8.0</TargetFramework>
    <IsPackable>false</IsPackable>
  </PropertyGroup>
  <ItemGroup>
    <PackageReference Include="Microsoft.NET.Test.Sdk" Version="17.4.0" />
    <PackageReference Include="MSTest.TestAdapter" Version="2.2.10" />
    <PackageReference Include="MSTest.TestFramework" Version="2.2.10" />
    <PackageReference Include="Appium.WebDriver" Version="4.4.0" />
    <PackageReference Include="System.Drawing.Common" Version="4.7.3" />
  </ItemGroup>
  <ItemGroup>
    <ProjectReference Include="..\CalculatorUITestFramework\CalculatorUITestFramework.csproj" />
  </ItemGroup>
  <ItemGroup>
    <None Update="CalculatorUITests.ci-internal.runsettings">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </None>
    <None Update="CalculatorUITests.release.runsettings">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </None>
    <None Update="CalculatorUITests.ci.runsettings">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </None>
  </ItemGroup>
</Project>