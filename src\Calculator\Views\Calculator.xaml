<UserControl x:Class="CalculatorApp.Calculator"
             xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
             xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
             xmlns:automation="using:CalculatorApp.ViewModel.Common.Automation"
             xmlns:common="using:CalculatorApp.Common"
             xmlns:contract7Present="http://schemas.microsoft.com/winfx/2006/xaml/presentation?IsApiContractPresent(Windows.Foundation.UniversalApiContract,7)"
             xmlns:contract8NotPresent="http://schemas.microsoft.com/winfx/2006/xaml/presentation?IsApiContractNotPresent(Windows.Foundation.UniversalApiContract,8)"
             xmlns:contract8Present="http://schemas.microsoft.com/winfx/2006/xaml/presentation?IsApiContractPresent(Windows.Foundation.UniversalApiContract,8)"
             xmlns:controls="using:CalculatorApp.Controls"
             xmlns:converters="using:CalculatorApp.Converters"
             xmlns:d="http://schemas.microsoft.com/expression/blend/2008"
             xmlns:local="using:CalculatorApp"
             xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006"
             xmlns:utils="using:CalculatorApp.Utils"
             xmlns:vmcommon="using:CalculatorApp.ViewModel.Common"
             Loaded="OnLoaded"
             mc:Ignorable="d">

    <UserControl.Resources>
        <!-- DataTemplates -->

        <DataTemplate x:Key="Operand" x:DataType="vmcommon:DisplayExpressionToken">
            <TextBlock Margin="2,0,0,0"
                       Foreground="{ThemeResource SystemControlPageTextBaseMediumBrush}"
                       Text="{x:Bind Token, Mode=OneWay}"/>
        </DataTemplate>

        <DataTemplate x:Key="Operator" x:DataType="vmcommon:DisplayExpressionToken">
            <TextBlock Margin="2,0,0,0"
                       Foreground="{ThemeResource SystemControlPageTextBaseMediumBrush}"
                       Text="{x:Bind Token, Mode=OneWay}"/>
        </DataTemplate>

        <DataTemplate x:Key="Separator" x:DataType="vmcommon:DisplayExpressionToken">
            <TextBlock x:Name="MainText" Text="{x:Bind Token, Mode=OneWay}"/>
        </DataTemplate>

        <!-- TextBox Styles -->

        <Style x:Key="NormalStyle" TargetType="controls:OverflowTextBlock">
            <Setter Property="HorizontalAlignment" Value="Stretch"/>
            <Setter Property="Template">
                <Setter.Value>
                    <ControlTemplate TargetType="controls:OverflowTextBlock">
                        <Grid x:Name="TokenContainer"
                              Background="{TemplateBinding Background}"
                              FlowDirection="LeftToRight">
                            <Grid.ColumnDefinitions>
                                <ColumnDefinition Width="12"/>
                                <ColumnDefinition/>
                                <ColumnDefinition Width="12"/>
                            </Grid.ColumnDefinitions>
                            <ScrollViewer x:Name="ExpressionContainer"
                                          Grid.Column="1"
                                          Padding="0,0,0,0"
                                          VerticalAlignment="{TemplateBinding VerticalContentAlignment}"
                                          Style="{StaticResource ResultsScrollerSnapped}"
                                          AutomationProperties.AccessibilityView="Raw">
                                <Grid x:Name="ExpressionContent">
                                    <ItemsControl x:Name="TokenList"
                                                  VerticalAlignment="Stretch"
                                                  HorizontalContentAlignment="Right"
                                                  VerticalContentAlignment="Stretch"
                                                  IsTabStop="False"
                                                  ItemTemplateSelector="{StaticResource ExpressionItemTemplateSelector}"
                                                  ItemsSource="{Binding ExpressionTokens}">
                                        <ItemsControl.ItemsPanel>
                                            <ItemsPanelTemplate>
                                                <ItemsStackPanel HorizontalAlignment="Right"
                                                                 VerticalAlignment="Stretch"
                                                                 Orientation="Horizontal"/>
                                            </ItemsPanelTemplate>
                                        </ItemsControl.ItemsPanel>
                                    </ItemsControl>
                                </Grid>
                            </ScrollViewer>
                            <Button x:Name="ScrollLeft"
                                    Grid.Column="0"
                                    Margin="-4,3,-4,0"
                                    Style="{StaticResource ScrollButtonStyle}"
                                    AutomationProperties.Name="{utils:ResourceString Name=scrollLeft/[using:Windows.UI.Xaml.Automation]AutomationProperties/Name}">
                                <FontIcon FontFamily="{ThemeResource CalculatorFontFamily}"
                                          FontSize="12"
                                          Glyph="&#xE96F;"/>
                            </Button>
                            <Button x:Name="ScrollRight"
                                    Grid.Column="2"
                                    Margin="-4,3,-4,0"
                                    Style="{StaticResource ScrollButtonStyle}"
                                    AutomationProperties.Name="{utils:ResourceString Name=scrollRight/[using:Windows.UI.Xaml.Automation]AutomationProperties/Name}">
                                <FontIcon FontFamily="{ThemeResource CalculatorFontFamily}"
                                          FontSize="12"
                                          Glyph="&#xE970;"/>
                            </Button>
                        </Grid>
                    </ControlTemplate>
                </Setter.Value>
            </Setter>
        </Style>

        <Style x:Key="AOTResultsStyle" TargetType="controls:OverflowTextBlock">
            <Setter Property="HorizontalAlignment" Value="Stretch"/>
            <Setter Property="Template">
                <Setter.Value>
                    <ControlTemplate TargetType="controls:OverflowTextBlock">
                        <Grid x:Name="TokenContainer"
                              Background="{TemplateBinding Background}"
                              FlowDirection="LeftToRight">
                            <Grid.Resources>
                                <ResourceDictionary>
                                    <ResourceDictionary.ThemeDictionaries>
                                        <ResourceDictionary x:Key="Light">
                                            <SolidColorBrush x:Key="ButtonBackgroundPointerOver" Color="Transparent"/>
                                            <SolidColorBrush x:Key="ButtonBackgroundPressed" Color="Transparent"/>
                                            <SolidColorBrush x:Key="ButtonBackgroundDisabled" Color="Transparent"/>
                                            <SolidColorBrush x:Key="ButtonForeground" Color="#99000000"/>
                                            <SolidColorBrush x:Key="ButtonForegroundPointerOver"
                                                             Opacity="0.8"
                                                             Color="{ThemeResource SystemAccentColor}"/>
                                            <SolidColorBrush x:Key="ButtonForegroundPressed"
                                                             Opacity="1.0"
                                                             Color="{ThemeResource SystemAccentColor}"/>
                                            <SolidColorBrush x:Key="ButtonForegroundDisabled" Color="#33000000"/>
                                        </ResourceDictionary>
                                        <ResourceDictionary x:Key="HighContrast">
                                            <SolidColorBrush x:Key="ButtonBackgroundPointerOver" Color="Transparent"/>
                                            <SolidColorBrush x:Key="ButtonBackgroundPressed" Color="Transparent"/>
                                            <SolidColorBrush x:Key="ButtonBackgroundDisabled" Color="Transparent"/>
                                            <SolidColorBrush x:Key="ButtonForeground"
                                                             Opacity="0.6"
                                                             Color="{ThemeResource SystemColorWindowTextColor}"/>
                                            <SolidColorBrush x:Key="ButtonForegroundPointerOver"
                                                             Opacity="0.8"
                                                             Color="{ThemeResource SystemColorHighlightColor}"/>
                                            <SolidColorBrush x:Key="ButtonForegroundPressed"
                                                             Opacity="1.0"
                                                             Color="{ThemeResource SystemColorHighlightColor}"/>
                                            <SolidColorBrush x:Key="ButtonForegroundDisabled"
                                                             Opacity="0.2"
                                                             Color="{ThemeResource SystemColorGrayTextColor}"/>
                                        </ResourceDictionary>
                                        <ResourceDictionary x:Key="Dark">
                                            <SolidColorBrush x:Key="ButtonBackgroundPointerOver" Color="Transparent"/>
                                            <SolidColorBrush x:Key="ButtonBackgroundPressed" Color="Transparent"/>
                                            <SolidColorBrush x:Key="ButtonBackgroundDisabled" Color="Transparent"/>
                                            <SolidColorBrush x:Key="ButtonForeground" Color="#99FFFFFF"/>
                                            <SolidColorBrush x:Key="ButtonForegroundPointerOver"
                                                             Opacity="0.8"
                                                             Color="{ThemeResource SystemAccentColorLight1}"/>
                                            <SolidColorBrush x:Key="ButtonForegroundPressed"
                                                             Opacity="1.0"
                                                             Color="{ThemeResource SystemAccentColorLight1}"/>
                                            <SolidColorBrush x:Key="ButtonForegroundDisabled" Color="#33FFFFFF"/>
                                        </ResourceDictionary>
                                    </ResourceDictionary.ThemeDictionaries>
                                </ResourceDictionary>
                            </Grid.Resources>
                            <Grid.ColumnDefinitions>
                                <ColumnDefinition Width="Auto"/>
                                <ColumnDefinition/>
                                <ColumnDefinition Width="Auto"/>
                            </Grid.ColumnDefinitions>
                            <ScrollViewer x:Name="ExpressionContainer"
                                          Grid.Column="0"
                                          Grid.ColumnSpan="3"
                                          Padding="0,0,0,0"
                                          VerticalAlignment="{TemplateBinding VerticalContentAlignment}"
                                          Style="{StaticResource ResultsScrollerSnapped}"
                                          AutomationProperties.AccessibilityView="Control">
                                <Grid x:Name="ExpressionContent">
                                    <TextBlock x:Name="EditableToken"
                                               Grid.Row="2"
                                               Margin="4,0,4,0"
                                               Padding="0"
                                               HorizontalAlignment="Right"
                                               VerticalAlignment="Stretch"
                                               FontWeight="SemiBold"
                                               AutomationProperties.AccessibilityView="Raw"
                                               Text="{Binding DisplayValue, Mode=OneWay}"/>
                                </Grid>
                            </ScrollViewer>

                            <Button x:Name="ScrollLeft"
                                    Grid.Column="0"
                                    Width="{TemplateBinding ScrollButtonsWidth}"
                                    Margin="0,3,0,0"
                                    VerticalAlignment="Stretch"
                                    Style="{StaticResource AlwaysOnTopScrollButtonStyleS}"
                                    Background="Transparent"
                                    AutomationProperties.Name="{utils:ResourceString Name=scrollLeft/[using:Windows.UI.Xaml.Automation]AutomationProperties/Name}">
                                <FontIcon FontFamily="{ThemeResource CalculatorFontFamily}"
                                          FontSize="{TemplateBinding ScrollButtonsFontSize}"
                                          Glyph="&#xE96F;"/>
                            </Button>
                            <Button x:Name="ScrollRight"
                                    Grid.Column="2"
                                    Width="{TemplateBinding ScrollButtonsWidth}"
                                    Margin="0,3,0,0"
                                    VerticalAlignment="Stretch"
                                    Style="{StaticResource AlwaysOnTopScrollButtonStyleS}"
                                    Background="Transparent"
                                    AutomationProperties.Name="{utils:ResourceString Name=scrollRight/[using:Windows.UI.Xaml.Automation]AutomationProperties/Name}">
                                <FontIcon FontFamily="{ThemeResource CalculatorFontFamily}"
                                          FontSize="{TemplateBinding ScrollButtonsFontSize}"
                                          Glyph="&#xE970;"/>
                            </Button>
                        </Grid>
                    </ControlTemplate>
                </Setter.Value>
            </Setter>
        </Style>

        <!-- Calculation Result Styles -->
        <Style x:Key="ResultsStyle"
               BasedOn="{StaticResource ConditionalCalculationResultStyle}"
               TargetType="controls:CalculationResult">
            <Setter Property="HorizontalContentAlignment" Value="Right"/>
            <Setter Property="VerticalContentAlignment" Value="Top"/>
            <Setter Property="DisplayMargin" Value="0,0,0,0"/>
            <Setter Property="IsActive" Value="True"/>
            <Setter Property="MinFontSize" Value="12"/>
        </Style>

        <!-- Button Styles -->
        <Style x:Key="ScrollButtonStyle"
               BasedOn="{StaticResource SubtleButtonStyle}"
               TargetType="Button">
            <Setter Property="BorderThickness" Value="0"/>
            <Setter Property="Margin" Value="0"/>
            <Setter Property="Padding" Value="0,0,0,0"/>
            <Setter Property="VerticalAlignment" Value="Center"/>
            <Setter Property="VerticalContentAlignment" Value="Center"/>
            <Setter Property="HorizontalContentAlignment" Value="Center"/>
            <Setter Property="Width" Value="20"/>
            <Setter Property="Height" Value="20"/>
            <Setter Property="MinWidth" Value="20"/>
            <Setter Property="MinHeight" Value="20"/>
            <Setter Property="Background" Value="{ThemeResource ControlFillColorDefault}"/>
            <Setter Property="Visibility" Value="Collapsed"/>
            <Setter Property="FontSize" Value="12"/>
        </Style>

        <Style x:Key="AlwaysOnTopScrollButtonStyleS" TargetType="Button">
            <Setter Property="BorderThickness" Value="0"/>
            <Setter Property="Padding" Value="0,0,0,0"/>
            <Setter Property="VerticalAlignment" Value="Center"/>
            <Setter Property="Visibility" Value="Collapsed"/>
        </Style>

        <!-- Flyout Styles -->

        <Style x:Key="MemoryFlyoutBaseStyle" TargetType="FlyoutPresenter">
            <Setter Property="MinWidth" Value="200"/>
            <Setter Property="MaxHeight" Value="2400"/>
            <Setter Property="MaxWidth" Value="2400"/>
            <Setter Property="ContentTransitions">
                <Setter.Value>
                    <TransitionCollection>
                        <EdgeUIThemeTransition Edge="Bottom"/>
                    </TransitionCollection>
                </Setter.Value>
            </Setter>
            <Setter Property="Template">
                <Setter.Value>
                    <ControlTemplate TargetType="FlyoutPresenter">
                        <Grid Margin="0"
                              Background="{ThemeResource BackgroundSmokeFillColorBrush}"
                              Tapped="OnMemoryFlyOutTapped">
                            <ContentPresenter HorizontalAlignment="Stretch"
                                              VerticalAlignment="Stretch"
                                              Content="{TemplateBinding Content}"
                                              ContentTemplate="{TemplateBinding ContentTemplate}"
                                              ContentTransitions="{TemplateBinding ContentTransitions}"/>
                        </Grid>
                    </ControlTemplate>
                </Setter.Value>
            </Setter>
        </Style>

        <contract8Present:Style x:Key="MemoryFlyoutStyle"
                                BasedOn="{StaticResource MemoryFlyoutBaseStyle}"
                                TargetType="FlyoutPresenter">
            <Setter Property="IsDefaultShadowEnabled" Value="False"/>
        </contract8Present:Style>
        <contract8NotPresent:Style x:Key="MemoryFlyoutStyle"
                                   BasedOn="{StaticResource MemoryFlyoutBaseStyle}"
                                   TargetType="FlyoutPresenter"/>

        <Style x:Key="HistoryFlyoutBaseStyle" TargetType="FlyoutPresenter">
            <Setter Property="MinWidth" Value="200"/>
            <Setter Property="MaxHeight" Value="2400"/>
            <Setter Property="MaxWidth" Value="2400"/>
            <Setter Property="ContentTransitions">
                <Setter.Value>
                    <TransitionCollection>
                        <EdgeUIThemeTransition Edge="Bottom"/>
                    </TransitionCollection>
                </Setter.Value>
            </Setter>
            <Setter Property="Template">
                <Setter.Value>
                    <ControlTemplate TargetType="FlyoutPresenter">
                        <Grid Margin="0"
                              Background="{ThemeResource BackgroundSmokeFillColorBrush}"
                              Tapped="OnHistoryFlyOutTapped">
                            <ContentPresenter HorizontalAlignment="Stretch"
                                              VerticalAlignment="Stretch"
                                              Content="{TemplateBinding Content}"
                                              ContentTemplate="{TemplateBinding ContentTemplate}"
                                              ContentTransitions="{TemplateBinding ContentTransitions}"/>
                        </Grid>
                    </ControlTemplate>
                </Setter.Value>
            </Setter>
        </Style>

        <contract8Present:Style x:Key="HistoryFlyoutStyle"
                                BasedOn="{StaticResource HistoryFlyoutBaseStyle}"
                                TargetType="FlyoutPresenter">
            <Setter Property="IsDefaultShadowEnabled" Value="False"/>
        </contract8Present:Style>
        <contract8NotPresent:Style x:Key="HistoryFlyoutStyle"
                                   BasedOn="{StaticResource HistoryFlyoutBaseStyle}"
                                   TargetType="FlyoutPresenter"/>

        <!-- Storyboards -->

        <Storyboard x:Name="Animate">
            <DoubleAnimationUsingKeyFrames Storyboard.TargetName="NumpadPanel" Storyboard.TargetProperty="(UIElement.RenderTransform).(CompositeTransform.ScaleX)">
                <EasingDoubleKeyFrame KeyTime="0" Value="0.92">
                    <EasingDoubleKeyFrame.EasingFunction>
                        <ExponentialEase EasingMode="EaseOut" Exponent="5"/>
                    </EasingDoubleKeyFrame.EasingFunction>
                </EasingDoubleKeyFrame>
                <EasingDoubleKeyFrame KeyTime="0:0:0.367" Value="1">
                    <EasingDoubleKeyFrame.EasingFunction>
                        <ExponentialEase EasingMode="EaseOut" Exponent="5"/>
                    </EasingDoubleKeyFrame.EasingFunction>
                </EasingDoubleKeyFrame>
            </DoubleAnimationUsingKeyFrames>
            <DoubleAnimationUsingKeyFrames Storyboard.TargetName="NumpadPanel" Storyboard.TargetProperty="(UIElement.RenderTransform).(CompositeTransform.ScaleY)">
                <EasingDoubleKeyFrame KeyTime="0" Value="0.92">
                    <EasingDoubleKeyFrame.EasingFunction>
                        <ExponentialEase EasingMode="EaseOut" Exponent="5"/>
                    </EasingDoubleKeyFrame.EasingFunction>
                </EasingDoubleKeyFrame>
                <EasingDoubleKeyFrame KeyTime="0:0:0.367" Value="1">
                    <EasingDoubleKeyFrame.EasingFunction>
                        <ExponentialEase EasingMode="EaseOut" Exponent="5"/>
                    </EasingDoubleKeyFrame.EasingFunction>
                </EasingDoubleKeyFrame>
            </DoubleAnimationUsingKeyFrames>
        </Storyboard>

        <Storyboard x:Name="AnimateWithoutResult">
            <DoubleAnimationUsingKeyFrames Storyboard.TargetName="NumpadPanel" Storyboard.TargetProperty="(UIElement.RenderTransform).(CompositeTransform.ScaleX)">
                <EasingDoubleKeyFrame KeyTime="0" Value="0.92">
                    <EasingDoubleKeyFrame.EasingFunction>
                        <ExponentialEase EasingMode="EaseOut" Exponent="5"/>
                    </EasingDoubleKeyFrame.EasingFunction>
                </EasingDoubleKeyFrame>
                <EasingDoubleKeyFrame KeyTime="0:0:0.367" Value="1">
                    <EasingDoubleKeyFrame.EasingFunction>
                        <ExponentialEase EasingMode="EaseOut" Exponent="5"/>
                    </EasingDoubleKeyFrame.EasingFunction>
                </EasingDoubleKeyFrame>
            </DoubleAnimationUsingKeyFrames>
            <DoubleAnimationUsingKeyFrames Storyboard.TargetName="NumpadPanel" Storyboard.TargetProperty="(UIElement.RenderTransform).(CompositeTransform.ScaleY)">
                <EasingDoubleKeyFrame KeyTime="0" Value="0.92">
                    <EasingDoubleKeyFrame.EasingFunction>
                        <ExponentialEase EasingMode="EaseOut" Exponent="5"/>
                    </EasingDoubleKeyFrame.EasingFunction>
                </EasingDoubleKeyFrame>
                <EasingDoubleKeyFrame KeyTime="0:0:0.367" Value="1">
                    <EasingDoubleKeyFrame.EasingFunction>
                        <ExponentialEase EasingMode="EaseOut" Exponent="5"/>
                    </EasingDoubleKeyFrame.EasingFunction>
                </EasingDoubleKeyFrame>
            </DoubleAnimationUsingKeyFrames>
        </Storyboard>

        <!-- Value Converters -->

        <converters:BooleanToVisibilityNegationConverter x:Key="BooleanToVisibilityNegationConverter"/>
        <converters:BooleanNegationConverter x:Key="BooleanNegationConverter"/>

        <!-- DataTemplate Selectors and Style Selectors -->

        <converters:ExpressionItemTemplateSelector x:Key="ExpressionItemTemplateSelector"
                                                   OperandTemplate="{StaticResource Operand}"
                                                   OperatorTemplate="{StaticResource Operator}"
                                                   SeparatorTemplate="{StaticResource Separator}"/>

        <!-- Miscellaneous Resources -->

        <automation:NarratorNotifier x:Name="NarratorNotifier" Announcement="{x:Bind Model.Announcement, Mode=OneWay}"/>

        <!-- Used by hidden shortcut buttons -->
        <x:Int32 x:Key="Zero">0</x:Int32>

        <MenuFlyout x:Key="DisplayContextMenu">
            <MenuFlyoutItem x:Name="CopyMenuItem"
                            x:Uid="CopyMenuItem"
                            Command="{x:Bind Model.CopyCommand}"
                            Icon="Copy"/>
            <MenuFlyoutItem x:Name="PasteMenuItem"
                            x:Uid="PasteMenuItem"
                            Command="{x:Bind Model.PasteCommand}"
                            Icon="Paste"/>
        </MenuFlyout>
    </UserControl.Resources>

    <Grid AutomationProperties.LandmarkType="Main">
        <Grid.ColumnDefinitions>
            <ColumnDefinition x:Name="ColumnMain"/>
            <ColumnDefinition x:Name="ColumnHistory"
                              Width="0"
                              MaxWidth="320"/>
        </Grid.ColumnDefinitions>

        <VisualStateManager.VisualStateGroups>
            <!-- Error Layout specific -->
            <VisualStateGroup x:Name="ErrorVisualStates">
                <VisualState x:Name="NoErrorLayout"/>
                <VisualState x:Name="ErrorLayout">
                    <VisualState.Setters>
                        <Setter Target="ClearMemoryButton.IsEnabled" Value="False"/>
                        <Setter Target="MemRecall.IsEnabled" Value="False"/>
                        <Setter Target="MemPlus.IsEnabled" Value="False"/>
                        <Setter Target="MemMinus.IsEnabled" Value="False"/>
                        <Setter Target="MemButton.IsEnabled" Value="False"/>
                    </VisualState.Setters>
                    <Storyboard Completed="OnErrorVisualStateCompleted"/>
                </VisualState>
            </VisualStateGroup>
            <!-- Always-on-Top specific -->
            <VisualStateGroup x:Name="DisplayModeVisualStates">
                <VisualState x:Name="DisplayModeAlwaysOnTop">
                    <VisualState.Setters>
                        <Setter Target="ClearMemoryButton.IsEnabled" Value="False"/>
                        <Setter Target="MemRecall.IsEnabled" Value="False"/>
                        <Setter Target="MemPlus.IsEnabled" Value="False"/>
                        <Setter Target="MemMinus.IsEnabled" Value="False"/>
                        <Setter Target="MemButton.IsEnabled" Value="False"/>
                        <Setter Target="RowExpression.Height" Value="0*"/>
                        <Setter Target="RowHamburger.Height" Value="0"/>
                        <Setter Target="RowMemoryControls.Height" Value="0*"/>
                    </VisualState.Setters>
                    <Storyboard Completed="OnDisplayVisualStateCompleted"/>
                </VisualState>
                <VisualState x:Name="DisplayModeNormal">
                    <VisualState.Setters>
                        <Setter Target="RowExpression.Height" Value="20*"/>
                        <Setter Target="RowHamburger.Height" Value="{StaticResource HamburgerHeightGridLength}"/>
                        <Setter Target="RowMemoryControls.Height" Value="32*"/>
                    </VisualState.Setters>
                    <Storyboard Completed="OnDisplayVisualStateCompleted"/>
                </VisualState>
            </VisualStateGroup>
            <!-- Mode specific -->
            <VisualStateGroup x:Name="ModeVisualStates">
                <VisualState x:Name="Standard">
                    <Storyboard Completed="OnModeVisualStateCompleted"/>
                </VisualState>
                <VisualState x:Name="Scientific">
                    <VisualState.Setters>
                        <Setter Target="RowDisplayControls.Height" Value="32*"/>
                        <Setter Target="RowDisplayControls.MinHeight" Value="32"/>
                        <Setter Target="RowNumPad.Height" Value="276*"/>
                    </VisualState.Setters>
                    <Storyboard Completed="OnModeVisualStateCompleted"/>
                </VisualState>
                <VisualState x:Name="Programmer">
                    <VisualState.Setters>
                        <Setter Target="RowDisplayControls.Height" Value="96*"/>
                        <Setter Target="RowDisplayControls.MinHeight" Value="96"/>
                        <Setter Target="RowNumPad.Height" Value="268*"/>
                        <Setter Target="M4.Width" Value="0.01*"/>
                        <Setter Target="M4.MaxWidth" Value="99999"/>
                        <Setter Target="M5.Width" Value="1*"/>
                        <Setter Target="M5.MaxWidth" Value="80"/>
                        <Setter Target="MemButton.(Grid.Column)" Value="5"/>
                        <Setter Target="MemoryButton.(Grid.Column)" Value="6"/>
                    </VisualState.Setters>
                    <Storyboard Completed="OnModeVisualStateCompleted"/>
                </VisualState>
            </VisualStateGroup>
            <!-- Layout specific -->
            <VisualStateGroup x:Name="LayoutVisualStates" CurrentStateChanged="OnVisualStateChanged">
                <VisualState x:Name="Portrait768x1366">
                    <VisualState.StateTriggers>
                        <AdaptiveTrigger MinWindowHeight="1366" MinWindowWidth="768"/>
                    </VisualState.StateTriggers>
                    <VisualState.Setters>
                        <Setter Target="ColumnHistory.Width" Value="320"/>
                        <Setter Target="DockPanel.Visibility" Value="Visible"/>
                        <Setter Target="M6.Width" Value="0"/>
                    </VisualState.Setters>
                    <Storyboard Completed="OnLayoutVisualStateCompleted"/>
                </VisualState>
                <VisualState x:Name="LargeWideView">
                    <VisualState.StateTriggers>
                        <AdaptiveTrigger MinWindowHeight="768" MinWindowWidth="1024"/>
                    </VisualState.StateTriggers>
                    <VisualState.Setters>
                        <Setter Target="ColumnHistory.Width" Value="320"/>
                        <Setter Target="DockPanel.Visibility" Value="Visible"/>
                        <Setter Target="M6.Width" Value="0"/>
                    </VisualState.Setters>
                    <Storyboard Completed="OnLayoutVisualStateCompleted"/>
                </VisualState>
                <VisualState x:Name="DockVisible">
                    <VisualState.StateTriggers>
                        <AdaptiveTrigger MinWindowHeight="0" MinWindowWidth="560"/>
                    </VisualState.StateTriggers>
                    <VisualState.Setters>
                        <Setter Target="ColumnMain.Width" Value="320*"/>
                        <Setter Target="ColumnHistory.Width" Value="240*"/>
                        <Setter Target="DockPanel.Visibility" Value="Visible"/>
                        <Setter Target="M6.Width" Value="0"/>
                    </VisualState.Setters>
                    <Storyboard Completed="OnLayoutVisualStateCompleted"/>
                </VisualState>
                <VisualState x:Name="MinSizeLayout">
                    <VisualState.StateTriggers>
                        <AdaptiveTrigger MinWindowHeight="{StaticResource AppMinWindowHeight}" MinWindowWidth="{StaticResource AppMinWindowWidth}"/>
                    </VisualState.StateTriggers>
                    <Storyboard Completed="OnLayoutVisualStateCompleted"/>
                </VisualState>
                <VisualState x:Name="DefaultLayout">
                    <VisualState.StateTriggers>
                        <AdaptiveTrigger MinWindowHeight="0" MinWindowWidth="0"/>
                    </VisualState.StateTriggers>
                    <VisualState.Setters>
                        <Setter Target="RowMemoryControls.MinHeight" Value="0"/>
                        <Setter Target="ClearMemoryButton.Style" Value="{StaticResource CaptionButtonStyle}"/>
                        <Setter Target="MemRecall.Style" Value="{StaticResource CaptionButtonStyle}"/>
                        <Setter Target="MemPlus.Style" Value="{StaticResource CaptionButtonStyle}"/>
                        <Setter Target="MemMinus.Style" Value="{StaticResource CaptionButtonStyle}"/>
                        <Setter Target="MemButton.Style" Value="{StaticResource CaptionButtonStyle}"/>
                        <Setter Target="MemoryButton.MinHeight" Value="0"/>
                    </VisualState.Setters>
                    <Storyboard Completed="OnLayoutVisualStateCompleted"/>
                </VisualState>
            </VisualStateGroup>
            <!-- Results display specific -->
            <VisualStateGroup>
                <VisualState x:Name="RegularAlwaysOnTop">
                    <VisualState.StateTriggers>
                        <AdaptiveTrigger MinWindowHeight="260"/>
                    </VisualState.StateTriggers>
                    <VisualState.Setters>
                        <Setter Target="RowResult.MinHeight" Value="54"/>
                        <Setter Target="RowResult.Height" Value="72*"/>
                    </VisualState.Setters>
                </VisualState>
                <VisualState x:Name="MinAlwaysOnTop">
                    <VisualState.StateTriggers>
                        <AdaptiveTrigger MinWindowHeight="0"/>
                    </VisualState.StateTriggers>
                    <VisualState.Setters>
                        <Setter Target="RowResult.MinHeight" Value="20"/>
                        <Setter Target="RowResult.Height" Value="72*"/>
                        <Setter Target="AlwaysOnTopResults.FontSize" Value="18"/>
                        <Setter Target="AlwaysOnTopResults.ScrollButtonsWidth" Value="14"/>
                        <Setter Target="AlwaysOnTopResults.ScrollButtonsFontSize" Value="12"/>
                    </VisualState.Setters>
                </VisualState>
            </VisualStateGroup>
            <VisualStateGroup>
                <VisualState x:Name="ResultsL">
                    <VisualState.StateTriggers>
                        <AdaptiveTrigger MinWindowHeight="800"/>
                    </VisualState.StateTriggers>
                    <VisualState.Setters>
                        <Setter Target="Results.MaxFontSize" Value="72"/>
                        <Setter Target="RowResult.MinHeight" Value="108"/>
                        <Setter Target="RowResult.Height" Value="72*"/>
                    </VisualState.Setters>
                </VisualState>
                <VisualState x:Name="ResultsM">
                    <VisualState.StateTriggers>
                        <AdaptiveTrigger x:Name="ResultsMVisualStateTrigger" MinWindowHeight="640"/>
                    </VisualState.StateTriggers>
                    <VisualState.Setters>
                        <Setter Target="Results.MaxFontSize" Value="46"/>
                        <Setter Target="RowResult.MinHeight" Value="72"/>
                        <Setter Target="RowResult.Height" Value="72*"/>
                    </VisualState.Setters>
                </VisualState>
                <VisualState x:Name="ResultsS">
                    <VisualState.StateTriggers>
                        <AdaptiveTrigger MinWindowHeight="0"/>
                    </VisualState.StateTriggers>
                    <VisualState.Setters>
                        <Setter Target="Results.MaxFontSize" Value="26"/>
                        <Setter Target="RowResult.MinHeight" Value="42"/>
                        <Setter Target="RowResult.Height" Value="42*"/>
                    </VisualState.Setters>
                </VisualState>
            </VisualStateGroup>
        </VisualStateManager.VisualStateGroups>

        <Grid FlowDirection="LeftToRight">
            <Grid.RowDefinitions>
                <RowDefinition x:Name="RowHamburger" Height="{StaticResource HamburgerHeightGridLength}"/>
                <RowDefinition x:Name="RowExpression"
                               Height="22*"
                               MinHeight="0"/>
                <RowDefinition x:Name="RowResult"
                               Height="72*"
                               MinHeight="20"/>
                <RowDefinition x:Name="RowDisplayControls" Height="0"/>
                <RowDefinition x:Name="RowMemoryControls"
                               Height="32*"
                               MinHeight="0"/>
                <RowDefinition x:Name="RowNumPad"
                               Height="308*"
                               MinHeight="0"/>
            </Grid.RowDefinitions>

            <controls:OverflowTextBlock x:Name="ExpressionText"
                                        Grid.Row="1"
                                        Margin="6,0,6,0"
                                        VerticalAlignment="Bottom"
                                        Style="{StaticResource NormalStyle}"
                                        AutomationProperties.AutomationId="CalculatorExpression"
                                        AutomationProperties.Name="{x:Bind Model.CalculationExpressionAutomationName, Mode=OneWay}"
                                        IsTabStop="False"
                                        TokensUpdated="{x:Bind Model.AreTokensUpdated, Mode=OneWay}"
                                        Visibility="{x:Bind Model.IsAlwaysOnTop, Converter={StaticResource BooleanToVisibilityNegationConverter}, Mode=OneWay}"/>
            <controls:CalculationResult x:Name="Results"
                                        x:Uid="CalculatorResults"
                                        Grid.Row="2"
                                        Margin="0,0,0,0"
                                        Style="{StaticResource ResultsStyle}"
                                        AutomationProperties.AutomationId="CalculatorResults"
                                        AutomationProperties.HeadingLevel="Level1"
                                        AutomationProperties.Name="{x:Bind Model.CalculationResultAutomationName, Mode=OneWay}"
                                        ContextCanceled="OnContextCanceled"
                                        ContextRequested="OnContextRequested"
                                        DisplayValue="{x:Bind Model.DisplayValue, Mode=OneWay}"
                                        IsInError="{x:Bind Model.IsInError, Mode=OneWay}"
                                        IsOperatorCommand="{x:Bind Model.IsOperatorCommand, Mode=OneWay}"
                                        TabIndex="1"
                                        Visibility="{x:Bind Model.IsAlwaysOnTop, Converter={StaticResource BooleanToVisibilityNegationConverter}, Mode=OneWay}"/>
            <controls:OverflowTextBlock x:Name="AlwaysOnTopResults"
                                        x:Uid="CalculatorAlwaysOnTopResults"
                                        Grid.Row="2"
                                        Margin="6,0,6,0"
                                        HorizontalContentAlignment="Right"
                                        Style="{StaticResource AOTResultsStyle}"
                                        FontSize="40"
                                        AutomationProperties.AutomationId="CalculatorAlwaysOnTopResults"
                                        AutomationProperties.HeadingLevel="Level1"
                                        AutomationProperties.Name="{x:Bind Model.CalculationResultAutomationName, Mode=OneWay}"
                                        IsActive="True"
                                        ScrollButtonsFontSize="28"
                                        ScrollButtonsPlacement="Above"
                                        ScrollButtonsWidth="28"
                                        TokensUpdated="{x:Bind Model.AreAlwaysOnTopResultsUpdated, Mode=OneWay}"
                                        UseSystemFocusVisuals="True"
                                        Visibility="{x:Bind Model.IsAlwaysOnTop, Mode=OneWay}"/>

            <!-- Programmer display panel controls -->
            <local:CalculatorProgrammerOperators x:Name="ProgrammerOperators"
                                                 Grid.Row="3"
                                                 TabIndex="6"
                                                 Visibility="{x:Bind Model.IsProgrammer, Mode=OneWay}"
                                                 IsEnabled="{x:Bind Model.IsProgrammer, Mode=OneWay}"
                                                 x:Load="False"/>

            <local:CalculatorProgrammerDisplayPanel x:Name="ProgrammerDisplayPanel"
                                                    Grid.Row="4"
                                                    TabIndex="7"
                                                    Visibility="{x:Bind Model.IsProgrammer, Mode=OneWay}"
                                                    IsEnabled="{x:Bind Model.IsProgrammer, Mode=OneWay}"
                                                    x:Load="False"/>

            <Button x:Name="HistoryButton"
                    Grid.Row="0"
                    Style="{StaticResource HistoryButtonStyle}"
                    common:KeyboardShortcutManager.VirtualKeyControlChord="{utils:ResourceVirtualKey Name=HistoryButton/[using:CalculatorApp.Common]KeyboardShortcutManager/VirtualKeyControlChord}"
                    AccessKey="{utils:ResourceString Name=HistoryButton/AccessKey}"
                    AutomationProperties.AutomationId="HistoryButton"
                    Command="{x:Bind HistoryButtonPressed, Mode=OneTime}"
                    Content="&#xe81c;"
                    ExitDisplayModeOnAccessKeyInvoked="False"
                    TabIndex="2"
                    ToolTipService.ToolTip="{utils:ResourceString Name=HistoryButton/[using:Windows.UI.Xaml.Controls]ToolTipService/ToolTip}"
                    Visibility="{x:Bind local:Calculator.ShouldDisplayHistoryButton(Model.IsAlwaysOnTop, Model.IsProgrammer, DockPanel.Visibility), Mode=OneWay}"
                    IsEnabled="{x:Bind Model.IsAlwaysOnTop, Converter={StaticResource BooleanNegationConverter}, Mode=OneWay}">
                <FlyoutBase.AttachedFlyout>
                    <Flyout x:Name="HistoryFlyout"
                            AutomationProperties.AutomationId="HistoryFlyout"
                            Closed="HistoryFlyout_Closed"
                            Closing="HistoryFlyout_Closing"
                            FlyoutPresenterStyle="{StaticResource HistoryFlyoutStyle}"
                            Opened="HistoryFlyout_Opened"
                            Placement="Full"/>
                </FlyoutBase.AttachedFlyout>
            </Button>

            <!-- Scientific angle buttons -->
            <local:CalculatorScientificAngleButtons x:Name="ScientificAngleButtons"
                                                    Grid.Row="3"
                                                    TabIndex="6"
                                                    Visibility="{x:Bind Model.IsScientific, Mode=OneWay}"
                                                    IsEnabled="{x:Bind Model.IsScientific, Mode=OneWay}"
                                                    x:Load="False"/>

            <!-- Memory panel controls -->
            <Grid x:Name="MemoryPanel"
                  Grid.Row="4"
                  Margin="3,0,3,0"
                  AutomationProperties.HeadingLevel="Level1"
                  AutomationProperties.Name="{utils:ResourceString Name=MemoryPanel/[using:Windows.UI.Xaml.Automation]AutomationProperties/Name}"
                  Visibility="{x:Bind Model.IsAlwaysOnTop, Converter={StaticResource BooleanToVisibilityNegationConverter}, Mode=OneWay}">
                <Grid.ColumnDefinitions>
                    <ColumnDefinition Width="1*" MaxWidth="80"/>
                    <ColumnDefinition Width="1*" MaxWidth="80"/>
                    <ColumnDefinition Width="1*" MaxWidth="80"/>
                    <ColumnDefinition Width="1*" MaxWidth="80"/>
                    <ColumnDefinition x:Name="M4"
                                      Width="1*"
                                      MaxWidth="80"/>
                    <ColumnDefinition x:Name="M5" Width="0.01*"/>
                    <ColumnDefinition x:Name="M6"
                                      Width="1*"
                                      MaxWidth="80"/>
                </Grid.ColumnDefinitions>

                <controls:CalculatorButton x:Name="ClearMemoryButton"
                                           Style="{StaticResource CaptionButtonStyle}"
                                           common:KeyboardShortcutManager.VirtualKeyControlChord="{utils:ResourceVirtualKey Name=ClearMemoryButton/[using:CalculatorApp.Common]KeyboardShortcutManager/VirtualKeyControlChord}"
                                           AutomationProperties.AutomationId="ClearMemoryButton"
                                           AutomationProperties.Name="{utils:ResourceString Name=ClearMemoryButton/[using:Windows.UI.Xaml.Automation]AutomationProperties/Name}"
                                           Command="{x:Bind Model.ClearMemoryCommand}"
                                           Content="&#xF754;"
                                           TabIndex="10"
                                           ToolTipService.ToolTip="{utils:ResourceString Name=ClearMemoryButton/[using:Windows.UI.Xaml.Controls]ToolTipService/ToolTip}"
                                           Visibility="{Binding IsProgrammer, Converter={StaticResource BooleanToVisibilityNegationConverter}}"/>
                <controls:CalculatorButton x:Name="MemRecall"
                                           Grid.Column="1"
                                           Style="{StaticResource CaptionButtonStyle}"
                                           common:KeyboardShortcutManager.VirtualKeyControlChord="{utils:ResourceVirtualKey Name=MemRecall/[using:CalculatorApp.Common]KeyboardShortcutManager/VirtualKeyControlChord}"
                                           AutomationProperties.AutomationId="MemRecall"
                                           AutomationProperties.Name="{utils:ResourceString Name=MemRecall/[using:Windows.UI.Xaml.Automation]AutomationProperties/Name}"
                                           Command="{x:Bind Model.MemoryItemPressed}"
                                           CommandParameter="{StaticResource Zero}"
                                           Content="&#xF755;"
                                           TabIndex="11"
                                           ToolTipService.ToolTip="{utils:ResourceString Name=MemRecall/[using:Windows.UI.Xaml.Controls]ToolTipService/ToolTip}"
                                           Visibility="{Binding IsProgrammer, Converter={StaticResource BooleanToVisibilityNegationConverter}}"/>
                <controls:CalculatorButton x:Name="MemPlus"
                                           Grid.Column="2"
                                           Style="{StaticResource CaptionButtonStyle}"
                                           common:KeyboardShortcutManager.VirtualKeyControlChord="{utils:ResourceVirtualKey Name=MemPlus/[using:CalculatorApp.Common]KeyboardShortcutManager/VirtualKeyControlChord}"
                                           AutomationProperties.AutomationId="MemPlus"
                                           AutomationProperties.Name="{utils:ResourceString Name=MemPlus/[using:Windows.UI.Xaml.Automation]AutomationProperties/Name}"
                                           Command="{x:Bind Model.MemoryAdd}"
                                           CommandParameter="{StaticResource Zero}"
                                           Content="&#xF757;"
                                           TabIndex="12"
                                           ToolTipService.ToolTip="{utils:ResourceString Name=MemPlus/[using:Windows.UI.Xaml.Controls]ToolTipService/ToolTip}"
                                           Visibility="{Binding IsProgrammer, Converter={StaticResource BooleanToVisibilityNegationConverter}}"/>
                <controls:CalculatorButton x:Name="MemMinus"
                                           Grid.Column="3"
                                           Style="{StaticResource CaptionButtonStyle}"
                                           common:KeyboardShortcutManager.VirtualKeyControlChord="{utils:ResourceVirtualKey Name=MemMinus/[using:CalculatorApp.Common]KeyboardShortcutManager/VirtualKeyControlChord}"
                                           AutomationProperties.AutomationId="MemMinus"
                                           AutomationProperties.Name="{utils:ResourceString Name=MemMinus/[using:Windows.UI.Xaml.Automation]AutomationProperties/Name}"
                                           Command="{x:Bind Model.MemorySubtract}"
                                           CommandParameter="{StaticResource Zero}"
                                           Content="&#xF758;"
                                           TabIndex="13"
                                           ToolTipService.ToolTip="{utils:ResourceString Name=MemMinus/[using:Windows.UI.Xaml.Controls]ToolTipService/ToolTip}"
                                           Visibility="{Binding IsProgrammer, Converter={StaticResource BooleanToVisibilityNegationConverter}}"/>
                <controls:CalculatorButton x:Name="MemButton"
                                           Grid.Column="4"
                                           Style="{StaticResource CaptionButtonStyle}"
                                           common:KeyboardShortcutManager.VirtualKeyControlChord="{utils:ResourceVirtualKey Name=memButton/[using:CalculatorApp.Common]KeyboardShortcutManager/VirtualKeyControlChord}"
                                           AutomationProperties.AutomationId="memButton"
                                           AutomationProperties.Name="{utils:ResourceString Name=memButton/[using:Windows.UI.Xaml.Automation]AutomationProperties/Name}"
                                           ButtonId="Memory"
                                           Content="&#xF756;"
                                           TabIndex="14"
                                           ToolTipService.ToolTip="{utils:ResourceString Name=memButton/[using:Windows.UI.Xaml.Controls]ToolTipService/ToolTip}"/>
                <Button x:Name="MemoryButton"
                        Grid.Column="6"
                        MinWidth="15.5"
                        MinHeight="8.402"
                        Style="{StaticResource CaptionButtonStyle}"
                        AccessKey="{utils:ResourceString Name=MemoryButton/AccessKey}"
                        AutomationProperties.AutomationId="MemoryButton"
                        Click="ToggleMemoryFlyout"
                        Content="&#xE61D;"
                        ExitDisplayModeOnAccessKeyInvoked="False"
                        TabIndex="15"
                        ToolTipService.ToolTip="{utils:ResourceString Name=MemoryButton/[using:Windows.UI.Xaml.Controls]ToolTipService/ToolTip}"
                        IsEnabled="{x:Bind Model.IsMemoryEmpty, Converter={StaticResource BooleanNegationConverter}, Mode=OneWay}">
                    <FlyoutBase.AttachedFlyout>
                        <Flyout x:Name="MemoryFlyout"
                                AutomationProperties.AutomationId="MemoryFlyout"
                                AutomationProperties.Name="{utils:ResourceString Name=MemoryFlyout/[using:Windows.UI.Xaml.Automation]AutomationProperties/Name}"
                                Closed="OnMemoryFlyoutClosed"
                                Closing="OnMemoryFlyoutClosing"
                                FlyoutPresenterStyle="{StaticResource MemoryFlyoutStyle}"
                                Opened="OnMemoryFlyoutOpened"
                                Placement="Full"/>
                    </FlyoutBase.AttachedFlyout>
                </Button>
            </Grid>

            <Grid x:Name="NumpadPanel"
                  Grid.Row="5"
                  Margin="3,0,3,3"
                  RenderTransformOrigin="0.5,0.5">
                <Grid.RenderTransform>
                    <CompositeTransform/>
                </Grid.RenderTransform>

                <local:OperatorsPanel x:Name="OpsPanel" IsBitFlipChecked="{x:Bind Model.IsBitFlipChecked, Mode=OneWay}"/>
            </Grid>
        </Grid>

        <!-- Docked Pivot panel for history/memory -->
        <Border x:Name="DockPanel"
                Grid.Row="1"
                Grid.Column="1"
                AutomationProperties.HeadingLevel="Level1"
                AutomationProperties.Name="{utils:ResourceString Name=DockPanel/[using:Windows.UI.Xaml.Automation]AutomationProperties/Name}"
                Visibility="Collapsed">
            <Border.Resources>
                <!--
                    This is a copy/paste of the Pivot template. The only change is setting the PivotPanel's ManipulationMode=None
                    to disable swipe navigation between History and Memory to enable the SwipeControl
                -->
                <ControlTemplate x:Key="DockPanelTemplate" TargetType="Pivot">
                    <Grid x:Name="RootElement"
                          HorizontalAlignment="{TemplateBinding HorizontalAlignment}"
                          VerticalAlignment="{TemplateBinding VerticalAlignment}"
                          Background="{TemplateBinding Background}">
                        <Grid.Resources>
                            <Style x:Key="BaseContentControlStyle" TargetType="ContentControl">
                                <Setter Property="FontFamily" Value="XamlAutoFontFamily"/>
                                <Setter Property="FontWeight" Value="SemiBold"/>
                                <Setter Property="FontSize" Value="15"/>
                                <Setter Property="Template">
                                    <Setter.Value>
                                        <ControlTemplate TargetType="ContentControl">
                                            <ContentPresenter Margin="{TemplateBinding Padding}"
                                                              HorizontalAlignment="{TemplateBinding HorizontalContentAlignment}"
                                                              VerticalAlignment="{TemplateBinding VerticalContentAlignment}"
                                                              Content="{TemplateBinding Content}"
                                                              ContentTemplate="{TemplateBinding ContentTemplate}"
                                                              ContentTransitions="{TemplateBinding ContentTransitions}"
                                                              OpticalMarginAlignment="TrimSideBearings"/>
                                        </ControlTemplate>
                                    </Setter.Value>
                                </Setter>
                            </Style>
                            <Style x:Key="TitleContentControlStyle"
                                   BasedOn="{StaticResource BaseContentControlStyle}"
                                   TargetType="ContentControl">
                                <Setter Property="FontFamily" Value="{ThemeResource PivotTitleFontFamily}"/>
                                <Setter Property="FontWeight" Value="{ThemeResource PivotTitleThemeFontWeight}"/>
                                <Setter Property="FontSize" Value="{ThemeResource PivotTitleFontSize}"/>
                            </Style>
                        </Grid.Resources>

                        <Grid.RowDefinitions>
                            <RowDefinition Height="Auto"/>
                            <RowDefinition Height="*"/>
                        </Grid.RowDefinitions>

                        <VisualStateManager.VisualStateGroups>
                            <VisualStateGroup x:Name="Orientation">
                                <VisualState x:Name="Portrait">
                                    <Storyboard>
                                        <ObjectAnimationUsingKeyFrames Storyboard.TargetName="TitleContentControl" Storyboard.TargetProperty="Margin">
                                            <DiscreteObjectKeyFrame KeyTime="0" Value="{ThemeResource PivotPortraitThemePadding}"/>
                                        </ObjectAnimationUsingKeyFrames>
                                    </Storyboard>
                                </VisualState>
                                <VisualState x:Name="Landscape">
                                    <Storyboard>
                                        <ObjectAnimationUsingKeyFrames Storyboard.TargetName="TitleContentControl" Storyboard.TargetProperty="Margin">
                                            <DiscreteObjectKeyFrame KeyTime="0" Value="{ThemeResource PivotLandscapeThemePadding}"/>
                                        </ObjectAnimationUsingKeyFrames>
                                    </Storyboard>
                                </VisualState>
                            </VisualStateGroup>
                            <VisualStateGroup x:Name="NavigationButtonsVisibility">
                                <VisualState x:Name="NavigationButtonsHidden"/>
                                <VisualState x:Name="NavigationButtonsVisible">
                                    <Storyboard>
                                        <ObjectAnimationUsingKeyFrames Storyboard.TargetName="NextButton" Storyboard.TargetProperty="Opacity">
                                            <DiscreteObjectKeyFrame KeyTime="0" Value="1"/>
                                        </ObjectAnimationUsingKeyFrames>
                                        <ObjectAnimationUsingKeyFrames Storyboard.TargetName="NextButton" Storyboard.TargetProperty="IsEnabled">
                                            <DiscreteObjectKeyFrame KeyTime="0" Value="True"/>
                                        </ObjectAnimationUsingKeyFrames>
                                        <ObjectAnimationUsingKeyFrames Storyboard.TargetName="PreviousButton" Storyboard.TargetProperty="Opacity">
                                            <DiscreteObjectKeyFrame KeyTime="0" Value="1"/>
                                        </ObjectAnimationUsingKeyFrames>
                                        <ObjectAnimationUsingKeyFrames Storyboard.TargetName="PreviousButton" Storyboard.TargetProperty="IsEnabled">
                                            <DiscreteObjectKeyFrame KeyTime="0" Value="True"/>
                                        </ObjectAnimationUsingKeyFrames>
                                    </Storyboard>
                                </VisualState>
                                <VisualState x:Name="PreviousButtonVisible">
                                    <Storyboard>
                                        <ObjectAnimationUsingKeyFrames Storyboard.TargetName="PreviousButton" Storyboard.TargetProperty="Opacity">
                                            <DiscreteObjectKeyFrame KeyTime="0" Value="1"/>
                                        </ObjectAnimationUsingKeyFrames>
                                        <ObjectAnimationUsingKeyFrames Storyboard.TargetName="PreviousButton" Storyboard.TargetProperty="IsEnabled">
                                            <DiscreteObjectKeyFrame KeyTime="0" Value="True"/>
                                        </ObjectAnimationUsingKeyFrames>
                                    </Storyboard>
                                </VisualState>
                                <VisualState x:Name="NextButtonVisible">
                                    <Storyboard>
                                        <ObjectAnimationUsingKeyFrames Storyboard.TargetName="NextButton" Storyboard.TargetProperty="Opacity">
                                            <DiscreteObjectKeyFrame KeyTime="0" Value="1"/>
                                        </ObjectAnimationUsingKeyFrames>
                                        <ObjectAnimationUsingKeyFrames Storyboard.TargetName="NextButton" Storyboard.TargetProperty="IsEnabled">
                                            <DiscreteObjectKeyFrame KeyTime="0" Value="True"/>
                                        </ObjectAnimationUsingKeyFrames>
                                    </Storyboard>
                                </VisualState>
                            </VisualStateGroup>
                            <VisualStateGroup x:Name="HeaderStates">
                                <VisualState x:Name="HeaderDynamic"/>
                                <VisualState x:Name="HeaderStatic">
                                    <Storyboard>
                                        <ObjectAnimationUsingKeyFrames Storyboard.TargetName="Header" Storyboard.TargetProperty="Visibility">
                                            <DiscreteObjectKeyFrame KeyTime="0" Value="Collapsed"/>
                                        </ObjectAnimationUsingKeyFrames>
                                        <ObjectAnimationUsingKeyFrames Storyboard.TargetName="StaticHeader" Storyboard.TargetProperty="Visibility">
                                            <DiscreteObjectKeyFrame KeyTime="0" Value="Visible"/>
                                        </ObjectAnimationUsingKeyFrames>
                                    </Storyboard>
                                </VisualState>
                            </VisualStateGroup>

                        </VisualStateManager.VisualStateGroups>

                        <ContentControl x:Name="TitleContentControl"
                                        Margin="{StaticResource PivotPortraitThemePadding}"
                                        Style="{StaticResource TitleContentControlStyle}"
                                        Content="{TemplateBinding Title}"
                                        ContentTemplate="{TemplateBinding TitleTemplate}"
                                        IsTabStop="False"
                                        Visibility="Collapsed"/>

                        <Grid Grid.Row="1">
                            <Grid.Resources>
                                <ControlTemplate x:Key="NextTemplate" TargetType="Button">
                                    <Border x:Name="Root"
                                            Background="{ThemeResource PivotNextButtonBackground}"
                                            BorderBrush="{ThemeResource PivotNextButtonBorderBrush}"
                                            BorderThickness="{ThemeResource PivotNavButtonBorderThemeThickness}">
                                        <VisualStateManager.VisualStateGroups>
                                            <VisualStateGroup x:Name="CommonStates">
                                                <VisualState x:Name="Normal"/>
                                                <VisualState x:Name="PointerOver">
                                                    <Storyboard>
                                                        <ObjectAnimationUsingKeyFrames Storyboard.TargetName="Root" Storyboard.TargetProperty="Background">
                                                            <DiscreteObjectKeyFrame KeyTime="0" Value="{ThemeResource PivotNextButtonBackgroundPointerOver}"/>
                                                        </ObjectAnimationUsingKeyFrames>
                                                        <ObjectAnimationUsingKeyFrames Storyboard.TargetName="Root" Storyboard.TargetProperty="BorderBrush">
                                                            <DiscreteObjectKeyFrame KeyTime="0" Value="{ThemeResource PivotNextButtonBorderBrushPointerOver}"/>
                                                        </ObjectAnimationUsingKeyFrames>
                                                        <ObjectAnimationUsingKeyFrames Storyboard.TargetName="Arrow" Storyboard.TargetProperty="Foreground">
                                                            <DiscreteObjectKeyFrame KeyTime="0" Value="{ThemeResource PivotNextButtonForegroundPointerOver}"/>
                                                        </ObjectAnimationUsingKeyFrames>
                                                    </Storyboard>
                                                </VisualState>
                                                <VisualState x:Name="Pressed">
                                                    <Storyboard>
                                                        <ObjectAnimationUsingKeyFrames Storyboard.TargetName="Root" Storyboard.TargetProperty="Background">
                                                            <DiscreteObjectKeyFrame KeyTime="0" Value="{ThemeResource PivotNextButtonBackgroundPressed}"/>
                                                        </ObjectAnimationUsingKeyFrames>
                                                        <ObjectAnimationUsingKeyFrames Storyboard.TargetName="Root" Storyboard.TargetProperty="BorderBrush">
                                                            <DiscreteObjectKeyFrame KeyTime="0" Value="{ThemeResource PivotNextButtonBorderBrushPressed}"/>
                                                        </ObjectAnimationUsingKeyFrames>
                                                        <ObjectAnimationUsingKeyFrames Storyboard.TargetName="Arrow" Storyboard.TargetProperty="Foreground">
                                                            <DiscreteObjectKeyFrame KeyTime="0" Value="{ThemeResource PivotNextButtonForegroundPressed}"/>
                                                        </ObjectAnimationUsingKeyFrames>
                                                    </Storyboard>
                                                </VisualState>
                                            </VisualStateGroup>
                                        </VisualStateManager.VisualStateGroups>
                                        <FontIcon x:Name="Arrow"
                                                  HorizontalAlignment="Center"
                                                  VerticalAlignment="Center"
                                                  Foreground="{ThemeResource PivotNextButtonForeground}"
                                                  FontFamily="{ThemeResource CalculatorFontFamily}"
                                                  FontSize="12"
                                                  Glyph="&#xE970;"
                                                  MirroredWhenRightToLeft="True"/>
                                    </Border>
                                </ControlTemplate>

                                <ControlTemplate x:Key="PreviousTemplate" TargetType="Button">
                                    <Border x:Name="Root"
                                            Background="{ThemeResource PivotPreviousButtonBackground}"
                                            BorderBrush="{ThemeResource PivotPreviousButtonBorderBrush}"
                                            BorderThickness="{ThemeResource PivotNavButtonBorderThemeThickness}">
                                        <VisualStateManager.VisualStateGroups>
                                            <VisualStateGroup x:Name="CommonStates">
                                                <VisualState x:Name="Normal"/>
                                                <VisualState x:Name="PointerOver">
                                                    <Storyboard>
                                                        <ObjectAnimationUsingKeyFrames Storyboard.TargetName="Root" Storyboard.TargetProperty="Background">
                                                            <DiscreteObjectKeyFrame KeyTime="0" Value="{ThemeResource PivotPreviousButtonBackgroundPointerOver}"/>
                                                        </ObjectAnimationUsingKeyFrames>
                                                        <ObjectAnimationUsingKeyFrames Storyboard.TargetName="Root" Storyboard.TargetProperty="BorderBrush">
                                                            <DiscreteObjectKeyFrame KeyTime="0" Value="{ThemeResource PivotPreviousButtonBorderBrushPointerOver}"/>
                                                        </ObjectAnimationUsingKeyFrames>
                                                        <ObjectAnimationUsingKeyFrames Storyboard.TargetName="Arrow" Storyboard.TargetProperty="Foreground">
                                                            <DiscreteObjectKeyFrame KeyTime="0" Value="{ThemeResource PivotPreviousButtonForegroundPointerOver}"/>
                                                        </ObjectAnimationUsingKeyFrames>
                                                    </Storyboard>
                                                </VisualState>
                                                <VisualState x:Name="Pressed">
                                                    <Storyboard>
                                                        <ObjectAnimationUsingKeyFrames Storyboard.TargetName="Root" Storyboard.TargetProperty="Background">
                                                            <DiscreteObjectKeyFrame KeyTime="0" Value="{ThemeResource PivotPreviousButtonBackgroundPressed}"/>
                                                        </ObjectAnimationUsingKeyFrames>
                                                        <ObjectAnimationUsingKeyFrames Storyboard.TargetName="Root" Storyboard.TargetProperty="BorderBrush">
                                                            <DiscreteObjectKeyFrame KeyTime="0" Value="{ThemeResource PivotPreviousButtonBorderBrushPressed}"/>
                                                        </ObjectAnimationUsingKeyFrames>
                                                        <ObjectAnimationUsingKeyFrames Storyboard.TargetName="Arrow" Storyboard.TargetProperty="Foreground">
                                                            <DiscreteObjectKeyFrame KeyTime="0" Value="{ThemeResource PivotPreviousButtonForegroundPressed}"/>
                                                        </ObjectAnimationUsingKeyFrames>
                                                    </Storyboard>
                                                </VisualState>
                                            </VisualStateGroup>
                                        </VisualStateManager.VisualStateGroups>
                                        <FontIcon x:Name="Arrow"
                                                  HorizontalAlignment="Center"
                                                  VerticalAlignment="Center"
                                                  Foreground="{ThemeResource PivotPreviousButtonForeground}"
                                                  FontFamily="{ThemeResource CalculatorFontFamily}"
                                                  FontSize="12"
                                                  Glyph="&#xE96F;"
                                                  MirroredWhenRightToLeft="True"/>
                                    </Border>
                                </ControlTemplate>
                            </Grid.Resources>

                            <ScrollViewer x:Name="ScrollViewer"
                                          Margin="{TemplateBinding Padding}"
                                          VerticalContentAlignment="Stretch"
                                          BringIntoViewOnFocusChange="False"
                                          HorizontalScrollBarVisibility="Hidden"
                                          HorizontalSnapPointsAlignment="Center"
                                          HorizontalSnapPointsType="MandatorySingle"
                                          Template="{StaticResource ScrollViewerScrollBarlessTemplate}"
                                          VerticalScrollBarVisibility="Disabled"
                                          VerticalScrollMode="Disabled"
                                          VerticalSnapPointsType="None"
                                          ZoomMode="Disabled">
                                <PivotPanel x:Name="Panel"
                                            VerticalAlignment="Stretch"
                                            ManipulationMode="None">
                                    <Grid x:Name="PivotLayoutElement">
                                        <Grid.RowDefinitions>
                                            <RowDefinition Height="Auto"/>
                                            <RowDefinition Height="*"/>
                                        </Grid.RowDefinitions>
                                        <Grid.ColumnDefinitions>
                                            <ColumnDefinition Width="Auto"/>
                                            <ColumnDefinition Width="*"/>
                                            <ColumnDefinition Width="Auto"/>
                                        </Grid.ColumnDefinitions>
                                        <Grid.RenderTransform>
                                            <CompositeTransform x:Name="PivotLayoutElementTranslateTransform"/>
                                        </Grid.RenderTransform>
                                        <ContentPresenter x:Name="LeftHeaderPresenter"
                                                          HorizontalAlignment="Stretch"
                                                          VerticalAlignment="Stretch"
                                                          Content="{TemplateBinding LeftHeader}"
                                                          ContentTemplate="{TemplateBinding LeftHeaderTemplate}"/>
                                        <ContentControl x:Name="HeaderClipper"
                                                        Grid.Column="1"
                                                        HorizontalContentAlignment="Stretch"
                                                        contract7Present:CornerRadius="{ThemeResource ControlCornerRadius}"
                                                        UseSystemFocusVisuals="{StaticResource UseSystemFocusVisuals}">
                                            <ContentControl.Clip>
                                                <RectangleGeometry x:Name="HeaderClipperGeometry"/>
                                            </ContentControl.Clip>
                                            <Grid Background="{ThemeResource PivotHeaderBackground}">
                                                <Grid.RenderTransform>
                                                    <CompositeTransform x:Name="HeaderOffsetTranslateTransform"/>
                                                </Grid.RenderTransform>
                                                <PivotHeaderPanel x:Name="StaticHeader" Visibility="Collapsed">
                                                    <PivotHeaderPanel.RenderTransform>
                                                        <CompositeTransform x:Name="StaticHeaderTranslateTransform"/>
                                                    </PivotHeaderPanel.RenderTransform>
                                                </PivotHeaderPanel>
                                                <PivotHeaderPanel x:Name="Header">
                                                    <PivotHeaderPanel.RenderTransform>
                                                        <CompositeTransform x:Name="HeaderTranslateTransform"/>
                                                    </PivotHeaderPanel.RenderTransform>
                                                </PivotHeaderPanel>
                                                <Rectangle x:Name="FocusFollower"
                                                           HorizontalAlignment="Stretch"
                                                           VerticalAlignment="Stretch"
                                                           Fill="Transparent"
                                                           Control.IsTemplateFocusTarget="True"
                                                           IsHitTestVisible="False"/>
                                            </Grid>
                                        </ContentControl>
                                        <Button x:Name="PreviousButton"
                                                Grid.Column="1"
                                                Width="20"
                                                Height="36"
                                                Margin="{ThemeResource PivotNavButtonMargin}"
                                                HorizontalAlignment="Left"
                                                VerticalAlignment="Top"
                                                Background="Transparent"
                                                Opacity="0"
                                                IsTabStop="False"
                                                Template="{StaticResource PreviousTemplate}"
                                                UseSystemFocusVisuals="False"
                                                IsEnabled="False"/>
                                        <Button x:Name="NextButton"
                                                Grid.Column="1"
                                                Width="20"
                                                Height="36"
                                                Margin="{ThemeResource PivotNavButtonMargin}"
                                                HorizontalAlignment="Right"
                                                VerticalAlignment="Top"
                                                Background="Transparent"
                                                Opacity="0"
                                                IsTabStop="False"
                                                Template="{StaticResource NextTemplate}"
                                                UseSystemFocusVisuals="False"
                                                IsEnabled="False"/>
                                        <ContentPresenter x:Name="RightHeaderPresenter"
                                                          Grid.Column="2"
                                                          HorizontalAlignment="Stretch"
                                                          VerticalAlignment="Stretch"
                                                          Content="{TemplateBinding RightHeader}"
                                                          ContentTemplate="{TemplateBinding RightHeaderTemplate}"/>
                                        <ItemsPresenter x:Name="PivotItemPresenter"
                                                        Grid.Row="1"
                                                        Grid.ColumnSpan="3">
                                            <ItemsPresenter.RenderTransform>
                                                <TransformGroup>
                                                    <TranslateTransform x:Name="ItemsPresenterTranslateTransform"/>
                                                    <CompositeTransform x:Name="ItemsPresenterCompositeTransform"/>
                                                </TransformGroup>
                                            </ItemsPresenter.RenderTransform>
                                        </ItemsPresenter>
                                    </Grid>
                                </PivotPanel>
                            </ScrollViewer>
                        </Grid>
                    </Grid>
                </ControlTemplate>
            </Border.Resources>

            <Pivot x:Name="DockPivot"
                   Margin="0,-3,0,0"
                   TabIndex="5"
                   Tapped="DockPanelTapped"
                   Template="{StaticResource DockPanelTemplate}">
                <Pivot.Resources>
                    <ResourceDictionary>
                        <ResourceDictionary.ThemeDictionaries>
                            <ResourceDictionary x:Key="Default">
                                <StaticResource x:Key="PivotHeaderItemForegroundUnselectedCustom" ResourceKey="TextFillColorPrimaryBrush"/>
                                <StaticResource x:Key="PivotHeaderItemForegroundUnselectedPointerOverCustom" ResourceKey="TextFillColorSecondaryBrush"/>
                                <StaticResource x:Key="PivotHeaderItemForegroundUnselectedPressedCustom" ResourceKey="TextFillColorTertiaryBrush"/>
                                <StaticResource x:Key="PivotHeaderItemForegroundSelectedCustom" ResourceKey="TextFillColorPrimaryBrush"/>
                                <StaticResource x:Key="PivotHeaderItemForegroundSelectedPointerOverCustom" ResourceKey="TextFillColorSecondaryBrush"/>
                                <StaticResource x:Key="PivotHeaderItemForegroundSelectedPressedCustom" ResourceKey="TextFillColorTertiaryBrush"/>
                            </ResourceDictionary>
                            <ResourceDictionary x:Key="HighContrast">
                                <StaticResource x:Key="PivotHeaderItemForegroundUnselectedCustom" ResourceKey="SystemControlForegroundBaseMediumBrush"/>
                                <StaticResource x:Key="PivotHeaderItemForegroundUnselectedPointerOverCustom" ResourceKey="SystemControlHighlightAltBaseMediumHighBrush"/>
                                <StaticResource x:Key="PivotHeaderItemForegroundUnselectedPressedCustom" ResourceKey="SystemControlHighlightAltBaseMediumHighBrush"/>
                                <StaticResource x:Key="PivotHeaderItemForegroundSelectedCustom" ResourceKey="SystemControlHighlightAltBaseHighBrush"/>
                                <StaticResource x:Key="PivotHeaderItemForegroundSelectedPointerOverCustom" ResourceKey="SystemControlHighlightAltBaseMediumHighBrush"/>
                                <StaticResource x:Key="PivotHeaderItemForegroundSelectedPressedCustom" ResourceKey="SystemControlHighlightAltBaseMediumHighBrush"/>
                            </ResourceDictionary>
                            <ResourceDictionary x:Key="Light">
                                <StaticResource x:Key="PivotHeaderItemForegroundUnselectedCustom" ResourceKey="TextFillColorPrimaryBrush"/>
                                <StaticResource x:Key="PivotHeaderItemForegroundUnselectedPointerOverCustom" ResourceKey="TextFillColorSecondaryBrush"/>
                                <StaticResource x:Key="PivotHeaderItemForegroundUnselectedPressedCustom" ResourceKey="TextFillColorTertiaryBrush"/>
                                <StaticResource x:Key="PivotHeaderItemForegroundSelectedCustom" ResourceKey="TextFillColorPrimaryBrush"/>
                                <StaticResource x:Key="PivotHeaderItemForegroundSelectedPointerOverCustom" ResourceKey="TextFillColorSecondaryBrush"/>
                                <StaticResource x:Key="PivotHeaderItemForegroundSelectedPressedCustom" ResourceKey="TextFillColorTertiaryBrush"/>
                            </ResourceDictionary>
                        </ResourceDictionary.ThemeDictionaries>

                        <!--
                            This is a copy/paste of DefaultPivotHeaderItemStyle, but
                            1. Updated the Foreground in ViewStates.
                            2. Updated the SelectedPipe
                        -->
                        <Style TargetType="PivotHeaderItem">
                            <Setter Property="FontSize" Value="{ThemeResource PivotHeaderItemFontSize}"/>
                            <Setter Property="FontFamily" Value="{ThemeResource PivotHeaderItemFontFamily}"/>
                            <Setter Property="FontWeight" Value="{ThemeResource PivotHeaderItemThemeFontWeight}"/>
                            <Setter Property="CharacterSpacing" Value="{ThemeResource PivotHeaderItemCharacterSpacing}"/>
                            <Setter Property="Background" Value="{ThemeResource PivotHeaderItemBackgroundUnselected}"/>
                            <Setter Property="Foreground" Value="{ThemeResource PivotHeaderItemForegroundUnselectedCustom}"/>
                            <Setter Property="Padding" Value="{ThemeResource PivotHeaderItemMargin}"/>
                            <Setter Property="Height" Value="48"/>
                            <Setter Property="VerticalContentAlignment" Value="Center"/>
                            <Setter Property="IsTabStop" Value="False"/>
                            <Setter Property="UseSystemFocusVisuals" Value="False"/>
                            <Setter Property="Template">
                                <Setter.Value>
                                    <ControlTemplate TargetType="PivotHeaderItem">
                                        <Grid x:Name="Grid"
                                              Padding="{TemplateBinding Padding}"
                                              Background="{TemplateBinding Background}"
                                              CornerRadius="{ThemeResource ControlCornerRadius}">

                                            <Grid.RenderTransform>
                                                <TranslateTransform x:Name="ContentPresenterTranslateTransform"/>
                                            </Grid.RenderTransform>

                                            <VisualStateManager.VisualStateGroups>
                                                <VisualStateGroup x:Name="SelectionStates">

                                                    <VisualStateGroup.Transitions>
                                                        <VisualTransition From="Unselected"
                                                                          GeneratedDuration="0:0:0.33"
                                                                          To="UnselectedLocked"/>
                                                        <VisualTransition From="UnselectedLocked"
                                                                          GeneratedDuration="0:0:0.33"
                                                                          To="Unselected"/>
                                                    </VisualStateGroup.Transitions>

                                                    <VisualState x:Name="Disabled">
                                                        <VisualState.Setters>
                                                            <Setter Target="SelectedPipe.Visibility" Value="Collapsed"/>
                                                        </VisualState.Setters>

                                                        <Storyboard>
                                                            <ObjectAnimationUsingKeyFrames Storyboard.TargetName="ContentPresenter" Storyboard.TargetProperty="Foreground">
                                                                <DiscreteObjectKeyFrame KeyTime="0" Value="{ThemeResource TextFillColorDisabledBrush}"/>
                                                            </ObjectAnimationUsingKeyFrames>
                                                            <ObjectAnimationUsingKeyFrames Storyboard.TargetName="Grid" Storyboard.TargetProperty="Background">
                                                                <DiscreteObjectKeyFrame KeyTime="0" Value="{ThemeResource PivotHeaderItemBackgroundDisabled}"/>
                                                            </ObjectAnimationUsingKeyFrames>
                                                        </Storyboard>
                                                    </VisualState>
                                                    <VisualState x:Name="Unselected">
                                                        <VisualState.Setters>
                                                            <Setter Target="SelectedPipe.Visibility" Value="Collapsed"/>
                                                        </VisualState.Setters>
                                                    </VisualState>
                                                    <VisualState x:Name="UnselectedLocked">
                                                        <VisualState.Setters>
                                                            <Setter Target="SelectedPipe.Visibility" Value="Collapsed"/>
                                                        </VisualState.Setters>

                                                        <Storyboard>
                                                            <DoubleAnimation Duration="0"
                                                                             Storyboard.TargetName="ContentPresenterTranslateTransform"
                                                                             Storyboard.TargetProperty="X"
                                                                             To="{ThemeResource PivotHeaderItemLockedTranslation}"/>
                                                            <DoubleAnimation Duration="0"
                                                                             Storyboard.TargetName="ContentPresenter"
                                                                             Storyboard.TargetProperty="(UIElement.Opacity)"
                                                                             To="0"/>
                                                        </Storyboard>
                                                    </VisualState>

                                                    <VisualState x:Name="Selected">

                                                        <Storyboard>
                                                            <ObjectAnimationUsingKeyFrames Storyboard.TargetName="ContentPresenter" Storyboard.TargetProperty="Foreground">
                                                                <DiscreteObjectKeyFrame KeyTime="0" Value="{ThemeResource PivotHeaderItemForegroundSelectedCustom}"/>
                                                            </ObjectAnimationUsingKeyFrames>
                                                            <ObjectAnimationUsingKeyFrames Storyboard.TargetName="Grid" Storyboard.TargetProperty="Background">
                                                                <DiscreteObjectKeyFrame KeyTime="0" Value="{ThemeResource PivotHeaderItemBackgroundSelected}"/>
                                                            </ObjectAnimationUsingKeyFrames>
                                                        </Storyboard>
                                                    </VisualState>
                                                    <VisualState x:Name="UnselectedPointerOver">
                                                        <VisualState.Setters>
                                                            <Setter Target="SelectedPipe.Visibility" Value="Collapsed"/>
                                                        </VisualState.Setters>

                                                        <Storyboard>
                                                            <ObjectAnimationUsingKeyFrames Storyboard.TargetName="ContentPresenter" Storyboard.TargetProperty="Foreground">
                                                                <DiscreteObjectKeyFrame KeyTime="0" Value="{ThemeResource PivotHeaderItemForegroundUnselectedPointerOverCustom}"/>
                                                            </ObjectAnimationUsingKeyFrames>
                                                            <ObjectAnimationUsingKeyFrames Storyboard.TargetName="Grid" Storyboard.TargetProperty="Background">
                                                                <DiscreteObjectKeyFrame KeyTime="0" Value="{ThemeResource PivotHeaderItemBackgroundUnselectedPointerOver}"/>
                                                            </ObjectAnimationUsingKeyFrames>
                                                        </Storyboard>
                                                    </VisualState>
                                                    <VisualState x:Name="SelectedPointerOver">

                                                        <Storyboard>
                                                            <ObjectAnimationUsingKeyFrames Storyboard.TargetName="ContentPresenter" Storyboard.TargetProperty="Foreground">
                                                                <DiscreteObjectKeyFrame KeyTime="0" Value="{ThemeResource PivotHeaderItemForegroundSelectedPointerOverCustom}"/>
                                                            </ObjectAnimationUsingKeyFrames>
                                                            <ObjectAnimationUsingKeyFrames Storyboard.TargetName="Grid" Storyboard.TargetProperty="Background">
                                                                <DiscreteObjectKeyFrame KeyTime="0" Value="{ThemeResource PivotHeaderItemBackgroundSelectedPointerOver}"/>
                                                            </ObjectAnimationUsingKeyFrames>
                                                            <ObjectAnimationUsingKeyFrames Storyboard.TargetName="SelectedPipe" Storyboard.TargetProperty="Fill">
                                                                <DiscreteObjectKeyFrame KeyTime="0" Value="{ThemeResource AccentFillColorSecondaryBrush}"/>
                                                            </ObjectAnimationUsingKeyFrames>
                                                        </Storyboard>
                                                    </VisualState>
                                                    <VisualState x:Name="UnselectedPressed">
                                                        <VisualState.Setters>
                                                            <Setter Target="SelectedPipe.Visibility" Value="Collapsed"/>
                                                        </VisualState.Setters>

                                                        <Storyboard>
                                                            <ObjectAnimationUsingKeyFrames Storyboard.TargetName="ContentPresenter" Storyboard.TargetProperty="Foreground">
                                                                <DiscreteObjectKeyFrame KeyTime="0" Value="{ThemeResource PivotHeaderItemForegroundUnselectedPressedCustom}"/>
                                                            </ObjectAnimationUsingKeyFrames>
                                                            <ObjectAnimationUsingKeyFrames Storyboard.TargetName="Grid" Storyboard.TargetProperty="Background">
                                                                <DiscreteObjectKeyFrame KeyTime="0" Value="{ThemeResource PivotHeaderItemBackgroundUnselectedPressed}"/>
                                                            </ObjectAnimationUsingKeyFrames>
                                                        </Storyboard>
                                                    </VisualState>
                                                    <VisualState x:Name="SelectedPressed">

                                                        <Storyboard>
                                                            <ObjectAnimationUsingKeyFrames Storyboard.TargetName="ContentPresenter" Storyboard.TargetProperty="Foreground">
                                                                <DiscreteObjectKeyFrame KeyTime="0" Value="{ThemeResource PivotHeaderItemForegroundSelectedPressedCustom}"/>
                                                            </ObjectAnimationUsingKeyFrames>
                                                            <ObjectAnimationUsingKeyFrames Storyboard.TargetName="Grid" Storyboard.TargetProperty="Background">
                                                                <DiscreteObjectKeyFrame KeyTime="0" Value="{ThemeResource PivotHeaderItemBackgroundSelectedPressed}"/>
                                                            </ObjectAnimationUsingKeyFrames>
                                                            <ObjectAnimationUsingKeyFrames Storyboard.TargetName="SelectedPipe" Storyboard.TargetProperty="Fill">
                                                                <DiscreteObjectKeyFrame KeyTime="0" Value="{ThemeResource AccentFillColorTertiaryBrush}"/>
                                                            </ObjectAnimationUsingKeyFrames>
                                                        </Storyboard>
                                                    </VisualState>
                                                </VisualStateGroup>

                                            </VisualStateManager.VisualStateGroups>

                                            <ContentPresenter x:Name="ContentPresenter"
                                                              HorizontalAlignment="{TemplateBinding HorizontalContentAlignment}"
                                                              VerticalAlignment="{TemplateBinding VerticalContentAlignment}"
                                                              FontFamily="{TemplateBinding FontFamily}"
                                                              FontSize="{TemplateBinding FontSize}"
                                                              FontWeight="{TemplateBinding FontWeight}"
                                                              Content="{TemplateBinding Content}"
                                                              ContentTemplate="{TemplateBinding ContentTemplate}"
                                                              OpticalMarginAlignment="TrimSideBearings"/>
                                            <Rectangle x:Name="SelectedPipe"
                                                       Width="16"
                                                       Height="3"
                                                       Margin="0,0,0,2"
                                                       HorizontalAlignment="Center"
                                                       VerticalAlignment="Bottom"
                                                       Fill="{ThemeResource AccentFillColorDefaultBrush}"
                                                       RadiusX="1.5"
                                                       RadiusY="1.5"/>

                                        </Grid>
                                    </ControlTemplate>
                                </Setter.Value>
                            </Setter>
                        </Style>
                    </ResourceDictionary>
                </Pivot.Resources>
                <PivotItem x:Name="HistoryPivotItem"
                           Margin="0,10,0,0"
                           AutomationProperties.AutomationId="HistoryLabel"
                           AutomationProperties.Name="{x:Bind HistoryPivotItemUiaName, Mode=OneWay}">
                    <PivotItem.Header>
                        <TextBlock AccessKey="{utils:ResourceString Name=HistoryLabel/AccessKey}"
                                   AccessKeyInvoked="OnHistoryAccessKeyInvoked"
                                   Text="{utils:ResourceString Name=HistoryLabel/Text}"/>
                    </PivotItem.Header>
                    <Border x:Name="DockHistoryHolder"/>
                </PivotItem>
                <PivotItem x:Name="MemoryPivotItem"
                           Margin="0,10,0,0"
                           AutomationProperties.AutomationId="MemoryLabel"
                           AutomationProperties.Name="{x:Bind MemoryPivotItemUiaName, Mode=OneWay}">
                    <PivotItem.Header>
                        <TextBlock AccessKey="{utils:ResourceString Name=MemoryLabel/AccessKey}"
                                   AccessKeyInvoked="OnMemoryAccessKeyInvoked"
                                   Text="{utils:ResourceString Name=MemoryLabel/Text}"/>
                    </PivotItem.Header>
                    <Border x:Name="DockMemoryHolder"/>
                </PivotItem>
            </Pivot>
        </Border>
    </Grid>
</UserControl>
