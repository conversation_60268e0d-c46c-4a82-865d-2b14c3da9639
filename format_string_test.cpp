#include <windows.h>
#include <iostream>
#include <string>
#include <memory>
#include <iomanip>
#include <vector>
#include <io.h>
#include <fcntl.h>

using namespace std;

// Simulate the vulnerable FormatMessage function
class FormatStringTester {
public:
    // Recreate the vulnerable function logic
    static wstring TestFormatMessage(const wstring& userInput) {
        const UINT32 length = 1024;
        auto spBuffer = make_unique<wchar_t[]>(length);
        
        // This is the vulnerable call - user input used directly as format string
        DWORD fmtReturnVal = FormatMessageW(
            FORMAT_MESSAGE_FROM_STRING,     // dwFlags
            userInput.c_str(),              // lpSource - USER CONTROLLED!
            0,                              // dwMessageId
            0,                              // dwLanguageId
            spBuffer.get(),                 // lpBuffer
            length,                         // nSize
            nullptr                         // Arguments - this is where the vulnerability lies
        );
        
        if (fmtReturnVal != 0) {
            return wstring(spBuffer.get());
        } else {
            DWORD error = GetLastError();
            wcout << L"FormatMessage failed with error: " << error << endl;
            return L"";
        }
    }
    
    // Test with arguments to show how format strings work
    static wstring TestFormatMessageWithArgs(const wstring& userInput, const wstring& arg1, const wstring& arg2) {
        const UINT32 length = 1024;
        auto spBuffer = make_unique<wchar_t[]>(length);
        
        // Create argument array
        const wchar_t* args[] = { arg1.c_str(), arg2.c_str() };
        
        DWORD fmtReturnVal = FormatMessageW(
            FORMAT_MESSAGE_FROM_STRING,
            userInput.c_str(),
            0,
            0,
            spBuffer.get(),
            length,
            (va_list*)args
        );
        
        if (fmtReturnVal != 0) {
            return wstring(spBuffer.get());
        } else {
            DWORD error = GetLastError();
            wcout << L"FormatMessage failed with error: " << error << endl;
            return L"";
        }
    }
};

void test_basic_format_strings() {
    wcout << L"=== Testing Basic Format String Vulnerabilities ===" << endl;
    
    // Test cases that demonstrate format string vulnerabilities
    vector<wstring> malicious_inputs = {
        L"Normal text",                    // Safe baseline
        L"Text with %1",                   // Basic format specifier
        L"Multiple %1 and %2",             // Multiple format specifiers
        L"%1!s! string format",            // String format
        L"%1!d! decimal format",           // Decimal format
        L"%1!x! hex format",               // Hex format
        L"%1!*s! width specifier",         // Width specifier (dangerous)
        L"%1!*.*s! precision specifier",   // Precision specifier (very dangerous)
        L"%99!s! out of bounds",           // Out of bounds argument
        L"%%1 escaped percent",            // Escaped percent
        L"%1!n! write specifier",          // Write to memory (if supported)
        L"%1!p! pointer format",           // Pointer format
    };
    
    for (const auto& input : malicious_inputs) {
        wcout << L"\nTesting input: " << input << endl;
        
        try {
            wstring result = FormatStringTester::TestFormatMessage(input);
            wcout << L"Result: " << result << endl;
            
            if (result.empty()) {
                wcout << L"🚨 POTENTIAL VULNERABILITY: FormatMessage failed - could indicate exploitation attempt" << endl;
            }
        } catch (...) {
            wcout << L"🚨 EXCEPTION: Crash detected during format string processing!" << endl;
        }
    }
}

void test_information_disclosure() {
    wcout << L"\n=== Testing Information Disclosure ===" << endl;
    
    // These format strings attempt to read from stack/memory
    vector<wstring> disclosure_tests = {
        L"%1!s!",                          // Read string from argument 1
        L"%2!s!",                          // Read string from argument 2  
        L"%3!s!",                          // Read string from argument 3 (likely uninitialized)
        L"%10!s!",                         // Read from far down the stack
        L"%1!p!",                          // Read pointer value
        L"%1!x! %2!x! %3!x!",             // Read multiple hex values
        L"%1!016x!",                       // Read 64-bit hex value
    };
    
    for (const auto& input : disclosure_tests) {
        wcout << L"\nTesting disclosure: " << input << endl;
        
        try {
            // Test with some known arguments
            wstring result = FormatStringTester::TestFormatMessageWithArgs(
                input, 
                L"KNOWN_ARG1", 
                L"KNOWN_ARG2"
            );
            
            wcout << L"Result: " << result << endl;
            
            // Check if we got unexpected data (potential memory disclosure)
            if (result.find(L"KNOWN_ARG") == wstring::npos && !result.empty()) {
                wcout << L"🚨 POTENTIAL MEMORY DISCLOSURE: Got unexpected data!" << endl;
            }
        } catch (...) {
            wcout << L"🚨 EXCEPTION during disclosure test!" << endl;
        }
    }
}

void test_memory_corruption() {
    wcout << L"\n=== Testing Memory Corruption Attempts ===" << endl;
    
    // These tests attempt to cause memory corruption
    vector<wstring> corruption_tests = {
        L"%1!*s!",                         // Width specifier without width argument
        L"%1!*.*s!",                       // Precision specifier without arguments
        L"%1!999999s!",                    // Extremely large width
        L"%1!-999999s!",                   // Negative width
        L"%999!s!",                        // Very high argument number
        L"%0!s!",                          // Zero argument number
        L"%1!%1!s!!",                      // Nested format specifiers
    };
    
    for (const auto& input : corruption_tests) {
        wcout << L"\nTesting corruption: " << input << endl;
        
        try {
            wstring result = FormatStringTester::TestFormatMessage(input);
            wcout << L"Result length: " << result.length() << endl;
            
            if (result.length() > 2000) {
                wcout << L"🚨 POTENTIAL BUFFER ISSUE: Extremely long result!" << endl;
            }
        } catch (...) {
            wcout << L"🚨 EXCEPTION: Potential memory corruption detected!" << endl;
        }
    }
}

void test_denial_of_service() {
    wcout << L"\n=== Testing Denial of Service ===" << endl;
    
    // These tests attempt to cause DoS through resource exhaustion
    vector<wstring> dos_tests = {
        L"%1!999999999s!",                 // Extremely large format width
        L"%1!*s!" + wstring(1000, L'A'),   // Long string with width specifier
        wstring(500, L'%') + L"1!s!",      // Many format specifiers
        L"%1!s!" + wstring(2000, L'X'),    // Long string after format
    };
    
    for (const auto& input : dos_tests) {
        wcout << L"\nTesting DoS (input length: " << input.length() << L")" << endl;
        
        auto start_time = GetTickCount64();
        
        try {
            wstring result = FormatStringTester::TestFormatMessage(input);
            
            auto end_time = GetTickCount64();
            auto duration = end_time - start_time;
            
            wcout << L"Processing time: " << duration << L"ms" << endl;
            wcout << L"Result length: " << result.length() << endl;
            
            if (duration > 5000) {  // More than 5 seconds
                wcout << L"🚨 POTENTIAL DoS: Extremely slow processing!" << endl;
            }
            
            if (result.length() > 10000) {
                wcout << L"🚨 POTENTIAL DoS: Extremely large output!" << endl;
            }
        } catch (...) {
            wcout << L"🚨 EXCEPTION during DoS test!" << endl;
        }
    }
}

int main() {
    wcout << L"Windows Calculator Format String Vulnerability Test" << endl;
    wcout << L"=" << wstring(55, L'=') << endl;
    
    // Set console to handle Unicode
    _setmode(_fileno(stdout), _O_U16TEXT);
    
    try {
        test_basic_format_strings();
        test_information_disclosure();
        test_memory_corruption();
        test_denial_of_service();
        
        wcout << L"\n=== Test Summary ===" << endl;
        wcout << L"Format string vulnerability testing completed." << endl;
        wcout << L"Check output above for any detected vulnerabilities." << endl;
        
    } catch (const exception& e) {
        cout << "Fatal error: " << e.what() << endl;
        return 1;
    }
    
    return 0;
}
