<UserControl x:Class="CalculatorApp.CalculatorStandardOperators"
             xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
             xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
             xmlns:common="using:CalculatorApp.Common"
             xmlns:controls="using:CalculatorApp.Controls"
             xmlns:d="http://schemas.microsoft.com/expression/blend/2008"
             xmlns:local="using:CalculatorApp"
             xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006"
             xmlns:triggers="using:CalculatorApp.Views.StateTriggers"
             xmlns:utils="using:CalculatorApp.Utils"
             x:Name="ControlRoot"
             XYFocusKeyboardNavigation="Enabled"
             mc:Ignorable="d">

    <Grid x:Name="Root">
        <Grid.RowDefinitions>
            <RowDefinition x:Name="R0" Height="1*"/>
            <RowDefinition x:Name="R1" Height="1*"/>
            <RowDefinition x:Name="R2" Height="1*"/>
            <RowDefinition x:Name="R3" Height="1*"/>
            <RowDefinition x:Name="R4" Height="1*"/>
            <RowDefinition x:Name="R5" Height="1*"/>
        </Grid.RowDefinitions>
        <Grid.ColumnDefinitions>
            <ColumnDefinition x:Name="GutterLeft" Width="0"/>
            <ColumnDefinition x:Name="C0" Width="0"/>
            <ColumnDefinition x:Name="C1" Width="1*"/>
            <ColumnDefinition x:Name="C2" Width="1*"/>
            <ColumnDefinition x:Name="C3" Width="1*"/>
            <ColumnDefinition x:Name="C4" Width="1*"/>
            <ColumnDefinition x:Name="GutterRight" Width="0"/>
        </Grid.ColumnDefinitions>

        <VisualStateManager.VisualStateGroups>
            <VisualStateGroup x:Name="ErrorVisualStates">
                <VisualState x:Name="NoErrorLayout"/>
                <VisualState x:Name="ErrorLayout">
                    <VisualState.Setters>
                        <Setter Target="PercentButton.IsEnabled" Value="False"/>
                        <Setter Target="SquareRootButton.IsEnabled" Value="False"/>
                        <Setter Target="XPower2Button.IsEnabled" Value="False"/>
                        <Setter Target="InvertButton.IsEnabled" Value="False"/>
                        <Setter Target="DivideButton.IsEnabled" Value="False"/>
                        <Setter Target="MultiplyButton.IsEnabled" Value="False"/>
                        <Setter Target="MinusButton.IsEnabled" Value="False"/>
                        <Setter Target="PlusButton.IsEnabled" Value="False"/>
                        <Setter Target="NegateButton.IsEnabled" Value="False"/>
                    </VisualState.Setters>
                </VisualState>
            </VisualStateGroup>
            <VisualStateGroup>
                <VisualState x:Name="Large">
                    <VisualState.StateTriggers>
                        <triggers:ControlSizeTrigger MinWidth="780"
                                                     MinHeight="814"
                                                     Source="{x:Bind Root}"/>
                    </VisualState.StateTriggers>
                    <VisualState.Setters>
                        <Setter Target="PercentButton.FontSize" Value="{StaticResource CalcButtonCaptionSize}"/>
                        <Setter Target="SquareRootButton.FontSize" Value="{StaticResource CalcButtonCaptionSize}"/>
                        <Setter Target="XPower2Button.FontSize" Value="{StaticResource CalcButtonCaptionSize}"/>
                        <Setter Target="InvertButton.FontSize" Value="{StaticResource CalcButtonCaptionSize}"/>
                        <Setter Target="NegateButton.FontSize" Value="{StaticResource CalcButtonCaptionSize}"/>
                        <Setter Target="ClearEntryButton.FontSize" Value="{StaticResource CalcButtonCaptionSize}"/>
                        <Setter Target="ClearButton.FontSize" Value="{StaticResource CalcButtonCaptionSize}"/>
                        <Setter Target="BackSpaceButton.FontSize" Value="{StaticResource CalcButtonCaptionSize}"/>
                        <Setter Target="DivideButton.FontSize" Value="{StaticResource CalcButtonCaptionSize}"/>
                        <Setter Target="MultiplyButton.FontSize" Value="{StaticResource CalcButtonCaptionSize}"/>
                        <Setter Target="MinusButton.FontSize" Value="{StaticResource CalcButtonCaptionSize}"/>
                        <Setter Target="PlusButton.FontSize" Value="{StaticResource CalcButtonCaptionSize}"/>
                        <Setter Target="EqualButton.FontSize" Value="{StaticResource CalcButtonCaptionSize}"/>

                        <Setter Target="NumberPad.ButtonStyle" Value="{StaticResource NumericButtonStyle38}"/>
                    </VisualState.Setters>
                </VisualState>
                <VisualState x:Name="Medium">
                    <VisualState.StateTriggers>
                        <triggers:ControlSizeTrigger MinWidth="468"
                                                     MinHeight="500"
                                                     Source="{x:Bind Root}"/>
                    </VisualState.StateTriggers>
                    <VisualState.Setters>
                        <Setter Target="PercentButton.FontSize" Value="{StaticResource CalcStandardOperatorCaptionSize}"/>
                        <Setter Target="SquareRootButton.FontSize" Value="{StaticResource CalcStandardOperatorCaptionSize}"/>
                        <Setter Target="XPower2Button.FontSize" Value="{StaticResource CalcStandardOperatorCaptionSize}"/>
                        <Setter Target="InvertButton.FontSize" Value="{StaticResource CalcStandardOperatorCaptionSize}"/>
                        <Setter Target="NegateButton.FontSize" Value="{StaticResource CalcStandardOperatorCaptionSizeLarge}"/>
                        <Setter Target="ClearEntryButton.FontSize" Value="{StaticResource CalcStandardOperatorCaptionSize}"/>
                        <Setter Target="ClearButton.FontSize" Value="{StaticResource CalcStandardOperatorCaptionSize}"/>
                        <Setter Target="BackSpaceButton.FontSize" Value="{StaticResource CalcStandardOperatorCaptionSize}"/>
                        <Setter Target="DivideButton.FontSize" Value="{StaticResource CalcStandardOperatorCaptionSize}"/>
                        <Setter Target="MultiplyButton.FontSize" Value="{StaticResource CalcStandardOperatorCaptionSize}"/>
                        <Setter Target="MinusButton.FontSize" Value="{StaticResource CalcStandardOperatorCaptionSize}"/>
                        <Setter Target="PlusButton.FontSize" Value="{StaticResource CalcStandardOperatorCaptionSize}"/>
                        <Setter Target="EqualButton.FontSize" Value="{StaticResource CalcStandardOperatorCaptionSize}"/>

                        <Setter Target="NumberPad.ButtonStyle" Value="{StaticResource NumericButtonStyle24}"/>
                    </VisualState.Setters>
                </VisualState>
                <VisualState x:Name="Small">
                    <VisualState.StateTriggers>
                        <AdaptiveTrigger MinWindowHeight="260" MinWindowWidth="0"/>
                    </VisualState.StateTriggers>
                    <VisualState.Setters>
                        <Setter Target="PercentButton.FontSize" Value="{StaticResource CalcOperatorCaptionSize}"/>
                        <Setter Target="SquareRootButton.FontSize" Value="{StaticResource CalcOperatorCaptionSize}"/>
                        <Setter Target="XPower2Button.FontSize" Value="{StaticResource CalcOperatorCaptionSize}"/>
                        <Setter Target="InvertButton.FontSize" Value="{StaticResource CalcOperatorCaptionSize}"/>
                        <Setter Target="NegateButton.FontSize" Value="{StaticResource CalcOperatorCaptionSize}"/>
                        <Setter Target="ClearEntryButton.FontSize" Value="{StaticResource CalcOperatorCaptionSize}"/>
                        <Setter Target="ClearButton.FontSize" Value="{StaticResource CalcOperatorCaptionSize}"/>
                        <Setter Target="BackSpaceButton.FontSize" Value="{StaticResource CalcOperatorCaptionSize}"/>
                        <Setter Target="DivideButton.FontSize" Value="{StaticResource CalcOperatorCaptionSize}"/>
                        <Setter Target="MultiplyButton.FontSize" Value="{StaticResource CalcOperatorCaptionSize}"/>
                        <Setter Target="MinusButton.FontSize" Value="{StaticResource CalcOperatorCaptionSize}"/>
                        <Setter Target="PlusButton.FontSize" Value="{StaticResource CalcOperatorCaptionSize}"/>
                        <Setter Target="EqualButton.FontSize" Value="{StaticResource CalcOperatorCaptionSize}"/>

                        <Setter Target="NumberPad.ButtonStyle" Value="{StaticResource NumericButtonStyle18}"/>
                    </VisualState.Setters>
                </VisualState>
                <VisualState x:Name="Tiny">
                    <VisualState.StateTriggers>
                        <AdaptiveTrigger MinWindowHeight="0" MinWindowWidth="0"/>
                    </VisualState.StateTriggers>
                    <VisualState.Setters>
                        <Setter Target="PercentButton.FontSize" Value="{StaticResource CalcStandardOperatorCaptionSizeTiny}"/>
                        <Setter Target="SquareRootButton.FontSize" Value="{StaticResource CalcStandardOperatorCaptionSizeTiny}"/>
                        <Setter Target="XPower2Button.FontSize" Value="{StaticResource CalcStandardOperatorCaptionSizeTiny}"/>
                        <Setter Target="InvertButton.FontSize" Value="{StaticResource CalcStandardOperatorCaptionSizeTiny}"/>
                        <Setter Target="NegateButton.FontSize" Value="{StaticResource CalcStandardOperatorCaptionSizeTiny}"/>
                        <Setter Target="ClearEntryButton.FontSize" Value="{StaticResource CalcStandardOperatorCaptionSizeTiny}"/>
                        <Setter Target="ClearButton.FontSize" Value="{StaticResource CalcStandardOperatorCaptionSizeTiny}"/>
                        <Setter Target="BackSpaceButton.FontSize" Value="{StaticResource CalcStandardOperatorCaptionSizeTiny}"/>
                        <Setter Target="DivideButton.FontSize" Value="{StaticResource CalcStandardOperatorCaptionSizeTiny}"/>
                        <Setter Target="MultiplyButton.FontSize" Value="{StaticResource CalcStandardOperatorCaptionSizeTiny}"/>
                        <Setter Target="MinusButton.FontSize" Value="{StaticResource CalcStandardOperatorCaptionSizeTiny}"/>
                        <Setter Target="PlusButton.FontSize" Value="{StaticResource CalcStandardOperatorCaptionSizeTiny}"/>
                        <Setter Target="EqualButton.FontSize" Value="{StaticResource CalcStandardOperatorCaptionSizeTiny}"/>

                        <Setter Target="NumberPad.ButtonStyle" Value="{StaticResource NumericButtonStyle12}"/>
                    </VisualState.Setters>
                </VisualState>
            </VisualStateGroup>
            <VisualStateGroup>
                <VisualState x:Name="ShowStandardFunctions">
                    <VisualState.StateTriggers>
                        <AdaptiveTrigger MinWindowHeight="394" MinWindowWidth="0"/>
                    </VisualState.StateTriggers>
                    <VisualState.Setters>
                        <Setter Target="R1.Height" Value="1*"/>
                        <Setter Target="StandardFunctions.Visibility" Value="Visible"/>
                    </VisualState.Setters>
                </VisualState>
                <VisualState x:Name="HideStandardFunctions">
                    <VisualState.StateTriggers>
                        <AdaptiveTrigger MinWindowHeight="0" MinWindowWidth="0"/>
                    </VisualState.StateTriggers>
                    <VisualState.Setters>
                        <Setter Target="R1.Height" Value="0*"/>
                        <Setter Target="StandardFunctions.Visibility" Value="Collapsed"/>
                        <Setter Target="PercentButton.Visibility" Value="Collapsed"/>
                        <Setter Target="PercentColumn.Width" Value="0"/>
                        <Setter Target="DisplayControls.Column" Value="1"/>
                        <Setter Target="DisplayControls.ColumnSpan" Value="4"/>
                        <Setter Target="StandardOperators.Row" Value="0"/>
                        <Setter Target="StandardOperators.RowSpan" Value="6"/>
                    </VisualState.Setters>
                </VisualState>
            </VisualStateGroup>
        </VisualStateManager.VisualStateGroups>

        <Grid x:Name="DisplayControls"
              Grid.Row="0"
              Grid.Column="2"
              Grid.ColumnSpan="4"
              AutomationProperties.HeadingLevel="Level1"
              AutomationProperties.Name="{utils:ResourceString Name=DisplayControls/[using:Windows.UI.Xaml.Automation]AutomationProperties/Name}">
            <Grid.ColumnDefinitions>
                <ColumnDefinition x:Name="PercentColumn"/>
                <ColumnDefinition/>
                <ColumnDefinition/>
                <ColumnDefinition/>
            </Grid.ColumnDefinitions>
            <controls:CalculatorButton x:Name="PercentButton"
                                       Grid.Column="0"
                                       Style="{StaticResource SymbolOperatorButtonStyle}"
                                       common:KeyboardShortcutManager.Character="{utils:ResourceString Name=percentButton/[using:CalculatorApp.Common]KeyboardShortcutManager/Character}"
                                       AutomationProperties.AutomationId="percentButton"
                                       AutomationProperties.Name="{utils:ResourceString Name=percentButton/[using:Windows.UI.Xaml.Automation]AutomationProperties/Name}"
                                       ButtonId="Percent"
                                       Content="&#xE94C;"/>
            <controls:CalculatorButton x:Name="ClearEntryButton"
                                       Grid.Column="1"
                                       Style="{StaticResource OperatorButtonStyle}"
                                       FontSize="16"
                                       common:KeyboardShortcutManager.VirtualKey="{utils:ResourceVirtualKey Name=clearEntryButton/[using:CalculatorApp.Common]KeyboardShortcutManager/VirtualKey}"
                                       AutomationProperties.AutomationId="clearEntryButton"
                                       AutomationProperties.Name="{utils:ResourceString Name=clearEntryButton/[using:Windows.UI.Xaml.Automation]AutomationProperties/Name}"
                                       ButtonId="ClearEntry"
                                       Content="CE"/>
            <controls:CalculatorButton x:Name="ClearButton"
                                       Grid.Column="2"
                                       Style="{StaticResource OperatorButtonStyle}"
                                       FontSize="16"
                                       common:KeyboardShortcutManager.VirtualKey="{utils:ResourceVirtualKey Name=clearButton/[using:CalculatorApp.Common]KeyboardShortcutManager/VirtualKey}"
                                       AutomationProperties.AutomationId="clearButton"
                                       AutomationProperties.Name="{utils:ResourceString Name=clearButton/[using:Windows.UI.Xaml.Automation]AutomationProperties/Name}"
                                       ButtonId="Clear"
                                       Content="C"/>
            <controls:CalculatorButton x:Name="BackSpaceButton"
                                       Grid.Column="3"
                                       Style="{StaticResource SymbolOperatorButtonStyle}"
                                       FontSize="16"
                                       common:KeyboardShortcutManager.VirtualKey="{utils:ResourceVirtualKey Name=backSpaceButton/[using:CalculatorApp.Common]KeyboardShortcutManager/VirtualKey}"
                                       AutomationProperties.AutomationId="backSpaceButton"
                                       AutomationProperties.Name="{utils:ResourceString Name=backSpaceButton/[using:Windows.UI.Xaml.Automation]AutomationProperties/Name}"
                                       ButtonId="Backspace"
                                       Content="&#xE94F;"/>
        </Grid>

        <Grid x:Name="StandardFunctions"
              Grid.Row="1"
              Grid.Column="2"
              Grid.ColumnSpan="4"
              AutomationProperties.HeadingLevel="Level1"
              AutomationProperties.Name="{utils:ResourceString Name=StandardFunctions/[using:Windows.UI.Xaml.Automation]AutomationProperties/Name}">
            <Grid.RowDefinitions>
                <RowDefinition Height="1*"/>
                <RowDefinition x:Name="FnR1" Height="0"/>
                <RowDefinition x:Name="FnR2" Height="0"/>
                <RowDefinition x:Name="FnR3" Height="0"/>
                <RowDefinition x:Name="FnR4" Height="0"/>
            </Grid.RowDefinitions>
            <Grid.ColumnDefinitions>
                <ColumnDefinition Width="1*"/>
                <ColumnDefinition x:Name="FnC1" Width="1*"/>
                <ColumnDefinition x:Name="FnC2" Width="1*"/>
                <ColumnDefinition x:Name="FnC3" Width="1*"/>
            </Grid.ColumnDefinitions>
            <controls:CalculatorButton x:Name="InvertButton"
                                       Style="{StaticResource SymbolOperatorButtonStyle}"
                                       FontSize="18"
                                       common:KeyboardShortcutManager.VirtualKey="{utils:ResourceVirtualKey Name=invertButton/[using:CalculatorApp.Common]KeyboardShortcutManager/VirtualKey}"
                                       AutomationProperties.AutomationId="invertButton"
                                       AutomationProperties.Name="{utils:ResourceString Name=invertButton/[using:Windows.UI.Xaml.Automation]AutomationProperties/Name}"
                                       ButtonId="Invert"
                                       Content="&#xf7c9;"/>
            <controls:CalculatorButton x:Name="XPower2Button"
                                       Grid.Column="1"
                                       Style="{StaticResource SymbolOperatorButtonStyle}"
                                       FontSize="18"
                                       common:KeyboardShortcutManager.VirtualKey="{utils:ResourceVirtualKey Name=xpower2Button/[using:CalculatorApp.Common]KeyboardShortcutManager/VirtualKey}"
                                       AutomationProperties.AutomationId="xpower2Button"
                                       AutomationProperties.Name="{utils:ResourceString Name=xpower2Button/[using:Windows.UI.Xaml.Automation]AutomationProperties/Name}"
                                       ButtonId="XPower2"
                                       Content="&#xf7c8;"/>
            <controls:CalculatorButton x:Name="SquareRootButton"
                                       Grid.Column="2"
                                       Style="{StaticResource SymbolOperatorButtonStyle}"
                                       common:KeyboardShortcutManager.Character="{utils:ResourceString Name=squareRootButton/[using:CalculatorApp.Common]KeyboardShortcutManager/Character}"
                                       AutomationProperties.AutomationId="squareRootButton"
                                       AutomationProperties.Name="{utils:ResourceString Name=squareRootButton/[using:Windows.UI.Xaml.Automation]AutomationProperties/Name}"
                                       ButtonId="Sqrt"
                                       Content="&#xF899;"/>
        </Grid>

        <Grid x:Name="StandardOperators"
              Grid.Row="1"
              Grid.RowSpan="5"
              Grid.Column="5"
              AutomationProperties.HeadingLevel="Level1"
              AutomationProperties.Name="{utils:ResourceString Name=StandardOperators/[using:Windows.UI.Xaml.Automation]AutomationProperties/Name}">
            <Grid.RowDefinitions>
                <RowDefinition/>
                <RowDefinition/>
                <RowDefinition/>
                <RowDefinition/>
                <RowDefinition/>
            </Grid.RowDefinitions>
            <controls:CalculatorButton x:Name="DivideButton"
                                       Grid.Row="0"
                                       Style="{StaticResource SymbolOperatorButtonStyle}"
                                       common:KeyboardShortcutManager.Character="{utils:ResourceString Name=divideButton/[using:CalculatorApp.Common]KeyboardShortcutManager/Character}"
                                       AuditoryFeedback="{utils:ResourceString Name=divideButton/[using:CalculatorApp.Controls]CalculatorButton/AuditoryFeedback}"
                                       AutomationProperties.AutomationId="divideButton"
                                       AutomationProperties.Name="{utils:ResourceString Name=divideButton/[using:Windows.UI.Xaml.Automation]AutomationProperties/Name}"
                                       ButtonId="Divide"
                                       Content="&#xE94A;"/>
            <controls:CalculatorButton x:Name="MultiplyButton"
                                       Grid.Row="1"
                                       Style="{StaticResource SymbolOperatorButtonStyle}"
                                       common:KeyboardShortcutManager.Character="{utils:ResourceString Name=multiplyButton/[using:CalculatorApp.Common]KeyboardShortcutManager/Character}"
                                       AuditoryFeedback="{utils:ResourceString Name=multiplyButton/[using:CalculatorApp.Controls]CalculatorButton/AuditoryFeedback}"
                                       AutomationProperties.AutomationId="multiplyButton"
                                       AutomationProperties.Name="{utils:ResourceString Name=multiplyButton/[using:Windows.UI.Xaml.Automation]AutomationProperties/Name}"
                                       ButtonId="Multiply"
                                       Content="&#xE947;"/>
            <controls:CalculatorButton x:Name="MinusButton"
                                       Grid.Row="2"
                                       Style="{StaticResource SymbolOperatorButtonStyle}"
                                       common:KeyboardShortcutManager.Character="{utils:ResourceString Name=minusButton/[using:CalculatorApp.Common]KeyboardShortcutManager/Character}"
                                       AuditoryFeedback="{utils:ResourceString Name=minusButton/[using:CalculatorApp.Controls]CalculatorButton/AuditoryFeedback}"
                                       AutomationProperties.AutomationId="minusButton"
                                       AutomationProperties.Name="{utils:ResourceString Name=minusButton/[using:Windows.UI.Xaml.Automation]AutomationProperties/Name}"
                                       ButtonId="Subtract"
                                       Content="&#xE949;"/>
            <controls:CalculatorButton x:Name="PlusButton"
                                       Grid.Row="3"
                                       Style="{StaticResource SymbolOperatorButtonStyle}"
                                       common:KeyboardShortcutManager.Character="{utils:ResourceString Name=plusButton/[using:CalculatorApp.Common]KeyboardShortcutManager/Character}"
                                       AuditoryFeedback="{utils:ResourceString Name=plusButton/[using:CalculatorApp.Controls]CalculatorButton/AuditoryFeedback}"
                                       AutomationProperties.AutomationId="plusButton"
                                       AutomationProperties.Name="{utils:ResourceString Name=plusButton/[using:Windows.UI.Xaml.Automation]AutomationProperties/Name}"
                                       ButtonId="Add"
                                       Content="&#xE948;"/>
            <controls:CalculatorButton x:Name="EqualButton"
                                       Grid.Row="4"
                                       Style="{StaticResource AccentEmphasizedCalcButtonStyle}"
                                       common:KeyboardShortcutManager.Character="{utils:ResourceString Name=equalButton/[using:CalculatorApp.Common]KeyboardShortcutManager/Character}"
                                       common:KeyboardShortcutManager.VirtualKey="{utils:ResourceVirtualKey Name=equalButton/[using:CalculatorApp.Common]KeyboardShortcutManager/VirtualKey}"
                                       AutomationProperties.AutomationId="equalButton"
                                       AutomationProperties.Name="{utils:ResourceString Name=equalButton/[using:Windows.UI.Xaml.Automation]AutomationProperties/Name}"
                                       ButtonId="Equals"
                                       Content="&#xE94E;"/>
        </Grid>

        <local:NumberPad x:Name="NumberPad"
                         Grid.Row="2"
                         Grid.RowSpan="4"
                         Grid.Column="2"
                         Grid.ColumnSpan="3"
                         AutomationProperties.HeadingLevel="Level1"
                         AutomationProperties.Name="{utils:ResourceString Name=NumberPad/[using:Windows.UI.Xaml.Automation]AutomationProperties/Name}"
                         ButtonStyle="{StaticResource NumericButtonStyle24}"/>

        <controls:CalculatorButton x:Name="NegateButton"
                                   Grid.Row="5"
                                   Grid.Column="2"
                                   Style="{StaticResource SymbolOperatorKeypadButtonStyle}"
                                   FontWeight="Normal"
                                   common:KeyboardShortcutManager.VirtualKey="{utils:ResourceVirtualKey Name=negateButton/[using:CalculatorApp.Common]KeyboardShortcutManager/VirtualKey}"
                                   AutomationProperties.AutomationId="negateButton"
                                   AutomationProperties.Name="{utils:ResourceString Name=negateButton/[using:Windows.UI.Xaml.Automation]AutomationProperties/Name}"
                                   ButtonId="Negate"
                                   Content="&#xF898;"/>
    </Grid>
</UserControl>
