# This template contains jobs to run unit tests.

parameters:
  platform: ''
  runsettingsFileName: ''

jobs:
- job: UnitTests${{ parameters.platform }}
  displayName: UnitTests ${{ parameters.platform }}
  dependsOn: Build${{ parameters.platform }}
  condition: succeeded()
  variables:
    skipComponentGovernanceDetection: true
    UnitTestsDir: $(Pipeline.Workspace)\drop-${{ parameters.platform }}\CalculatorUnitTests\AppPackages\CalculatorUnitTests_Test
  steps:
  - checkout: self
    fetchDepth: 1
  
  - download: current
    displayName: Download CalculatorUnitTests
    artifact: drop-${{ parameters.platform }}
    patterns: '**/CalculatorUnitTests_Test/**'

  - powershell: |
      $(Build.SourcesDirectory)/build/scripts/SignTestApp.ps1 -AppToSign '$(UnitTestsDir)\CalculatorUnitTests.msix'
    displayName: Sign unit tests

  - task: VSTest@2
    displayName: Run CalculatorUnitTests
    inputs:
      testAssemblyVer2: $(UnitTestsDir)\CalculatorUnitTests.msix
      otherConsoleOptions: /Platform:${{ parameters.platform }}
