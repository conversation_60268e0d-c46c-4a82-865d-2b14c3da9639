<UserControl x:Class="CalculatorApp.CalculatorProgrammerDisplayPanel"
             xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
             xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
             xmlns:common="using:CalculatorApp.Common"
             xmlns:controls="using:CalculatorApp.Controls"
             xmlns:converters="using:CalculatorApp.Converters"
             xmlns:d="http://schemas.microsoft.com/expression/blend/2008"
             xmlns:local="using:CalculatorApp"
             xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006"
             xmlns:utils="using:CalculatorApp.Utils"
             Margin="3,0,0,0"
             d:DesignHeight="300"
             d:DesignWidth="400"
             mc:Ignorable="d">

    <UserControl.Resources>
        <converters:BooleanNegationConverter x:Key="BooleanNegationConverter"/>
    </UserControl.Resources>

    <Grid x:Name="ProgrammerDisplay">
        <Grid.ColumnDefinitions>
            <ColumnDefinition Width="2*" MaxWidth="160"/>
            <ColumnDefinition Width="0.01*"/>
            <ColumnDefinition Width="1*" MaxWidth="80"/>
            <ColumnDefinition Width="1*" MaxWidth="80"/>
            <ColumnDefinition Width="1*" MaxWidth="80"/>
            <ColumnDefinition Width="1*" MaxWidth="80"/>
        </Grid.ColumnDefinitions>

        <VisualStateManager.VisualStateGroups>
            <VisualStateGroup x:Name="Layouts">
                <VisualState x:Name="MinSizeLayout">
                    <VisualState.StateTriggers>
                        <AdaptiveTrigger MinWindowHeight="{StaticResource AppMinWindowHeight}" MinWindowWidth="{StaticResource AppMinWindowWidth}"/>
                    </VisualState.StateTriggers>
                </VisualState>
                <VisualState x:Name="DefaultLayout">
                    <VisualState.StateTriggers>
                        <AdaptiveTrigger MinWindowHeight="0" MinWindowWidth="0"/>
                    </VisualState.StateTriggers>
                </VisualState>
            </VisualStateGroup>
            <VisualStateGroup x:Name="ErrorVisualStates">
                <VisualState x:Name="NoErrorLayout"/>
                <VisualState x:Name="ErrorLayout">
                    <VisualState.Setters>
                        <Setter Target="QwordButton.IsEnabled" Value="False"/>
                        <Setter Target="DwordButton.IsEnabled" Value="False"/>
                        <Setter Target="WordButton.IsEnabled" Value="False"/>
                        <Setter Target="ByteButton.IsEnabled" Value="False"/>
                    </VisualState.Setters>
                </VisualState>
            </VisualStateGroup>
        </VisualStateManager.VisualStateGroups>
        <Grid Grid.Column="0"
              AutomationProperties.HeadingLevel="Level1"
              AutomationProperties.Name="{utils:ResourceString Name=InputModeSelectionGroup/[using:Windows.UI.Xaml.Automation]AutomationProperties/Name}">
            <Grid.ColumnDefinitions>
                <ColumnDefinition Width="1*" MaxWidth="80"/>
                <ColumnDefinition Width="1*" MaxWidth="80"/>
            </Grid.ColumnDefinitions>

            <RadioButton x:Name="FullKeypad"
                         Style="{StaticResource ConditionalProgKeypadRadioButtonStyle}"
                         AutomationProperties.AutomationId="fullKeypad"
                         AutomationProperties.Name="{utils:ResourceString Name=fullKeypad/[using:Windows.UI.Xaml.Automation]AutomationProperties/Name}"
                         Content="&#xe75f;"
                         IsChecked="{x:Bind Model.IsBitFlipChecked, Converter={StaticResource BooleanNegationConverter}, Mode=TwoWay}"
                         ToolTipService.ToolTip="{utils:ResourceString Name=fullKeypad/[using:Windows.UI.Xaml.Controls]ToolTipService/ToolTip}"/>
            <RadioButton x:Name="BitFlip"
                         Grid.Column="1"
                         Style="{StaticResource ConditionalProgKeypadRadioButtonStyle}"
                         AutomationProperties.AutomationId="bitFlip"
                         AutomationProperties.Name="{utils:ResourceString Name=bitFlip/[using:Windows.UI.Xaml.Automation]AutomationProperties/Name}"
                         Content="&#xf7d0;"
                         IsChecked="{x:Bind Model.IsBitFlipChecked, Mode=TwoWay}"
                         ToolTipService.ToolTip="{utils:ResourceString Name=bitFlip/[using:Windows.UI.Xaml.Controls]ToolTipService/ToolTip}"/>
        </Grid>

        <Button x:Name="QwordButton"
                Grid.Column="0"
                Grid.ColumnSpan="7"
                Style="{StaticResource ProgWordSizeButtonStyle}"
                common:KeyboardShortcutManager.VirtualKey="{utils:ResourceVirtualKey Name=qwordButton/[using:CalculatorApp.Common]KeyboardShortcutManager/VirtualKey}"
                AutomationProperties.AutomationId="qwordButton"
                AutomationProperties.Name="{utils:ResourceString Name=qwordButton/[using:Windows.UI.Xaml.Automation]AutomationProperties/Name}"
                Command="{x:Bind BitLengthButtonPressed, Mode=OneTime}"
                CommandParameter="0"
                Content="QWORD"/>
        <Button x:Name="DwordButton"
                Grid.Column="0"
                Grid.ColumnSpan="7"
                Style="{StaticResource ProgWordSizeButtonStyle}"
                common:KeyboardShortcutManager.VirtualKey="{utils:ResourceVirtualKey Name=dwordButton/[using:CalculatorApp.Common]KeyboardShortcutManager/VirtualKey}"
                AutomationProperties.AutomationId="dwordButton"
                AutomationProperties.Name="{utils:ResourceString Name=dwordButton/[using:Windows.UI.Xaml.Automation]AutomationProperties/Name}"
                Command="{x:Bind BitLengthButtonPressed, Mode=OneTime}"
                CommandParameter="1"
                Content="DWORD"
                Visibility="Collapsed"/>
        <Button x:Name="WordButton"
                Grid.Column="0"
                Grid.ColumnSpan="7"
                Style="{StaticResource ProgWordSizeButtonStyle}"
                common:KeyboardShortcutManager.VirtualKey="{utils:ResourceVirtualKey Name=wordButton/[using:CalculatorApp.Common]KeyboardShortcutManager/VirtualKey}"
                AutomationProperties.AutomationId="wordButton"
                AutomationProperties.Name="{utils:ResourceString Name=wordButton/[using:Windows.UI.Xaml.Automation]AutomationProperties/Name}"
                Command="{x:Bind BitLengthButtonPressed, Mode=OneTime}"
                CommandParameter="2"
                Content="WORD"
                Visibility="Collapsed"/>
        <Button x:Name="ByteButton"
                Grid.Column="0"
                Grid.ColumnSpan="7"
                Style="{StaticResource ProgWordSizeButtonStyle}"
                common:KeyboardShortcutManager.VirtualKey="{utils:ResourceVirtualKey Name=byteButton/[using:CalculatorApp.Common]KeyboardShortcutManager/VirtualKey}"
                AutomationProperties.AutomationId="byteButton"
                AutomationProperties.Name="{utils:ResourceString Name=byteButton/[using:Windows.UI.Xaml.Automation]AutomationProperties/Name}"
                Command="{x:Bind BitLengthButtonPressed, Mode=OneTime}"
                CommandParameter="3"
                Content="BYTE"
                Visibility="Collapsed"/>
    </Grid>
</UserControl>
