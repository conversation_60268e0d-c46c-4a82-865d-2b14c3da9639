<UserControl x:Class="CalculatorApp.CalculatorProgrammerRadixOperators"
             xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
             xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
             xmlns:Windows10version1803="http://schemas.microsoft.com/winfx/2006/xaml/presentation?IsApiContractNotPresent(Windows.Foundation.UniversalApiContract, 7)"
             xmlns:Windows10version1809="http://schemas.microsoft.com/winfx/2006/xaml/presentation?IsApiContractPresent(Windows.Foundation.UniversalApiContract, 7)"
             xmlns:common="using:CalculatorApp.Common"
             xmlns:controls="using:CalculatorApp.Controls"
             xmlns:converters="using:CalculatorApp.Converters"
             xmlns:d="http://schemas.microsoft.com/expression/blend/2008"
             xmlns:local="using:CalculatorApp"
             xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006"
             xmlns:triggers="using:CalculatorApp.Views.StateTriggers"
             xmlns:utils="using:CalculatorApp.Utils"
             x:Name="ControlRoot"
             d:DesignHeight="395"
             d:DesignWidth="315"
             XYFocusKeyboardNavigation="Enabled"
             mc:Ignorable="d">
    <UserControl.Resources>
        <converters:BooleanToVisibilityConverter x:Key="BooleanToVisibilityConverter"/>
        <converters:BooleanToVisibilityNegationConverter x:Key="BooleanToVisibilityNegationConverter"/>
    </UserControl.Resources>

    <Grid x:Name="ProgRadixOps">
        <Grid.RowDefinitions>
            <RowDefinition x:Name="OperatorPanelRow" Height="Auto"/>
            <RowDefinition Height="1*"/>
            <RowDefinition Height="1*"/>
            <RowDefinition Height="1*"/>
            <RowDefinition Height="1*"/>
            <RowDefinition Height="1*"/>
            <RowDefinition Height="1*"/>
        </Grid.RowDefinitions>
        <Grid.ColumnDefinitions>
            <ColumnDefinition x:Name="GutterLeft" Width="0"/>
            <ColumnDefinition x:Name="C0" Width="1*"/>
            <ColumnDefinition x:Name="C1" Width="1*"/>
            <ColumnDefinition x:Name="C2" Width="1*"/>
            <ColumnDefinition x:Name="C3" Width="1*"/>
            <ColumnDefinition x:Name="C4" Width="1*"/>
            <ColumnDefinition x:Name="GutterRight" Width="0"/>
        </Grid.ColumnDefinitions>

        <VisualStateManager.VisualStateGroups>
            <VisualStateGroup x:Name="ErrorVisualStates">
                <VisualState x:Name="NoErrorLayout"/>
                <VisualState x:Name="ErrorLayout">
                    <VisualState.Setters>
                        <Setter Target="RolButton.IsEnabled" Value="False"/>
                        <Setter Target="RorButton.IsEnabled" Value="False"/>
                        <Setter Target="LshButton.IsEnabled" Value="False"/>
                        <Setter Target="RshButton.IsEnabled" Value="False"/>
                        <Setter Target="OrButton.IsEnabled" Value="False"/>
                        <Setter Target="XorButton.IsEnabled" Value="False"/>
                        <Setter Target="NotButton.IsEnabled" Value="False"/>
                        <Setter Target="AndButton.IsEnabled" Value="False"/>

                        <Setter Target="ModButton.IsEnabled" Value="False"/>

                        <Setter Target="DivideButton.IsEnabled" Value="False"/>
                        <Setter Target="MultiplyButton.IsEnabled" Value="False"/>
                        <Setter Target="MinusButton.IsEnabled" Value="False"/>
                        <Setter Target="PlusButton.IsEnabled" Value="False"/>

                        <Setter Target="OpenParenthesisButton.IsEnabled" Value="False"/>
                        <Setter Target="CloseParenthesisButton.IsEnabled" Value="False"/>
                        <Setter Target="NegateButton.IsEnabled" Value="False"/>
                        <Setter Target="BitShiftButton.IsEnabled" Value="False"/>
                        <Setter Target="BitwiseButton.IsEnabled" Value="False"/>
                    </VisualState.Setters>
                </VisualState>
            </VisualStateGroup>
            <VisualStateGroup>
                <VisualState x:Name="Large">
                    <VisualState.StateTriggers>
                        <triggers:ControlSizeTrigger MinWidth="1053"
                                                     MinHeight="729"
                                                     Source="{x:Bind ProgRadixOps}"/>
                    </VisualState.StateTriggers>
                    <VisualState.Setters>
                        <Setter Target="NumberPad.ButtonStyle" Value="{StaticResource NumericButtonStyle38}"/>
                        <Setter Target="AButton.Style" Value="{StaticResource NumericButtonStyle38}"/>
                        <Setter Target="BButton.Style" Value="{StaticResource NumericButtonStyle38}"/>
                        <Setter Target="CButton.Style" Value="{StaticResource NumericButtonStyle38}"/>
                        <Setter Target="DButton.Style" Value="{StaticResource NumericButtonStyle38}"/>
                        <Setter Target="EButton.Style" Value="{StaticResource NumericButtonStyle38}"/>
                        <Setter Target="FButton.Style" Value="{StaticResource NumericButtonStyle38}"/>

                        <Setter Target="ModButton.FontSize" Value="{StaticResource CalcButtonCaptionSize}"/>
                        <Setter Target="DivideButton.FontSize" Value="{StaticResource CalcButtonCaptionSize}"/>
                        <Setter Target="MultiplyButton.FontSize" Value="{StaticResource CalcButtonCaptionSize}"/>
                        <Setter Target="MinusButton.FontSize" Value="{StaticResource CalcButtonCaptionSize}"/>
                        <Setter Target="PlusButton.FontSize" Value="{StaticResource CalcButtonCaptionSize}"/>
                        <Setter Target="EqualButton.FontSize" Value="{StaticResource CalcButtonCaptionSize}"/>
                        <Setter Target="ClearButton.FontSize" Value="{StaticResource CalcButtonCaptionSize}"/>
                        <Setter Target="ClearEntryButton.FontSize" Value="{StaticResource CalcButtonCaptionSize}"/>
                        <Setter Target="BackSpaceButton.FontSize" Value="{StaticResource CalcButtonCaptionSize}"/>
                        <Setter Target="NegateButton.FontSize" Value="{StaticResource CalcButtonCaptionSize}"/>
                        <Setter Target="OpenParenthesisButton.FontSize" Value="{StaticResource CalcButtonCaptionSize}"/>
                        <Setter Target="CloseParenthesisButton.FontSize" Value="{StaticResource CalcButtonCaptionSize}"/>

                        <Setter Target="AndButton.FontSize" Value="{StaticResource CalcButtonCaptionSize}"/>
                        <Setter Target="OrButton.FontSize" Value="{StaticResource CalcButtonCaptionSize}"/>
                        <Setter Target="XorButton.FontSize" Value="{StaticResource CalcButtonCaptionSize}"/>
                        <Setter Target="NandButton.FontSize" Value="{StaticResource CalcButtonCaptionSize}"/>
                        <Setter Target="NorButton.FontSize" Value="{StaticResource CalcButtonCaptionSize}"/>
                        <Setter Target="RolButton.FontSize" Value="{StaticResource CalcButtonCaptionSize}"/>
                        <Setter Target="RorButton.FontSize" Value="{StaticResource CalcButtonCaptionSize}"/>
                        <Setter Target="RolCarryButton.FontSize" Value="{StaticResource CalcButtonCaptionSize}"/>
                        <Setter Target="RorCarryButton.FontSize" Value="{StaticResource CalcButtonCaptionSize}"/>
                        <Setter Target="LshButton.FontSize" Value="{StaticResource CalcButtonCaptionSize}"/>
                        <Setter Target="RshButton.FontSize" Value="{StaticResource CalcButtonCaptionSize}"/>
                        <Setter Target="LshLogicalButton.FontSize" Value="{StaticResource CalcButtonCaptionSize}"/>
                        <Setter Target="RshLogicalButton.FontSize" Value="{StaticResource CalcButtonCaptionSize}"/>
                        <Setter Target="NotButton.FontSize" Value="{StaticResource CalcButtonCaptionSize}"/>

                        <Setter Target="OperatorPanelRow.MinHeight" Value="{StaticResource OperatorPanelButtonRowSizeLarge}"/>

                        <Setter Target="BitwiseButton.Style" Value="{StaticResource OperatorPanelButtonLargeStyle}"/>
                        <Setter Target="BitShiftButton.Style" Value="{StaticResource OperatorPanelButtonLargeStyle}"/>

                        <Setter Target="BitwiseGrid.MinWidth" Value="387"/>
                        <Setter Target="BitwiseGrid.MinHeight" Value="192"/>
                    </VisualState.Setters>
                </VisualState>
                <VisualState x:Name="Medium">
                    <VisualState.StateTriggers>
                        <triggers:ControlSizeTrigger MinWidth="630"
                                                     MinHeight="437"
                                                     Source="{x:Bind ProgRadixOps}"/>
                    </VisualState.StateTriggers>
                    <VisualState.Setters>
                        <Setter Target="NumberPad.ButtonStyle" Value="{StaticResource NumericButtonStyle24}"/>
                        <Setter Target="AButton.Style" Value="{StaticResource NumericButtonStyle24}"/>
                        <Setter Target="BButton.Style" Value="{StaticResource NumericButtonStyle24}"/>
                        <Setter Target="CButton.Style" Value="{StaticResource NumericButtonStyle24}"/>
                        <Setter Target="DButton.Style" Value="{StaticResource NumericButtonStyle24}"/>
                        <Setter Target="EButton.Style" Value="{StaticResource NumericButtonStyle24}"/>
                        <Setter Target="FButton.Style" Value="{StaticResource NumericButtonStyle24}"/>

                        <Setter Target="ModButton.FontSize" Value="{StaticResource CalcStandardOperatorCaptionSize}"/>
                        <Setter Target="DivideButton.FontSize" Value="{StaticResource CalcStandardOperatorCaptionSize}"/>
                        <Setter Target="MultiplyButton.FontSize" Value="{StaticResource CalcStandardOperatorCaptionSize}"/>
                        <Setter Target="MinusButton.FontSize" Value="{StaticResource CalcStandardOperatorCaptionSize}"/>
                        <Setter Target="PlusButton.FontSize" Value="{StaticResource CalcStandardOperatorCaptionSize}"/>
                        <Setter Target="EqualButton.FontSize" Value="{StaticResource CalcStandardOperatorCaptionSize}"/>
                        <Setter Target="ClearButton.FontSize" Value="{StaticResource CalcStandardOperatorCaptionSize}"/>
                        <Setter Target="ClearEntryButton.FontSize" Value="{StaticResource CalcStandardOperatorCaptionSize}"/>
                        <Setter Target="BackSpaceButton.FontSize" Value="{StaticResource CalcStandardOperatorCaptionSize}"/>
                        <Setter Target="NegateButton.FontSize" Value="{StaticResource CalcStandardOperatorCaptionSizeLarge}"/>
                        <Setter Target="OpenParenthesisButton.FontSize" Value="{StaticResource CalcStandardOperatorCaptionSize}"/>
                        <Setter Target="CloseParenthesisButton.FontSize" Value="{StaticResource CalcStandardOperatorCaptionSize}"/>

                        <Setter Target="AndButton.FontSize" Value="{StaticResource CalcStandardOperatorCaptionSize}"/>
                        <Setter Target="OrButton.FontSize" Value="{StaticResource CalcStandardOperatorCaptionSize}"/>
                        <Setter Target="XorButton.FontSize" Value="{StaticResource CalcStandardOperatorCaptionSize}"/>
                        <Setter Target="NandButton.FontSize" Value="{StaticResource CalcStandardOperatorCaptionSize}"/>
                        <Setter Target="NorButton.FontSize" Value="{StaticResource CalcStandardOperatorCaptionSize}"/>
                        <Setter Target="RolButton.FontSize" Value="{StaticResource CalcStandardOperatorCaptionSize}"/>
                        <Setter Target="RorButton.FontSize" Value="{StaticResource CalcStandardOperatorCaptionSize}"/>
                        <Setter Target="RolCarryButton.FontSize" Value="{StaticResource CalcStandardOperatorCaptionSize}"/>
                        <Setter Target="RorCarryButton.FontSize" Value="{StaticResource CalcStandardOperatorCaptionSize}"/>
                        <Setter Target="LshButton.FontSize" Value="{StaticResource CalcStandardOperatorCaptionSize}"/>
                        <Setter Target="RshButton.FontSize" Value="{StaticResource CalcStandardOperatorCaptionSize}"/>
                        <Setter Target="LshLogicalButton.FontSize" Value="{StaticResource CalcStandardOperatorCaptionSize}"/>
                        <Setter Target="RshLogicalButton.FontSize" Value="{StaticResource CalcStandardOperatorCaptionSize}"/>
                        <Setter Target="NotButton.FontSize" Value="{StaticResource CalcStandardOperatorCaptionSize}"/>

                        <Setter Target="OperatorPanelRow.MinHeight" Value="{StaticResource OperatorPanelButtonRowSizeMedium}"/>

                        <Setter Target="BitwiseButton.Style" Value="{StaticResource OperatorPanelButtonMediumStyle}"/>
                        <Setter Target="BitShiftButton.Style" Value="{StaticResource OperatorPanelButtonMediumStyle}"/>

                        <Setter Target="BitwiseGrid.MinWidth" Value="416"/>
                        <Setter Target="BitwiseGrid.MinHeight" Value="144"/>
                    </VisualState.Setters>
                </VisualState>
                <VisualState x:Name="Small">
                    <VisualState.StateTriggers>
                        <AdaptiveTrigger MinWindowHeight="{StaticResource AppMinWindowHeight}" MinWindowWidth="{StaticResource AppMinWindowWidth}"/>
                    </VisualState.StateTriggers>
                    <VisualState.Setters>
                        <Setter Target="NumberPad.ButtonStyle" Value="{StaticResource NumericButtonStyle18}"/>
                        <Setter Target="AButton.Style" Value="{StaticResource NumericButtonStyle14}"/>
                        <Setter Target="BButton.Style" Value="{StaticResource NumericButtonStyle14}"/>
                        <Setter Target="CButton.Style" Value="{StaticResource NumericButtonStyle14}"/>
                        <Setter Target="DButton.Style" Value="{StaticResource NumericButtonStyle14}"/>
                        <Setter Target="EButton.Style" Value="{StaticResource NumericButtonStyle14}"/>
                        <Setter Target="FButton.Style" Value="{StaticResource NumericButtonStyle14}"/>

                        <Setter Target="ModButton.FontSize" Value="{StaticResource CalcOperatorCaptionSize}"/>
                        <Setter Target="DivideButton.FontSize" Value="{StaticResource CalcOperatorCaptionSize}"/>
                        <Setter Target="MultiplyButton.FontSize" Value="{StaticResource CalcOperatorCaptionSize}"/>
                        <Setter Target="MinusButton.FontSize" Value="{StaticResource CalcOperatorCaptionSize}"/>
                        <Setter Target="PlusButton.FontSize" Value="{StaticResource CalcOperatorCaptionSize}"/>
                        <Setter Target="EqualButton.FontSize" Value="{StaticResource CalcOperatorCaptionSize}"/>
                        <Setter Target="ClearButton.FontSize" Value="{StaticResource CalcOperatorCaptionSize}"/>
                        <Setter Target="ClearEntryButton.FontSize" Value="{StaticResource CalcOperatorCaptionSize}"/>
                        <Setter Target="BackSpaceButton.FontSize" Value="{StaticResource CalcOperatorCaptionSize}"/>
                        <Setter Target="NegateButton.FontSize" Value="{StaticResource CalcOperatorCaptionSize}"/>
                        <Setter Target="OpenParenthesisButton.FontSize" Value="{StaticResource CalcOperatorCaptionSize}"/>
                        <Setter Target="CloseParenthesisButton.FontSize" Value="{StaticResource CalcOperatorCaptionSize}"/>

                        <Setter Target="AndButton.FontSize" Value="{StaticResource CalcOperatorCaptionSize}"/>
                        <Setter Target="OrButton.FontSize" Value="{StaticResource CalcOperatorCaptionSize}"/>
                        <Setter Target="XorButton.FontSize" Value="{StaticResource CalcOperatorCaptionSize}"/>
                        <Setter Target="NandButton.FontSize" Value="{StaticResource CalcOperatorCaptionSize}"/>
                        <Setter Target="NorButton.FontSize" Value="{StaticResource CalcOperatorCaptionSize}"/>
                        <Setter Target="RolButton.FontSize" Value="{StaticResource CalcOperatorCaptionSize}"/>
                        <Setter Target="RorButton.FontSize" Value="{StaticResource CalcOperatorCaptionSize}"/>
                        <Setter Target="RolCarryButton.FontSize" Value="{StaticResource CalcOperatorCaptionSize}"/>
                        <Setter Target="RorCarryButton.FontSize" Value="{StaticResource CalcOperatorCaptionSize}"/>
                        <Setter Target="LshButton.FontSize" Value="{StaticResource CalcOperatorCaptionSize}"/>
                        <Setter Target="RshButton.FontSize" Value="{StaticResource CalcOperatorCaptionSize}"/>
                        <Setter Target="LshLogicalButton.FontSize" Value="{StaticResource CalcOperatorCaptionSize}"/>
                        <Setter Target="RshLogicalButton.FontSize" Value="{StaticResource CalcOperatorCaptionSize}"/>
                        <Setter Target="NotButton.FontSize" Value="{StaticResource CalcOperatorCaptionSize}"/>

                        <Setter Target="OperatorPanelRow.MinHeight" Value="{StaticResource OperatorPanelButtonRowSizeSmall}"/>

                        <Setter Target="BitwiseButton.Style" Value="{StaticResource OperatorPanelButtonSmallStyle}"/>
                        <Setter Target="BitShiftButton.Style" Value="{StaticResource OperatorPanelButtonSmallStyle}"/>

                        <Setter Target="BitwiseGrid.MinWidth" Value="194"/>
                        <Setter Target="BitwiseGrid.MinHeight" Value="96"/>
                    </VisualState.Setters>
                </VisualState>
            </VisualStateGroup>
        </VisualStateManager.VisualStateGroups>

        <controls:OperatorPanelListView Grid.ColumnSpan="6"
                                        Background="{ThemeResource AppOperatorPanelBackground}"
                                        AutomationProperties.HeadingLevel="Level1"
                                        AutomationProperties.Name="{utils:ResourceString Name=ProgrammerOperatorPanel/[using:Windows.UI.Xaml.Automation]AutomationProperties/Name}">

            <controls:OperatorPanelButton x:Name="BitwiseButton"
                                          Style="{StaticResource OperatorPanelButtonStyle}"
                                          AutomationProperties.AutomationId="bitwiseButton"
                                          AutomationProperties.Name="{utils:ResourceString Name=bitwiseButton/[using:Windows.UI.Xaml.Automation]AutomationProperties/Name}"
                                          Glyph="&#xF895;"
                                          Text="{utils:ResourceString Name=bitwiseButton/Text}">
                <controls:OperatorPanelButton.FlyoutMenu>
                    <Flyout x:Name="BitwiseFlyout"
                            Windows10version1803:Placement="Bottom"
                            Windows10version1809:AreOpenCloseAnimationsEnabled="False"
                            Windows10version1809:Placement="BottomEdgeAlignedLeft"
                            FlyoutPresenterStyle="{ThemeResource OperatorPanelFlyoutStyle}">
                        <Grid x:Name="BitwiseGrid"
                              MinWidth="258"
                              MinHeight="96"
                              XYFocusKeyboardNavigation="Enabled">
                            <Grid.RowDefinitions>
                                <RowDefinition Height="1*"/>
                                <RowDefinition Height="1*"/>
                            </Grid.RowDefinitions>
                            <Grid.ColumnDefinitions>
                                <ColumnDefinition Width="1*"/>
                                <ColumnDefinition Width="1*"/>
                                <ColumnDefinition Width="1*"/>
                            </Grid.ColumnDefinitions>
                            <!--
                                Because of the way we handle keyboard shortcuts, the IsEnabled property must be bound to a button outside the flyout.
                                This is because the content from within the Flyout do not inherit the IsEnabled property of the flyout parent,
                                causing the shortcut keys to be used when the control would normally be disabled.
                            -->
                            <controls:CalculatorButton x:Name="AndButton"
                                                       Style="{StaticResource OperatorButtonStyle}"
                                                       FontSize="12"
                                                       common:KeyboardShortcutManager.Character="{utils:ResourceString Name=andButton/[using:CalculatorApp.Common]KeyboardShortcutManager/Character}"
                                                       AuditoryFeedback="{utils:ResourceString Name=andButton/[using:CalculatorApp.Controls]CalculatorButton/AuditoryFeedback}"
                                                       AutomationProperties.AutomationId="andButton"
                                                       AutomationProperties.Name="{utils:ResourceString Name=andButton/[using:Windows.UI.Xaml.Automation]AutomationProperties/Name}"
                                                       ButtonId="And"
                                                       Click="FlyoutButton_Clicked"
                                                       Command="{x:Bind Model.ButtonPressed}"
                                                       Content="AND"
                                                       IsEnabled="{x:Bind BitwiseButton.IsEnabled, Mode=OneWay}"/>

                            <controls:CalculatorButton x:Name="OrButton"
                                                       Grid.Column="1"
                                                       Style="{StaticResource OperatorButtonStyle}"
                                                       FontSize="12"
                                                       common:KeyboardShortcutManager.Character="{utils:ResourceString Name=orButton/[using:CalculatorApp.Common]KeyboardShortcutManager/Character}"
                                                       AuditoryFeedback="{utils:ResourceString Name=orButton/[using:CalculatorApp.Controls]CalculatorButton/AuditoryFeedback}"
                                                       AutomationProperties.AutomationId="orButton"
                                                       AutomationProperties.Name="{utils:ResourceString Name=orButton/[using:Windows.UI.Xaml.Automation]AutomationProperties/Name}"
                                                       ButtonId="Or"
                                                       Click="FlyoutButton_Clicked"
                                                       Command="{x:Bind Model.ButtonPressed}"
                                                       Content="OR"
                                                       IsEnabled="{x:Bind BitwiseButton.IsEnabled, Mode=OneWay}"/>

                            <controls:CalculatorButton x:Name="NotButton"
                                                       Grid.Column="2"
                                                       Style="{StaticResource OperatorButtonStyle}"
                                                       FontSize="12"
                                                       common:KeyboardShortcutManager.Character="{utils:ResourceString Name=notButton/[using:CalculatorApp.Common]KeyboardShortcutManager/Character}"
                                                       AutomationProperties.AutomationId="notButton"
                                                       AutomationProperties.Name="{utils:ResourceString Name=notButton/[using:Windows.UI.Xaml.Automation]AutomationProperties/Name}"
                                                       ButtonId="Not"
                                                       Click="FlyoutButton_Clicked"
                                                       Command="{x:Bind Model.ButtonPressed}"
                                                       Content="NOT"
                                                       IsEnabled="{x:Bind BitwiseButton.IsEnabled, Mode=OneWay}"/>

                            <controls:CalculatorButton x:Name="NandButton"
                                                       Grid.Row="1"
                                                       Style="{StaticResource OperatorButtonStyle}"
                                                       FontSize="12"
                                                       common:KeyboardShortcutManager.Character="{utils:ResourceString Name=nandButton/[using:CalculatorApp.Common]KeyboardShortcutManager/Character}"
                                                       AuditoryFeedback="{utils:ResourceString Name=nandButton/[using:CalculatorApp.Controls]CalculatorButton/AuditoryFeedback}"
                                                       AutomationProperties.AutomationId="nandButton"
                                                       AutomationProperties.Name="{utils:ResourceString Name=nandButton/[using:Windows.UI.Xaml.Automation]AutomationProperties/Name}"
                                                       ButtonId="Nand"
                                                       Click="FlyoutButton_Clicked"
                                                       Command="{x:Bind Model.ButtonPressed}"
                                                       Content="NAND"
                                                       IsEnabled="{x:Bind BitwiseButton.IsEnabled, Mode=OneWay}"/>

                            <controls:CalculatorButton x:Name="NorButton"
                                                       Grid.Row="1"
                                                       Grid.Column="1"
                                                       Style="{StaticResource OperatorButtonStyle}"
                                                       FontSize="12"
                                                       common:KeyboardShortcutManager.Character="{utils:ResourceString Name=norButton/[using:CalculatorApp.Common]KeyboardShortcutManager/Character}"
                                                       AuditoryFeedback="{utils:ResourceString Name=norButton/[using:CalculatorApp.Controls]CalculatorButton/AuditoryFeedback}"
                                                       AutomationProperties.AutomationId="norButton"
                                                       AutomationProperties.Name="{utils:ResourceString Name=norButton/[using:Windows.UI.Xaml.Automation]AutomationProperties/Name}"
                                                       ButtonId="Nor"
                                                       Click="FlyoutButton_Clicked"
                                                       Command="{x:Bind Model.ButtonPressed}"
                                                       Content="NOR"
                                                       IsEnabled="{x:Bind BitwiseButton.IsEnabled, Mode=OneWay}"/>

                            <controls:CalculatorButton x:Name="XorButton"
                                                       Grid.Row="1"
                                                       Grid.Column="2"
                                                       Style="{StaticResource OperatorButtonStyle}"
                                                       FontSize="12"
                                                       common:KeyboardShortcutManager.Character="{utils:ResourceString Name=xorButton/[using:CalculatorApp.Common]KeyboardShortcutManager/Character}"
                                                       AuditoryFeedback="{utils:ResourceString Name=xorButton/[using:CalculatorApp.Controls]CalculatorButton/AuditoryFeedback}"
                                                       AutomationProperties.AutomationId="xorButton"
                                                       AutomationProperties.Name="{utils:ResourceString Name=xorButton/[using:Windows.UI.Xaml.Automation]AutomationProperties/Name}"
                                                       ButtonId="Xor"
                                                       Click="FlyoutButton_Clicked"
                                                       Command="{x:Bind Model.ButtonPressed}"
                                                       Content="XOR"
                                                       IsEnabled="{x:Bind BitwiseButton.IsEnabled, Mode=OneWay}"/>
                        </Grid>
                    </Flyout>
                </controls:OperatorPanelButton.FlyoutMenu>
            </controls:OperatorPanelButton>
            <controls:OperatorPanelButton x:Name="BitShiftButton"
                                          Style="{StaticResource OperatorPanelButtonStyle}"
                                          AutomationProperties.AutomationId="bitShiftButton"
                                          AutomationProperties.Name="{utils:ResourceString Name=bitShiftButton/[using:Windows.UI.Xaml.Automation]AutomationProperties/Name}"
                                          Glyph="&#xE301;"
                                          Text="{utils:ResourceString Name=bitShiftButton/Text}">
                <controls:OperatorPanelButton.FlyoutMenu>
                    <Flyout x:Name="BitShiftFlyout"
                            Windows10version1809:AreOpenCloseAnimationsEnabled="False"
                            FlyoutPresenterStyle="{ThemeResource OperatorPanelFlyoutStyle}"
                            Opened="BitShiftFlyout_Opened"
                            Placement="Bottom">
                        <StackPanel MaxWidth="192" Padding="12">
                            <RadioButton x:Name="ArithmeticShiftButton"
                                         AutomationProperties.AutomationId="arithmeticShiftButton"
                                         Checked="BitshiftFlyout_Checked"
                                         Content="{utils:ResourceString Name=arithmeticShiftButton/Content}"/>
                            <RadioButton x:Name="LogicalShiftButton"
                                         AutomationProperties.AutomationId="logicalShiftButton"
                                         Checked="BitshiftFlyout_Checked"
                                         Content="{utils:ResourceString Name=logicalShiftButton/Content}"/>
                            <RadioButton x:Name="RotateCircularButton"
                                         AutomationProperties.AutomationId="rotateCircularButton"
                                         Checked="BitshiftFlyout_Checked"
                                         Content="{utils:ResourceString Name=rotateCircularButton/Content}"/>
                            <RadioButton x:Name="RotateCarryShiftButton"
                                         AutomationProperties.AutomationId="rotateCarryShiftButton"
                                         Checked="BitshiftFlyout_Checked"
                                         Content="{utils:ResourceString Name=rotateCarryShiftButton/Content}"/>
                        </StackPanel>
                    </Flyout>
                </controls:OperatorPanelButton.FlyoutMenu>
            </controls:OperatorPanelButton>
        </controls:OperatorPanelListView>

        <Grid Grid.Row="1"
              Grid.Column="2"
              Grid.ColumnSpan="2"
              AutomationProperties.HeadingLevel="Level1"
              AutomationProperties.Name="{utils:ResourceString Name=ProgrammerOperators/[using:Windows.UI.Xaml.Automation]AutomationProperties/Name}">
            <Grid.ColumnDefinitions>
                <ColumnDefinition/>
                <ColumnDefinition/>
            </Grid.ColumnDefinitions>
            <controls:CalculatorButton x:Name="RolButton"
                                       Grid.Column="0"
                                       Style="{StaticResource SymbolOperatorButtonStyle}"
                                       FontSize="12"
                                       common:KeyboardShortcutManager.Character="{utils:ResourceString Name=rolButton/[using:CalculatorApp.Common]KeyboardShortcutManager/Character}"
                                       x:DeferLoadStrategy="Lazy"
                                       AutomationProperties.AutomationId="rolButton"
                                       AutomationProperties.Name="{utils:ResourceString Name=rolButton/[using:Windows.UI.Xaml.Automation]AutomationProperties/Name}"
                                       ButtonId="Rol"
                                       Content="&#xF88E;"
                                       Visibility="Collapsed"/>
            <controls:CalculatorButton x:Name="RorButton"
                                       Grid.Column="1"
                                       Style="{StaticResource SymbolOperatorButtonStyle}"
                                       FontSize="12"
                                       common:KeyboardShortcutManager.Character="{utils:ResourceString Name=rorButton/[using:CalculatorApp.Common]KeyboardShortcutManager/Character}"
                                       x:DeferLoadStrategy="Lazy"
                                       AutomationProperties.AutomationId="rorButton"
                                       AutomationProperties.Name="{utils:ResourceString Name=rorButton/[using:Windows.UI.Xaml.Automation]AutomationProperties/Name}"
                                       ButtonId="Ror"
                                       Content="&#xF88F;"
                                       Visibility="Collapsed"/>

            <controls:CalculatorButton x:Name="RolCarryButton"
                                       Style="{StaticResource SymbolOperatorButtonStyle}"
                                       FontSize="12"
                                       common:KeyboardShortcutManager.Character="{utils:ResourceString Name=rolCarryButton/[using:CalculatorApp.Common]KeyboardShortcutManager/Character}"
                                       x:DeferLoadStrategy="Lazy"
                                       AutomationProperties.AutomationId="rolButton"
                                       AutomationProperties.Name="{utils:ResourceString Name=rolCarryButton/[using:Windows.UI.Xaml.Automation]AutomationProperties/Name}"
                                       ButtonId="RolC"
                                       Content="&#xF88E;"
                                       Visibility="Collapsed"/>

            <controls:CalculatorButton x:Name="RorCarryButton"
                                       Grid.Column="1"
                                       Style="{StaticResource SymbolOperatorButtonStyle}"
                                       FontSize="12"
                                       common:KeyboardShortcutManager.Character="{utils:ResourceString Name=rorCarryButton/[using:CalculatorApp.Common]KeyboardShortcutManager/Character}"
                                       x:DeferLoadStrategy="Lazy"
                                       AutomationProperties.AutomationId="rorCarryButton"
                                       AutomationProperties.Name="{utils:ResourceString Name=rorCarryButton/[using:Windows.UI.Xaml.Automation]AutomationProperties/Name}"
                                       ButtonId="RorC"
                                       Content="&#xF88F;"
                                       Visibility="Collapsed"/>

            <controls:CalculatorButton x:Name="LshButton"
                                       Style="{StaticResource SymbolOperatorButtonStyle}"
                                       FontSize="12"
                                       common:KeyboardShortcutManager.Character="{utils:ResourceString Name=lshButton/[using:CalculatorApp.Common]KeyboardShortcutManager/Character}"
                                       AuditoryFeedback="{utils:ResourceString Name=lshButton/[using:CalculatorApp.Controls]CalculatorButton/AuditoryFeedback}"
                                       AutomationProperties.AutomationId="lshButton"
                                       AutomationProperties.Name="{utils:ResourceString Name=lshButton/[using:Windows.UI.Xaml.Automation]AutomationProperties/Name}"
                                       ButtonId="Lsh"
                                       Content="&#xF88E;"/>
            <controls:CalculatorButton x:Name="RshButton"
                                       Grid.Column="1"
                                       Style="{StaticResource SymbolOperatorButtonStyle}"
                                       FontSize="12"
                                       common:KeyboardShortcutManager.Character="{utils:ResourceString Name=rshButton/[using:CalculatorApp.Common]KeyboardShortcutManager/Character}"
                                       AuditoryFeedback="{utils:ResourceString Name=rshButton/[using:CalculatorApp.Controls]CalculatorButton/AuditoryFeedback}"
                                       AutomationProperties.AutomationId="rshButton"
                                       AutomationProperties.Name="{utils:ResourceString Name=rshButton/[using:Windows.UI.Xaml.Automation]AutomationProperties/Name}"
                                       ButtonId="Rsh"
                                       Content="&#xF88F;"/>

            <controls:CalculatorButton x:Name="LshLogicalButton"
                                       x:Uid="LshLogicalButton"
                                       Style="{StaticResource SymbolOperatorButtonStyle}"
                                       FontSize="12"
                                       x:DeferLoadStrategy="Lazy"
                                       AutomationProperties.AutomationId="lshLogicalButton"
                                       ButtonId="Lsh"
                                       Content="&#xF88E;"
                                       Visibility="Collapsed"/>
            <controls:CalculatorButton x:Name="RshLogicalButton"
                                       Grid.Column="1"
                                       Style="{StaticResource SymbolOperatorButtonStyle}"
                                       FontSize="12"
                                       common:KeyboardShortcutManager.Character="{utils:ResourceString Name=rshLogicalButton/[using:CalculatorApp.Common]KeyboardShortcutManager/Character}"
                                       x:DeferLoadStrategy="Lazy"
                                       AuditoryFeedback="{utils:ResourceString Name=rshLogicalButton/[using:CalculatorApp.Controls]CalculatorButton/AuditoryFeedback}"
                                       AutomationProperties.AutomationId="rshLogicalButton"
                                       AutomationProperties.Name="{utils:ResourceString Name=rshLogicalButton/[using:Windows.UI.Xaml.Automation]AutomationProperties/Name}"
                                       ButtonId="RshL"
                                       Content="&#xF88F;"
                                       Visibility="Collapsed"/>
        </Grid>

        <Grid Grid.Row="1"
              Grid.Column="4"
              Grid.ColumnSpan="2"
              AutomationProperties.HeadingLevel="Level1"
              AutomationProperties.Name="{utils:ResourceString Name=DisplayControls/[using:Windows.UI.Xaml.Automation]AutomationProperties/Name}">
            <Grid.ColumnDefinitions>
                <ColumnDefinition/>
                <ColumnDefinition/>
            </Grid.ColumnDefinitions>
            <controls:CalculatorButton x:Name="ClearButton"
                                       Style="{StaticResource OperatorButtonStyle}"
                                       FontSize="12"
                                       common:KeyboardShortcutManager.VirtualKey="{utils:ResourceVirtualKey Name=clearButton/[using:CalculatorApp.Common]KeyboardShortcutManager/VirtualKey}"
                                       AutomationProperties.AutomationId="clearButton"
                                       AutomationProperties.Name="{utils:ResourceString Name=clearButton/[using:Windows.UI.Xaml.Automation]AutomationProperties/Name}"
                                       ButtonId="Clear"
                                       Content="C"
                                       LostFocus="ClearButton_LostFocus"
                                       Visibility="{x:Bind Model.IsInputEmpty, Mode=OneWay, Converter={StaticResource BooleanToVisibilityConverter}}"/>

            <controls:CalculatorButton x:Name="ClearEntryButton"
                                       Style="{StaticResource OperatorButtonStyle}"
                                       FontSize="12"
                                       common:KeyboardShortcutManager.VirtualKey="{utils:ResourceVirtualKey Name=clearEntryButton/[using:CalculatorApp.Common]KeyboardShortcutManager/VirtualKey}"
                                       AutomationProperties.AutomationId="clearEntryButton"
                                       AutomationProperties.Name="{utils:ResourceString Name=clearEntryButton/[using:Windows.UI.Xaml.Automation]AutomationProperties/Name}"
                                       ButtonId="ClearEntry"
                                       Content="CE"
                                       LostFocus="ClearEntryButton_LostFocus"
                                       Visibility="{x:Bind Model.IsInputEmpty, Mode=OneWay, Converter={StaticResource BooleanToVisibilityNegationConverter}}"/>

            <controls:CalculatorButton x:Name="BackSpaceButton"
                                       Grid.Column="1"
                                       Style="{StaticResource SymbolOperatorButtonStyle}"
                                       FontSize="12"
                                       common:KeyboardShortcutManager.VirtualKey="{utils:ResourceVirtualKey Name=backSpaceButton/[using:CalculatorApp.Common]KeyboardShortcutManager/VirtualKey}"
                                       AutomationProperties.AutomationId="backSpaceButton"
                                       AutomationProperties.Name="{utils:ResourceString Name=backSpaceButton/[using:Windows.UI.Xaml.Automation]AutomationProperties/Name}"
                                       ButtonId="Backspace"
                                       Content="&#xE94F;"/>
        </Grid>

        <controls:CalculatorButton x:Name="OpenParenthesisButton"
                                   Grid.Row="2"
                                   Grid.Column="2"
                                   Style="{StaticResource ParenthesisCalcButtonStyle}"
                                   FontSize="18"
                                   common:KeyboardShortcutManager.Character="{utils:ResourceString Name=openParenthesisButton/[using:CalculatorApp.Common]KeyboardShortcutManager/Character}"
                                   AutomationProperties.AutomationId="openParenthesisButton"
                                   AutomationProperties.Name="{utils:ResourceString Name=openParenthesisButton/[using:Windows.UI.Xaml.Automation]AutomationProperties/Name}"
                                   ButtonId="OpenParenthesis"
                                   Content="("
                                   GotFocus="OpenParenthesisButton_GotFocus"
                                   Tag="{x:Bind ParenthesisCountToString(Model.OpenParenthesisCount), Mode=OneWay}"/>

        <controls:CalculatorButton x:Name="CloseParenthesisButton"
                                   Grid.Row="2"
                                   Grid.Column="3"
                                   Style="{StaticResource OperatorButtonStyle}"
                                   FontSize="18"
                                   common:KeyboardShortcutManager.Character="{utils:ResourceString Name=closeParenthesisButton/[using:CalculatorApp.Common]KeyboardShortcutManager/Character}"
                                   AutomationProperties.AutomationId="closeParenthesisButton"
                                   AutomationProperties.Name="{utils:ResourceString Name=closeParenthesisButton/[using:Windows.UI.Xaml.Automation]AutomationProperties/Name}"
                                   ButtonId="CloseParenthesis"
                                   Content=")"/>

        <controls:CalculatorButton x:Name="ModButton"
                                   Grid.Row="2"
                                   Grid.Column="4"
                                   Style="{StaticResource OperatorButtonStyle}"
                                   FontSize="12"
                                   common:KeyboardShortcutManager.Character="{utils:ResourceString Name=modButton/[using:CalculatorApp.Common]KeyboardShortcutManager/Character}"
                                   AuditoryFeedback="{utils:ResourceString Name=modButton/[using:CalculatorApp.Controls]CalculatorButton/AuditoryFeedback}"
                                   AutomationProperties.AutomationId="modButton"
                                   AutomationProperties.Name="{utils:ResourceString Name=modButton/[using:Windows.UI.Xaml.Automation]AutomationProperties/Name}"
                                   ButtonId="Mod"
                                   Content="%"/>

        <Grid Grid.Row="2"
              Grid.RowSpan="5"
              Grid.Column="5"
              AutomationProperties.HeadingLevel="Level1"
              AutomationProperties.Name="{utils:ResourceString Name=StandardOperators/[using:Windows.UI.Xaml.Automation]AutomationProperties/Name}">
            <Grid.RowDefinitions>
                <RowDefinition/>
                <RowDefinition/>
                <RowDefinition/>
                <RowDefinition/>
                <RowDefinition/>
            </Grid.RowDefinitions>
            <controls:CalculatorButton x:Name="DivideButton"
                                       Grid.Row="0"
                                       Style="{StaticResource SymbolOperatorButtonStyle}"
                                       FontSize="12"
                                       common:KeyboardShortcutManager.Character="{utils:ResourceString Name=divideButton/[using:CalculatorApp.Common]KeyboardShortcutManager/Character}"
                                       AuditoryFeedback="{utils:ResourceString Name=divideButton/[using:CalculatorApp.Controls]CalculatorButton/AuditoryFeedback}"
                                       AutomationProperties.AutomationId="divideButton"
                                       AutomationProperties.Name="{utils:ResourceString Name=divideButton/[using:Windows.UI.Xaml.Automation]AutomationProperties/Name}"
                                       ButtonId="Divide"
                                       Content="&#xE94A;"/>

            <controls:CalculatorButton x:Name="MultiplyButton"
                                       Grid.Row="1"
                                       Style="{StaticResource SymbolOperatorButtonStyle}"
                                       FontSize="12"
                                       common:KeyboardShortcutManager.Character="{utils:ResourceString Name=multiplyButton/[using:CalculatorApp.Common]KeyboardShortcutManager/Character}"
                                       AuditoryFeedback="{utils:ResourceString Name=multiplyButton/[using:CalculatorApp.Controls]CalculatorButton/AuditoryFeedback}"
                                       AutomationProperties.AutomationId="multiplyButton"
                                       AutomationProperties.Name="{utils:ResourceString Name=multiplyButton/[using:Windows.UI.Xaml.Automation]AutomationProperties/Name}"
                                       ButtonId="Multiply"
                                       Content="&#xE947;"/>

            <controls:CalculatorButton x:Name="MinusButton"
                                       Grid.Row="2"
                                       Style="{StaticResource SymbolOperatorButtonStyle}"
                                       FontSize="12"
                                       common:KeyboardShortcutManager.Character="{utils:ResourceString Name=minusButton/[using:CalculatorApp.Common]KeyboardShortcutManager/Character}"
                                       AuditoryFeedback="{utils:ResourceString Name=minusButton/[using:CalculatorApp.Controls]CalculatorButton/AuditoryFeedback}"
                                       AutomationProperties.AutomationId="minusButton"
                                       AutomationProperties.Name="{utils:ResourceString Name=minusButton/[using:Windows.UI.Xaml.Automation]AutomationProperties/Name}"
                                       ButtonId="Subtract"
                                       Content="&#xE949;"/>

            <controls:CalculatorButton x:Name="PlusButton"
                                       Grid.Row="3"
                                       Style="{StaticResource SymbolOperatorButtonStyle}"
                                       FontSize="12"
                                       common:KeyboardShortcutManager.Character="{utils:ResourceString Name=plusButton/[using:CalculatorApp.Common]KeyboardShortcutManager/Character}"
                                       AuditoryFeedback="{utils:ResourceString Name=plusButton/[using:CalculatorApp.Controls]CalculatorButton/AuditoryFeedback}"
                                       AutomationProperties.AutomationId="plusButton"
                                       AutomationProperties.Name="{utils:ResourceString Name=plusButton/[using:Windows.UI.Xaml.Automation]AutomationProperties/Name}"
                                       ButtonId="Add"
                                       Content="&#xE948;"/>

            <controls:CalculatorButton x:Name="EqualButton"
                                       Grid.Row="4"
                                       Style="{StaticResource AccentEmphasizedCalcButtonStyle}"
                                       FontSize="12"
                                       common:KeyboardShortcutManager.Character="{utils:ResourceString Name=equalButton/[using:CalculatorApp.Common]KeyboardShortcutManager/Character}"
                                       common:KeyboardShortcutManager.VirtualKey="{utils:ResourceVirtualKey Name=equalButton/[using:CalculatorApp.Common]KeyboardShortcutManager/VirtualKey}"
                                       AutomationProperties.AutomationId="equalButton"
                                       AutomationProperties.Name="{utils:ResourceString Name=equalButton/[using:Windows.UI.Xaml.Automation]AutomationProperties/Name}"
                                       ButtonId="Equals"
                                       Content="&#xE94E;"/>
        </Grid>

        <Grid Grid.Row="1"
              Grid.RowSpan="6"
              Grid.Column="1"
              Grid.ColumnSpan="5"
              AutomationProperties.AutomationId="NumberPad"
              AutomationProperties.HeadingLevel="Level1"
              AutomationProperties.Name="{utils:ResourceString Name=NumberPad/[using:Windows.UI.Xaml.Automation]AutomationProperties/Name}">
            <Grid.RowDefinitions>
                <RowDefinition/>
                <RowDefinition/>
                <RowDefinition/>
                <RowDefinition/>
                <RowDefinition/>
                <RowDefinition/>
            </Grid.RowDefinitions>
            <Grid.ColumnDefinitions>
                <ColumnDefinition/>
                <ColumnDefinition/>
                <ColumnDefinition/>
                <ColumnDefinition/>
                <ColumnDefinition/>
            </Grid.ColumnDefinitions>
            <controls:CalculatorButton x:Name="AButton"
                                       Grid.Row="0"
                                       Grid.Column="0"
                                       Style="{StaticResource NumericButtonStyle12}"
                                       common:KeyboardShortcutManager.VirtualKey="{utils:ResourceVirtualKey Name=aButton/[using:CalculatorApp.Common]KeyboardShortcutManager/VirtualKey}"
                                       common:KeyboardShortcutManager.VirtualKeyShiftChord="{utils:ResourceVirtualKey Name=aButton/[using:CalculatorApp.Common]KeyboardShortcutManager/VirtualKeyShiftChord}"
                                       AutomationProperties.AutomationId="aButton"
                                       AutomationProperties.Name="{utils:ResourceString Name=aButton/[using:Windows.UI.Xaml.Automation]AutomationProperties/Name}"
                                       ButtonId="A"
                                       Content="A"
                                       IsEnabled="{x:Bind Model.AreHEXButtonsEnabled, Mode=OneWay}"/>

            <controls:CalculatorButton x:Name="BButton"
                                       Grid.Row="1"
                                       Grid.Column="0"
                                       Style="{StaticResource NumericButtonStyle12}"
                                       common:KeyboardShortcutManager.VirtualKey="{utils:ResourceVirtualKey Name=bButton/[using:CalculatorApp.Common]KeyboardShortcutManager/VirtualKey}"
                                       common:KeyboardShortcutManager.VirtualKeyShiftChord="{utils:ResourceVirtualKey Name=bButton/[using:CalculatorApp.Common]KeyboardShortcutManager/VirtualKeyShiftChord}"
                                       AutomationProperties.AutomationId="bButton"
                                       AutomationProperties.Name="{utils:ResourceString Name=bButton/[using:Windows.UI.Xaml.Automation]AutomationProperties/Name}"
                                       ButtonId="B"
                                       Content="B"
                                       IsEnabled="{x:Bind Model.AreHEXButtonsEnabled, Mode=OneWay}"/>

            <controls:CalculatorButton x:Name="CButton"
                                       Grid.Row="2"
                                       Grid.Column="0"
                                       Style="{StaticResource NumericButtonStyle12}"
                                       common:KeyboardShortcutManager.VirtualKey="{utils:ResourceVirtualKey Name=cButton/[using:CalculatorApp.Common]KeyboardShortcutManager/VirtualKey}"
                                       common:KeyboardShortcutManager.VirtualKeyShiftChord="{utils:ResourceVirtualKey Name=cButton/[using:CalculatorApp.Common]KeyboardShortcutManager/VirtualKeyShiftChord}"
                                       AutomationProperties.AutomationId="cButton"
                                       AutomationProperties.Name="{utils:ResourceString Name=cButton/[using:Windows.UI.Xaml.Automation]AutomationProperties/Name}"
                                       ButtonId="C"
                                       Content="C"
                                       IsEnabled="{x:Bind Model.AreHEXButtonsEnabled, Mode=OneWay}"/>

            <controls:CalculatorButton x:Name="DButton"
                                       Grid.Row="3"
                                       Grid.Column="0"
                                       Style="{StaticResource NumericButtonStyle12}"
                                       common:KeyboardShortcutManager.VirtualKey="{utils:ResourceVirtualKey Name=dButton/[using:CalculatorApp.Common]KeyboardShortcutManager/VirtualKey}"
                                       common:KeyboardShortcutManager.VirtualKeyShiftChord="{utils:ResourceVirtualKey Name=dButton/[using:CalculatorApp.Common]KeyboardShortcutManager/VirtualKeyShiftChord}"
                                       AutomationProperties.AutomationId="dButton"
                                       AutomationProperties.Name="{utils:ResourceString Name=dButton/[using:Windows.UI.Xaml.Automation]AutomationProperties/Name}"
                                       ButtonId="D"
                                       Content="D"
                                       IsEnabled="{x:Bind Model.AreHEXButtonsEnabled, Mode=OneWay}"/>

            <controls:CalculatorButton x:Name="EButton"
                                       Grid.Row="4"
                                       Grid.Column="0"
                                       Style="{StaticResource NumericButtonStyle12}"
                                       common:KeyboardShortcutManager.VirtualKey="{utils:ResourceVirtualKey Name=eButton/[using:CalculatorApp.Common]KeyboardShortcutManager/VirtualKey}"
                                       common:KeyboardShortcutManager.VirtualKeyShiftChord="{utils:ResourceVirtualKey Name=eButton/[using:CalculatorApp.Common]KeyboardShortcutManager/VirtualKeyShiftChord}"
                                       AutomationProperties.AutomationId="eButton"
                                       AutomationProperties.Name="{utils:ResourceString Name=eButton/[using:Windows.UI.Xaml.Automation]AutomationProperties/Name}"
                                       ButtonId="E"
                                       Content="E"
                                       IsEnabled="{x:Bind Model.AreHEXButtonsEnabled, Mode=OneWay}"/>

            <controls:CalculatorButton x:Name="FButton"
                                       Grid.Row="5"
                                       Grid.Column="0"
                                       Style="{StaticResource NumericButtonStyle12}"
                                       common:KeyboardShortcutManager.VirtualKey="{utils:ResourceVirtualKey Name=fButton/[using:CalculatorApp.Common]KeyboardShortcutManager/VirtualKey}"
                                       common:KeyboardShortcutManager.VirtualKeyShiftChord="{utils:ResourceVirtualKey Name=fButton/[using:CalculatorApp.Common]KeyboardShortcutManager/VirtualKeyShiftChord}"
                                       AutomationProperties.AutomationId="fButton"
                                       AutomationProperties.Name="{utils:ResourceString Name=fButton/[using:Windows.UI.Xaml.Automation]AutomationProperties/Name}"
                                       ButtonId="F"
                                       Content="F"
                                       IsEnabled="{x:Bind Model.AreHEXButtonsEnabled, Mode=OneWay}"/>

            <local:NumberPad x:Name="NumberPad"
                             Grid.Row="2"
                             Grid.RowSpan="4"
                             Grid.Column="1"
                             Grid.ColumnSpan="3"
                             ButtonStyle="{StaticResource NumericButtonStyle18}"
                             CurrentRadixType="{x:Bind Model.CurrentRadixType, Mode=OneWay}"/>
        </Grid>

        <controls:CalculatorButton x:Name="NegateButton"
                                   Grid.Row="6"
                                   Grid.Column="2"
                                   Style="{StaticResource SymbolOperatorKeypadButtonStyle}"
                                   FontSize="12"
                                   FontWeight="Normal"
                                   common:KeyboardShortcutManager.VirtualKey="{utils:ResourceVirtualKey Name=negateButton/[using:CalculatorApp.Common]KeyboardShortcutManager/VirtualKey}"
                                   AutomationProperties.AutomationId="negateButton"
                                   AutomationProperties.Name="{utils:ResourceString Name=negateButton/[using:Windows.UI.Xaml.Automation]AutomationProperties/Name}"
                                   ButtonId="Negate"
                                   Content="&#xF898;"/>
    </Grid>
</UserControl>
