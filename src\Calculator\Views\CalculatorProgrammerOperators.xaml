<UserControl x:Class="CalculatorApp.CalculatorProgrammerOperators"
             xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
             xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
             xmlns:common="using:CalculatorApp.Common"
             xmlns:contract7NotPresent="http://schemas.microsoft.com/winfx/2006/xaml/presentation?IsApiContractNotPresent(Windows.Foundation.UniversalApiContract,7)"
             xmlns:contract7Present="http://schemas.microsoft.com/winfx/2006/xaml/presentation?IsApiContractPresent(Windows.Foundation.UniversalApiContract,7)"
             xmlns:controls="using:CalculatorApp.Controls"
             xmlns:d="http://schemas.microsoft.com/expression/blend/2008"
             xmlns:designdata="using:CalculatorApp.DesignData"
             xmlns:local="using:CalculatorApp"
             xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006"
             xmlns:utils="using:CalculatorApp.Utils"
             x:Name="ControlRoot"
             d:DesignHeight="400"
             d:DesignWidth="315"
             mc:Ignorable="d">

    <UserControl.Resources>
        <!-- Programmer mode display radio button -->
        <Style x:Key="ProgModeRadioButtonStyle" TargetType="RadioButton">
            <Setter Property="Foreground" Value="{ThemeResource TextFillColorPrimaryBrush}"/>
            <Setter Property="MinHeight" Value="12"/>
            <Setter Property="MinWidth" Value="280"/>
            <Setter Property="MaxHeight" Value="60"/>
            <Setter Property="VerticalAlignment" Value="Stretch"/>
            <Setter Property="HorizontalAlignment" Value="Stretch"/>
            <Setter Property="Padding" Value="8,0,0,0"/>
            <Setter Property="Typography.NumeralAlignment" Value="Tabular"/>
            <Setter Property="FontSize" Value="{ThemeResource CaptionFontSize}"/>
            <Setter Property="FontWeight" Value="Normal"/>
            <Setter Property="IsTextScaleFactorEnabled" Value="True"/>
            <Setter Property="FocusVisualMargin" Value="-3"/>
            <Setter Property="Template">
                <Setter.Value>
                    <ControlTemplate TargetType="RadioButton">
                        <Grid x:Name="ControlRoot"
                              VerticalAlignment="Stretch"
                              Background="{ThemeResource SubtleFillColorTransparentBrush}"
                              contract7Present:CornerRadius="{TemplateBinding CornerRadius}">
                            <VisualStateManager.VisualStateGroups>
                                <VisualStateGroup x:Name="CommonStates">
                                    <VisualState x:Name="Normal"/>
                                    <VisualState x:Name="PointerOver">
                                        <VisualState.Setters>
                                            <Setter Target="ControlRoot.Background" Value="{ThemeResource SubtleFillColorSecondaryBrush}"/>
                                        </VisualState.Setters>
                                    </VisualState>
                                    <VisualState x:Name="Pressed">
                                        <VisualState.Setters>
                                            <Setter Target="ControlRoot.Background" Value="{ThemeResource SubtleFillColorTertiaryBrush}"/>
                                            <Setter Target="ContentPresenter.Foreground" Value="{ThemeResource TextFillColorSecondaryBrush}"/>
                                            <Setter Target="LabelPresenter.Foreground" Value="{ThemeResource TextFillColorSecondaryBrush}"/>
                                        </VisualState.Setters>
                                    </VisualState>
                                    <VisualState x:Name="Disabled">
                                        <VisualState.Setters>
                                            <Setter Target="RevealBackplate.Visibility" Value="Collapsed"/>
                                        </VisualState.Setters>
                                    </VisualState>
                                </VisualStateGroup>
                                <VisualStateGroup x:Name="CheckStates">
                                    <VisualState x:Name="Checked">
                                        <VisualState.Setters>
                                            <Setter Target="CheckedStatesGrid.Opacity" Value="1"/>
                                            <Setter Target="NormalStatesGrid.Opacity" Value="0"/>
                                        </VisualState.Setters>
                                    </VisualState>
                                    <VisualState x:Name="Unchecked"/>
                                    <VisualState x:Name="Indeterminate"/>
                                </VisualStateGroup>
                            </VisualStateManager.VisualStateGroups>

                            <Border x:Name="RevealBackplate"
                                    Margin="0,-1,0,0"
                                    Background="{ThemeResource ListViewItemRevealBackground}"
                                    BorderBrush="{ThemeResource ListViewItemRevealBorderBrush}"
                                    BorderThickness="0,1,0,1"/>

                            <Grid x:Name="NormalStatesGrid">
                                <Grid.ColumnDefinitions>
                                    <ColumnDefinition Width="3"/>
                                    <ColumnDefinition Width="Auto" MinWidth="48"/>
                                    <ColumnDefinition Width="*"/>
                                </Grid.ColumnDefinitions>
                                <TextBlock x:Name="ContentPresenter"
                                           Grid.Column="2"
                                           Margin="4,0,4,0"
                                           HorizontalAlignment="Left"
                                           VerticalAlignment="Center"
                                           Foreground="{ThemeResource SystemControlForegroundBaseHighBrush}"
                                           FontFamily="{TemplateBinding FontFamily}"
                                           FontSize="{TemplateBinding FontSize}"
                                           FontWeight="{TemplateBinding FontWeight}"
                                           AutomationProperties.AccessibilityView="Raw"
                                           IsTextScaleFactorEnabled="{TemplateBinding IsTextScaleFactorEnabled}"
                                           Text="{TemplateBinding Content}"
                                           TextWrapping="Wrap"/>
                                <TextBlock x:Name="LabelPresenter"
                                           Grid.Column="1"
                                           Margin="{TemplateBinding Padding}"
                                           HorizontalAlignment="Left"
                                           VerticalAlignment="Center"
                                           Foreground="{ThemeResource SystemControlForegroundBaseHighBrush}"
                                           FontFamily="{TemplateBinding FontFamily}"
                                           FontSize="{TemplateBinding FontSize}"
                                           FontWeight="{TemplateBinding FontWeight}"
                                           AutomationProperties.AccessibilityView="Raw"
                                           IsTextScaleFactorEnabled="{TemplateBinding IsTextScaleFactorEnabled}"
                                           Text="{TemplateBinding Tag}"/>
                            </Grid>
                            <Grid x:Name="CheckedStatesGrid"
                                  Background="{ThemeResource SystemControlHighlightTransparentBrush}"
                                  Opacity="0">
                                <Grid.ColumnDefinitions>
                                    <ColumnDefinition Width="3"/>
                                    <ColumnDefinition Width="Auto" MinWidth="48"/>
                                    <ColumnDefinition Width="*"/>
                                </Grid.ColumnDefinitions>
                                <Rectangle x:Name="CheckedRectangle"
                                           Grid.Column="0"
                                           MaxHeight="16"
                                           Margin="0,5,0,5"
                                           HorizontalAlignment="Stretch"
                                           VerticalAlignment="Stretch"
                                           Fill="{ThemeResource AccentFillColorDefaultBrush}"
                                           RadiusX="1.5"
                                           RadiusY="1.5"/>
                                <TextBlock x:Name="CheckedContentPresenter"
                                           Grid.Column="2"
                                           Margin="4,0,4,0"
                                           HorizontalAlignment="Left"
                                           VerticalAlignment="Center"
                                           Foreground="{ThemeResource SystemControlHighlightAltBaseHighBrush}"
                                           FontFamily="{TemplateBinding FontFamily}"
                                           FontSize="{TemplateBinding FontSize}"
                                           FontWeight="{TemplateBinding FontWeight}"
                                           AutomationProperties.AccessibilityView="Raw"
                                           IsTextScaleFactorEnabled="{TemplateBinding IsTextScaleFactorEnabled}"
                                           Text="{TemplateBinding Content}"
                                           TextWrapping="Wrap"/>
                                <TextBlock x:Name="CheckedLabelPresenter"
                                           Grid.Column="1"
                                           Margin="{TemplateBinding Padding}"
                                           HorizontalAlignment="Left"
                                           VerticalAlignment="Center"
                                           Foreground="{ThemeResource SystemControlHighlightAltBaseHighBrush}"
                                           FontFamily="{TemplateBinding FontFamily}"
                                           FontSize="{TemplateBinding FontSize}"
                                           FontWeight="{TemplateBinding FontWeight}"
                                           AutomationProperties.AccessibilityView="Raw"
                                           IsTextScaleFactorEnabled="{TemplateBinding IsTextScaleFactorEnabled}"
                                           Text="{TemplateBinding Tag}"/>
                            </Grid>
                        </Grid>
                    </ControlTemplate>
                </Setter.Value>
            </Setter>
        </Style>
        <contract7NotPresent:Style x:Key="ConditionalProgModeRadioButtonStyle"
                                   BasedOn="{StaticResource ProgModeRadioButtonStyle}"
                                   TargetType="RadioButton"/>
        <contract7Present:Style x:Key="ConditionalProgModeRadioButtonStyle"
                                BasedOn="{StaticResource ProgModeRadioButtonStyle}"
                                TargetType="RadioButton">
            <Setter Property="CornerRadius" Value="{ThemeResource ControlCornerRadius}"/>
        </contract7Present:Style>

        <MenuFlyout x:Key="ProgrammerOperatorsContextMenu" x:Name="ProgrammerOperatorsContextMenu">
            <MenuFlyoutItem x:Name="CopyMenuItem"
                            x:Uid="CopyMenuItem"
                            Click="OnCopyMenuItemClicked"
                            Icon="Copy"/>
        </MenuFlyout>
    </UserControl.Resources>
    <Grid x:Name="ProgrammerOperators"
          MaxHeight="244"
          Margin="4,0"
          AutomationProperties.HeadingLevel="Level1"
          AutomationProperties.Name="{utils:ResourceString Name=RadixGroup/[using:Windows.UI.Xaml.Automation]AutomationProperties/Name}"
          FlowDirection="LeftToRight">
        <Grid.RowDefinitions>
            <RowDefinition Height="1*" MinHeight="0"/>
            <RowDefinition Height="1*" MinHeight="0"/>
            <RowDefinition Height="1*" MinHeight="0"/>
            <RowDefinition Height="1*" MinHeight="{Binding ActualHeight, ElementName=BinaryButton, FallbackValue=16, Mode=OneWay}"/>
            <RowDefinition Height="4"/>
        </Grid.RowDefinitions>
        <VisualStateManager.VisualStateGroups>
            <VisualStateGroup>
                <VisualState x:Name="LargeLayout">
                    <VisualState.StateTriggers>
                        <AdaptiveTrigger MinWindowHeight="768" MinWindowWidth="1024"/>
                    </VisualState.StateTriggers>
                </VisualState>
                <VisualState x:Name="MinSizeLayout">
                    <VisualState.StateTriggers>
                        <AdaptiveTrigger MinWindowHeight="{StaticResource AppMinWindowHeight}" MinWindowWidth="{StaticResource AppMinWindowWidth}"/>
                    </VisualState.StateTriggers>
                </VisualState>
                <VisualState x:Name="DefaultLayout">
                    <VisualState.StateTriggers>
                        <AdaptiveTrigger MinWindowHeight="0" MinWindowWidth="0"/>
                    </VisualState.StateTriggers>
                </VisualState>
            </VisualStateGroup>
        </VisualStateManager.VisualStateGroups>
        <controls:RadixButton x:Name="HexButton"
                              HorizontalAlignment="Stretch"
                              Style="{ThemeResource ConditionalProgModeRadioButtonStyle}"
                              common:KeyboardShortcutManager.VirtualKey="{utils:ResourceVirtualKey Name=hexButton/[using:CalculatorApp.Common]KeyboardShortcutManager/VirtualKey}"
                              AutomationProperties.AutomationId="hexButton"
                              AutomationProperties.Name="{x:Bind Model.HexDisplayValue_AutomationName, Mode=OneWay}"
                              Checked="HexButtonChecked"
                              Content="{x:Bind Model.HexDisplayValue, Mode=OneWay}"
                              ContextFlyout="{StaticResource ProgrammerOperatorsContextMenu}"
                              GroupName="BaseConversion"
                              Tag="{utils:ResourceString Name=hexButton/Tag}"/>
        <controls:RadixButton x:Name="DecimalButton"
                              Grid.Row="1"
                              HorizontalAlignment="Stretch"
                              Style="{ThemeResource ConditionalProgModeRadioButtonStyle}"
                              common:KeyboardShortcutManager.VirtualKey="{utils:ResourceVirtualKey Name=decimalButton/[using:CalculatorApp.Common]KeyboardShortcutManager/VirtualKey}"
                              AutomationProperties.AutomationId="decimalButton"
                              AutomationProperties.Name="{x:Bind Model.DecDisplayValue_AutomationName, Mode=OneWay}"
                              Checked="DecButtonChecked"
                              Content="{x:Bind Model.DecimalDisplayValue, Mode=OneWay}"
                              ContextFlyout="{StaticResource ProgrammerOperatorsContextMenu}"
                              GroupName="BaseConversion"
                              IsChecked="true"
                              Tag="{utils:ResourceString Name=decimalButton/Tag}"/>
        <controls:RadixButton x:Name="OctButton"
                              Grid.Row="2"
                              HorizontalAlignment="Stretch"
                              Style="{ThemeResource ConditionalProgModeRadioButtonStyle}"
                              common:KeyboardShortcutManager.VirtualKey="{utils:ResourceVirtualKey Name=octButton/[using:CalculatorApp.Common]KeyboardShortcutManager/VirtualKey}"
                              AutomationProperties.AutomationId="octolButton"
                              AutomationProperties.Name="{x:Bind Model.OctDisplayValue_AutomationName, Mode=OneWay}"
                              Checked="OctButtonChecked"
                              Content="{x:Bind Model.OctalDisplayValue, Mode=OneWay}"
                              ContextFlyout="{StaticResource ProgrammerOperatorsContextMenu}"
                              GroupName="BaseConversion"
                              Tag="{utils:ResourceString Name=octButton/Tag}"/>
        <controls:RadixButton x:Name="BinaryButton"
                              Grid.Row="3"
                              HorizontalAlignment="Stretch"
                              Style="{ThemeResource ConditionalProgModeRadioButtonStyle}"
                              common:KeyboardShortcutManager.VirtualKey="{utils:ResourceVirtualKey Name=binaryButton/[using:CalculatorApp.Common]KeyboardShortcutManager/VirtualKey}"
                              AutomationProperties.AutomationId="binaryButton"
                              AutomationProperties.Name="{x:Bind Model.BinDisplayValue_AutomationName, Mode=OneWay}"
                              Checked="BinButtonChecked"
                              Content="{x:Bind Model.BinaryDisplayValue, Mode=OneWay}"
                              ContextFlyout="{StaticResource ProgrammerOperatorsContextMenu}"
                              GroupName="BaseConversion"
                              Tag="{utils:ResourceString Name=binaryButton/Tag}"/>
    </Grid>
</UserControl>
