---
name: Localization Suggestion
about: Report a problem or suggested change to Calculator's localized content.
title: '[Localization] '
labels: 'Area: World-Readiness'
assignees: ''
---
<!--
PLEASE NOTE: 
We cannot _merge_ any suggested localization changes to our localized resources files. These files are automatically generated from an internal localization process.  Any suggestion submitted this way will be duplicated into our internal localization system, and then closed here.

Alternatively, you can launch feedback-hub://, click on the "Language Community" tab on the left-side of the app, and follow the steps to submit a localization suggestion that way.  (The "Language Community" tab currently will only be visible if your system is running a non-English language).

Before filing a bug
- Ensure the bug reproduces on the latest version of the app.
- Search existing issues and make sure this issue is not already filed.
-->

**Describe the bug**
<!-- A clear and concise description of what the bug is. -->

**Steps To Reproduce**
<!--
Steps to reproduce the behavior:
1. Go to '...'
2. Click on '....'
3. Scroll down to '....'
4. See error
-->

**Expected behavior**
<!-- A clear and concise description of what you expected to happen. -->

**Screenshots**
<!-- If applicable, add screenshots to help explain your problem. -->

**Device and Application Information**
 - OS Build:
 - Architecture:
 - Application Version:
 - Region: 
 - Dev Version Installed: 
 
<!--
Run the following commands in Powershell and copy/paste the output.
" - OS Build: $([Environment]::OSVersion.Version)"
" - Architecture: $((Get-AppxPackage -Name Microsoft.WindowsCalculator).Architecture)"
" - Application Version: $((Get-AppxPackage -Name Microsoft.WindowsCalculator).Version)"
" - Region: $((Get-Culture).Name)"
" - Dev Version Installed: $($null -ne (Get-AppxPackage -Name Microsoft.WindowsCalculator.Dev))"
-->

**Additional context**
<!-- Add any other context about the problem here. -->
