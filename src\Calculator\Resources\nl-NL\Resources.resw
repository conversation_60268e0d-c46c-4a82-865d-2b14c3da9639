﻿<?xml version="1.0" encoding="utf-8"?>
<root>
  <!-- 
    Microsoft ResX Schema 
    
    Version 2.0
    
    The primary goals of this format is to allow a simple XML format 
    that is mostly human readable. The generation and parsing of the 
    various data types are done through the TypeConverter classes 
    associated with the data types.
    
    Example:
    
    ... ado.net/XML headers & schema ...
    <resheader name="resmimetype">text/microsoft-resx</resheader>
    <resheader name="version">2.0</resheader>
    <resheader name="reader">System.Resources.ResXResourceReader, System.Windows.Forms, ...</resheader>
    <resheader name="writer">System.Resources.ResXResourceWriter, System.Windows.Forms, ...</resheader>
    <data name="Name1"><value>this is my long string</value><comment>this is a comment</comment></data>
    <data name="Color1" type="System.Drawing.Color, System.Drawing">Blue</data>
    <data name="Bitmap1" mimetype="application/x-microsoft.net.object.binary.base64">
        <value>[base64 mime encoded serialized .NET Framework object]</value>
    </data>
    <data name="Icon1" type="System.Drawing.Icon, System.Drawing" mimetype="application/x-microsoft.net.object.bytearray.base64">
        <value>[base64 mime encoded string representing a byte array form of the .NET Framework object]</value>
        <comment>This is a comment</comment>
    </data>
                
    There are any number of "resheader" rows that contain simple 
    name/value pairs.
    
    Each data row contains a name, and value. The row also contains a 
    type or mimetype. Type corresponds to a .NET class that support 
    text/value conversion through the TypeConverter architecture. 
    Classes that don't support this are serialized and stored with the 
    mimetype set.
    
    The mimetype is used for serialized objects, and tells the 
    ResXResourceReader how to depersist the object. This is currently not 
    extensible. For a given mimetype the value must be set accordingly:
    
    Note - application/x-microsoft.net.object.binary.base64 is the format 
    that the ResXResourceWriter will generate, however the reader can 
    read any of the formats listed below.
    
    mimetype: application/x-microsoft.net.object.binary.base64
    value   : The object must be serialized with 
            : System.Runtime.Serialization.Formatters.Binary.BinaryFormatter
            : and then encoded with base64 encoding.
    
    mimetype: application/x-microsoft.net.object.soap.base64
    value   : The object must be serialized with 
            : System.Runtime.Serialization.Formatters.Soap.SoapFormatter
            : and then encoded with base64 encoding.

    mimetype: application/x-microsoft.net.object.bytearray.base64
    value   : The object must be serialized into a byte array 
            : using a System.ComponentModel.TypeConverter
            : and then encoded with base64 encoding.
    -->
  <xsd:schema id="root" xmlns="" xmlns:xsd="http://www.w3.org/2001/XMLSchema" xmlns:msdata="urn:schemas-microsoft-com:xml-msdata">
    <xsd:import namespace="http://www.w3.org/XML/1998/namespace" />
    <xsd:element name="root" msdata:IsDataSet="true">
      <xsd:complexType>
        <xsd:choice maxOccurs="unbounded">
          <xsd:element name="metadata">
            <xsd:complexType>
              <xsd:sequence>
                <xsd:element name="value" type="xsd:string" minOccurs="0" />
              </xsd:sequence>
              <xsd:attribute name="name" use="required" type="xsd:string" />
              <xsd:attribute name="type" type="xsd:string" />
              <xsd:attribute name="mimetype" type="xsd:string" />
              <xsd:attribute ref="xml:space" />
            </xsd:complexType>
          </xsd:element>
          <xsd:element name="assembly">
            <xsd:complexType>
              <xsd:attribute name="alias" type="xsd:string" />
              <xsd:attribute name="name" type="xsd:string" />
            </xsd:complexType>
          </xsd:element>
          <xsd:element name="data">
            <xsd:complexType>
              <xsd:sequence>
                <xsd:element name="value" type="xsd:string" minOccurs="0" msdata:Ordinal="1" />
                <xsd:element name="comment" type="xsd:string" minOccurs="0" msdata:Ordinal="2" />
              </xsd:sequence>
              <xsd:attribute name="name" type="xsd:string" use="required" msdata:Ordinal="1" />
              <xsd:attribute name="type" type="xsd:string" msdata:Ordinal="3" />
              <xsd:attribute name="mimetype" type="xsd:string" msdata:Ordinal="4" />
              <xsd:attribute ref="xml:space" />
            </xsd:complexType>
          </xsd:element>
          <xsd:element name="resheader">
            <xsd:complexType>
              <xsd:sequence>
                <xsd:element name="value" type="xsd:string" minOccurs="0" msdata:Ordinal="1" />
              </xsd:sequence>
              <xsd:attribute name="name" type="xsd:string" use="required" />
            </xsd:complexType>
          </xsd:element>
        </xsd:choice>
      </xsd:complexType>
    </xsd:element>
  </xsd:schema>
  <resheader name="resmimetype">
    <value>text/microsoft-resx</value>
  </resheader>
  <resheader name="version">
    <value>2.0</value>
  </resheader>
  <resheader name="reader">
    <value>System.Resources.ResXResourceReader, System.Windows.Forms, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </resheader>
  <resheader name="writer">
    <value>System.Resources.ResXResourceWriter, System.Windows.Forms, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </resheader>
  <data name="AppName" xml:space="preserve">
    <value>Rekenmachine</value>
    <comment>{@Appx_ShortDisplayName@}{StringCategory="Feature Title"} This is the title of the official application when published through Windows Store.</comment>
  </data>
  <data name="DevAppName" xml:space="preserve">
    <value>Rekenmachine [Dev]</value>
    <comment>{@Appx_ShortDisplayName@}{StringCategory="Feature Title"} This is the name of the application when built by a user via GitHub. We use a different name to make it easier for users to distinguish the apps when both this version and the Store version are installed on the same device.</comment>
  </data>
  <data name="AppStoreName" xml:space="preserve">
    <value>Windows Rekenmachine</value>
    <comment>{@Appx_DisplayName@}{StringCategory="Feature Title"} Name that shows up in the app store. It contains "Windows" to distinguish it from 3rd party calculator apps.</comment>
  </data>
  <data name="DevAppStoreName" xml:space="preserve">
    <value>Windows Rekenmachine [Dev]</value>
    <comment>{@Appx_DisplayName@}{StringCategory="Feature Title"} Name that shows up in the app store. It contains "Windows" to distinguish it from 3rd party calculator apps. This is the the version of the name used when the app is built by a user via GitHub.</comment>
  </data>
  <data name="AppDescription" xml:space="preserve">
    <value>Rekenmachine</value>
    <comment>{@Appx_Description@} This description is used for the official application when published through Windows Store.</comment>
  </data>
  <data name="DevAppDescription" xml:space="preserve">
    <value>Rekenmachine [Dev]</value>
    <comment>{@Appx_Description@} This is the description of the application when built by a user via GitHub. We use a different description to make it easier for users to distinguish the apps when both this version and the Store version are installed on the same device.</comment>
  </data>
  <data name="copyMenuItem" xml:space="preserve">
    <value>Kopie</value>
    <comment>Copy context menu string</comment>
  </data>
  <data name="pasteMenuItem" xml:space="preserve">
    <value>Plakken</value>
    <comment>Paste context menu string</comment>
  </data>
  <data name="SupplementaryResultsHeader.Text" xml:space="preserve">
    <value>Ongeveer gelijk aan</value>
    <comment>The text that shows at the bottom of the converter to head the supplementary results. Indicates that the main result is approximately equal to the supplementary results.</comment>
  </data>
  <data name="BitFlipItemAutomationName" xml:space="preserve">
    <value>%1, waarde %2</value>
    <comment>{Locked="%1","%2"}. String used in automation name for each bit in bit flip. %1 will be replaced by the position of the bit (1st bit, 3rd bit), %2 by a binary value (1 or 0)</comment>
  </data>
  <data name="BitPosition" xml:space="preserve">
    <value>%1 bit</value>
    <comment>{Locked="%1"}. Sub-string used to indicate the position of a bit (e.g. 1st bit, 2nd bit...)</comment>
  </data>
  <data name="63" xml:space="preserve">
    <value>63ste</value>
    <comment>Sub-string used in automation name for 63 bit in bit flip</comment>
  </data>
  <data name="62" xml:space="preserve">
    <value>62ste</value>
    <comment>Sub-string used in automation name for 62 bit in bit flip</comment>
  </data>
  <data name="61" xml:space="preserve">
    <value>61ste</value>
    <comment>Sub-string used in automation name for 61 bit in bit flip</comment>
  </data>
  <data name="60" xml:space="preserve">
    <value>60ste</value>
    <comment>Sub-string used in automation name for 60 bit in bit flip</comment>
  </data>
  <data name="59" xml:space="preserve">
    <value>59ste</value>
    <comment>Sub-string used in automation name for 59 bit in bit flip</comment>
  </data>
  <data name="58" xml:space="preserve">
    <value>58ste</value>
    <comment>Sub-string used in automation name for 58 bit in bit flip</comment>
  </data>
  <data name="57" xml:space="preserve">
    <value>57ste</value>
    <comment>Sub-string used in automation name for 57 bit in bit flip</comment>
  </data>
  <data name="56" xml:space="preserve">
    <value>56ste</value>
    <comment>Sub-string used in automation name for 56 bit in bit flip</comment>
  </data>
  <data name="55" xml:space="preserve">
    <value>55ste</value>
    <comment>Sub-string used in automation name for 55 bit in bit flip</comment>
  </data>
  <data name="54" xml:space="preserve">
    <value>54ste</value>
    <comment>Sub-string used in automation name for 54 bit in bit flip</comment>
  </data>
  <data name="53" xml:space="preserve">
    <value>53ste</value>
    <comment>Sub-string used in automation name for 53 bit in bit flip</comment>
  </data>
  <data name="52" xml:space="preserve">
    <value>52ste</value>
    <comment>Sub-string used in automation name for 52 bit in bit flip</comment>
  </data>
  <data name="51" xml:space="preserve">
    <value>51ste</value>
    <comment>Sub-string used in automation name for 51 bit in bit flip</comment>
  </data>
  <data name="50" xml:space="preserve">
    <value>50ste</value>
    <comment>Sub-string used in automation name for 50 bit in bit flip</comment>
  </data>
  <data name="49" xml:space="preserve">
    <value>49ste</value>
    <comment>Sub-string used in automation name for 49 bit in bit flip</comment>
  </data>
  <data name="48" xml:space="preserve">
    <value>48ste</value>
    <comment>Sub-string used in automation name for 48 bit in bit flip</comment>
  </data>
  <data name="47" xml:space="preserve">
    <value>47ste</value>
    <comment>Sub-string used in automation name for 47 bit in bit flip</comment>
  </data>
  <data name="46" xml:space="preserve">
    <value>46ste</value>
    <comment>Sub-string used in automation name for 46 bit in bit flip</comment>
  </data>
  <data name="45" xml:space="preserve">
    <value>45ste</value>
    <comment>Sub-string used in automation name for 45 bit in bit flip</comment>
  </data>
  <data name="44" xml:space="preserve">
    <value>44ste</value>
    <comment>Sub-string used in automation name for 44 bit in bit flip</comment>
  </data>
  <data name="43" xml:space="preserve">
    <value>43ste</value>
    <comment>Sub-string used in automation name for 43 bit in bit flip</comment>
  </data>
  <data name="42" xml:space="preserve">
    <value>42ste</value>
    <comment>Sub-string used in automation name for 42 bit in bit flip</comment>
  </data>
  <data name="41" xml:space="preserve">
    <value>41ste</value>
    <comment>Sub-string used in automation name for 41 bit in bit flip</comment>
  </data>
  <data name="40" xml:space="preserve">
    <value>40ste</value>
    <comment>Sub-string used in automation name for 40 bit in bit flip</comment>
  </data>
  <data name="39" xml:space="preserve">
    <value>39ste</value>
    <comment>Sub-string used in automation name for 39 bit in bit flip</comment>
  </data>
  <data name="38" xml:space="preserve">
    <value>38ste</value>
    <comment>Sub-string used in automation name for 38 bit in bit flip</comment>
  </data>
  <data name="37" xml:space="preserve">
    <value>37ste</value>
    <comment>Sub-string used in automation name for 37 bit in bit flip</comment>
  </data>
  <data name="36" xml:space="preserve">
    <value>36ste</value>
    <comment>Sub-string used in automation name for 36 bit in bit flip</comment>
  </data>
  <data name="35" xml:space="preserve">
    <value>35ste</value>
    <comment>Sub-string used in automation name for 35 bit in bit flip</comment>
  </data>
  <data name="34" xml:space="preserve">
    <value>34ste</value>
    <comment>Sub-string used in automation name for 34 bit in bit flip</comment>
  </data>
  <data name="33" xml:space="preserve">
    <value>33ste</value>
    <comment>Sub-string used in automation name for 33 bit in bit flip</comment>
  </data>
  <data name="32" xml:space="preserve">
    <value>32ste</value>
    <comment>Sub-string used in automation name for 32 bit in bit flip</comment>
  </data>
  <data name="31" xml:space="preserve">
    <value>31ste</value>
    <comment>Sub-string used in automation name for 31 bit in bit flip</comment>
  </data>
  <data name="30" xml:space="preserve">
    <value>30ste</value>
    <comment>Sub-string used in automation name for 30 bit in bit flip</comment>
  </data>
  <data name="29" xml:space="preserve">
    <value>29ste</value>
    <comment>Sub-string used in automation name for 29 bit in bit flip</comment>
  </data>
  <data name="28" xml:space="preserve">
    <value>28ste</value>
    <comment>Sub-string used in automation name for 28 bit in bit flip</comment>
  </data>
  <data name="27" xml:space="preserve">
    <value>27ste</value>
    <comment>Sub-string used in automation name for 27 bit in bit flip</comment>
  </data>
  <data name="26" xml:space="preserve">
    <value>26ste</value>
    <comment>Sub-string used in automation name for 26 bit in bit flip</comment>
  </data>
  <data name="25" xml:space="preserve">
    <value>25ste</value>
    <comment>Sub-string used in automation name for 25 bit in bit flip</comment>
  </data>
  <data name="24" xml:space="preserve">
    <value>24ste</value>
    <comment>Sub-string used in automation name for 24 bit in bit flip</comment>
  </data>
  <data name="23" xml:space="preserve">
    <value>23ste</value>
    <comment>Sub-string used in automation name for 23 bit in bit flip</comment>
  </data>
  <data name="22" xml:space="preserve">
    <value>22ste</value>
    <comment>Sub-string used in automation name for 22 bit in bit flip</comment>
  </data>
  <data name="21" xml:space="preserve">
    <value>21ste</value>
    <comment>Sub-string used in automation name for 21 bit in bit flip</comment>
  </data>
  <data name="20" xml:space="preserve">
    <value>20ste</value>
    <comment>Sub-string used in automation name for 20 bit in bit flip</comment>
  </data>
  <data name="19" xml:space="preserve">
    <value>19de</value>
    <comment>Sub-string used in automation name for 19 bit in bit flip</comment>
  </data>
  <data name="18" xml:space="preserve">
    <value>18de</value>
    <comment>Sub-string used in automation name for 18 bit in bit flip</comment>
  </data>
  <data name="17" xml:space="preserve">
    <value>17de</value>
    <comment>Sub-string used in automation name for 17 bit in bit flip</comment>
  </data>
  <data name="16" xml:space="preserve">
    <value>16de</value>
    <comment>Sub-string used in automation name for 16 bit in bit flip</comment>
  </data>
  <data name="15" xml:space="preserve">
    <value>15de</value>
    <comment>Sub-string used in automation name for 15 bit in bit flip</comment>
  </data>
  <data name="14" xml:space="preserve">
    <value>14de</value>
    <comment>Sub-string used in automation name for 14 bit in bit flip</comment>
  </data>
  <data name="13" xml:space="preserve">
    <value>13de</value>
    <comment>Sub-string used in automation name for 13 bit in bit flip</comment>
  </data>
  <data name="12" xml:space="preserve">
    <value>12de</value>
    <comment>Sub-string used in automation name for 12 bit in bit flip</comment>
  </data>
  <data name="11" xml:space="preserve">
    <value>11de</value>
    <comment>Sub-string used in automation name for 11 bit in bit flip</comment>
  </data>
  <data name="10" xml:space="preserve">
    <value>10de</value>
    <comment>Sub-string used in automation name for 10 bit in bit flip</comment>
  </data>
  <data name="9" xml:space="preserve">
    <value>9de</value>
    <comment>Sub-string used in automation name for 9 bit in bit flip</comment>
  </data>
  <data name="8" xml:space="preserve">
    <value>8ste</value>
    <comment>Sub-string used in automation name for 8 bit in bit flip</comment>
  </data>
  <data name="7" xml:space="preserve">
    <value>7de</value>
    <comment>Sub-string used in automation name for 7 bit in bit flip</comment>
  </data>
  <data name="6" xml:space="preserve">
    <value>6de</value>
    <comment>Sub-string used in automation name for 6 bit in bit flip</comment>
  </data>
  <data name="5" xml:space="preserve">
    <value>5de</value>
    <comment>Sub-string used in automation name for 5 bit in bit flip</comment>
  </data>
  <data name="4" xml:space="preserve">
    <value>4de</value>
    <comment>Sub-string used in automation name for 4 bit in bit flip</comment>
  </data>
  <data name="3" xml:space="preserve">
    <value>3de</value>
    <comment>Sub-string used in automation name for 3 bit in bit flip</comment>
  </data>
  <data name="2" xml:space="preserve">
    <value>2de</value>
    <comment>Sub-string used in automation name for 2 bit in bit flip</comment>
  </data>
  <data name="1" xml:space="preserve">
    <value>1ste</value>
    <comment>Sub-string used in automation name for 1 bit in bit flip</comment>
  </data>
  <data name="LeastSignificantBit" xml:space="preserve">
    <value>minst significante bit</value>
    <comment>Used to describe the first bit of a binary number. Used in bit flip</comment>
  </data>
  <data name="MemoryButton_Open" xml:space="preserve">
    <value>Geheugen-flyout openen</value>
    <comment>This is the automation name and label for the memory button when the memory flyout is closed.</comment>
  </data>
  <data name="MemoryButton_Close" xml:space="preserve">
    <value>Geheugen-flyout sluiten</value>
    <comment>This is the automation name and label for the memory button when the memory flyout is open.</comment>
  </data>
  <data name="AlwaysOnTop_Enter" xml:space="preserve">
    <value>Op voorgrond behouden</value>
    <comment>This is the tool tip automation name for the always-on-top button when out of always-on-top mode.</comment>
  </data>
  <data name="AlwaysOnTop_Exit" xml:space="preserve">
    <value>Terug naar volledige weergave</value>
    <comment>This is the tool tip automation name for the always-on-top button when in always-on-top mode.</comment>
  </data>
  <data name="MemoryButton.[using:Windows.UI.Xaml.Controls]ToolTipService.ToolTip" xml:space="preserve">
    <value>Geheugen</value>
    <comment>This is the tool tip automation name for the memory button.</comment>
  </data>
  <data name="HistoryButton.[using:Windows.UI.Xaml.Controls]ToolTipService.ToolTip" xml:space="preserve">
    <value>Geschiedenis (Ctrl+H)</value>
    <comment>This is the tool tip automation name for the history button.</comment>
  </data>
  <data name="bitFlip.[using:Windows.UI.Xaml.Controls]ToolTipService.ToolTip" xml:space="preserve">
    <value>Bit-wissel-toetsenblok</value>
    <comment>This is the tool tip automation name for the bitFlip button.</comment>
  </data>
  <data name="fullKeypad.[using:Windows.UI.Xaml.Controls]ToolTipService.ToolTip" xml:space="preserve">
    <value>Volledig toetsenblok</value>
    <comment>This is the tool tip automation name for the numberPad button.</comment>
  </data>
  <data name="ClearMemoryButton.[using:Windows.UI.Xaml.Controls]ToolTipService.ToolTip" xml:space="preserve">
    <value>Geheugen wissen (Ctrl+L)</value>
    <comment>This is the tool tip automation name for the Clear Memory (MC) button.</comment>
  </data>
  <data name="MemoryLabel.Text" xml:space="preserve">
    <value>Geheugen</value>
    <comment>The text that shows as the header for the memory list</comment>
  </data>
  <data name="MemoryPivotItem.[using:Windows.UI.Xaml.Automation]AutomationProperties.Name" xml:space="preserve">
    <value>Geheugen</value>
    <comment>The automation name for the Memory pivot item that is shown when Calculator is in wide layout.</comment>
  </data>
  <data name="HistoryLabel.Text" xml:space="preserve">
    <value>Geschiedenis</value>
    <comment>The text that shows as the header for the history list</comment>
  </data>
  <data name="HistoryPivotItem.[using:Windows.UI.Xaml.Automation]AutomationProperties.Name" xml:space="preserve">
    <value>Geschiedenis</value>
    <comment>The automation name for the History pivot item that is shown when Calculator is in wide layout.</comment>
  </data>
  <data name="converterModeButton.Content" xml:space="preserve">
    <value>Conversieprogramma</value>
    <comment>Label for a control that activates the unit converter mode.</comment>
  </data>
  <data name="scientificModeButton.Content" xml:space="preserve">
    <value>Wetenschappelijk</value>
    <comment>Label for a control that activates scientific mode calculator layout</comment>
  </data>
  <data name="standardModeButton.Content" xml:space="preserve">
    <value>Standaard</value>
    <comment>Label for a control that activates standard mode calculator layout.</comment>
  </data>
  <data name="converterModeButton.[using:Windows.UI.Xaml.Automation]AutomationProperties.Name" xml:space="preserve">
    <value>Conversiemodus</value>
    <comment>Screen reader prompt for a control that activates the unit converter mode.</comment>
  </data>
  <data name="scientificModeButton.[using:Windows.UI.Xaml.Automation]AutomationProperties.Name" xml:space="preserve">
    <value>Wetenschappelijke modus</value>
    <comment>Screen reader prompt for a control that activates scientific mode calculator layout</comment>
  </data>
  <data name="standardModeButton.[using:Windows.UI.Xaml.Automation]AutomationProperties.Name" xml:space="preserve">
    <value>Standaardmodus</value>
    <comment>Screen reader prompt for a control that activates standard mode calculator layout.</comment>
  </data>
  <data name="ClearHistory.Name" xml:space="preserve">
    <value>Alle geschiedenis wissen</value>
    <comment>"ClearHistory" used on the calculator history pane that stores the calculation history.</comment>
  </data>
  <data name="ClearHistory.[using:Windows.UI.Xaml.Controls]ToolTipService.ToolTip" xml:space="preserve">
    <value>Alle geschiedenis wissen</value>
    <comment>This is the tool tip automation name for the Clear History button.</comment>
  </data>
  <data name="HideHistory.Name" xml:space="preserve">
    <value>Verbergen</value>
    <comment>"HideHistory" used on the calculator history pane that stores the calculation history.</comment>
  </data>
  <data name="StandardModeText" xml:space="preserve">
    <value>Standaard</value>
    <comment>The text that shows in the dropdown navigation control in snapped mode when standard calculator mode is selected.</comment>
  </data>
  <data name="ScientificModeText" xml:space="preserve">
    <value>Wetenschappelijk</value>
    <comment>The text that shows in the dropdown navigation control in snapped mode when scientific calculator mode is selected.</comment>
  </data>
  <data name="ProgrammerModeText" xml:space="preserve">
    <value>Programmeur</value>
    <comment>The text that shows in the dropdown navigation control in snapped mode when programmer calculator mode is selected.</comment>
  </data>
  <data name="ConverterModeText" xml:space="preserve">
    <value>Conversieprogramma</value>
    <comment>The text that shows in the dropdown navigation control for the converter group. The previous key for this was "ConverterMode.Text".</comment>
  </data>
  <data name="CalculatorModeText" xml:space="preserve">
    <value>Rekenmachine</value>
    <comment>The text that shows in the dropdown navigation control for the calculator group.</comment>
  </data>
  <data name="ConverterModeTextCaps" xml:space="preserve">
    <value>Conversieprogramma</value>
    <comment>The text that shows in the dropdown navigation control for the converter group in upper case. The previous key for this was "ConverterMode.Text".</comment>
  </data>
  <data name="CalculatorModeTextCaps" xml:space="preserve">
    <value>Rekenmachine</value>
    <comment>The text that shows in the dropdown navigation control for the calculator group in upper case.</comment>
  </data>
  <data name="ConverterModePluralText" xml:space="preserve">
    <value>Conversieprogramma's</value>
    <comment>Pluralized version of the converter group text, used for the screen reader prompt.</comment>
  </data>
  <data name="CalculatorModePluralText" xml:space="preserve">
    <value>Rekenmachines</value>
    <comment>Pluralized version of the calculator group text, used for the screen reader prompt.</comment>
  </data>
  <data name="Format_CalculatorResults" xml:space="preserve">
    <value>Weergave is %1</value>
    <comment>{Locked="%1"}. Screen reader prompt for the Calculator results text block. %1 = Localized display value, e.g. "50".</comment>
  </data>
  <data name="Format_CalculatorAlwaysOnTopResults" xml:space="preserve">
    <value>Expressie is %1, huidige invoer is %2</value>
    <comment>{Locked="%1","%2"}. Screen reader prompt for the Calculator always-on-top expression. %1 = Expression, e.g. "50 + 2 - 60 +", %2 = Localized display value, e.g. "50".</comment>
  </data>
  <data name="Format_CalculatorResults_Decimal" xml:space="preserve">
    <value>Weergave is %1 punt</value>
    <comment>{Locked="%1"}. Automation label for the calculator display in the specific case where the user has just pressed the decimal separator button. For example, the user wants to input "7.5".  When they have input "7." they will hear "Display is 7 point". "point" should be localized to the locale's appropriate decimal separator.</comment>
  </data>
  <data name="Format_CalculatorExpression" xml:space="preserve">
    <value>Expressie is %1</value>
    <comment>{Locked="%1"}. Screen reader prompt for the Calculator expression results. %1 = Localized display value, e.g. "50 + 2 - 60 +".</comment>
  </data>
  <data name="Display_Copied" xml:space="preserve">
    <value>Schermwaarde gekopieerd naar klembord</value>
    <comment>Screen reader prompt for the Calculator display copy button, when the button is invoked.</comment>
  </data>
  <data name="HistoryPane" xml:space="preserve">
    <value>Geschiedenis</value>
    <comment>Screen reader prompt for the history flyout</comment>
  </data>
  <data name="MemoryPane" xml:space="preserve">
    <value>Geheugen</value>
    <comment>Screen reader prompt for the memory flyout</comment>
  </data>
  <data name="Format_HexButtonValue" xml:space="preserve">
    <value>Hexadecimaal %1</value>
    <comment>{Locked="%1"}. Screen reader prompt for the hexadecimal value in Programmer mode. %1 = the localized hexadecimal value, e.g. "21B4 8F73".</comment>
  </data>
  <data name="Format_DecButtonValue" xml:space="preserve">
    <value>Decimaal %1</value>
    <comment>{Locked="%1"}. Screen reader prompt for the decimal value in Programmer mode. %1 = the localized decimal value, e.g. "5,732".</comment>
  </data>
  <data name="Format_OctButtonValue" xml:space="preserve">
    <value>Octaal %1</value>
    <comment>{Locked="%1"}. Screen reader prompt for the octal value in Programmer mode. %1 = the localized octal value, e.g. "155 174".</comment>
  </data>
  <data name="Format_BinButtonValue" xml:space="preserve">
    <value>Binair %1</value>
    <comment>{Locked="%1"}. Screen reader prompt for the binary value in Programmer mode. %1 = the localized binary value, e.g. "0010 1011".</comment>
  </data>
  <data name="ClearHistory.[using:Windows.UI.Xaml.Automation]AutomationProperties.Name" xml:space="preserve">
    <value>Alle geschiedenis wissen</value>
    <comment>Screen reader prompt for the Calculator History Clear button</comment>
  </data>
  <data name="HistoryList_Cleared" xml:space="preserve">
    <value>Geschiedenis gewist</value>
    <comment>Screen reader prompt for the Calculator History Clear button, when the button is invoked.</comment>
  </data>
  <data name="HideHistory.[using:Windows.UI.Xaml.Automation]AutomationProperties.Name" xml:space="preserve">
    <value>Geschiedenis verbergen</value>
    <comment>Screen reader prompt for the Calculator History Hide button</comment>
  </data>
  <data name="HistoryButton_Open" xml:space="preserve">
    <value>Geschiedenis-flyout openen</value>
    <comment>Screen reader prompt for the Calculator History button, when the flyout is closed.</comment>
  </data>
  <data name="HistoryButton_Close" xml:space="preserve">
    <value>Geschiedenis-flyout sluiten</value>
    <comment>Screen reader prompt for the Calculator History button, when the flyout is open.</comment>
  </data>
  <data name="memButton.[using:Windows.UI.Xaml.Automation]AutomationProperties.Name" xml:space="preserve">
    <value>Opslaan in geheugen</value>
    <comment>Screen reader prompt for the Calculator Memory button</comment>
  </data>
  <data name="memButton.[using:Windows.UI.Xaml.Controls]ToolTipService.ToolTip" xml:space="preserve">
    <value>In geheugen opslaan (Ctrl+M)</value>
    <comment>This is the tool tip automation name for the Memory Store (MS) button.</comment>
  </data>
  <data name="ClearMemoryButton.[using:Windows.UI.Xaml.Automation]AutomationProperties.Name" xml:space="preserve">
    <value>Geheugen wissen</value>
    <comment>Screen reader prompt for the Calculator Clear Memory button</comment>
  </data>
  <data name="Memory_Cleared" xml:space="preserve">
    <value>Geheugen gewist</value>
    <comment>Screen reader prompt for the Calculator Clear Memory button, when the button is invoked.</comment>
  </data>
  <data name="MemRecall.[using:Windows.UI.Xaml.Automation]AutomationProperties.Name" xml:space="preserve">
    <value>Ophalen uit geheugen</value>
    <comment>Screen reader prompt for the Calculator Memory Recall button</comment>
  </data>
  <data name="MemRecall.[using:Windows.UI.Xaml.Controls]ToolTipService.ToolTip" xml:space="preserve">
    <value>Geheugenitem ophalen (Ctrl+R)</value>
    <comment>This is the tool tip automation name for the Memory Recall (MR) button.</comment>
  </data>
  <data name="MemPlus.[using:Windows.UI.Xaml.Automation]AutomationProperties.Name" xml:space="preserve">
    <value>Toevoegen aan geheugen</value>
    <comment>Screen reader prompt for the Calculator Memory Add button</comment>
  </data>
  <data name="MemPlus.[using:Windows.UI.Xaml.Controls]ToolTipService.ToolTip" xml:space="preserve">
    <value>Bij geheugenitem optellen (Ctrl+P)</value>
    <comment>This is the tool tip automation name for the Memory Add (M+) button.</comment>
  </data>
  <data name="MemMinus.[using:Windows.UI.Xaml.Automation]AutomationProperties.Name" xml:space="preserve">
    <value>Van geheugenitem aftrekken</value>
    <comment>Screen reader prompt for the Calculator Memory Subtract button</comment>
  </data>
  <data name="MemMinus.[using:Windows.UI.Xaml.Controls]ToolTipService.ToolTip" xml:space="preserve">
    <value>Van geheugenitem aftrekken (Ctrl+Q)</value>
    <comment>This is the tool tip automation name for the Memory Subtract (M-) button.</comment>
  </data>
  <data name="ClearMemoryItemButton.[using:Windows.UI.Xaml.Automation]AutomationProperties.Name" xml:space="preserve">
    <value>Geheugenitem wissen</value>
    <comment>Screen reader prompt for the Calculator Clear Memory button</comment>
  </data>
  <data name="ClearMemoryItemButton.[using:Windows.UI.Xaml.Controls]ToolTipService.ToolTip" xml:space="preserve">
    <value>Geheugenitem wissen</value>
    <comment>This is the tool tip automation name for the Clear Memory Item (MC) button in the Memory list.</comment>
  </data>
  <data name="MemPlusItem.[using:Windows.UI.Xaml.Automation]AutomationProperties.Name" xml:space="preserve">
    <value>Toevoegen aan geheugenitem</value>
    <comment>Screen reader prompt for the Calculator Memory Add button in the Memory list</comment>
  </data>
  <data name="MemPlusItem.[using:Windows.UI.Xaml.Controls]ToolTipService.ToolTip" xml:space="preserve">
    <value>Toevoegen aan geheugenitem</value>
    <comment>This is the tool tip automation name for the Calculator Memory Add button in the Memory list</comment>
  </data>
  <data name="MemMinusItem.[using:Windows.UI.Xaml.Automation]AutomationProperties.Name" xml:space="preserve">
    <value>Van geheugenitem aftrekken</value>
    <comment>Screen reader prompt for the Calculator Memory Subtract button in the Memory list</comment>
  </data>
  <data name="MemMinusItem.[using:Windows.UI.Xaml.Controls]ToolTipService.ToolTip" xml:space="preserve">
    <value>Van geheugenitem aftrekken</value>
    <comment>This is the tool tip automation name for the Calculator Memory Subtract button in the Memory list</comment>
  </data>
  <data name="ClearMemorySwipeItem.[using:Windows.UI.Xaml.Automation]AutomationProperties.Name" xml:space="preserve">
    <value>Geheugenitem wissen</value>
    <comment>Screen reader prompt for the Calculator Clear Memory button</comment>
  </data>
  <data name="ClearMemoryMenuItem.Text" xml:space="preserve">
    <value>Geheugenitem wissen</value>
    <comment>Text string for the Calculator Clear Memory option in the Memory list context menu</comment>
  </data>
  <data name="MemPlusSwipeItem.[using:Windows.UI.Xaml.Automation]AutomationProperties.Name" xml:space="preserve">
    <value>Toevoegen aan geheugenitem</value>
    <comment>Screen reader prompt for the Calculator Memory Add swipe button in the Memory list</comment>
  </data>
  <data name="MemPlusMenuItem.Text" xml:space="preserve">
    <value>Toevoegen aan geheugenitem</value>
    <comment>Text string for the Calculator Memory Add option in the Memory list context menu</comment>
  </data>
  <data name="MemMinusSwipeItem.[using:Windows.UI.Xaml.Automation]AutomationProperties.Name" xml:space="preserve">
    <value>Van geheugenitem aftrekken</value>
    <comment>Screen reader prompt for the Calculator Memory Subtract swipe button in the Memory list</comment>
  </data>
  <data name="MemMinusMenuItem.Text" xml:space="preserve">
    <value>Van geheugenitem aftrekken</value>
    <comment>Text string for the Calculator Memory Subtract option in the Memory list context menu</comment>
  </data>
  <data name="DeleteHistorySwipeItem.Text" xml:space="preserve">
    <value>Verwijderen</value>
    <comment>Text string for the Calculator Delete swipe button in the History list</comment>
  </data>
  <data name="CopyHistoryMenuItem.Text" xml:space="preserve">
    <value>Kopiëren</value>
    <comment>Text string for the Calculator Copy option in the History list context menu</comment>
  </data>
  <data name="DeleteHistoryMenuItem.Text" xml:space="preserve">
    <value>Verwijderen</value>
    <comment>Text string for the Calculator Delete option in the History list context menu</comment>
  </data>
  <data name="DeleteHistorySwipeItem.[using:Windows.UI.Xaml.Automation]AutomationProperties.Name" xml:space="preserve">
    <value>Geschiedenisitem verwijderen</value>
    <comment>Screen reader prompt for the Calculator Delete swipe button in the History list</comment>
  </data>
  <data name="DeleteHistoryMenuItem.[using:Windows.UI.Xaml.Automation]AutomationProperties.Name" xml:space="preserve">
    <value>Geschiedenisitem verwijderen</value>
    <comment>Screen reader prompt for the Calculator Delete option in the History list context menu</comment>
  </data>
  <data name="backSpaceButton.[using:Windows.UI.Xaml.Automation]AutomationProperties.Name" xml:space="preserve">
    <value>Backspace</value>
    <comment>Screen reader prompt for the Calculator Backspace button</comment>
  </data>
  <data name="BinaryZero.Text" xml:space="preserve">
    <value>0</value>
    <comment>Screen reader prompt for the Calculator number "0" button</comment>
  </data>
  <data name="BinaryOne.Text" xml:space="preserve">
    <value>1</value>
    <comment>Screen reader prompt for the Calculator number "1" button</comment>
  </data>
  <data name="num0Button.[using:Windows.UI.Xaml.Automation]AutomationProperties.Name" xml:space="preserve">
    <value>Nul</value>
    <comment>Screen reader prompt for the Calculator number "0" button</comment>
  </data>
  <data name="num1Button.[using:Windows.UI.Xaml.Automation]AutomationProperties.Name" xml:space="preserve">
    <value>Een</value>
    <comment>Screen reader prompt for the Calculator number "1" button</comment>
  </data>
  <data name="num2Button.[using:Windows.UI.Xaml.Automation]AutomationProperties.Name" xml:space="preserve">
    <value>Twee</value>
    <comment>Screen reader prompt for the Calculator number "2" button</comment>
  </data>
  <data name="num3Button.[using:Windows.UI.Xaml.Automation]AutomationProperties.Name" xml:space="preserve">
    <value>Drie</value>
    <comment>Screen reader prompt for the Calculator number "3" button</comment>
  </data>
  <data name="num4Button.[using:Windows.UI.Xaml.Automation]AutomationProperties.Name" xml:space="preserve">
    <value>Vier</value>
    <comment>Screen reader prompt for the Calculator number "4" button</comment>
  </data>
  <data name="num5Button.[using:Windows.UI.Xaml.Automation]AutomationProperties.Name" xml:space="preserve">
    <value>Vijf</value>
    <comment>Screen reader prompt for the Calculator number "5" button</comment>
  </data>
  <data name="num6Button.[using:Windows.UI.Xaml.Automation]AutomationProperties.Name" xml:space="preserve">
    <value>Zes</value>
    <comment>Screen reader prompt for the Calculator number "6" button</comment>
  </data>
  <data name="num7Button.[using:Windows.UI.Xaml.Automation]AutomationProperties.Name" xml:space="preserve">
    <value>Zeven</value>
    <comment>Screen reader prompt for the Calculator number "7" button</comment>
  </data>
  <data name="num8Button.[using:Windows.UI.Xaml.Automation]AutomationProperties.Name" xml:space="preserve">
    <value>Acht</value>
    <comment>Screen reader prompt for the Calculator number "8" button</comment>
  </data>
  <data name="num9Button.[using:Windows.UI.Xaml.Automation]AutomationProperties.Name" xml:space="preserve">
    <value>Negen</value>
    <comment>Screen reader prompt for the Calculator number "9" button</comment>
  </data>
  <data name="aButton.[using:Windows.UI.Xaml.Automation]AutomationProperties.Name" xml:space="preserve">
    <value>A</value>
    <comment>Screen reader prompt for the Calculator number "A" button</comment>
  </data>
  <data name="bButton.[using:Windows.UI.Xaml.Automation]AutomationProperties.Name" xml:space="preserve">
    <value>B</value>
    <comment>Screen reader prompt for the Calculator number "B" button</comment>
  </data>
  <data name="cButton.[using:Windows.UI.Xaml.Automation]AutomationProperties.Name" xml:space="preserve">
    <value>C</value>
    <comment>Screen reader prompt for the Calculator number "C" button</comment>
  </data>
  <data name="dButton.[using:Windows.UI.Xaml.Automation]AutomationProperties.Name" xml:space="preserve">
    <value>D</value>
    <comment>Screen reader prompt for the Calculator number "D" button</comment>
  </data>
  <data name="eButton.[using:Windows.UI.Xaml.Automation]AutomationProperties.Name" xml:space="preserve">
    <value>E</value>
    <comment>Screen reader prompt for the Calculator number "E" button</comment>
  </data>
  <data name="fButton.[using:Windows.UI.Xaml.Automation]AutomationProperties.Name" xml:space="preserve">
    <value>F</value>
    <comment>Screen reader prompt for the Calculator number "F" button</comment>
  </data>
  <data name="andButton.[using:Windows.UI.Xaml.Automation]AutomationProperties.Name" xml:space="preserve">
    <value>En</value>
    <comment>Screen reader prompt for the Calculator And button</comment>
  </data>
  <data name="orButton.[using:Windows.UI.Xaml.Automation]AutomationProperties.Name" xml:space="preserve">
    <value>Of</value>
    <comment>Screen reader prompt for the Calculator Or button</comment>
  </data>
  <data name="notButton.[using:Windows.UI.Xaml.Automation]AutomationProperties.Name" xml:space="preserve">
    <value>Niet</value>
    <comment>Screen reader prompt for the Calculator Not button</comment>
  </data>
  <data name="rolButton.[using:Windows.UI.Xaml.Automation]AutomationProperties.Name" xml:space="preserve">
    <value>Naar links roteren</value>
    <comment>Screen reader prompt for the Calculator ROL button</comment>
  </data>
  <data name="rorButton.[using:Windows.UI.Xaml.Automation]AutomationProperties.Name" xml:space="preserve">
    <value>Naar rechts roteren</value>
    <comment>Screen reader prompt for the Calculator ROR button</comment>
  </data>
  <data name="lshButton.[using:Windows.UI.Xaml.Automation]AutomationProperties.Name" xml:space="preserve">
    <value>Schuiven naar links</value>
    <comment>Screen reader prompt for the Calculator LSH button</comment>
  </data>
  <data name="rshButton.[using:Windows.UI.Xaml.Automation]AutomationProperties.Name" xml:space="preserve">
    <value>Schuiven naar rechts</value>
    <comment>Screen reader prompt for the Calculator RSH button</comment>
  </data>
  <data name="xorButton.[using:Windows.UI.Xaml.Automation]AutomationProperties.Name" xml:space="preserve">
    <value>Exclusief of</value>
    <comment>Screen reader prompt for the Calculator XOR button</comment>
  </data>
  <data name="qwordButton.[using:Windows.UI.Xaml.Automation]AutomationProperties.Name" xml:space="preserve">
    <value>Viervoudig woord in-/uitschakelen</value>
    <comment>Screen reader prompt for the Calculator qword button. Should read as "Quadruple word toggle button".</comment>
  </data>
  <data name="dwordButton.[using:Windows.UI.Xaml.Automation]AutomationProperties.Name" xml:space="preserve">
    <value>Dubbel woord in-/uitschakelen</value>
    <comment>Screen reader prompt for the Calculator dword button. Should read as "Double word toggle button".</comment>
  </data>
  <data name="wordButton.[using:Windows.UI.Xaml.Automation]AutomationProperties.Name" xml:space="preserve">
    <value>Woorden in-/uitschakelen</value>
    <comment>Screen reader prompt for the Calculator word button. Should read as "Word toggle button".</comment>
  </data>
  <data name="byteButton.[using:Windows.UI.Xaml.Automation]AutomationProperties.Name" xml:space="preserve">
    <value>Byte in-/uitschakelen</value>
    <comment>Screen reader prompt for the Calculator byte button. Should read as "Byte toggle button".</comment>
  </data>
  <data name="bitFlip.[using:Windows.UI.Xaml.Automation]AutomationProperties.Name" xml:space="preserve">
    <value>Bit-wissel-toetsenblok</value>
    <comment>Screen reader prompt for the Calculator bitFlip button</comment>
  </data>
  <data name="fullKeypad.[using:Windows.UI.Xaml.Automation]AutomationProperties.Name" xml:space="preserve">
    <value>Volledig toetsenblok</value>
    <comment>Screen reader prompt for the Calculator numberPad button</comment>
  </data>
  <data name="decimalSeparatorButton.[using:Windows.UI.Xaml.Automation]AutomationProperties.Name" xml:space="preserve">
    <value>Decimaalteken</value>
    <comment>Screen reader prompt for the "." button</comment>
  </data>
  <data name="clearEntryButton.[using:Windows.UI.Xaml.Automation]AutomationProperties.Name" xml:space="preserve">
    <value>Invoer wissen</value>
    <comment>Screen reader prompt for the "CE" button</comment>
  </data>
  <data name="clearButton.[using:Windows.UI.Xaml.Automation]AutomationProperties.Name" xml:space="preserve">
    <value>Wissen</value>
    <comment>Screen reader prompt for the "C" button</comment>
  </data>
  <data name="divideButton.[using:Windows.UI.Xaml.Automation]AutomationProperties.Name" xml:space="preserve">
    <value>Delen door</value>
    <comment>Screen reader prompt for the divide button on the number pad</comment>
  </data>
  <data name="multiplyButton.[using:Windows.UI.Xaml.Automation]AutomationProperties.Name" xml:space="preserve">
    <value>Vermenigvuldigen met</value>
    <comment>Screen reader prompt for the multiply button on the number pad</comment>
  </data>
  <data name="equalButton.[using:Windows.UI.Xaml.Automation]AutomationProperties.Name" xml:space="preserve">
    <value>Is gelijk aan</value>
    <comment>Screen reader prompt for the equals button on the scientific operator keypad</comment>
  </data>
  <data name="shiftButton.[using:Windows.UI.Xaml.Automation]AutomationProperties.Name" xml:space="preserve">
    <value>Inverse-functie</value>
    <comment>Screen reader prompt for the shift button on the number pad in scientific mode.</comment>
  </data>
  <data name="minusButton.[using:Windows.UI.Xaml.Automation]AutomationProperties.Name" xml:space="preserve">
    <value>Min</value>
    <comment>Screen reader prompt for the minus button on the number pad</comment>
  </data>
  <data name="minus" xml:space="preserve">
    <value>Min</value>
    <comment>We use this resource to replace "-" sign for accessibility. So expression like, 1 - 3 = -2 becomes 1 minus 3 = minus 2</comment>
  </data>
  <data name="plusButton.[using:Windows.UI.Xaml.Automation]AutomationProperties.Name" xml:space="preserve">
    <value>Plus</value>
    <comment>Screen reader prompt for the plus button on the number pad</comment>
  </data>
  <data name="squareRootButton.[using:Windows.UI.Xaml.Automation]AutomationProperties.Name" xml:space="preserve">
    <value>Wortel</value>
    <comment>Screen reader prompt for the square root button on the scientific operator keypad</comment>
  </data>
  <data name="percentButton.[using:Windows.UI.Xaml.Automation]AutomationProperties.Name" xml:space="preserve">
    <value>Procent</value>
    <comment>Screen reader prompt for the percent button on the scientific operator keypad</comment>
  </data>
  <data name="negateButton.[using:Windows.UI.Xaml.Automation]AutomationProperties.Name" xml:space="preserve">
    <value>Positief negatief</value>
    <comment>Screen reader prompt for the negate button on the scientific operator keypad</comment>
  </data>
  <data name="converterNegateButton.[using:Windows.UI.Xaml.Automation]AutomationProperties.Name" xml:space="preserve">
    <value>Positief negatief</value>
    <comment>Screen reader prompt for the negate button on the converter operator keypad</comment>
  </data>
  <data name="invertButton.[using:Windows.UI.Xaml.Automation]AutomationProperties.Name" xml:space="preserve">
    <value>Reciproque</value>
    <comment>Screen reader prompt for the invert button on the scientific operator keypad</comment>
  </data>
  <data name="openParenthesisButton.[using:Windows.UI.Xaml.Automation]AutomationProperties.Name" xml:space="preserve">
    <value>Haakje openen</value>
    <comment>Screen reader prompt for the Calculator "(" button on the scientific operator keypad</comment>
  </data>
  <data name="Format_OpenParenthesisAutomationNamePrefix" xml:space="preserve">
    <value>Haakje openen, aantal haakjes openen %1</value>
    <comment>{Locked="%1"} Screen reader prompt for the Calculator "(" button on the scientific operator keypad. %1 is the localized count of open parenthesis, e.g. "2".</comment>
  </data>
  <data name="closeParenthesisButton.[using:Windows.UI.Xaml.Automation]AutomationProperties.Name" xml:space="preserve">
    <value>Haakje sluiten</value>
    <comment>Screen reader prompt for the Calculator ")" button on the scientific operator keypad</comment>
  </data>
  <data name="Format_OpenParenthesisCountAutomationNamePrefix" xml:space="preserve">
    <value>Aantal haakjes openen %1</value>
    <comment>{Locked="%1"} Screen reader prompt for the Calculator "(" button on the scientific and programmer operator keypad. %1 is the localized count of open parenthesis, e.g. "2".</comment>
  </data>
  <data name="NoRightParenthesisAdded_Announcement" xml:space="preserve">
    <value>Er zijn geen haakjes openen om te sluiten.</value>
    <comment>{Locked="%1"} Screen reader prompt for the Calculator when the ")" button on the scientific and programmer operator keypad cannot be added to the equation. e.g. "1+)".</comment>
  </data>
  <data name="ftoeButton.[using:Windows.UI.Xaml.Automation]AutomationProperties.Name" xml:space="preserve">
    <value>Wetenschappelijke notatie</value>
    <comment>Screen reader prompt for the Calculator F-E the scientific operator keypad</comment>
  </data>
  <data name="hyperbolicButton.[using:Windows.UI.Xaml.Automation]AutomationProperties.Name" xml:space="preserve">
    <value>Hyperbolische functie</value>
    <comment>Screen reader prompt for the Calculator button HYP in the scientific operator keypad</comment>
  </data>
  <data name="piButton.[using:Windows.UI.Xaml.Automation]AutomationProperties.Name" xml:space="preserve">
    <value>Pi</value>
    <comment>Screen reader prompt for the Calculator pi button  on the scientific operator keypad</comment>
  </data>
  <data name="sinButton.[using:Windows.UI.Xaml.Automation]AutomationProperties.Name" xml:space="preserve">
    <value>Sinus</value>
    <comment>Screen reader prompt for the Calculator sin button  on the scientific operator keypad</comment>
  </data>
  <data name="cosButton.[using:Windows.UI.Xaml.Automation]AutomationProperties.Name" xml:space="preserve">
    <value>Cosinus</value>
    <comment>Screen reader prompt for the Calculator cos button  on the scientific operator keypad</comment>
  </data>
  <data name="tanButton.[using:Windows.UI.Xaml.Automation]AutomationProperties.Name" xml:space="preserve">
    <value>Tangens</value>
    <comment>Screen reader prompt for the Calculator tan button  on the scientific operator keypad</comment>
  </data>
  <data name="sinhButton.[using:Windows.UI.Xaml.Automation]AutomationProperties.Name" xml:space="preserve">
    <value>Sinus hyperbolicus</value>
    <comment>Screen reader prompt for the Calculator sinh button  on the scientific operator keypad</comment>
  </data>
  <data name="coshButton.[using:Windows.UI.Xaml.Automation]AutomationProperties.Name" xml:space="preserve">
    <value>Cosinus hyperbolicus</value>
    <comment>Screen reader prompt for the Calculator cosh button  on the scientific operator keypad</comment>
  </data>
  <data name="tanhButton.[using:Windows.UI.Xaml.Automation]AutomationProperties.Name" xml:space="preserve">
    <value>Tangens hyperbolicus</value>
    <comment>Screen reader prompt for the Calculator tanh button  on the scientific operator keypad</comment>
  </data>
  <data name="xpower2Button.[using:Windows.UI.Xaml.Automation]AutomationProperties.Name" xml:space="preserve">
    <value>Vierkant</value>
    <comment>Screen reader prompt for the x squared on the scientific operator keypad. </comment>
  </data>
  <data name="xpower3Button.[using:Windows.UI.Xaml.Automation]AutomationProperties.Name" xml:space="preserve">
    <value>Kubus</value>
    <comment>Screen reader prompt for the x cubed on the scientific operator keypad. </comment>
  </data>
  <data name="invsinButton.[using:Windows.UI.Xaml.Automation]AutomationProperties.Name" xml:space="preserve">
    <value>Arcsinus</value>
    <comment>Screen reader prompt for the inverted sin on the scientific operator keypad.</comment>
  </data>
  <data name="invcosButton.[using:Windows.UI.Xaml.Automation]AutomationProperties.Name" xml:space="preserve">
    <value>Arccosinus</value>
    <comment>Screen reader prompt for the inverted cos on the scientific operator keypad.</comment>
  </data>
  <data name="invtanButton.[using:Windows.UI.Xaml.Automation]AutomationProperties.Name" xml:space="preserve">
    <value>Arctangens</value>
    <comment>Screen reader prompt for the inverted tan on the scientific operator keypad.</comment>
  </data>
  <data name="invsinhButton.[using:Windows.UI.Xaml.Automation]AutomationProperties.Name" xml:space="preserve">
    <value>Arcsinus hyperbolicus</value>
    <comment>Screen reader prompt for the inverted sinh on the scientific operator keypad.</comment>
  </data>
  <data name="invcoshButton.[using:Windows.UI.Xaml.Automation]AutomationProperties.Name" xml:space="preserve">
    <value>Arccosinus hyperbolicus</value>
    <comment>Screen reader prompt for the inverted cosh on the scientific operator keypad.</comment>
  </data>
  <data name="invtanhButton.[using:Windows.UI.Xaml.Automation]AutomationProperties.Name" xml:space="preserve">
    <value>Arctangens hyperbolicus</value>
    <comment>Screen reader prompt for the inverted tanh on the scientific operator keypad.</comment>
  </data>
  <data name="powerButton.[using:Windows.UI.Xaml.Automation]AutomationProperties.Name" xml:space="preserve">
    <value>'X' tot de macht</value>
    <comment>Screen reader prompt for x power y button on the scientific operator keypad. </comment>
  </data>
  <data name="powerOf10Button.[using:Windows.UI.Xaml.Automation]AutomationProperties.Name" xml:space="preserve">
    <value>Tien tot de macht</value>
    <comment>Screen reader prompt for the 10 power x button on the scientific operator keypad.</comment>
  </data>
  <data name="powerOfEButton.[using:Windows.UI.Xaml.Automation]AutomationProperties.Name" xml:space="preserve">
    <value>'e' tot de macht</value>
    <comment>Screen reader for the e power x on the scientific operator keypad.</comment>
  </data>
  <data name="ySquareRootButton.[using:Windows.UI.Xaml.Automation]AutomationProperties.Name" xml:space="preserve">
    <value>'y' wortel van 'x'</value>
    <comment>Screen reader for the yth root of x on the scientific operator keypad. Note: String is meant to read out whatever the "Yth root" mathematical operator sounds like.</comment>
  </data>
  <data name="logBase10Button.[using:Windows.UI.Xaml.Automation]AutomationProperties.Name" xml:space="preserve">
    <value>Logaritme</value>
    <comment>Screen reader for the log base 10  on the scientific operator keypad</comment>
  </data>
  <data name="logBaseEButton.[using:Windows.UI.Xaml.Automation]AutomationProperties.Name" xml:space="preserve">
    <value>Natuurlijk logaritme</value>
    <comment>Screen reader for the log base e on the scientific operator keypad</comment>
  </data>
  <data name="modButton.[using:Windows.UI.Xaml.Automation]AutomationProperties.Name" xml:space="preserve">
    <value>Modulo</value>
    <comment>Screen reader for the mod button on the scientific operator keypad</comment>
  </data>
  <data name="expButton.[using:Windows.UI.Xaml.Automation]AutomationProperties.Name" xml:space="preserve">
    <value>Exponentieel</value>
    <comment>Screen reader for the exp button on the scientific operator keypad</comment>
  </data>
  <data name="dmsButton.[using:Windows.UI.Xaml.Automation]AutomationProperties.Name" xml:space="preserve">
    <value>Graad minuut seconde</value>
    <comment>Screen reader for the exp button on the scientific operator keypad</comment>
  </data>
  <data name="degreesButton.[using:Windows.UI.Xaml.Automation]AutomationProperties.Name" xml:space="preserve">
    <value>Graden</value>
    <comment>Screen reader for the exp button on the scientific operator keypad</comment>
  </data>
  <data name="intButton.[using:Windows.UI.Xaml.Automation]AutomationProperties.Name" xml:space="preserve">
    <value>Geheel getal</value>
    <comment>Screen reader for the int button on the scientific operator keypad</comment>
  </data>
  <data name="fractButton.[using:Windows.UI.Xaml.Automation]AutomationProperties.Name" xml:space="preserve">
    <value>Breuk</value>
    <comment>Screen reader for the frac button on the scientific operator keypad</comment>
  </data>
  <data name="factorialButton.[using:Windows.UI.Xaml.Automation]AutomationProperties.Name" xml:space="preserve">
    <value>Faculteit</value>
    <comment>Screen reader for the factorial button on the basic operator keypad</comment>
  </data>
  <data name="degButton.[using:Windows.UI.Xaml.Automation]AutomationProperties.Name" xml:space="preserve">
    <value>Graden in-/uitschakelen</value>
    <comment>This the Deg button's Degree mode automation nameon the scientific operator keypad. Should read as "Degrees toggle button".</comment>
  </data>
  <data name="gradButton.[using:Windows.UI.Xaml.Automation]AutomationProperties.Name" xml:space="preserve">
    <value>Gradiënten in-/uitschakelen</value>
    <comment>This is the Deg button's Grad mode automation name on the scientific operator keypad. Should read as "Gradians toggle button".</comment>
  </data>
  <data name="radButton.[using:Windows.UI.Xaml.Automation]AutomationProperties.Name" xml:space="preserve">
    <value>Radialen in-/uitschakelen</value>
    <comment>This is the Deg button's Rad mode automation name on the scientific operator keypad. Should read as "Radians toggle button".</comment>
  </data>
  <data name="FlyoutNav.[using:Windows.UI.Xaml.Automation]AutomationProperties.Name" xml:space="preserve">
    <value>Vervolgkeuzelijst Modus</value>
    <comment>Screen reader prompt for the Mode dropdown field in Snapped and Portrait modes.</comment>
  </data>
  <data name="Categories.[using:Windows.UI.Xaml.Automation]AutomationProperties.Name" xml:space="preserve">
    <value>Vervolgkeuzelijst Categorieën</value>
    <comment>Screen reader prompt for the Categories dropdown field.</comment>
  </data>
  <data name="EnterAlwaysOnTopButton.[using:Windows.UI.Xaml.Automation]AutomationProperties.Name" xml:space="preserve">
    <value>Op voorgrond behouden</value>
    <comment>Screen reader prompt for the Always-on-Top button when in normal mode.</comment>
  </data>
  <data name="ExitAlwaysOnTopButton.[using:Windows.UI.Xaml.Automation]AutomationProperties.Name" xml:space="preserve">
    <value>Terug naar volledige weergave</value>
    <comment>Screen reader prompt for the Always-on-Top button when in Always-on-Top mode.</comment>
  </data>
  <data name="EnterAlwaysOnTopButton.[using:Windows.UI.Xaml.Controls]ToolTipService.ToolTip" xml:space="preserve">
    <value>Op voorgrond houden (Alt+Up)</value>
    <comment>This is the tool tip automation name for the Always-on-Top button when in normal mode.</comment>
  </data>
  <data name="ExitAlwaysOnTopButton.[using:Windows.UI.Xaml.Controls]ToolTipService.ToolTip" xml:space="preserve">
    <value>Terug naar volledige weergave (Alt+Down)</value>
    <comment>This is the tool tip automation name for the Always-on-Top button when in Always-on-Top mode.</comment>
  </data>
  <data name="Format_ValueFrom" xml:space="preserve">
    <value>Converteren van %1 %2</value>
    <comment>Screen reader prompt for the Unit Converter Value1 i.e. top number field. %1 = DisplayValue, %2 = Unit field localized name.</comment>
  </data>
  <data name="Format_ValueFrom_Decimal" xml:space="preserve">
    <value>Converteren van %1 punt %2</value>
    <comment>{Locked="%1"}. Automation label for the calculator display in the specific case where the user has just pressed the decimal separator button. For example, the user wants to input "7.5".  When they have input "7." they will hear "Convert from 7 point _current_unit_". "point" should be localized to the locale's appropriate decimal separator.</comment>
  </data>
  <data name="Format_ValueTo" xml:space="preserve">
    <value>Converteren naar %1 %2</value>
    <comment>Screen reader prompt for the Unit Converter Value2 i.e. bottom number field. %1 = DisplayValue, %2 = Unit field localized name.</comment>
  </data>
  <data name="Format_ConversionResult" xml:space="preserve">
    <value>%1 %2 is %3 %4</value>
    <comment>Screen reader prompt for a conversion result, ie "2 liters is 2,000 milliliters" . %1 = From unit display value, %2 = From unit, %3 = To unit display value, %4 = To unit.</comment>
  </data>
  <data name="InputUnit_Name" xml:space="preserve">
    <value>Invoereenheid</value>
    <comment>Screen reader prompt for the Unit Converter Units1 i.e. top units field.</comment>
  </data>
  <data name="OutputUnit_Name" xml:space="preserve">
    <value>Uitvoereenheid</value>
    <comment>Screen reader prompt for the Unit Converter Units2 i.e. bottom units field.</comment>
  </data>
  <data name="CategoryName_AreaText" xml:space="preserve">
    <value>Oppervlakte</value>
    <comment>Unit conversion category name called Area (eg. area of a sports field in square meters)</comment>
  </data>
  <data name="CategoryName_DataText" xml:space="preserve">
    <value>Gegevens</value>
    <comment>Unit conversion category name called Data</comment>
  </data>
  <data name="CategoryName_EnergyText" xml:space="preserve">
    <value>Energie</value>
    <comment>Unit conversion category name called Energy. (eg. the energy in a battery or in food)</comment>
  </data>
  <data name="CategoryName_LengthText" xml:space="preserve">
    <value>Lengte</value>
    <comment>Unit conversion category name called Length</comment>
  </data>
  <data name="CategoryName_PowerText" xml:space="preserve">
    <value>Vermogen</value>
    <comment>Unit conversion category name called Power (eg. the power of an engine or a light bulb)</comment>
  </data>
  <data name="CategoryName_SpeedText" xml:space="preserve">
    <value>Snelheid</value>
    <comment>Unit conversion category name called Speed</comment>
  </data>
  <data name="CategoryName_TimeText" xml:space="preserve">
    <value>Tijd</value>
    <comment>Unit conversion category name called Time</comment>
  </data>
  <data name="CategoryName_VolumeText" xml:space="preserve">
    <value>Volume</value>
    <comment>Unit conversion category name called Volume (eg. cups, teaspoons, milliliters)</comment>
  </data>
  <data name="CategoryName_TemperatureText" xml:space="preserve">
    <value>Temperatuur</value>
    <comment>Unit conversion category name called Temperature</comment>
  </data>
  <data name="CategoryName_WeightText" xml:space="preserve">
    <value>Gewicht en massa</value>
    <comment>Unit conversion category name called Weight and mass. Note that this category includes units of both mass and weight. People use the word "weight" in everyday life for measuring things such as food and people. In case a language has same word for "weight" and "mass" please use one word only.</comment>
  </data>
  <data name="CategoryName_PressureText" xml:space="preserve">
    <value>Druk</value>
    <comment>Unit conversion category name called Pressure</comment>
  </data>
  <data name="CategoryName_AngleText" xml:space="preserve">
    <value>Hoek</value>
    <comment>Unit conversion category name called Angle</comment>
  </data>
  <data name="CategoryName_CurrencyText" xml:space="preserve">
    <value>Valuta</value>
    <comment>Unit conversion category name called Currency</comment>
  </data>
  <data name="UnitName_FluidOunceUK" xml:space="preserve">
    <value>Fluid ounces (VK)</value>
    <comment>A measurement unit for volume, in plural. (Please choose the most appropriate plural form to fit any number between 0 and 999,999,999,999,999)</comment>
  </data>
  <data name="UnitAbbreviation_FluidOunceUK" xml:space="preserve">
    <value>fl oz (VK)</value>
    <comment>An abbreviation for a measurement unit of volume</comment>
  </data>
  <data name="UnitName_FluidOunceUS" xml:space="preserve">
    <value>Fluid ounces (VS)</value>
    <comment>A measurement unit for volume. (Please choose the most appropriate plural form to fit any number between 0 and 999,999,999,999,999)</comment>
  </data>
  <data name="UnitAbbreviation_FluidOunceUS" xml:space="preserve">
    <value>fl oz (VS)</value>
    <comment>An abbreviation for a measurement unit of volume</comment>
  </data>
  <data name="UnitName_GallonUK" xml:space="preserve">
    <value>Gallons (VK)</value>
    <comment>A measurement unit for volume. (Please choose the most appropriate plural form to fit any number between 0 and 999,999,999,999,999)</comment>
  </data>
  <data name="UnitAbbreviation_GallonUK" xml:space="preserve">
    <value>gal (VK)</value>
    <comment>An abbreviation for a measurement unit of volume</comment>
  </data>
  <data name="UnitName_GallonUS" xml:space="preserve">
    <value>Gallons (VS)</value>
    <comment>A measurement unit for volume. (Please choose the most appropriate plural form to fit any number between 0 and 999,999,999,999,999)</comment>
  </data>
  <data name="UnitAbbreviation_GallonUS" xml:space="preserve">
    <value>gal (VS)</value>
    <comment>An abbreviation for a measurement unit of volume</comment>
  </data>
  <data name="UnitName_Liter" xml:space="preserve">
    <value>Liter</value>
    <comment>A measurement unit for volume. (Please choose the most appropriate plural form to fit any number between 0 and 999,999,999,999,999)</comment>
  </data>
  <data name="UnitAbbreviation_Liter" xml:space="preserve">
    <value>L</value>
    <comment>An abbreviation for a measurement unit of volume</comment>
  </data>
  <data name="UnitName_Milliliter" xml:space="preserve">
    <value>Milliliter</value>
    <comment>A measurement unit for volume. (Please choose the most appropriate plural form to fit any number between 0 and 999,999,999,999,999)</comment>
  </data>
  <data name="UnitAbbreviation_Milliliter" xml:space="preserve">
    <value>ml</value>
    <comment>An abbreviation for a measurement unit of volume</comment>
  </data>
  <data name="UnitName_PintUK" xml:space="preserve">
    <value>Pints (VK)</value>
    <comment>A measurement unit for volume. (Please choose the most appropriate plural form to fit any number between 0 and 999,999,999,999,999)</comment>
  </data>
  <data name="UnitAbbreviation_PintUK" xml:space="preserve">
    <value>pt (VK)</value>
    <comment>An abbreviation for a measurement unit of volume</comment>
  </data>
  <data name="UnitName_PintUS" xml:space="preserve">
    <value>Pints (VS)</value>
    <comment>A measurement unit for volume. (Please choose the most appropriate plural form to fit any number between 0 and 999,999,999,999,999)</comment>
  </data>
  <data name="UnitAbbreviation_PintUS" xml:space="preserve">
    <value>pt (VS)</value>
    <comment>An abbreviation for a measurement unit of volume</comment>
  </data>
  <data name="UnitName_TablespoonUS" xml:space="preserve">
    <value>Tablespoons (VS)</value>
    <comment>A measurement unit for volume. (Please choose the most appropriate plural form to fit any number between 0 and 999,999,999,999,999)</comment>
  </data>
  <data name="UnitAbbreviation_TablespoonUS" xml:space="preserve">
    <value>tbsp. (VS)</value>
    <comment>An abbreviation for a measurement unit of volume</comment>
  </data>
  <data name="UnitName_TeaspoonUS" xml:space="preserve">
    <value>Teaspoons (VS)</value>
    <comment>A measurement unit for volume. (Please choose the most appropriate plural form to fit any number between 0 and 999,999,999,999,999)</comment>
  </data>
  <data name="UnitAbbreviation_TeaspoonUS" xml:space="preserve">
    <value>tsp. (VS)</value>
    <comment>An abbreviation for a measurement unit of volume</comment>
  </data>
  <data name="UnitName_TablespoonUK" xml:space="preserve">
    <value>Tablespoons (VK)</value>
    <comment>A measurement unit for volume. (Please choose the most appropriate plural form to fit any number between 0 and 999,999,999,999,999)</comment>
  </data>
  <data name="UnitAbbreviation_TablespoonUK" xml:space="preserve">
    <value>tbsp. (VK)</value>
    <comment>An abbreviation for a measurement unit of volume</comment>
  </data>
  <data name="UnitName_TeaspoonUK" xml:space="preserve">
    <value>Teaspoons (VK)</value>
    <comment>A measurement unit for volume. (Please choose the most appropriate plural form to fit any number between 0 and 999,999,999,999,999)</comment>
  </data>
  <data name="UnitAbbreviation_TeaspoonUK" xml:space="preserve">
    <value>tsp. (VK)</value>
    <comment>An abbreviation for a measurement unit of volume</comment>
  </data>
  <data name="UnitName_QuartUK" xml:space="preserve">
    <value>Quarts (VK)</value>
    <comment>A measurement unit for volume. (Please choose the most appropriate plural form to fit any number between 0 and 999,999,999,999,999)</comment>
  </data>
  <data name="UnitAbbreviation_QuartUK" xml:space="preserve">
    <value>qt (VK)</value>
    <comment>An abbreviation for a measurement unit of volume</comment>
  </data>
  <data name="UnitName_QuartUS" xml:space="preserve">
    <value>Quarts (VS)</value>
    <comment>A measurement unit for volume. (Please choose the most appropriate plural form to fit any number between 0 and 999,999,999,999,999)</comment>
  </data>
  <data name="UnitAbbreviation_QuartUS" xml:space="preserve">
    <value>qt (VS)</value>
    <comment>An abbreviation for a measurement unit of volume</comment>
  </data>
  <data name="UnitName_CupUS" xml:space="preserve">
    <value>Cups (VS)</value>
    <comment>A measurement unit for volume. (Please choose the most appropriate plural form to fit any number between 0 and 999,999,999,999,999)</comment>
  </data>
  <data name="UnitAbbreviation_CupUS" xml:space="preserve">
    <value>cup (VS)</value>
    <comment>An abbreviation for a measurement unit of volume</comment>
  </data>
  <data name="UnitAbbreviation_Angstrom" xml:space="preserve">
    <value>A</value>
    <comment>An abbreviation for a measurement unit of length</comment>
  </data>
  <data name="UnitAbbreviation_Acre" xml:space="preserve">
    <value>ac</value>
    <comment>An abbreviation for a measurement unit of volume</comment>
  </data>
  <data name="UnitAbbreviation_Bit" xml:space="preserve">
    <value>b</value>
    <comment>An abbreviation for a measurement unit of data</comment>
  </data>
  <data name="UnitAbbreviation_BritishThermalUnit" xml:space="preserve">
    <value>BTU</value>
    <comment>An abbreviation for a measurement unit of volume</comment>
  </data>
  <data name="UnitAbbreviation_BTUPerMinute" xml:space="preserve">
    <value>BTU/min</value>
    <comment>An abbreviation for a measurement unit of power</comment>
  </data>
  <data name="UnitAbbreviation_Byte" xml:space="preserve">
    <value>B</value>
    <comment>An abbreviation for a measurement unit of data</comment>
  </data>
  <data name="UnitAbbreviation_Calorie" xml:space="preserve">
    <value>cal</value>
    <comment>An abbreviation for a measurement unit of energy</comment>
  </data>
  <data name="UnitAbbreviation_Centimeter" xml:space="preserve">
    <value>cm</value>
    <comment>An abbreviation for a measurement unit of length</comment>
  </data>
  <data name="UnitAbbreviation_CentimetersPerSecond" xml:space="preserve">
    <value>cm/s</value>
    <comment>An abbreviation for a measurement unit of speed</comment>
  </data>
  <data name="UnitAbbreviation_CubicCentimeter" xml:space="preserve">
    <value>cm³</value>
    <comment>An abbreviation for a measurement unit of volume</comment>
  </data>
  <data name="UnitAbbreviation_CubicFoot" xml:space="preserve">
    <value>ft³</value>
    <comment>An abbreviation for a measurement unit of volume</comment>
  </data>
  <data name="UnitAbbreviation_CubicInch" xml:space="preserve">
    <value>in³</value>
    <comment>An abbreviation for a measurement unit of volume</comment>
  </data>
  <data name="UnitAbbreviation_CubicMeter" xml:space="preserve">
    <value>m³</value>
    <comment>An abbreviation for a measurement unit of volume</comment>
  </data>
  <data name="UnitAbbreviation_CubicYard" xml:space="preserve">
    <value>yd³</value>
    <comment>An abbreviation for a measurement unit of volume</comment>
  </data>
  <data name="UnitAbbreviation_Day" xml:space="preserve">
    <value>d</value>
    <comment>An abbreviation for a measurement unit of time</comment>
  </data>
  <data name="UnitAbbreviation_DegreesCelsius" xml:space="preserve">
    <value>°C</value>
    <comment>An abbreviation for "degrees Celsius"</comment>
  </data>
  <data name="UnitAbbreviation_DegreesFahrenheit" xml:space="preserve">
    <value>°F</value>
    <comment>An abbreviation for a "degrees Fahrenheit"</comment>
  </data>
  <data name="UnitAbbreviation_Electron-Volt" xml:space="preserve">
    <value>eV</value>
    <comment>An abbreviation for a measurement unit of energy</comment>
  </data>
  <data name="UnitAbbreviation_Foot" xml:space="preserve">
    <value>voet</value>
    <comment>An abbreviation for a measurement unit of length</comment>
  </data>
  <data name="UnitAbbreviation_FeetPerSecond" xml:space="preserve">
    <value>ft/s</value>
    <comment>An abbreviation for a measurement unit of speed</comment>
  </data>
  <data name="UnitAbbreviation_Foot-Pound" xml:space="preserve">
    <value>ft•lb</value>
    <comment>An abbreviation for a measurement unit of energy</comment>
  </data>
  <data name="UnitAbbreviation_Gigabit" xml:space="preserve">
    <value>Gb</value>
    <comment>An abbreviation for a measurement unit of data</comment>
  </data>
  <data name="UnitAbbreviation_Gigabyte" xml:space="preserve">
    <value>GB</value>
    <comment>An abbreviation for a measurement unit of data</comment>
  </data>
  <data name="UnitAbbreviation_Hectare" xml:space="preserve">
    <value>ha</value>
    <comment>An abbreviation for a measurement unit of area</comment>
  </data>
  <data name="UnitAbbreviation_Horsepower" xml:space="preserve">
    <value>pk (VS)</value>
    <comment>An abbreviation for a measurement unit of power</comment>
  </data>
  <data name="UnitAbbreviation_Hour" xml:space="preserve">
    <value>u</value>
    <comment>An abbreviation for a measurement unit of time</comment>
  </data>
  <data name="UnitAbbreviation_Inch" xml:space="preserve">
    <value>in</value>
    <comment>An abbreviation for a measurement unit of length</comment>
  </data>
  <data name="UnitAbbreviation_Joule" xml:space="preserve">
    <value>J</value>
    <comment>An abbreviation for a measurement unit of energy</comment>
  </data>
  <data name="UnitAbbreviation_Kilowatthour" xml:space="preserve">
    <value>kWh</value>
    <comment>An abbreviation for a measurement unit of electricity consumption</comment>
  </data>
  <data name="UnitAbbreviation_Kelvin" xml:space="preserve">
    <value>K</value>
    <comment>An abbreviation for the temperature system "Kelvin" (eg. 0 degrees Celsius = 273 Kelvin)</comment>
  </data>
  <data name="UnitAbbreviation_Kilobit" xml:space="preserve">
    <value>Kb</value>
    <comment>An abbreviation for a measurement unit of data</comment>
  </data>
  <data name="UnitAbbreviation_Kilobyte" xml:space="preserve">
    <value>KB</value>
    <comment>An abbreviation for a measurement unit of data</comment>
  </data>
  <data name="UnitAbbreviation_Kilocalorie" xml:space="preserve">
    <value>kcal</value>
    <comment>An abbreviation for a measurement unit of energy</comment>
  </data>
  <data name="UnitAbbreviation_Kilojoule" xml:space="preserve">
    <value>kJ</value>
    <comment>An abbreviation for a measurement unit of energy</comment>
  </data>
  <data name="UnitAbbreviation_Kilometer" xml:space="preserve">
    <value>km</value>
    <comment>An abbreviation for a measurement unit of length</comment>
  </data>
  <data name="UnitAbbreviation_KilometersPerHour" xml:space="preserve">
    <value>km/u</value>
    <comment>An abbreviation for a measurement unit of speed</comment>
  </data>
  <data name="UnitAbbreviation_Kilowatt" xml:space="preserve">
    <value>kW</value>
    <comment>An abbreviation for a measurement unit of power</comment>
  </data>
  <data name="UnitAbbreviation_Knot" xml:space="preserve">
    <value>kn</value>
    <comment>An abbreviation for a measurement unit of speed</comment>
  </data>
  <data name="UnitAbbreviation_Mach" xml:space="preserve">
    <value>M</value>
    <comment>An abbreviation for a measurement of speed (Mach is the speed of sound, Mach 2 is 2 times the speed of sound)</comment>
  </data>
  <data name="UnitAbbreviation_Megabit" xml:space="preserve">
    <value>Mb</value>
    <comment>An abbreviation for a measurement unit of data</comment>
  </data>
  <data name="UnitAbbreviation_Megabyte" xml:space="preserve">
    <value>MB</value>
    <comment>An abbreviation for a measurement unit of data</comment>
  </data>
  <data name="UnitAbbreviation_Meter" xml:space="preserve">
    <value>m</value>
    <comment>An abbreviation for a measurement unit of length</comment>
  </data>
  <data name="UnitAbbreviation_MetersPerSecond" xml:space="preserve">
    <value>m/s</value>
    <comment>An abbreviation for a measurement unit of speed</comment>
  </data>
  <data name="UnitAbbreviation_Micron" xml:space="preserve">
    <value>µm</value>
    <comment>An abbreviation for a measurement unit of length</comment>
  </data>
  <data name="UnitAbbreviation_Microsecond" xml:space="preserve">
    <value>µs</value>
    <comment>An abbreviation for a measurement unit of time</comment>
  </data>
  <data name="UnitAbbreviation_Mile" xml:space="preserve">
    <value>mijl</value>
    <comment>An abbreviation for a measurement unit of length</comment>
  </data>
  <data name="UnitAbbreviation_MilesPerHour" xml:space="preserve">
    <value>mph</value>
    <comment>An abbreviation for a measurement unit of speed</comment>
  </data>
  <data name="UnitAbbreviation_Millimeter" xml:space="preserve">
    <value>mm</value>
    <comment>An abbreviation for a measurement unit of length</comment>
  </data>
  <data name="UnitAbbreviation_Millisecond" xml:space="preserve">
    <value>ms</value>
    <comment>An abbreviation for a measurement unit of time</comment>
  </data>
  <data name="UnitAbbreviation_Minute" xml:space="preserve">
    <value>min</value>
    <comment>An abbreviation for a measurement unit of time</comment>
  </data>
  <data name="UnitAbbreviation_Nanometer" xml:space="preserve">
    <value>zeemijl</value>
    <comment>An abbreviation for a measurement unit of length</comment>
  </data>
  <data name="UnitAbbreviation_NauticalMile" xml:space="preserve">
    <value>nmi</value>
    <comment>An abbreviation for a measurement unit of length</comment>
  </data>
  <data name="UnitAbbreviation_Petabit" xml:space="preserve">
    <value>Pb</value>
    <comment>An abbreviation for a measurement unit of data</comment>
  </data>
  <data name="UnitAbbreviation_Petabyte" xml:space="preserve">
    <value>PB</value>
    <comment>An abbreviation for a measurement unit of data</comment>
  </data>
  <data name="UnitAbbreviation_Foot-PoundPerMinute" xml:space="preserve">
    <value>ft•lb/min</value>
    <comment>An abbreviation for a measurement unit of power</comment>
  </data>
  <data name="UnitAbbreviation_Second" xml:space="preserve">
    <value>s</value>
    <comment>An abbreviation for a measurement unit of time</comment>
  </data>
  <data name="UnitAbbreviation_SquareCentimeter" xml:space="preserve">
    <value>cm²</value>
    <comment>An abbreviation for a measurement unit of area</comment>
  </data>
  <data name="UnitAbbreviation_SquareFoot" xml:space="preserve">
    <value>ft²</value>
    <comment>An abbreviation for a measurement unit of area</comment>
  </data>
  <data name="UnitAbbreviation_SquareInch" xml:space="preserve">
    <value>in²</value>
    <comment>An abbreviation for a measurement unit of area</comment>
  </data>
  <data name="UnitAbbreviation_SquareKilometer" xml:space="preserve">
    <value>km²</value>
    <comment>An abbreviation for a measurement unit of area</comment>
  </data>
  <data name="UnitAbbreviation_SquareMeter" xml:space="preserve">
    <value>m²</value>
    <comment>An abbreviation for a measurement unit of area</comment>
  </data>
  <data name="UnitAbbreviation_SquareMile" xml:space="preserve">
    <value>mi²</value>
    <comment>An abbreviation for a measurement unit of area</comment>
  </data>
  <data name="UnitAbbreviation_SquareMillimeter" xml:space="preserve">
    <value>mm²</value>
    <comment>An abbreviation for a measurement unit of area</comment>
  </data>
  <data name="UnitAbbreviation_SquareYard" xml:space="preserve">
    <value>yd²</value>
    <comment>An abbreviation for a measurement unit of area</comment>
  </data>
  <data name="UnitAbbreviation_Terabit" xml:space="preserve">
    <value>Tb</value>
    <comment>An abbreviation for a measurement unit of data</comment>
  </data>
  <data name="UnitAbbreviation_Terabyte" xml:space="preserve">
    <value>TB</value>
    <comment>An abbreviation for a measurement unit of data</comment>
  </data>
  <data name="UnitAbbreviation_Watt" xml:space="preserve">
    <value>W</value>
    <comment>An abbreviation for a measurement unit of power</comment>
  </data>
  <data name="UnitAbbreviation_Week" xml:space="preserve">
    <value>wk</value>
    <comment>An abbreviation for a measurement unit of time</comment>
  </data>
  <data name="UnitAbbreviation_Yard" xml:space="preserve">
    <value>yd</value>
    <comment>An abbreviation for a measurement unit of length</comment>
  </data>
  <data name="UnitAbbreviation_Year" xml:space="preserve">
    <value>jr</value>
    <comment>An abbreviation for a measurement unit of time</comment>
  </data>
  <data name="UnitAbbreviation_Gibibits" xml:space="preserve">
    <value>Gi</value>
    <comment>An abbreviation for a measurement unit of data</comment>
  </data>
  <data name="UnitAbbreviation_Gibibytes" xml:space="preserve">
    <value>GiB</value>
    <comment>An abbreviation for a measurement unit of data</comment>
  </data>
  <data name="UnitAbbreviation_Kibibits" xml:space="preserve">
    <value>Ki</value>
    <comment>An abbreviation for a measurement unit of data</comment>
  </data>
  <data name="UnitAbbreviation_Kibibytes" xml:space="preserve">
    <value>KiB</value>
    <comment>An abbreviation for a measurement unit of data</comment>
  </data>
  <data name="UnitAbbreviation_Mebibits" xml:space="preserve">
    <value>Mi</value>
    <comment>An abbreviation for a measurement unit of data</comment>
  </data>
  <data name="UnitAbbreviation_Mebibytes" xml:space="preserve">
    <value>MiB</value>
    <comment>An abbreviation for a measurement unit of data</comment>
  </data>
  <data name="UnitAbbreviation_Nibble" xml:space="preserve">
    <value>nybl</value>
    <comment>An abbreviation for a measurement unit of data</comment>
  </data>
  <data name="UnitAbbreviation_Pebibits" xml:space="preserve">
    <value>Pi</value>
    <comment>An abbreviation for a measurement unit of data</comment>
  </data>
  <data name="UnitAbbreviation_Pebibytes" xml:space="preserve">
    <value>PiB</value>
    <comment>An abbreviation for a measurement unit of data</comment>
  </data>
  <data name="UnitAbbreviation_Tebibits" xml:space="preserve">
    <value>Ti</value>
    <comment>An abbreviation for a measurement unit of data</comment>
  </data>
  <data name="UnitAbbreviation_Tebibytes" xml:space="preserve">
    <value>TiB</value>
    <comment>An abbreviation for a measurement unit of data</comment>
  </data>
  <data name="UnitAbbreviation_Exabits" xml:space="preserve">
    <value>E</value>
    <comment>An abbreviation for a measurement unit of data</comment>
  </data>
  <data name="UnitAbbreviation_Exabytes" xml:space="preserve">
    <value>EB</value>
    <comment>An abbreviation for a measurement unit of data</comment>
  </data>
  <data name="UnitAbbreviation_Exbibits" xml:space="preserve">
    <value>Ei</value>
    <comment>An abbreviation for a measurement unit of data</comment>
  </data>
  <data name="UnitAbbreviation_Exbibytes" xml:space="preserve">
    <value>EiB</value>
    <comment>An abbreviation for a measurement unit of data</comment>
  </data>
  <data name="UnitAbbreviation_Zetabits" xml:space="preserve">
    <value>Z</value>
    <comment>An abbreviation for a measurement unit of data</comment>
  </data>
  <data name="UnitAbbreviation_Zetabytes" xml:space="preserve">
    <value>ZB</value>
    <comment>An abbreviation for a measurement unit of data</comment>
  </data>
  <data name="UnitAbbreviation_Zebibits" xml:space="preserve">
    <value>Zi</value>
    <comment>An abbreviation for a measurement unit of data</comment>
  </data>
  <data name="UnitAbbreviation_Zebibytes" xml:space="preserve">
    <value>ZiB</value>
    <comment>An abbreviation for a measurement unit of data</comment>
  </data>
  <data name="UnitAbbreviation_Yottabit" xml:space="preserve">
    <value>Y</value>
    <comment>An abbreviation for a measurement unit of data</comment>
  </data>
  <data name="UnitAbbreviation_Yottabyte" xml:space="preserve">
    <value>YB</value>
    <comment>An abbreviation for a measurement unit of data</comment>
  </data>
  <data name="UnitAbbreviation_Yobibits" xml:space="preserve">
    <value>Yi</value>
    <comment>An abbreviation for a measurement unit of data</comment>
  </data>
  <data name="UnitAbbreviation_Yobibytes" xml:space="preserve">
    <value>YiB</value>
    <comment>An abbreviation for a measurement unit of data</comment>
  </data>
  <data name="UnitName_Acre" xml:space="preserve">
    <value>Acre</value>
    <comment>A measurement unit for area. (Please choose the most appropriate plural form to fit any number between 0 and 999,999,999,999,999)</comment>
  </data>
  <data name="UnitName_Bit" xml:space="preserve">
    <value>Bits</value>
    <comment>A measurement unit for data. (Please choose the most appropriate plural form to fit any number between 0 and 999,999,999,999,999)</comment>
  </data>
  <data name="UnitName_BritishThermalUnit" xml:space="preserve">
    <value>British Thermal Units</value>
    <comment>A measurement unit for energy. (Please choose the most appropriate plural form to fit any number between 0 and 999,999,999,999,999)</comment>
  </data>
  <data name="UnitName_BTUPerMinute" xml:space="preserve">
    <value>BTU's/minuut</value>
    <comment>A measurement unit for power: British Thermal Units per minute. (Please choose the most appropriate plural form to fit any number between 0 and 999,999,999,999,999)</comment>
  </data>
  <data name="UnitName_Byte" xml:space="preserve">
    <value>Bytes</value>
    <comment>A measurement unit for data. (Please choose the most appropriate plural form to fit any number between 0 and 999,999,999,999,999)</comment>
  </data>
  <data name="UnitName_Calorie" xml:space="preserve">
    <value>Thermische calorieën</value>
    <comment>A measurement unit for energy. Please note that this is the "small calorie" used in science for measuring heat energy, not the "large calorie" commonly used for measuring food energy. If there is a simple term to distinguish this one from the large "Food calorie", please use that. (Please choose the most appropriate plural form to fit any number between 0 and 999,999,999,999,999)</comment>
  </data>
  <data name="UnitName_Centimeter" xml:space="preserve">
    <value>Centimeter</value>
    <comment>A measurement unit for length. (Please choose the most appropriate plural form to fit any number between 0 and 999,999,999,999,999)</comment>
  </data>
  <data name="UnitName_CentimetersPerSecond" xml:space="preserve">
    <value>Centimeter per seconde</value>
    <comment>A measurement unit for speed. (Please choose the most appropriate plural form to fit any number between 0 and 999,999,999,999,999)</comment>
  </data>
  <data name="UnitName_CubicCentimeter" xml:space="preserve">
    <value>Kubieke centimeter</value>
    <comment>A measurement unit for volume. (Please choose the most appropriate plural form to fit any number between 0 and 999,999,999,999,999)</comment>
  </data>
  <data name="UnitName_CubicFoot" xml:space="preserve">
    <value>Kubieke voet</value>
    <comment>A measurement unit for volume. (Please choose the most appropriate plural form to fit any number between 0 and 999,999,999,999,999)</comment>
  </data>
  <data name="UnitName_CubicInch" xml:space="preserve">
    <value>Kubieke inch</value>
    <comment>A measurement unit for volume. (Please choose the most appropriate plural form to fit any number between 0 and 999,999,999,999,999)</comment>
  </data>
  <data name="UnitName_CubicMeter" xml:space="preserve">
    <value>Kubieke meter</value>
    <comment>A measurement unit for volume. (Please choose the most appropriate plural form to fit any number between 0 and 999,999,999,999,999)</comment>
  </data>
  <data name="UnitName_CubicYard" xml:space="preserve">
    <value>Kubieke yard</value>
    <comment>A measurement unit for volume. (Please choose the most appropriate plural form to fit any number between 0 and 999,999,999,999,999)</comment>
  </data>
  <data name="UnitName_Day" xml:space="preserve">
    <value>Dagen</value>
    <comment>A measurement unit for time. (Please choose the most appropriate plural form to fit any number between 0 and 999,999,999,999,999)</comment>
  </data>
  <data name="UnitName_DegreesCelsius" xml:space="preserve">
    <value>Celsius</value>
    <comment>An option in the unit converter to select degrees Celsius</comment>
  </data>
  <data name="UnitName_DegreesFahrenheit" xml:space="preserve">
    <value>Fahrenheit</value>
    <comment>An option in the unit converter to select degrees Fahrenheit</comment>
  </data>
  <data name="UnitName_Electron-Volt" xml:space="preserve">
    <value>Electronvolt</value>
    <comment>A measurement unit for energy. (Please choose the most appropriate plural form to fit any number between 0 and 999,999,999,999,999)</comment>
  </data>
  <data name="UnitName_Foot" xml:space="preserve">
    <value>Voet</value>
    <comment>A measurement unit for length. (Please choose the most appropriate plural form to fit any number between 0 and 999,999,999,999,999)</comment>
  </data>
  <data name="UnitName_FeetPerSecond" xml:space="preserve">
    <value>Voet per seconde</value>
    <comment>A measurement unit for speed. (Please choose the most appropriate plural form to fit any number between 0 and 999,999,999,999,999)</comment>
  </data>
  <data name="UnitName_Foot-Pound" xml:space="preserve">
    <value>Voetpond</value>
    <comment>A measurement unit for energy. (Please choose the most appropriate plural form to fit any number between 0 and 999,999,999,999,999)</comment>
  </data>
  <data name="UnitName_Foot-PoundPerMinute" xml:space="preserve">
    <value>Voetpond/minuut</value>
    <comment>A measurement unit for power. (Please choose the most appropriate plural form to fit any number between 0 and 999,999,999,999,999)</comment>
  </data>
  <data name="UnitName_Gigabit" xml:space="preserve">
    <value>Gigabit</value>
    <comment>A measurement unit for data. (Please choose the most appropriate plural form to fit any number between 0 and 999,999,999,999,999)</comment>
  </data>
  <data name="UnitName_Gigabyte" xml:space="preserve">
    <value>Gigabyte</value>
    <comment>A measurement unit for data. (Please choose the most appropriate plural form to fit any number between 0 and 999,999,999,999,999)</comment>
  </data>
  <data name="UnitName_Hectare" xml:space="preserve">
    <value>Hectare</value>
    <comment>A measurement unit for area. (Please choose the most appropriate plural form to fit any number between 0 and 999,999,999,999,999)</comment>
  </data>
  <data name="UnitName_Horsepower" xml:space="preserve">
    <value>Paardenkracht (VS)</value>
    <comment>A measurement unit for power</comment>
  </data>
  <data name="UnitName_Hour" xml:space="preserve">
    <value>Uren</value>
    <comment>A measurement unit for time. (Please choose the most appropriate plural form to fit any number between 0 and 999,999,999,999,999)</comment>
  </data>
  <data name="UnitName_Inch" xml:space="preserve">
    <value>Inch</value>
    <comment>A measurement unit for length. (Please choose the most appropriate plural form to fit any number between 0 and 999,999,999,999,999)</comment>
  </data>
  <data name="UnitName_Joule" xml:space="preserve">
    <value>Joules</value>
    <comment>A measurement unit for energy. (Please choose the most appropriate plural form to fit any number between 0 and 999,999,999,999,999)</comment>
  </data>
  <data name="UnitName_Kilowatthour" xml:space="preserve">
    <value>Kilowatt-uren</value>
    <comment>A measurement unit for electricity consumption. (Please choose the most appropriate plural form to fit any number between 0 and 999,999,999,999,999)</comment>
  </data>
  <data name="UnitName_Kelvin" xml:space="preserve">
    <value>Kelvin</value>
    <comment>An option in the unit converter to select the temperature system "Kelvin" (eg. 0 degrees Celsius = 273 Kelvin). At least in English, Kelvin does not use "degrees". A measurement is just stated as "273 Kelvin".</comment>
  </data>
  <data name="UnitName_Kilobit" xml:space="preserve">
    <value>Kilobit</value>
    <comment>A measurement unit for data. (Please choose the most appropriate plural form to fit any number between 0 and 999,999,999,999,999)</comment>
  </data>
  <data name="UnitName_Kilobyte" xml:space="preserve">
    <value>Kilobyte</value>
    <comment>A measurement unit for data. (Please choose the most appropriate plural form to fit any number between 0 and 999,999,999,999,999)</comment>
  </data>
  <data name="UnitName_Kilocalorie" xml:space="preserve">
    <value>Voedingscalorieën</value>
    <comment>A measurement unit for energy. The scientific name is kilocalorie, but this is what is commonly referred to as a "calorie" or "large calorie" when talking about food. Please use the everyday-use word for food energy calories if there is one. (Please choose the most appropriate plural form to fit any number between 0 and 999,999,999,999,999)</comment>
  </data>
  <data name="UnitName_Kilojoule" xml:space="preserve">
    <value>Kilojoules</value>
    <comment>A measurement unit for energy. (Please choose the most appropriate plural form to fit any number between 0 and 999,999,999,999,999)</comment>
  </data>
  <data name="UnitName_Kilometer" xml:space="preserve">
    <value>Kilometer</value>
    <comment>A measurement unit for length. (Please choose the most appropriate plural form to fit any number between 0 and 999,999,999,999,999)</comment>
  </data>
  <data name="UnitName_KilometersPerHour" xml:space="preserve">
    <value>Kilometer per uur</value>
    <comment>A measurement unit for speed. (Please choose the most appropriate plural form to fit any number between 0 and 999,999,999,999,999)</comment>
  </data>
  <data name="UnitName_Kilowatt" xml:space="preserve">
    <value>Kilowatt</value>
    <comment>A measurement unit for power. (Please choose the most appropriate plural form to fit any number between 0 and 999,999,999,999,999)</comment>
  </data>
  <data name="UnitName_Knot" xml:space="preserve">
    <value>Knopen</value>
    <comment>A nautical/aerial measurement unit for speed. (Please choose the most appropriate plural form to fit any number between 0 and 999,999,999,999,999)</comment>
  </data>
  <data name="UnitName_Mach" xml:space="preserve">
    <value>Mach</value>
    <comment>A measurement of speed (Mach is the speed of sound, Mach 2 is 2 times the speed of sound)</comment>
  </data>
  <data name="UnitName_Megabit" xml:space="preserve">
    <value>Megabit</value>
    <comment>A measurement unit for data. (Please choose the most appropriate plural form to fit any number between 0 and 999,999,999,999,999)</comment>
  </data>
  <data name="UnitName_Megabyte" xml:space="preserve">
    <value>Megabytes</value>
    <comment>A measurement unit for data. (Please choose the most appropriate plural form to fit any number between 0 and 999,999,999,999,999)</comment>
  </data>
  <data name="UnitName_Meter" xml:space="preserve">
    <value>Meter</value>
    <comment>A measurement unit for length. (Please choose the most appropriate plural form to fit any number between 0 and 999,999,999,999,999)</comment>
  </data>
  <data name="UnitName_MetersPerSecond" xml:space="preserve">
    <value>Meter per seconde</value>
    <comment>A measurement unit for speed. (Please choose the most appropriate plural form to fit any number between 0 and 999,999,999,999,999)</comment>
  </data>
  <data name="UnitName_Micron" xml:space="preserve">
    <value>Micron</value>
    <comment>A measurement unit for length. (Please choose the most appropriate plural form to fit any number between 0 and 999,999,999,999,999)</comment>
  </data>
  <data name="UnitName_Microsecond" xml:space="preserve">
    <value>Microseconden</value>
    <comment>A measurement unit for time. (Please choose the most appropriate plural form to fit any number between 0 and 999,999,999,999,999)</comment>
  </data>
  <data name="UnitName_Mile" xml:space="preserve">
    <value>Mijl</value>
    <comment>A measurement unit for length. (Please choose the most appropriate plural form to fit any number between 0 and 999,999,999,999,999)</comment>
  </data>
  <data name="UnitName_MilesPerHour" xml:space="preserve">
    <value>Mijl per uur</value>
    <comment>A measurement unit for speed. (Please choose the most appropriate plural form to fit any number between 0 and 999,999,999,999,999)</comment>
  </data>
  <data name="UnitName_Millimeter" xml:space="preserve">
    <value>Millimeter</value>
    <comment>A measurement unit for length. (Please choose the most appropriate plural form to fit any number between 0 and 999,999,999,999,999)</comment>
  </data>
  <data name="UnitName_Millisecond" xml:space="preserve">
    <value>Milliseconden</value>
    <comment>A measurement unit for time. (Please choose the most appropriate plural form to fit any number between 0 and 999,999,999,999,999)</comment>
  </data>
  <data name="UnitName_Minute" xml:space="preserve">
    <value>Minuten</value>
    <comment>A measurement unit for time. (Please choose the most appropriate plural form to fit any number between 0 and 999,999,999,999,999)</comment>
  </data>
  <data name="UnitName_Nibble" xml:space="preserve">
    <value>Nibble</value>
    <comment>A measurement unit for data. (Please choose the most appropriate plural form to fit any number between 0 and 999,999,999,999,999)</comment>
  </data>
  <data name="UnitName_Nanometer" xml:space="preserve">
    <value>Nanometer</value>
    <comment>A measurement unit for length. (Please choose the most appropriate plural form to fit any number between 0 and 999,999,999,999,999)</comment>
  </data>
  <data name="UnitName_Angstrom" xml:space="preserve">
    <value>Ångström</value>
    <comment>A measurement unit for length. (Please choose the most appropriate plural form to fit any number between 0 and 999,999,999,999,999)</comment>
  </data>
  <data name="UnitName_NauticalMile" xml:space="preserve">
    <value>Zeemijlen</value>
    <comment>A measurement unit for length. (Please choose the most appropriate plural form to fit any number between 0 and 999,999,999,999,999)</comment>
  </data>
  <data name="UnitName_Petabit" xml:space="preserve">
    <value>Petabits</value>
    <comment>A measurement unit for data. (Please choose the most appropriate plural form to fit any number between 0 and 999,999,999,999,999)</comment>
  </data>
  <data name="UnitName_Petabyte" xml:space="preserve">
    <value>Petabyte</value>
    <comment>A measurement unit for data. (Please choose the most appropriate plural form to fit any number between 0 and 999,999,999,999,999)</comment>
  </data>
  <data name="UnitName_Second" xml:space="preserve">
    <value>Seconden</value>
    <comment>A measurement unit for time. (Please choose the most appropriate plural form to fit any number between 0 and 999,999,999,999,999)</comment>
  </data>
  <data name="UnitName_SquareCentimeter" xml:space="preserve">
    <value>Vierkante centimeter</value>
    <comment>A measurement unit for area. (Please choose the most appropriate plural form to fit any number between 0 and 999,999,999,999,999)</comment>
  </data>
  <data name="UnitName_SquareFoot" xml:space="preserve">
    <value>Vierkante voet</value>
    <comment>A measurement unit for area. (Please choose the most appropriate plural form to fit any number between 0 and 999,999,999,999,999)</comment>
  </data>
  <data name="UnitName_SquareInch" xml:space="preserve">
    <value>Vierkante inch</value>
    <comment>A measurement unit for area. (Please choose the most appropriate plural form to fit any number between 0 and 999,999,999,999,999)</comment>
  </data>
  <data name="UnitName_SquareKilometer" xml:space="preserve">
    <value>Vierkante kilometer</value>
    <comment>A measurement unit for area. (Please choose the most appropriate plural form to fit any number between 0 and 999,999,999,999,999)</comment>
  </data>
  <data name="UnitName_SquareMeter" xml:space="preserve">
    <value>Vierkante meter</value>
    <comment>A measurement unit for area. (Please choose the most appropriate plural form to fit any number between 0 and 999,999,999,999,999)</comment>
  </data>
  <data name="UnitName_SquareMile" xml:space="preserve">
    <value>Vierkante mijl</value>
    <comment>A measurement unit for area. (Please choose the most appropriate plural form to fit any number between 0 and 999,999,999,999,999)</comment>
  </data>
  <data name="UnitName_SquareMillimeter" xml:space="preserve">
    <value>Vierkante millimeter</value>
    <comment>A measurement unit for area. (Please choose the most appropriate plural form to fit any number between 0 and 999,999,999,999,999)</comment>
  </data>
  <data name="UnitName_SquareYard" xml:space="preserve">
    <value>Vierkante yard</value>
    <comment>A measurement unit for area. (Please choose the most appropriate plural form to fit any number between 0 and 999,999,999,999,999)</comment>
  </data>
  <data name="UnitName_Terabit" xml:space="preserve">
    <value>Terabit</value>
    <comment>A measurement unit for data. (Please choose the most appropriate plural form to fit any number between 0 and 999,999,999,999,999)</comment>
  </data>
  <data name="UnitName_Terabyte" xml:space="preserve">
    <value>Terabyte</value>
    <comment>A measurement unit for data. (Please choose the most appropriate plural form to fit any number between 0 and 999,999,999,999,999)</comment>
  </data>
  <data name="UnitName_Watt" xml:space="preserve">
    <value>Watt</value>
    <comment>A measurement unit for power. (Please choose the most appropriate plural form to fit any number between 0 and 999,999,999,999,999)</comment>
  </data>
  <data name="UnitName_Week" xml:space="preserve">
    <value>Weken</value>
    <comment>A measurement unit for time. (Please choose the most appropriate plural form to fit any number between 0 and 999,999,999,999,999)</comment>
  </data>
  <data name="UnitName_Yard" xml:space="preserve">
    <value>Yard</value>
    <comment>A measurement unit for length. (Please choose the most appropriate plural form to fit any number between 0 and 999,999,999,999,999)</comment>
  </data>
  <data name="UnitName_Year" xml:space="preserve">
    <value>Jaar</value>
    <comment>A measurement unit for time. (Please choose the most appropriate plural form to fit any number between 0 and 999,999,999,999,999)</comment>
  </data>
  <data name="UnitAbbreviation_Carat" xml:space="preserve">
    <value>Cd</value>
    <comment>An abbreviation for a measurement unit of weight</comment>
  </data>
  <data name="UnitAbbreviation_Degree" xml:space="preserve">
    <value>graden</value>
    <comment>An abbreviation for a measurement unit of Angle</comment>
  </data>
  <data name="UnitAbbreviation_Radian" xml:space="preserve">
    <value>rad</value>
    <comment>An abbreviation for a measurement unit of Angle</comment>
  </data>
  <data name="UnitAbbreviation_Gradian" xml:space="preserve">
    <value>grad</value>
    <comment>An abbreviation for a measurement unit of Angle</comment>
  </data>
  <data name="UnitAbbreviation_Atmosphere" xml:space="preserve">
    <value>atm</value>
    <comment>An abbreviation for a measurement unit of Pressure</comment>
  </data>
  <data name="UnitAbbreviation_Bar" xml:space="preserve">
    <value>ba</value>
    <comment>An abbreviation for a measurement unit of Pressure</comment>
  </data>
  <data name="UnitAbbreviation_KiloPascal" xml:space="preserve">
    <value>kPa</value>
    <comment>An abbreviation for a measurement unit of Pressure</comment>
  </data>
  <data name="UnitAbbreviation_MillimeterOfMercury" xml:space="preserve">
    <value>mmHg</value>
    <comment>An abbreviation for a measurement unit of Pressure</comment>
  </data>
  <data name="UnitAbbreviation_Pascal" xml:space="preserve">
    <value>Pa</value>
    <comment>An abbreviation for a measurement unit of Pressure</comment>
  </data>
  <data name="UnitAbbreviation_PSI" xml:space="preserve">
    <value>psi</value>
    <comment>An abbreviation for a measurement unit of Pressure</comment>
  </data>
  <data name="UnitAbbreviation_Centigram" xml:space="preserve">
    <value>cg</value>
    <comment>An abbreviation for a measurement unit of weight</comment>
  </data>
  <data name="UnitAbbreviation_Decagram" xml:space="preserve">
    <value>dag</value>
    <comment>An abbreviation for a measurement unit of weight</comment>
  </data>
  <data name="UnitAbbreviation_Decigram" xml:space="preserve">
    <value>dg</value>
    <comment>An abbreviation for a measurement unit of weight</comment>
  </data>
  <data name="UnitAbbreviation_Gram" xml:space="preserve">
    <value>g</value>
    <comment>An abbreviation for a measurement unit of weight</comment>
  </data>
  <data name="UnitAbbreviation_Hectogram" xml:space="preserve">
    <value>hg</value>
    <comment>An abbreviation for a measurement unit of weight</comment>
  </data>
  <data name="UnitAbbreviation_Kilogram" xml:space="preserve">
    <value>kg</value>
    <comment>An abbreviation for a measurement unit of weight</comment>
  </data>
  <data name="UnitAbbreviation_LongTon" xml:space="preserve">
    <value>ton (VK)</value>
    <comment>An abbreviation for a measurement unit of weight</comment>
  </data>
  <data name="UnitAbbreviation_Milligram" xml:space="preserve">
    <value>mg</value>
    <comment>An abbreviation for a measurement unit of weight</comment>
  </data>
  <data name="UnitAbbreviation_Ounce" xml:space="preserve">
    <value>oz</value>
    <comment>An abbreviation for a measurement unit of weight</comment>
  </data>
  <data name="UnitAbbreviation_Pound" xml:space="preserve">
    <value>lb</value>
    <comment>An abbreviation for a measurement unit of weight</comment>
  </data>
  <data name="UnitAbbreviation_ShortTon" xml:space="preserve">
    <value>ton (VS)</value>
    <comment>An abbreviation for a measurement unit of weight</comment>
  </data>
  <data name="UnitAbbreviation_Stone" xml:space="preserve">
    <value>st</value>
    <comment>An abbreviation for a measurement unit of weight</comment>
  </data>
  <data name="UnitAbbreviation_Tonne" xml:space="preserve">
    <value>t</value>
    <comment>An abbreviation for a measurement unit of weight</comment>
  </data>
  <data name="UnitName_Carat" xml:space="preserve">
    <value>Karaat</value>
    <comment>A measurement unit for weight. (Please choose the most appropriate plural form to fit any number between 0 and 999,999,999,999,999)</comment>
  </data>
  <data name="UnitName_Degree" xml:space="preserve">
    <value>Graden</value>
    <comment>A measurement unit for Angle.</comment>
  </data>
  <data name="UnitName_Radian" xml:space="preserve">
    <value>Radialen</value>
    <comment>A measurement unit for Angle.</comment>
  </data>
  <data name="UnitName_Gradian" xml:space="preserve">
    <value>Gradiënten</value>
    <comment>A measurement unit for Angle.</comment>
  </data>
  <data name="UnitName_Atmosphere" xml:space="preserve">
    <value>Atmosfeer</value>
    <comment>A measurement unit for Pressure.</comment>
  </data>
  <data name="UnitName_Bar" xml:space="preserve">
    <value>Bar</value>
    <comment>A measurement unit for Pressure.</comment>
  </data>
  <data name="UnitName_KiloPascal" xml:space="preserve">
    <value>Kilopascal</value>
    <comment>A measurement unit for Pressure.</comment>
  </data>
  <data name="UnitName_MillimeterOfMercury" xml:space="preserve">
    <value>Millimeters kwik </value>
    <comment>A measurement unit for Pressure.</comment>
  </data>
  <data name="UnitName_Pascal" xml:space="preserve">
    <value>Pascal</value>
    <comment>A measurement unit for Pressure.</comment>
  </data>
  <data name="UnitName_PSI" xml:space="preserve">
    <value>Pond per vierkante inch</value>
    <comment>A measurement unit for Pressure.</comment>
  </data>
  <data name="UnitName_Centigram" xml:space="preserve">
    <value>Centigram</value>
    <comment>A measurement unit for weight. (Please choose the most appropriate plural form to fit any number between 0 and 999,999,999,999,999)</comment>
  </data>
  <data name="UnitName_Decagram" xml:space="preserve">
    <value>Decagram</value>
    <comment>A measurement unit for weight. Note: Dekagram is spelled "decagram" everywhere except where US English is used. (EN-US dekagram, elsewhere decagram). (Please choose the most appropriate plural form to fit any number between 0 and 999,999,999,999,999)</comment>
  </data>
  <data name="UnitName_Decigram" xml:space="preserve">
    <value>Decigram</value>
    <comment>A measurement unit for weight. (Please choose the most appropriate plural form to fit any number between 0 and 999,999,999,999,999)</comment>
  </data>
  <data name="UnitName_Gram" xml:space="preserve">
    <value>Gram</value>
    <comment>A measurement unit for weight. (Please choose the most appropriate plural form to fit any number between 0 and 999,999,999,999,999)</comment>
  </data>
  <data name="UnitName_Hectogram" xml:space="preserve">
    <value>Hectogram</value>
    <comment>A measurement unit for weight. (Please choose the most appropriate plural form to fit any number between 0 and 999,999,999,999,999)</comment>
  </data>
  <data name="UnitName_Kilogram" xml:space="preserve">
    <value>Kilogram</value>
    <comment>A measurement unit for weight. (Please choose the most appropriate plural form to fit any number between 0 and 999,999,999,999,999)</comment>
  </data>
  <data name="UnitName_LongTon" xml:space="preserve">
    <value>Long tons (VK)</value>
    <comment>A measurement unit for weight. This is the UK version of ton. (Please choose the most appropriate plural form to fit any number between 0 and 999,999,999,999,999)</comment>
  </data>
  <data name="UnitName_Milligram" xml:space="preserve">
    <value>Milligram</value>
    <comment>A measurement unit for weight. (Please choose the most appropriate plural form to fit any number between 0 and 999,999,999,999,999)</comment>
  </data>
  <data name="UnitName_Ounce" xml:space="preserve">
    <value>Ounces</value>
    <comment>A measurement unit for weight. (Please choose the most appropriate plural form to fit any number between 0 and 999,999,999,999,999)</comment>
  </data>
  <data name="UnitName_Pound" xml:space="preserve">
    <value>Ponden</value>
    <comment>A measurement unit for weight. (Please choose the most appropriate plural form to fit any number between 0 and 999,999,999,999,999)</comment>
  </data>
  <data name="UnitName_ShortTon" xml:space="preserve">
    <value>Short tons (VS)</value>
    <comment>A measurement unit for weight. This is the US version of ton. (Please choose the most appropriate plural form to fit any number between 0 and 999,999,999,999,999)</comment>
  </data>
  <data name="UnitName_Stone" xml:space="preserve">
    <value>Stone</value>
    <comment>A measurement unit for weight. Equal to 14 pounds. Note that this is the plural form of the word in English (eg. "This man weighs 11 stone."). (Please choose the most appropriate plural form to fit any number between 0 and 999,999,999,999,999)</comment>
  </data>
  <data name="UnitName_Tonne" xml:space="preserve">
    <value>Metrische ton</value>
    <comment>A measurement unit for weight. This is the metric version of tonne. (Please choose the most appropriate plural form to fit any number between 0 and 999,999,999,999,999)</comment>
  </data>
  <data name="UnitName_CD" xml:space="preserve">
    <value>Cd’s</value>
    <comment>A compact disc, used as a comparison measurement unit for data storage. (Please choose the most appropriate plural form to fit any number between 0 and 999,999,999,999,999)</comment>
  </data>
  <data name="UnitAbbreviation_CD" xml:space="preserve">
    <value>Cd’s</value>
    <comment>A compact disc, used as a comparison measurement unit for data storage. (Please choose the most appropriate plural form to fit any number between 0 and 999,999,999,999,999)</comment>
  </data>
  <data name="UnitName_SoccerField" xml:space="preserve">
    <value>voetbalvelden</value>
    <comment>A professional-sized soccer field, used as a comparison measurement unit for area. (Please choose the most appropriate plural form to fit any number between 0 and 999,999,999,999,999)</comment>
  </data>
  <data name="UnitAbbreviation_SoccerField" xml:space="preserve">
    <value>voetbalvelden</value>
    <comment>A professional-sized soccer field, used as a comparison measurement unit for area. (Please choose the most appropriate plural form to fit any number between 0 and 999,999,999,999,999)</comment>
  </data>
  <data name="UnitName_FloppyDisk" xml:space="preserve">
    <value>diskettes</value>
    <comment>A 1.44 MB floppy disk, used as a comparison measurement unit for data storage. (Please choose the most appropriate plural form to fit any number between 0 and 999,999,999,999,999)</comment>
  </data>
  <data name="UnitAbbreviation_FloppyDisk" xml:space="preserve">
    <value>diskettes</value>
    <comment>A 1.44 MB floppy disk, used as a comparison measurement unit for data storage. (Please choose the most appropriate plural form to fit any number between 0 and 999,999,999,999,999)</comment>
  </data>
  <data name="UnitName_DVD" xml:space="preserve">
    <value>Dvd’s</value>
    <comment>A DVD, used as a comparison measurement unit for data storage. (Please choose the most appropriate plural form to fit any number between 0 and 999,999,999,999,999)</comment>
  </data>
  <data name="UnitAbbreviation_DVD" xml:space="preserve">
    <value>Dvd’s</value>
    <comment>A DVD, used as a comparison measurement unit for data storage. (Please choose the most appropriate plural form to fit any number between 0 and 999,999,999,999,999)</comment>
  </data>
  <data name="UnitName_Battery" xml:space="preserve">
    <value>batterijen</value>
    <comment>AA-cell battery, used as a comparison measurement unit for data storage. (Please choose the most appropriate plural form to fit any number between 0 and 999,999,999,999,999)</comment>
  </data>
  <data name="UnitAbbreviation_Battery" xml:space="preserve">
    <value>batterijen</value>
    <comment>AA-cell battery, used as a comparison measurement unit for data storage. (Please choose the most appropriate plural form to fit any number between 0 and 999,999,999,999,999)</comment>
  </data>
  <data name="UnitName_Paperclip" xml:space="preserve">
    <value>paperclips</value>
    <comment>A standard paperclip, used as a comparison measurement unit for length. (Please choose the most appropriate plural form to fit any number between 0 and 999,999,999,999,999)</comment>
  </data>
  <data name="UnitAbbreviation_Paperclip" xml:space="preserve">
    <value>paperclips</value>
    <comment>A standard paperclip, used as a comparison measurement unit for length. (Please choose the most appropriate plural form to fit any number between 0 and 999,999,999,999,999)</comment>
  </data>
  <data name="UnitName_JumboJet" xml:space="preserve">
    <value>jumbojets</value>
    <comment>A jumbo jet (eg. Boeing 747), used as a comparison measurement unit for length. (Please choose the most appropriate plural form to fit any number between 0 and 999,999,999,999,999)</comment>
  </data>
  <data name="UnitAbbreviation_JumboJet" xml:space="preserve">
    <value>jumbojets</value>
    <comment>A jumbo jet (eg. Boeing 747), used as a comparison measurement unit for length. (Please choose the most appropriate plural form to fit any number between 0 and 999,999,999,999,999)</comment>
  </data>
  <data name="UnitName_LightBulb" xml:space="preserve">
    <value>lampen</value>
    <comment>A light bulb, used as a comparison measurement unit for power (60 watts). (Please choose the most appropriate plural form to fit any number between 0 and 999,999,999,999,999)</comment>
  </data>
  <data name="UnitAbbreviation_LightBulb" xml:space="preserve">
    <value>lampen</value>
    <comment>A light bulb, used as a comparison measurement unit for power (60 watts). (Please choose the most appropriate plural form to fit any number between 0 and 999,999,999,999,999)</comment>
  </data>
  <data name="UnitName_Horse" xml:space="preserve">
    <value>paarden</value>
    <comment>A horse, used as a comparison measurement unit for power (~1 horsepower) or speed. (Please choose the most appropriate plural form to fit any number between 0 and 999,999,999,999,999)</comment>
  </data>
  <data name="UnitAbbreviation_Horse" xml:space="preserve">
    <value>paarden</value>
    <comment>A horse, used as a comparison measurement unit for power (~1 horsepower) or speed. (Please choose the most appropriate plural form to fit any number between 0 and 999,999,999,999,999)</comment>
  </data>
  <data name="UnitName_Bathtub" xml:space="preserve">
    <value>badkuipen</value>
    <comment>A bathtub full of water, used as a comparison measurement unit for volume. (Please choose the most appropriate plural form to fit any number between 0 and 999,999,999,999,999)</comment>
  </data>
  <data name="UnitAbbreviation_Bathtub" xml:space="preserve">
    <value>badkuipen</value>
    <comment>A bathtub full of water, used as a comparison measurement unit for volume. (Please choose the most appropriate plural form to fit any number between 0 and 999,999,999,999,999)</comment>
  </data>
  <data name="UnitName_Snowflake" xml:space="preserve">
    <value>sneeuwvlokjes</value>
    <comment>A snowflake, used as a comparison measurement unit for weight. (Please choose the most appropriate plural form to fit any number between 0 and 999,999,999,999,999)</comment>
  </data>
  <data name="UnitAbbreviation_Snowflake" xml:space="preserve">
    <value>sneeuwvlokjes</value>
    <comment>A snowflake, used as a comparison measurement unit for weight. (Please choose the most appropriate plural form to fit any number between 0 and 999,999,999,999,999)</comment>
  </data>
  <data name="UnitName_Elephant" xml:space="preserve">
    <value>olifanten</value>
    <comment>An elephant, used as a comparison measurement unit for weight. (Please choose the most appropriate plural form to fit any number between 0 and 999,999,999,999,999)</comment>
  </data>
  <data name="UnitAbbreviation_Elephant" xml:space="preserve">
    <value>olifanten</value>
    <comment>An elephant, used as a comparison measurement unit for weight. (Please choose the most appropriate plural form to fit any number between 0 and 999,999,999,999,999)</comment>
  </data>
  <data name="UnitName_Turtle" xml:space="preserve">
    <value>schildpadden</value>
    <comment>A turtle, used as a comparison measurement unit for speed. (Please choose the most appropriate plural form to fit any number between 0 and 999,999,999,999,999)</comment>
  </data>
  <data name="UnitAbbreviation_Turtle" xml:space="preserve">
    <value>schildpadden</value>
    <comment>A turtle, used as a comparison measurement unit for speed. (Please choose the most appropriate plural form to fit any number between 0 and 999,999,999,999,999)</comment>
  </data>
  <data name="UnitName_Jet" xml:space="preserve">
    <value>straalvliegtuigen</value>
    <comment>A jet plane, used as a comparison measurement unit for speed. (Please choose the most appropriate plural form to fit any number between 0 and 999,999,999,999,999)</comment>
  </data>
  <data name="UnitAbbreviation_Jet" xml:space="preserve">
    <value>straalvliegtuigen</value>
    <comment>A jet plane, used as a comparison measurement unit for speed. (Please choose the most appropriate plural form to fit any number between 0 and 999,999,999,999,999)</comment>
  </data>
  <data name="UnitName_Whale" xml:space="preserve">
    <value>walvissen</value>
    <comment>A blue whale, used as a comparison measurement unit for weight. (Please choose the most appropriate plural form to fit any number between 0 and 999,999,999,999,999)</comment>
  </data>
  <data name="UnitAbbreviation_Whale" xml:space="preserve">
    <value>walvissen</value>
    <comment>A blue whale, used as a comparison measurement unit for weight. (Please choose the most appropriate plural form to fit any number between 0 and 999,999,999,999,999)</comment>
  </data>
  <data name="UnitName_CoffeeCup" xml:space="preserve">
    <value>koffiekopjes</value>
    <comment>A coffee cup, used as a comparison measurement unit for volume. (Please choose the most appropriate plural form to fit any number between 0 and 999,999,999,999,999)</comment>
  </data>
  <data name="UnitAbbreviation_CoffeeCup" xml:space="preserve">
    <value>koffiekopjes</value>
    <comment>A coffee cup, used as a comparison measurement unit for volume. (Please choose the most appropriate plural form to fit any number between 0 and 999,999,999,999,999)</comment>
  </data>
  <data name="UnitName_SwimmingPool" xml:space="preserve">
    <value>zwembaden</value>
    <comment>An Olympic-sized swimming pool, used as a comparison measurement unit for volume. (Please choose the most appropriate plural form to fit any number between 0 and 999,999,999,999,999)</comment>
  </data>
  <data name="UnitAbbreviation_SwimmingPool" xml:space="preserve">
    <value>zwembaden</value>
    <comment>An Olympic-sized swimming pool, used as a comparison measurement unit for volume. (Please choose the most appropriate plural form to fit any number between 0 and 999,999,999,999,999)</comment>
  </data>
  <data name="UnitName_Hand" xml:space="preserve">
    <value>handen</value>
    <comment>A human hand, used as a comparison measurement unit for length or area. (Please choose the most appropriate plural form to fit any number between 0 and 999,999,999,999,999)</comment>
  </data>
  <data name="UnitAbbreviation_Hand" xml:space="preserve">
    <value>handen</value>
    <comment>A human hand, used as a comparison measurement unit for length or area. (Please choose the most appropriate plural form to fit any number between 0 and 999,999,999,999,999)</comment>
  </data>
  <data name="UnitName_Paper" xml:space="preserve">
    <value>vellen papier</value>
    <comment>A sheet of 8.5 x 11 inch paper, used as a comparison measurement unit for area. (Please choose the most appropriate plural form to fit any number between 0 and 999,999,999,999,999)</comment>
  </data>
  <data name="UnitAbbreviation_Paper" xml:space="preserve">
    <value>vellen papier</value>
    <comment>A sheet of 8.5 x 11 inch paper, used as a comparison measurement unit for area. (Please choose the most appropriate plural form to fit any number between 0 and 999,999,999,999,999)</comment>
  </data>
  <data name="UnitName_Castle" xml:space="preserve">
    <value>kastelen</value>
    <comment>A castle, used as a comparison measurement unit for area (floorspace). (Please choose the most appropriate plural form to fit any number between 0 and 999,999,999,999,999)</comment>
  </data>
  <data name="UnitAbbreviation_Castle" xml:space="preserve">
    <value>kastelen</value>
    <comment>A castle, used as a comparison measurement unit for area (floorspace). (Please choose the most appropriate plural form to fit any number between 0 and 999,999,999,999,999)</comment>
  </data>
  <data name="UnitName_Banana" xml:space="preserve">
    <value>bananen</value>
    <comment>A banana, used as a comparison measurement unit for food energy. (Please choose the most appropriate plural form to fit any number between 0 and 999,999,999,999,999)</comment>
  </data>
  <data name="UnitAbbreviation_Banana" xml:space="preserve">
    <value>bananen</value>
    <comment>A banana, used as a comparison measurement unit for food energy. (Please choose the most appropriate plural form to fit any number between 0 and 999,999,999,999,999)</comment>
  </data>
  <data name="UnitName_SliceOfCake" xml:space="preserve">
    <value>plakjes cake</value>
    <comment>A slice of chocolate cake, used as a comparison measurement unit for food energy. (Please choose the most appropriate plural form to fit any number between 0 and 999,999,999,999,999)</comment>
  </data>
  <data name="UnitAbbreviation_SliceOfCake" xml:space="preserve">
    <value>plakjes cake</value>
    <comment>A slice of chocolate cake, used as a comparison measurement unit for food energy. (Please choose the most appropriate plural form to fit any number between 0 and 999,999,999,999,999)</comment>
  </data>
  <data name="UnitName_TrainEngine" xml:space="preserve">
    <value>treinmotoren</value>
    <comment>A train engine, used as a comparison measurement unit for power. (Please choose the most appropriate plural form to fit any number between 0 and 999,999,999,999,999)</comment>
  </data>
  <data name="UnitAbbreviation_TrainEngine" xml:space="preserve">
    <value>treinmotoren</value>
    <comment>A train engine, used as a comparison measurement unit for power. (Please choose the most appropriate plural form to fit any number between 0 and 999,999,999,999,999)</comment>
  </data>
  <data name="UnitName_SoccerBall" xml:space="preserve">
    <value>voetballen</value>
    <comment>A soccer ball, used as a comparison measurement unit for weight. (Please choose the most appropriate plural form to fit any number between 0 and 999,999,999,999,999)</comment>
  </data>
  <data name="UnitAbbreviation_SoccerBall" xml:space="preserve">
    <value>voetballen</value>
    <comment>A soccer ball, used as a comparison measurement unit for weight. (Please choose the most appropriate plural form to fit any number between 0 and 999,999,999,999,999)</comment>
  </data>
  <data name="MemoryItemHelpText" xml:space="preserve">
    <value>Geheugenitem</value>
    <comment>Help text used by accessibility tools to indicate that an item in the list of memory values is a memory item.</comment>
  </data>
  <data name="SupplementaryUnit_AutomationName" xml:space="preserve">
    <value>%1 %2</value>
    <comment>This string is what is read by Narrator, and other screen readers, for the supplementary value at the bottom of the converter view, %1 = the value of the supplementary unit (i.e. 0.5), %2 = the unit itself (i.e. inches, meters, etc)</comment>
  </data>
  <data name="AboutControlBackButton.[using:Windows.UI.Xaml.Automation]AutomationProperties.Name" xml:space="preserve">
    <value>Terug</value>
    <comment>Screen reader prompt for the About panel back button</comment>
  </data>
  <data name="AboutControlBackButton.[using:Windows.UI.Xaml.Controls]ToolTipService.ToolTip" xml:space="preserve">
    <value>Terug</value>
    <comment>Content of tooltip being displayed on AboutControlBackButton</comment>
  </data>
  <data name="AboutEULA.Text" xml:space="preserve">
    <value>Licentievoorwaarden voor Microsoft-software</value>
    <comment>Displayed on a link to the Microsoft Software License Terms on the About panel</comment>
  </data>
  <data name="PreviewTag.Text" xml:space="preserve">
    <value>Voorbeeldweergave</value>
    <comment>Label displayed next to upcoming features</comment>
  </data>
  <data name="AboutControlPrivacyStatement.Text" xml:space="preserve">
    <value>Privacyverklaring van Microsoft</value>
    <comment>Displayed on a link to the Microsoft Privacy Statement on the About panel</comment>
  </data>
  <data name="AboutControlCopyright" xml:space="preserve">
    <value>© %1 Microsoft. Alle rechten voorbehouden.</value>
    <comment>{Locked="%1"}. Copyright statement, displayed on the About panel. %1 = the current year (4 digits)</comment>
  </data>
  <data name="AboutControlContribute" xml:space="preserve">
    <value>Voor meer informatie over hoe u kunt bijdragen aan Windows-rekenmachine, bekijkt u het project op %HL%GitHub%HL%.</value>
    <comment>{Locked="%HL%GitHub%HL%"}. GitHub link, Displayed on the About panel</comment>
  </data>
  <data name="AboutGroupTitle.Text" xml:space="preserve">
    <value>Info over</value>
    <comment>Subtitle of about message on Settings page</comment>
  </data>
  <data name="FeedbackButton.Content" xml:space="preserve">
    <value>Feedback verzenden</value>
    <comment>The text that shows in the dropdown navigation control to give the user the option to send feedback about the app and it launches Windows Feedback app</comment>
  </data>
  <data name="HistoryEmpty.Text" xml:space="preserve">
    <value>Er is nog geen geschiedenis.</value>
    <comment>The text that shows as the header for the history list</comment>
  </data>
  <data name="MemoryPaneEmpty.Text" xml:space="preserve">
    <value>Er is niets opgeslagen in het geheugen.</value>
    <comment>The text that shows as the header for the memory list</comment>
  </data>
  <data name="MemoryFlyout.[using:Windows.UI.Xaml.Automation]AutomationProperties.Name" xml:space="preserve">
    <value>Geheugen</value>
    <comment>Screen reader prompt for the negate button on the converter operator keypad</comment>
  </data>
  <data name="CannotPaste" xml:space="preserve">
    <value>Deze expressie kan niet worden geplakt</value>
    <comment>The paste operation cannot be performed, if the expression is invalid.</comment>
  </data>
  <data name="UnitName_Gibibits" xml:space="preserve">
    <value>Gibibit</value>
    <comment>A measurement unit for data. (Please choose the most appropriate plural form to fit any number between 0 and 999,999,999,999,999)</comment>
  </data>
  <data name="UnitName_Gibibytes" xml:space="preserve">
    <value>Gibibyte</value>
    <comment>A measurement unit for data. (Please choose the most appropriate plural form to fit any number between 0 and 999,999,999,999,999)</comment>
  </data>
  <data name="UnitName_Kibibits" xml:space="preserve">
    <value>Kibibits</value>
    <comment>A measurement unit for data. (Please choose the most appropriate plural form to fit any number between 0 and 999,999,999,999,999)</comment>
  </data>
  <data name="UnitName_Kibibytes" xml:space="preserve">
    <value>Kibibyte</value>
    <comment>A measurement unit for data. (Please choose the most appropriate plural form to fit any number between 0 and 999,999,999,999,999)</comment>
  </data>
  <data name="UnitName_Mebibits" xml:space="preserve">
    <value>Mebibit</value>
    <comment>A measurement unit for data. (Please choose the most appropriate plural form to fit any number between 0 and 999,999,999,999,999)</comment>
  </data>
  <data name="UnitName_Mebibytes" xml:space="preserve">
    <value>Mebibyte</value>
    <comment>A measurement unit for data. (Please choose the most appropriate plural form to fit any number between 0 and 999,999,999,999,999)</comment>
  </data>
  <data name="UnitName_Pebibits" xml:space="preserve">
    <value>Pebibit</value>
    <comment>A measurement unit for data. (Please choose the most appropriate plural form to fit any number between 0 and 999,999,999,999,999)</comment>
  </data>
  <data name="UnitName_Pebibytes" xml:space="preserve">
    <value>Pebibyte</value>
    <comment>A measurement unit for data. (Please choose the most appropriate plural form to fit any number between 0 and 999,999,999,999,999)</comment>
  </data>
  <data name="UnitName_Tebibits" xml:space="preserve">
    <value>Tebibit</value>
    <comment>A measurement unit for data. (Please choose the most appropriate plural form to fit any number between 0 and 999,999,999,999,999)</comment>
  </data>
  <data name="UnitName_Tebibytes" xml:space="preserve">
    <value>Tebibyte</value>
    <comment>A measurement unit for data. (Please choose the most appropriate plural form to fit any number between 0 and 999,999,999,999,999)</comment>
  </data>
  <data name="UnitName_Exabits" xml:space="preserve">
    <value>Exabits</value>
    <comment>A measurement unit for data. (Please choose the most appropriate plural form to fit any number between 0 and 999,999,999,999,999)</comment>
  </data>
  <data name="UnitName_Exabytes" xml:space="preserve">
    <value>Exabyte</value>
    <comment>A measurement unit for data. (Please choose the most appropriate plural form to fit any number between 0 and 999,999,999,999,999)</comment>
  </data>
  <data name="UnitName_Exbibits" xml:space="preserve">
    <value>Exbibit</value>
    <comment>A measurement unit for data. (Please choose the most appropriate plural form to fit any number between 0 and 999,999,999,999,999)</comment>
  </data>
  <data name="UnitName_Exbibytes" xml:space="preserve">
    <value>Exbibytes</value>
    <comment>A measurement unit for data. (Please choose the most appropriate plural form to fit any number between 0 and 999,999,999,999,999)</comment>
  </data>
  <data name="UnitName_Zetabits" xml:space="preserve">
    <value>Zettabits</value>
    <comment>A measurement unit for data. (Please choose the most appropriate plural form to fit any number between 0 and 999,999,999,999,999)</comment>
  </data>
  <data name="UnitName_Zetabytes" xml:space="preserve">
    <value>Zettabytes</value>
    <comment>A measurement unit for data. (Please choose the most appropriate plural form to fit any number between 0 and 999,999,999,999,999)</comment>
  </data>
  <data name="UnitName_Zebibits" xml:space="preserve">
    <value>Zebibit</value>
    <comment>A measurement unit for data. (Please choose the most appropriate plural form to fit any number between 0 and 999,999,999,999,999)</comment>
  </data>
  <data name="UnitName_Zebibytes" xml:space="preserve">
    <value>Zebibyte</value>
    <comment>A measurement unit for data. (Please choose the most appropriate plural form to fit any number between 0 and 999,999,999,999,999)</comment>
  </data>
  <data name="UnitName_Yottabit" xml:space="preserve">
    <value>Yottabits</value>
    <comment>A measurement unit for data. (Please choose the most appropriate plural form to fit any number between 0 and 999,999,999,999,999)</comment>
  </data>
  <data name="UnitName_Yottabyte" xml:space="preserve">
    <value>Yottabytes</value>
    <comment>A measurement unit for data. (Please choose the most appropriate plural form to fit any number between 0 and 999,999,999,999,999)</comment>
  </data>
  <data name="UnitName_Yobibits" xml:space="preserve">
    <value>Yobibit</value>
    <comment>A measurement unit for data. (Please choose the most appropriate plural form to fit any number between 0 and 999,999,999,999,999)</comment>
  </data>
  <data name="UnitName_Yobibytes" xml:space="preserve">
    <value>Yobibyte</value>
    <comment>A measurement unit for data. (Please choose the most appropriate plural form to fit any number between 0 and 999,999,999,999,999)</comment>
  </data>
  <data name="DateCalculationModeText" xml:space="preserve">
    <value>Datumberekening</value>
  </data>
  <data name="DateCalculationOption.[using:Windows.UI.Xaml.Automation]AutomationProperties.Name" xml:space="preserve">
    <value>Berekeningsmodus</value>
    <comment>Automation label for the Date calculation Mode combobox. Users will hear "Calculation mode combobox".</comment>
  </data>
  <data name="AddOption.Content" xml:space="preserve">
    <value>Toevoegen</value>
    <comment>Add toggle button text</comment>
  </data>
  <data name="Date_AddSubtractOption.Content" xml:space="preserve">
    <value>Dagen optellen of aftrekken</value>
    <comment>Add or Subtract days option</comment>
  </data>
  <data name="DateLabel.Text" xml:space="preserve">
    <value>Datum</value>
    <comment>Date result label</comment>
  </data>
  <data name="Date_DifferenceOption.Content" xml:space="preserve">
    <value>Verschil tussen datums</value>
    <comment>Date difference option</comment>
  </data>
  <data name="DaysLabel.Text" xml:space="preserve">
    <value>Dagen</value>
    <comment>Add/Subtract Days label</comment>
  </data>
  <data name="Date_DifferenceLabel.Text" xml:space="preserve">
    <value>Verschil</value>
    <comment>Difference result label</comment>
  </data>
  <data name="DateDiff_FromHeader.Header" xml:space="preserve">
    <value>Vanaf</value>
    <comment>From Date Header for Difference Date Picker</comment>
  </data>
  <data name="MonthsLabel.Text" xml:space="preserve">
    <value>Maanden</value>
    <comment>Add/Subtract Months label</comment>
  </data>
  <data name="SubtractOption.Content" xml:space="preserve">
    <value>Aftrekken</value>
    <comment>Subtract toggle button text</comment>
  </data>
  <data name="DateDiff_ToHeader.Header" xml:space="preserve">
    <value>Tot</value>
    <comment>To Date Header for Difference Date Picker</comment>
  </data>
  <data name="YearsLabel.Text" xml:space="preserve">
    <value>Jaar</value>
    <comment>Add/Subtract Years label</comment>
  </data>
  <data name="Date_OutOfBoundMessage" xml:space="preserve">
    <value>Datum buiten bereik</value>
    <comment>Out of bound message shown as result when the date calculation exceeds the bounds</comment>
  </data>
  <data name="Date_Day" xml:space="preserve">
    <value>dag</value>
  </data>
  <data name="Date_Days" xml:space="preserve">
    <value>dagen</value>
  </data>
  <data name="Date_Month" xml:space="preserve">
    <value>maand</value>
  </data>
  <data name="Date_Months" xml:space="preserve">
    <value>maanden</value>
  </data>
  <data name="Date_SameDates" xml:space="preserve">
    <value>Dezelfde datums</value>
  </data>
  <data name="Date_Week" xml:space="preserve">
    <value>week</value>
  </data>
  <data name="Date_Weeks" xml:space="preserve">
    <value>weken</value>
  </data>
  <data name="Date_Year" xml:space="preserve">
    <value>jaar</value>
  </data>
  <data name="Date_Years" xml:space="preserve">
    <value>jaren</value>
  </data>
  <data name="Date_DifferenceResultAutomationName" xml:space="preserve">
    <value>Verschil %1</value>
    <comment>Automation name for reading out the date difference. %1 =  Date difference</comment>
  </data>
  <data name="Date_ResultingDateAutomationName" xml:space="preserve">
    <value>Resulterende datum %1</value>
    <comment>Automation name for reading out the resulting date in Add/Subtract mode. %1 = Resulting date</comment>
  </data>
  <data name="HeaderAutomationName_Calculator" xml:space="preserve">
    <value>%1 Rekenmachinemodus</value>
    <comment>{Locked='%1'} Automation name for when the mode header is focused. %1 = the current calculator mode: Scientific, Standard, or Programmer.</comment>
  </data>
  <data name="HeaderAutomationName_Converter" xml:space="preserve">
    <value>%1 Conversiemodus</value>
    <comment>{Locked='%1'} Automation name for when the mode header is focused. %1 = the current converter mode: "Weight and mass", "Energy", "Volume", etc.</comment>
  </data>
  <data name="HeaderAutomationName_Date" xml:space="preserve">
    <value>Datumberekeningsmodus</value>
    <comment>Automation name for when the mode header is focused and the current mode is Date calculation.</comment>
  </data>
  <data name="DockPanel.[using:Windows.UI.Xaml.Automation]AutomationProperties.Name" xml:space="preserve">
    <value>Geschiedenis- en geheugenlijsten</value>
    <comment>Automation name for the group of controls for history and memory lists.</comment>
  </data>
  <data name="MemoryPanel.[using:Windows.UI.Xaml.Automation]AutomationProperties.Name" xml:space="preserve">
    <value>Geheugencontrollers</value>
    <comment>Automation name for the group of memory controls (Mem Clear, Mem Recall, Mem Add, Mem Subtract, Mem Store, Memory flyout toggle)</comment>
  </data>
  <data name="StandardFunctions.[using:Windows.UI.Xaml.Automation]AutomationProperties.Name" xml:space="preserve">
    <value>Standaardfuncties</value>
    <comment>Automation name for the group of standard function buttons (Percent, Square Root, Square, Cube, Reciprocal)</comment>
  </data>
  <data name="DisplayControls.[using:Windows.UI.Xaml.Automation]AutomationProperties.Name" xml:space="preserve">
    <value>Beeldschermcontrollers</value>
    <comment>Automation name for the group of display control buttons (Clear, Clear Entry, and Backspace)</comment>
  </data>
  <data name="StandardOperators.[using:Windows.UI.Xaml.Automation]AutomationProperties.Name" xml:space="preserve">
    <value>Standaardoperators</value>
    <comment>Automation name for the group of standard operator buttons (Add, Subtract, Multiply, Divide, and Equals)</comment>
  </data>
  <data name="NumberPad.[using:Windows.UI.Xaml.Automation]AutomationProperties.Name" xml:space="preserve">
    <value>Numeriek toetsenbord</value>
    <comment>Automation name for the group of NumberPad buttons (0-9, A-F and Decimal button)</comment>
  </data>
  <data name="ScientificAngleOperators.[using:Windows.UI.Xaml.Automation]AutomationProperties.Name" xml:space="preserve">
    <value>Hoekoperators</value>
    <comment>Automation name for the group of Scientific angle operators (Degree mode, Hyperbolic toggle, and Precision button)</comment>
  </data>
  <data name="ScientificFunctions.[using:Windows.UI.Xaml.Automation]AutomationProperties.Name" xml:space="preserve">
    <value>Wetenschappelijke functies</value>
    <comment>Automation name for the group of Scientific functions.</comment>
  </data>
  <data name="RadixGroup.[using:Windows.UI.Xaml.Automation]AutomationProperties.Name" xml:space="preserve">
    <value>Radix selecteren</value>
    <comment>Automation name for the group of radices (Hexadecimal, Decimal, Octal, Binary). https://en.wikipedia.org/wiki/Radix </comment>
  </data>
  <data name="ProgrammerOperators.[using:Windows.UI.Xaml.Automation]AutomationProperties.Name" xml:space="preserve">
    <value>Programmeeroperators</value>
    <comment>Automation name for the group of programmer operators (RoL, RoR, Lsh, Rsh, OR, XOR, NOT, AND).</comment>
  </data>
  <data name="InputModeSelectionGroup.[using:Windows.UI.Xaml.Automation]AutomationProperties.Name" xml:space="preserve">
    <value>Invoermodusselectie</value>
    <comment>Automation name for the group of input mode toggling buttons.</comment>
  </data>
  <data name="BitFlipPanel.[using:Windows.UI.Xaml.Automation]AutomationProperties.Name" xml:space="preserve">
    <value>Toetsenblok voor het omzetten van bits</value>
    <comment>Automation name for the group of bit toggling buttons.</comment>
  </data>
  <data name="scrollLeft.[using:Windows.UI.Xaml.Automation]AutomationProperties.Name" xml:space="preserve">
    <value>Expressie naar links schuiven</value>
    <comment>Automation label for the "scroll left" button that appears when an expression is too large to fit in the window.</comment>
  </data>
  <data name="scrollRight.[using:Windows.UI.Xaml.Automation]AutomationProperties.Name" xml:space="preserve">
    <value>Expressie naar rechts schuiven</value>
    <comment>Automation label for the "scroll right" button that appears when an expression is too large to fit in the window.</comment>
  </data>
  <data name="Format_MaxDigitsReached" xml:space="preserve">
    <value>Max. aantal cijfers bereikt. %1</value>
    <comment>{Locked='%1'} Formatting string for a Narrator announcement when user reaches max digits. The %1 is the automation name of the display. Users will hear "Max digits reached. Display is _current_value_".</comment>
  </data>
  <data name="Format_ButtonPressAuditoryFeedback" xml:space="preserve">
    <value>%1 %2</value>
    <comment>{Locked='%1','%2'} Formatting string for a Narrator announcement when user presses a button with auditory feedback. "%1" is the display value and "%2" is the button press feedback. Example, user presses "plus" and hears "Display is 7 plus".</comment>
  </data>
  <data name="Format_MemorySave" xml:space="preserve">
    <value>%1 opgeslagen in geheugen</value>
    <comment>{Locked='%1'} Formatting string for a Narrator announcement when the user stores a number to memory. The %1 is the automation name of the display. Users will hear "_current_value_ saved to memory".</comment>
  </data>
  <data name="Format_MemorySlotChanged" xml:space="preserve">
    <value>Geheugensleuf %1 is %2</value>
    <comment>{Locked='%1','%2'} Formatting string for a Narrator announcement when the user changes a memory slot. The %1 is the index of the memory slot and %2 is the new value. For example, users might hear "Memory slot 2 is 37".</comment>
  </data>
  <data name="Format_MemorySlotCleared" xml:space="preserve">
    <value>Geheugensleuf %1 gewist</value>
    <comment>{Locked='%1'} Formatting string for a Narrator announcement when the user clears a memory slot. The %1 is the index of the memory slot. For example, users might hear "Memory slot 2 cleared".</comment>
  </data>
  <data name="divideButton.[using:CalculatorApp.Controls]CalculatorButton.AuditoryFeedback" xml:space="preserve">
    <value>gedeeld door</value>
    <comment>Auditory feedback for screen reader users. Users will hear "Display is 7 divided by" when the button is pressed.</comment>
  </data>
  <data name="multiplyButton.[using:CalculatorApp.Controls]CalculatorButton.AuditoryFeedback" xml:space="preserve">
    <value>maal</value>
    <comment>Auditory feedback for screen reader users. Users will hear "Display is 7 times" when the button is pressed.</comment>
  </data>
  <data name="minusButton.[using:CalculatorApp.Controls]CalculatorButton.AuditoryFeedback" xml:space="preserve">
    <value>min</value>
    <comment>Auditory feedback for screen reader users. Users will hear "Display is 7 minus" when the button is pressed.</comment>
  </data>
  <data name="plusButton.[using:CalculatorApp.Controls]CalculatorButton.AuditoryFeedback" xml:space="preserve">
    <value>plus</value>
    <comment>Auditory feedback for screen reader users. Users will hear "Display is 7 plus" when the button is pressed.</comment>
  </data>
  <data name="powerButton.[using:CalculatorApp.Controls]CalculatorButton.AuditoryFeedback" xml:space="preserve">
    <value>tot de macht</value>
    <comment>Auditory feedback for screen reader users. Users will hear "Display is 7 to the power of" when the button is pressed.</comment>
  </data>
  <data name="ySquareRootButton.[using:CalculatorApp.Controls]CalculatorButton.AuditoryFeedback" xml:space="preserve">
    <value>y wortel</value>
    <comment>Auditory feedback for screen reader users. Users will hear "Display is 7 y root" when the button is pressed.</comment>
  </data>
  <data name="modButton.[using:CalculatorApp.Controls]CalculatorButton.AuditoryFeedback" xml:space="preserve">
    <value>mod</value>
    <comment>Auditory feedback for screen reader users. Users will hear "Display is 7 mod" when the button is pressed.</comment>
  </data>
  <data name="lshButton.[using:CalculatorApp.Controls]CalculatorButton.AuditoryFeedback" xml:space="preserve">
    <value>schuiven naar links</value>
    <comment>Auditory feedback for screen reader users. Users will hear "Display is 7 left shift" when the button is pressed.</comment>
  </data>
  <data name="rshButton.[using:CalculatorApp.Controls]CalculatorButton.AuditoryFeedback" xml:space="preserve">
    <value>schuiven naar rechts</value>
    <comment>Auditory feedback for screen reader users. Users will hear "Display is 7 right shift" when the button is pressed.</comment>
  </data>
  <data name="orButton.[using:CalculatorApp.Controls]CalculatorButton.AuditoryFeedback" xml:space="preserve">
    <value>of</value>
    <comment>Auditory feedback for screen reader users. Users will hear "Display is 7 or" when the button is pressed. OR is a mathematical operation on two binary values.</comment>
  </data>
  <data name="xorButton.[using:CalculatorApp.Controls]CalculatorButton.AuditoryFeedback" xml:space="preserve">
    <value>x of</value>
    <comment>Auditory feedback for screen reader users. Users will hear "Display is 7 x or" when the button is pressed. XOR is a mathematical operation on two binary values. Here the feedback is "x or" in order to get the correct pronunciation.</comment>
  </data>
  <data name="andButton.[using:CalculatorApp.Controls]CalculatorButton.AuditoryFeedback" xml:space="preserve">
    <value>en</value>
    <comment>Auditory feedback for screen reader users. Users will hear "Display is 7 and" when the button is pressed. AND is a mathematical operation on two binary values.</comment>
  </data>
  <data name="CurrencyFromToRatioFormat" xml:space="preserve">
    <value>%1 %2 = %3 %4</value>
    <comment>The exact ratio between converted currencies, e.g. "1 USD = 0.8885 EUR". %1 will always be '1'. %2 is the From currency code. %3 is the formatted conversion ratio. %4 is the To currency code.</comment>
  </data>
  <data name="CurrencyTimestampFormat" xml:space="preserve">
    <value>Bijgewerkt %1 %2</value>
    <comment>The timestamp of currency conversion ratios fetched from an online service. %1 is the date. %2 is the time. Example: "Updated Sep 28, 2016 5:42 PM"</comment>
  </data>
  <data name="RefreshButtonText.Content" xml:space="preserve">
    <value>Tarieven bijwerken</value>
    <comment>The text displayed for a hyperlink button that refreshes currency converter ratios.</comment>
  </data>
  <data name="DataChargesMayApply" xml:space="preserve">
    <value>Er worden mogelijk datakosten in rekening gebracht.</value>
    <comment>The text displayed when users are on a metered connection and using currency converter.</comment>
  </data>
  <data name="FailedToRefresh" xml:space="preserve">
    <value>Nieuwe tarieven kunnen niet worden opgehaald. Probeer het later opnieuw.</value>
    <comment>The text displayed when currency ratio data fails to load.</comment>
  </data>
  <data name="OfflineStatusHyperlinkText" xml:space="preserve">
    <value>Offline. Controleer je %HL%netwerkinstellingen%HL%</value>
    <comment>Status text displayed when currency converter is disconnected from the internet. The text "Notification Settings" should be surrounded by %HL% since they are used to indicate that that text should be the hyperlink text. {Locked="%HL%"}</comment>
  </data>
  <data name="UpdatingCurrencyRates" xml:space="preserve">
    <value>Valutawisselkoersen worden bijgewerkt</value>
    <comment>This string is what is read by Narrator, and other screen readers, when the "Update rates" button in the Currency Converter is clicked.</comment>
  </data>
  <data name="CurrencyRatesUpdated" xml:space="preserve">
    <value>Valutawisselkoersen bijgewerkt</value>
    <comment>This string is what is read by Narrator, and other screen readers, when the currency rates in Currency converter have successfully updated.</comment>
  </data>
  <data name="CurrencyRatesUpdateFailed" xml:space="preserve">
    <value>Kan wisselkoersen niet bijwerken</value>
    <comment>This string is what is read by Narrator, and other screen readers, when the currency rates in Currency converter have failed to update.</comment>
  </data>
  <data name="HistoryButton.AccessKey" xml:space="preserve">
    <value>I</value>
    <comment>Access key for the History button. {StringCategory="Accelerator"}</comment>
  </data>
  <data name="MemoryButton.AccessKey" xml:space="preserve">
    <value>M</value>
    <comment>Access key for the Memory button. {StringCategory="Accelerator"}</comment>
  </data>
  <data name="NavView.AccessKey" xml:space="preserve">
    <value>H</value>
    <comment>Access key for the Hamburger button. {StringCategory="Accelerator"}</comment>
  </data>
  <data name="CategoryName_AngleAccessKey" xml:space="preserve">
    <value>AN</value>
    <comment>AccessKey for the angle converter navbar item. {StringCategory="Accelerator"}</comment>
  </data>
  <data name="CategoryName_AreaAccessKey" xml:space="preserve">
    <value>AR</value>
    <comment>AccessKey for the area converter navbar item. {StringCategory="Accelerator"}</comment>
  </data>
  <data name="CategoryName_CurrencyAccessKey" xml:space="preserve">
    <value>C</value>
    <comment>AccessKey for the currency converter navbar item. {StringCategory="Accelerator"}</comment>
  </data>
  <data name="CategoryName_DataAccessKey" xml:space="preserve">
    <value>D</value>
    <comment>AccessKey for the data converter navbar item. {StringCategory="Accelerator"}</comment>
  </data>
  <data name="CategoryName_EnergyAccessKey" xml:space="preserve">
    <value>E</value>
    <comment>AccessKey for the energy converter navbar item. {StringCategory="Accelerator"}</comment>
  </data>
  <data name="CategoryName_LengthAccessKey" xml:space="preserve">
    <value>L</value>
    <comment>AccessKey for the length converter navbar item. {StringCategory="Accelerator"}</comment>
  </data>
  <data name="CategoryName_PowerAccessKey" xml:space="preserve">
    <value>PO</value>
    <comment>AccessKey for the power converter navbar item. {StringCategory="Accelerator"}</comment>
  </data>
  <data name="CategoryName_PressureAccessKey" xml:space="preserve">
    <value>PR</value>
    <comment>AccessKey for the pressure converter navbar item. {StringCategory="Accelerator"}</comment>
  </data>
  <data name="CategoryName_SpeedAccessKey" xml:space="preserve">
    <value>S</value>
    <comment>AccessKey for the speed converter navbar item. {StringCategory="Accelerator"}</comment>
  </data>
  <data name="CategoryName_TimeAccessKey" xml:space="preserve">
    <value>TI</value>
    <comment>AccessKey for the time converter navbar item. {StringCategory="Accelerator"}</comment>
  </data>
  <data name="CategoryName_VolumeAccessKey" xml:space="preserve">
    <value>V</value>
    <comment>AccessKey for the volume converter navbar item. {StringCategory="Accelerator"}</comment>
  </data>
  <data name="CategoryName_WeightAccessKey" xml:space="preserve">
    <value>W</value>
    <comment>AccessKey for the weight converter navbar item. {StringCategory="Accelerator"}</comment>
  </data>
  <data name="CategoryName_TemperatureAccessKey" xml:space="preserve">
    <value>TE</value>
    <comment>AccessKey for the temperature converter navbar item. {StringCategory="Accelerator"}</comment>
  </data>
  <data name="ClearHistory.AccessKey" xml:space="preserve">
    <value>C</value>
    <comment>Access key for the Clear history button.{StringCategory="Accelerator"}</comment>
  </data>
  <data name="ClearMemory.AccessKey" xml:space="preserve">
    <value>C</value>
    <comment>Access key for the Clear memory button. {StringCategory="Accelerator"}</comment>
  </data>
  <data name="ClearMemory.[using:Windows.UI.Xaml.Controls]ToolTipService.ToolTip" xml:space="preserve">
    <value>Geheugen wissen (Ctrl+L)</value>
    <comment>This is the tool tip automation name for the Clear Memory button in the Memory Pane.</comment>
  </data>
  <data name="ClearMemory.[using:Windows.UI.Xaml.Automation]AutomationProperties.Name" xml:space="preserve">
    <value>Geheugen wissen</value>
    <comment>Screen reader prompt for the Calculator Clear Memory button in the Memory Pane</comment>
  </data>
  <data name="HistoryLabel.AccessKey" xml:space="preserve">
    <value>I</value>
    <comment>Access key for the History pivot item.{StringCategory="Accelerator"}</comment>
  </data>
  <data name="MemoryLabel.AccessKey" xml:space="preserve">
    <value>M</value>
    <comment>Access key for the Memory pivot item.{StringCategory="Accelerator"}</comment>
  </data>
  <data name="SineDegrees" xml:space="preserve">
    <value>graden sinus</value>
    <comment>Name for the sine function in degrees mode. Used by screen readers.</comment>
  </data>
  <data name="SineRadians" xml:space="preserve">
    <value>radialen sinus</value>
    <comment>Name for the sine function in radians mode. Used by screen readers.</comment>
  </data>
  <data name="SineGradians" xml:space="preserve">
    <value>gradiënten sinus</value>
    <comment>Name for the sine function in gradians mode. Used by screen readers.</comment>
  </data>
  <data name="InverseSineDegrees" xml:space="preserve">
    <value>graden arcsinus</value>
    <comment>Name for the inverse sine function in degrees mode. Used by screen readers.</comment>
  </data>
  <data name="InverseSineRadians" xml:space="preserve">
    <value>radialen arcsinus</value>
    <comment>Name for the inverse sine function in radians mode. Used by screen readers.</comment>
  </data>
  <data name="InverseSineGradians" xml:space="preserve">
    <value>gradiënten arcsinus</value>
    <comment>Name for the inverse sine function in gradians mode. Used by screen readers.</comment>
  </data>
  <data name="HyperbolicSine" xml:space="preserve">
    <value>sinus hyperbolicus</value>
    <comment>Name for the hyperbolic sine function. Used by screen readers.</comment>
  </data>
  <data name="InverseHyperbolicSine" xml:space="preserve">
    <value>arcsinus hyperbolicus</value>
    <comment>Name for the inverse hyperbolic sine function. Used by screen readers.</comment>
  </data>
  <data name="CosineDegrees" xml:space="preserve">
    <value>graden cosinus</value>
    <comment>Name for the cosine function in degrees mode. Used by screen readers.</comment>
  </data>
  <data name="CosineRadians" xml:space="preserve">
    <value>radialen cosinus</value>
    <comment>Name for the cosine function in radians mode. Used by screen readers.</comment>
  </data>
  <data name="CosineGradians" xml:space="preserve">
    <value>gradiënten cosinus</value>
    <comment>Name for the cosine function in gradians mode. Used by screen readers.</comment>
  </data>
  <data name="InverseCosineDegrees" xml:space="preserve">
    <value>graden arccosinus</value>
    <comment>Name for the inverse cosine function in degrees mode. Used by screen readers.</comment>
  </data>
  <data name="InverseCosineRadians" xml:space="preserve">
    <value>radialen arccosinus</value>
    <comment>Name for the inverse cosine function in radians mode. Used by screen readers.</comment>
  </data>
  <data name="InverseCosineGradians" xml:space="preserve">
    <value>gradiënten arccosinus</value>
    <comment>Name for the inverse cosine function in gradians mode. Used by screen readers.</comment>
  </data>
  <data name="HyperbolicCosine" xml:space="preserve">
    <value>cosinus hyperbolicus</value>
    <comment>Name for the hyperbolic cosine function. Used by screen readers.</comment>
  </data>
  <data name="InverseHyperbolicCosine" xml:space="preserve">
    <value>arccosinus hyperbolicus</value>
    <comment>Name for the inverse hyperbolic cosine function. Used by screen readers.</comment>
  </data>
  <data name="TangentDegrees" xml:space="preserve">
    <value>graden tangens</value>
    <comment>Name for the tangent function in degrees mode. Used by screen readers.</comment>
  </data>
  <data name="TangentRadians" xml:space="preserve">
    <value>radialen tangens</value>
    <comment>Name for the tangent function in radians mode. Used by screen readers.</comment>
  </data>
  <data name="TangentGradians" xml:space="preserve">
    <value>gradiënten tangens</value>
    <comment>Name for the tangent function in gradians mode. Used by screen readers.</comment>
  </data>
  <data name="InverseTangentDegrees" xml:space="preserve">
    <value>graden arctangens</value>
    <comment>Name for the inverse tangent function in degrees mode. Used by screen readers.</comment>
  </data>
  <data name="InverseTangentRadians" xml:space="preserve">
    <value>radialen arctangens</value>
    <comment>Name for the inverse tangent function in radians mode. Used by screen readers.</comment>
  </data>
  <data name="InverseTangentGradians" xml:space="preserve">
    <value>gradiënten arctangens</value>
    <comment>Name for the inverse tangent function in gradians mode. Used by screen readers.</comment>
  </data>
  <data name="HyperbolicTangent" xml:space="preserve">
    <value>tangens hyperbolicus</value>
    <comment>Name for the hyperbolic tangent function. Used by screen readers.</comment>
  </data>
  <data name="InverseHyperbolicTangent" xml:space="preserve">
    <value>arctangens hyperbolicus</value>
    <comment>Name for the inverse hyperbolic tangent function. Used by screen readers.</comment>
  </data>
  <data name="SecantDegrees" xml:space="preserve">
    <value>graden secans</value>
    <comment>Name for the secant function in degrees mode. Used by screen readers.</comment>
  </data>
  <data name="SecantRadians" xml:space="preserve">
    <value>radialen secans</value>
    <comment>Name for the secant function in radians mode. Used by screen readers.</comment>
  </data>
  <data name="SecantGradians" xml:space="preserve">
    <value>gradiënten secans</value>
    <comment>Name for the secant function in gradians mode. Used by screen readers.</comment>
  </data>
  <data name="InverseSecantDegrees" xml:space="preserve">
    <value>graden inverse secans</value>
    <comment>Name for the inverse secant function in degrees mode. Used by screen readers.</comment>
  </data>
  <data name="InverseSecantRadians" xml:space="preserve">
    <value>radialen inverse secans</value>
    <comment>Name for the inverse secant function in radians mode. Used by screen readers.</comment>
  </data>
  <data name="InverseSecantGradians" xml:space="preserve">
    <value>gradiënten inverse secans</value>
    <comment>Name for the inverse secant function in gradians mode. Used by screen readers.</comment>
  </data>
  <data name="HyperbolicSecant" xml:space="preserve">
    <value>secans hyperbolicus</value>
    <comment>Name for the hyperbolic secant function. Used by screen readers.</comment>
  </data>
  <data name="InverseHyperbolicSecant" xml:space="preserve">
    <value>inverse secans hyperbolicus</value>
    <comment>Name for the inverse hyperbolic secant function. Used by screen readers.</comment>
  </data>
  <data name="CosecantDegrees" xml:space="preserve">
    <value>graden cosecans</value>
    <comment>Name for the cosecant function in degrees mode. Used by screen readers.</comment>
  </data>
  <data name="CosecantRadians" xml:space="preserve">
    <value>radialen cosecans</value>
    <comment>Name for the cosecant function in radians mode. Used by screen readers.</comment>
  </data>
  <data name="CosecantGradians" xml:space="preserve">
    <value>gradiënten cosecans</value>
    <comment>Name for the cosecant function in gradians mode. Used by screen readers.</comment>
  </data>
  <data name="InverseCosecantDegrees" xml:space="preserve">
    <value>graden inverse cosecans</value>
    <comment>Name for the inverse cosecant function in degrees mode. Used by screen readers.</comment>
  </data>
  <data name="InverseCosecantRadians" xml:space="preserve">
    <value>radialen inverse cosecans</value>
    <comment>Name for the inverse cosecant function in radians mode. Used by screen readers.</comment>
  </data>
  <data name="InverseCosecantGradians" xml:space="preserve">
    <value>gradiënten inverse cosecans</value>
    <comment>Name for the inverse cosecant function in gradians mode. Used by screen readers.</comment>
  </data>
  <data name="HyperbolicCosecant" xml:space="preserve">
    <value>cosecans hyperbolicus</value>
    <comment>Name for the hyperbolic cosecant function. Used by screen readers.</comment>
  </data>
  <data name="InverseHyperbolicCosecant" xml:space="preserve">
    <value>inverse cosecans hyperbolicus</value>
    <comment>Name for the inverse hyperbolic cosecant function. Used by screen readers.</comment>
  </data>
  <data name="CotangentDegrees" xml:space="preserve">
    <value>graden contangens</value>
    <comment>Name for the cotangent function in degrees mode. Used by screen readers.</comment>
  </data>
  <data name="CotangentRadians" xml:space="preserve">
    <value>Radialen cotangens</value>
    <comment>Name for the cotangent function in radians mode. Used by screen readers.</comment>
  </data>
  <data name="CotangentGradians" xml:space="preserve">
    <value>gradiënten cotangens</value>
    <comment>Name for the cotangent function in gradians mode. Used by screen readers.</comment>
  </data>
  <data name="InverseCotangentDegrees" xml:space="preserve">
    <value>graden inverse cotangens</value>
    <comment>Name for the inverse cotangent function in degrees mode. Used by screen readers.</comment>
  </data>
  <data name="InverseCotangentRadians" xml:space="preserve">
    <value>radialen inverse cotangens</value>
    <comment>Name for the inverse cotangent function in radians mode. Used by screen readers.</comment>
  </data>
  <data name="InverseCotangentGradians" xml:space="preserve">
    <value>gradiënten inverse cotangens</value>
    <comment>Name for the inverse cotangent function in gradians mode. Used by screen readers.</comment>
  </data>
  <data name="HyperbolicCotangent" xml:space="preserve">
    <value>cotangens hyperbolicus</value>
    <comment>Name for the hyperbolic cotangent function. Used by screen readers.</comment>
  </data>
  <data name="InverseHyperbolicCotangent" xml:space="preserve">
    <value>inverse cotangens hyperbolicus</value>
    <comment>Name for the inverse hyperbolic cotangent function. Used by screen readers.</comment>
  </data>
  <data name="CubeRoot" xml:space="preserve">
    <value>Derdemachtswortel</value>
    <comment>Name for the cube root function. Used by screen readers.</comment>
  </data>
  <data name="Logy" xml:space="preserve">
    <value>Logaritmische basis</value>
    <comment>Name for the logbasey function. Used by screen readers.</comment>
  </data>
  <data name="AbsoluteValue" xml:space="preserve">
    <value>Absolute waarde</value>
    <comment>Name for the absolute value function. Used by screen readers.</comment>
  </data>
  <data name="LeftShift" xml:space="preserve">
    <value>schuiven naar links</value>
    <comment>Name for the programmer function that shifts bits to the left. Used by screen readers.</comment>
  </data>
  <data name="RightShift" xml:space="preserve">
    <value>schuiven naar rechts</value>
    <comment>Name for the programmer function that shifts bits to the right. Used by screen readers.</comment>
  </data>
  <data name="Factorial" xml:space="preserve">
    <value>faculteit</value>
    <comment>Name for the factorial function. Used by screen readers.</comment>
  </data>
  <data name="DegreeMinuteSecond" xml:space="preserve">
    <value>graden minuut seconde</value>
    <comment>Name for the degree minute second (dms) function. Used by screen readers.</comment>
  </data>
  <data name="NaturalLog" xml:space="preserve">
    <value>natuurlijke logaritme</value>
    <comment>Name for the natural log (ln) function. Used by screen readers.</comment>
  </data>
  <data name="Square" xml:space="preserve">
    <value>vierkant</value>
    <comment>Name for the square function. Used by screen readers.</comment>
  </data>
  <data name="YRoot" xml:space="preserve">
    <value>y wortel</value>
    <comment>Name for the y root function. Used by screen readers.</comment>
  </data>
  <data name="NavCategoryItem_AutomationNameFormat" xml:space="preserve">
    <value>%1 %2</value>
    <comment>{Locked='%1','%2'}.  Format string for the accessible name of a Calculator menu item, used by screen readers. "%1" is the item name, e.g. Standard, Programmer, etc. %2 is the category name, e.g. Calculator, Converter. An example when formatted is "Standard Calculator" or "Currency Converter".</comment>
  </data>
  <data name="NavCategoryHeader_AutomationNameFormat" xml:space="preserve">
    <value>Categorie %1</value>
    <comment>{Locked='%1'} Format string for the accessible name of a Calculator menu category header, used by screen readers. "%1" is the pluralized category name, e.g. Calculators, Converters. An example when formatted is "Calculators category".</comment>
  </data>
  <data name="AboutControlServicesAgreement.Text" xml:space="preserve">
    <value>Microsoft-servicesovereenkomst</value>
    <comment>Displayed on a link to the Microsoft Services Agreement in the about this app information</comment>
  </data>
  <data name="UnitAbbreviation_Pyeong" xml:space="preserve">
    <value>Pyeong</value>
    <comment>An abbreviation for a measurement unit of area.</comment>
  </data>
  <data name="UnitName_Pyeong" xml:space="preserve">
    <value>Pyeong</value>
    <comment>A measurement unit for area.</comment>
  </data>
  <data name="AddSubtract_Date_FromHeader.Header" xml:space="preserve">
    <value>Vanaf</value>
    <comment>From Date Header for AddSubtract Date Picker</comment>
  </data>
  <data name="CalculationResultScrollLeft.[using:Windows.UI.Xaml.Automation]AutomationProperties.Name" xml:space="preserve">
    <value>Berekeningsresultaat naar links schuiven</value>
    <comment>Automation label for the "Scroll Left" button that appears when a calculation result is too large to fit in calculation result text box.</comment>
  </data>
  <data name="CalculationResultScrollRight.[using:Windows.UI.Xaml.Automation]AutomationProperties.Name" xml:space="preserve">
    <value>Berekeningsresultaat naar rechts schuiven</value>
    <comment>Automation label for the "Scroll Right" button that appears when a calculation result is too large to fit in calculation result text box.</comment>
  </data>
  <data name="CalculationFailed" xml:space="preserve">
    <value>Berekening mislukt</value>
    <comment>Text displayed when the application is not able to do a calculation</comment>
  </data>
  <data name="logBaseY.[using:Windows.UI.Xaml.Automation]AutomationProperties.Name" xml:space="preserve">
    <value>Basislogboek Y</value>
    <comment>Screen reader prompt for the logBaseY button</comment>
  </data>
  <data name="trigButton.Text" xml:space="preserve">
    <value>Trigonometrie</value>
    <comment>Displayed on the button that contains a flyout for the trig functions in scientific mode.</comment>
  </data>
  <data name="funcButton.Text" xml:space="preserve">
    <value>Functie</value>
    <comment>Displayed on the button that contains a flyout for the general functions in scientific mode.</comment>
  </data>
  <data name="inequalityButton.Text" xml:space="preserve">
    <value>Ongelijkheden</value>
    <comment>Displayed on the button that contains a flyout for the inequality functions.</comment>
  </data>
  <data name="inequalityButton.[using:Windows.UI.Xaml.Automation]AutomationProperties.Name" xml:space="preserve">
    <value>Ongelijkheden</value>
    <comment>Screen reader prompt for the Inequalities button</comment>
  </data>
  <data name="bitwiseButton.Text" xml:space="preserve">
    <value>Bitwise</value>
    <comment>Displayed on the button that contains a flyout for the bitwise functions in programmer mode.</comment>
  </data>
  <data name="bitShiftButton.Text" xml:space="preserve">
    <value>Bitshift</value>
    <comment>Displayed on the button that contains a flyout for the bit shift functions in programmer mode.</comment>
  </data>
  <data name="trigShiftButton.[using:Windows.UI.Xaml.Automation]AutomationProperties.Name" xml:space="preserve">
    <value>Inverse-functie</value>
    <comment>Screen reader prompt for the shift button in the trig flyout in scientific mode.</comment>
  </data>
  <data name="hypButton.[using:Windows.UI.Xaml.Automation]AutomationProperties.Name" xml:space="preserve">
    <value>Hyperbolische functie</value>
    <comment>Screen reader prompt for the Calculator button HYP in the scientific flyout keypad</comment>
  </data>
  <data name="secButton.[using:Windows.UI.Xaml.Automation]AutomationProperties.Name" xml:space="preserve">
    <value>Secans</value>
    <comment>Screen reader prompt for the Calculator button sec in the scientific flyout keypad</comment>
  </data>
  <data name="sechButton.[using:Windows.UI.Xaml.Automation]AutomationProperties.Name" xml:space="preserve">
    <value>Secans hyperbolicus</value>
    <comment>Screen reader prompt for the Calculator button sech in the scientific flyout keypad</comment>
  </data>
  <data name="invsecButton.[using:Windows.UI.Xaml.Automation]AutomationProperties.Name" xml:space="preserve">
    <value>Arcsecans</value>
    <comment>Screen reader prompt for the Calculator button arc sec in the scientific flyout keypad</comment>
  </data>
  <data name="invsechButton.[using:Windows.UI.Xaml.Automation]AutomationProperties.Name" xml:space="preserve">
    <value>Arcsecans hyperbolicus</value>
    <comment>Screen reader prompt for the Calculator button arc sec in the scientific flyout keypad</comment>
  </data>
  <data name="cscButton.[using:Windows.UI.Xaml.Automation]AutomationProperties.Name" xml:space="preserve">
    <value>Cosecans</value>
    <comment>Screen reader prompt for the Calculator button csc in the scientific flyout keypad</comment>
  </data>
  <data name="cschButton.[using:Windows.UI.Xaml.Automation]AutomationProperties.Name" xml:space="preserve">
    <value>Cosecans hyperbolicus</value>
    <comment>Screen reader prompt for the Calculator button csch in the scientific flyout keypad</comment>
  </data>
  <data name="invcscButton.[using:Windows.UI.Xaml.Automation]AutomationProperties.Name" xml:space="preserve">
    <value>Arc-cosecans</value>
    <comment>Screen reader prompt for the Calculator button arc csc in the scientific flyout keypad</comment>
  </data>
  <data name="invcschButton.[using:Windows.UI.Xaml.Automation]AutomationProperties.Name" xml:space="preserve">
    <value>Arc-cosecans hyperbolicus</value>
    <comment>Screen reader prompt for the Calculator button arc csc in the scientific flyout keypad</comment>
  </data>
  <data name="cotButton.[using:Windows.UI.Xaml.Automation]AutomationProperties.Name" xml:space="preserve">
    <value>Cotangens</value>
    <comment>Screen reader prompt for the Calculator button cot in the scientific flyout keypad</comment>
  </data>
  <data name="cothButton.[using:Windows.UI.Xaml.Automation]AutomationProperties.Name" xml:space="preserve">
    <value>Cotangens hyperbolicus</value>
    <comment>Screen reader prompt for the Calculator button coth in the scientific flyout keypad</comment>
  </data>
  <data name="invcotButton.[using:Windows.UI.Xaml.Automation]AutomationProperties.Name" xml:space="preserve">
    <value>Arc-cotangens</value>
    <comment>Screen reader prompt for the Calculator button arc cot in the scientific flyout keypad</comment>
  </data>
  <data name="invcothButton.[using:Windows.UI.Xaml.Automation]AutomationProperties.Name" xml:space="preserve">
    <value>Arc-cotangens hyperbolicus</value>
    <comment>Screen reader prompt for the Calculator button arc coth in the scientific flyout keypad</comment>
  </data>
  <data name="floorButton.[using:Windows.UI.Xaml.Automation]AutomationProperties.Name" xml:space="preserve">
    <value>Afronden beneden</value>
    <comment>Screen reader prompt for the Calculator button floor in the scientific flyout keypad</comment>
  </data>
  <data name="ceilButton.[using:Windows.UI.Xaml.Automation]AutomationProperties.Name" xml:space="preserve">
    <value>Afronden boven</value>
    <comment>Screen reader prompt for the Calculator button ceiling in the scientific flyout keypad</comment>
  </data>
  <data name="randButton.[using:Windows.UI.Xaml.Automation]AutomationProperties.Name" xml:space="preserve">
    <value>Willekeurig</value>
    <comment>Screen reader prompt for the Calculator button random in the scientific flyout keypad</comment>
  </data>
  <data name="absButton.[using:Windows.UI.Xaml.Automation]AutomationProperties.Name" xml:space="preserve">
    <value>Absolute waarde</value>
    <comment>Screen reader prompt for the Calculator button abs in the scientific flyout keypad</comment>
  </data>
  <data name="eulerButton.[using:Windows.UI.Xaml.Automation]AutomationProperties.Name" xml:space="preserve">
    <value>Euler-constante</value>
    <comment>Screen reader prompt for the Calculator button e in the scientific flyout keypad</comment>
  </data>
  <data name="twoPowerXButton.[using:Windows.UI.Xaml.Automation]AutomationProperties.Name" xml:space="preserve">
    <value>Twee tot de macht</value>
    <comment>Screen reader prompt for the Calculator button 2^x in the scientific flyout keypad</comment>
  </data>
  <data name="nandButton.[using:Windows.UI.Xaml.Automation]AutomationProperties.Name" xml:space="preserve">
    <value>Niet En</value>
    <comment>Screen reader prompt for the Calculator button nand in the scientific flyout keypad</comment>
  </data>
  <data name="nandButton.[using:CalculatorApp.Controls]CalculatorButton.AuditoryFeedback" xml:space="preserve">
    <value>Niet En</value>
    <comment>Auditory feedback for screen reader users. Users will hear "Display is 7 nand" when the button is pressed. NAND is a mathematical operation on two binary values.</comment>
  </data>
  <data name="norButton.[using:Windows.UI.Xaml.Automation]AutomationProperties.Name" xml:space="preserve">
    <value>Niet Of</value>
    <comment>Screen reader prompt for the Calculator button nor in the scientific flyout keypad</comment>
  </data>
  <data name="norButton.[using:CalculatorApp.Controls]CalculatorButton.AuditoryFeedback" xml:space="preserve">
    <value>Niet Of</value>
    <comment>Auditory feedback for screen reader users. Users will hear "Display is 7 nor" when the button is pressed. NAND is a mathematical operation on two binary values.</comment>
  </data>
  <data name="rolCarryButton.[using:Windows.UI.Xaml.Automation]AutomationProperties.Name" xml:space="preserve">
    <value>Draaien links met overnemen</value>
    <comment>Screen reader prompt for the Calculator button rol with carry in the scientific flyout keypad</comment>
  </data>
  <data name="rorCarryButton.[using:Windows.UI.Xaml.Automation]AutomationProperties.Name" xml:space="preserve">
    <value>Draaien rechts met overnemen</value>
    <comment>Screen reader prompt for the Calculator button ror with carry in the scientific flyout keypad</comment>
  </data>
  <data name="lshLogicalButton.[using:Windows.UI.Xaml.Automation]AutomationProperties.Name" xml:space="preserve">
    <value>Schuiven naar links</value>
    <comment>Screen reader prompt for the Calculator button lshLogical in the scientific flyout keypad</comment>
  </data>
  <data name="lshLogicalButton.[using:CalculatorApp.Controls]CalculatorButton.AuditoryFeedback" xml:space="preserve">
    <value>Verschuiving links</value>
    <comment>Auditory feedback for screen reader users. Users will hear "Display is 7 left shift" when the button is pressed. NAND is a mathematical operation on two binary values.</comment>
  </data>
  <data name="rshLogicalButton.[using:Windows.UI.Xaml.Automation]AutomationProperties.Name" xml:space="preserve">
    <value>Schuiven naar rechts</value>
    <comment>Screen reader prompt for the Calculator button rshLogical in the scientific flyout keypad</comment>
  </data>
  <data name="rshLogicalButton.[using:CalculatorApp.Controls]CalculatorButton.AuditoryFeedback" xml:space="preserve">
    <value>Verplaatsing rechts</value>
    <comment>Auditory feedback for screen reader users. Users will hear "Display is 7 right shift" when the button is pressed. NAND is a mathematical operation on two binary values.</comment>
  </data>
  <data name="arithmeticShiftButton.Content" xml:space="preserve">
    <value>Rekenkundige verschuiving</value>
    <comment>Label for a radio button that toggles arithmetic shift behavior for the shift operations.</comment>
  </data>
  <data name="logicalShiftButton.Content" xml:space="preserve">
    <value>Logische verschuiving</value>
    <comment>Label for a radio button that toggles logical shift behavior for the shift operations.</comment>
  </data>
  <data name="rotateCircularButton.Content" xml:space="preserve">
    <value>Circulaire verschuiving draaien</value>
    <comment>Label for a radio button that toggles rotate circular behavior for the shift operations.</comment>
  </data>
  <data name="rotateCarryShiftButton.Content" xml:space="preserve">
    <value>Draaien via overnemen circulaire verschuiving</value>
    <comment>Label for a radio button that toggles rotate circular with carry behavior for the shift operations.</comment>
  </data>
  <data name="cubeRootButton.[using:Windows.UI.Xaml.Automation]AutomationProperties.Name" xml:space="preserve">
    <value>Derdemachtswortel</value>
    <comment>Screen reader prompt for the cube root button on the scientific operator keypad</comment>
  </data>
  <data name="trigButton.[using:Windows.UI.Xaml.Automation]AutomationProperties.Name" xml:space="preserve">
    <value>Trigonometrie</value>
    <comment>Screen reader prompt for the square root button on the scientific operator keypad</comment>
  </data>
  <data name="funcButton.[using:Windows.UI.Xaml.Automation]AutomationProperties.Name" xml:space="preserve">
    <value>Functies</value>
    <comment>Screen reader prompt for the square root button on the scientific operator keypad</comment>
  </data>
  <data name="bitwiseButton.[using:Windows.UI.Xaml.Automation]AutomationProperties.Name" xml:space="preserve">
    <value>Bitwise</value>
    <comment>Screen reader prompt for the square root button on the scientific operator keypad</comment>
  </data>
  <data name="bitShiftButton.[using:Windows.UI.Xaml.Automation]AutomationProperties.Name" xml:space="preserve">
    <value>Bitshift</value>
    <comment>Screen reader prompt for the square root button on the scientific operator keypad</comment>
  </data>
  <data name="ScientificOperatorPanel.[using:Windows.UI.Xaml.Automation]AutomationProperties.Name" xml:space="preserve">
    <value>Operatorpanelen wetenschappelijk</value>
    <comment>Screen reader prompt for the Scientific Operator Panels on the scientific operator keypad</comment>
  </data>
  <data name="ProgrammerOperatorPanel.[using:Windows.UI.Xaml.Automation]AutomationProperties.Name" xml:space="preserve">
    <value>Operatorpanelen programmeren</value>
    <comment>Screen reader prompt for the Programmer Operator Panels on the programmer operator keypad</comment>
  </data>
  <data name="MostSignificantBit" xml:space="preserve">
    <value>meest significante bit</value>
    <comment>Used to describe the last bit of a binary number. Used in bit flip</comment>
  </data>
  <data name="GraphingCalculatorModeText" xml:space="preserve">
    <value>Grafisch</value>
    <comment>Name of the Graphing mode of the Calculator app. Displayed in the navigation menu.</comment>
  </data>
  <data name="plotButton.[using:Windows.UI.Xaml.Automation]AutomationProperties.Name" xml:space="preserve">
    <value>Plotten</value>
    <comment>Screen reader prompt for the plot button on the graphing calculator operator keypad</comment>
  </data>
  <data name="graphViewButton.[using:Windows.UI.Xaml.Controls]ToolTipService.ToolTip" xml:space="preserve">
    <value>Weergave automatisch vernieuwen (CTRL + 0)</value>
    <comment>This is the tool tip automation name for the Calculator graph view button.</comment>
  </data>
  <data name="graphViewButton.[using:Windows.UI.Xaml.Automation]AutomationProperties.Name" xml:space="preserve">
    <value>Grafiekweergave</value>
    <comment>Screen reader prompt for the graph view button.</comment>
  </data>
  <data name="GraphViewAutomaticBestFitAnnouncement" xml:space="preserve">
    <value>Automatisch passend maken</value>
    <comment>Announcement used in Graphing Calculator when graph view button is clicked and automatic best fit is set</comment>
  </data>
  <data name="GraphViewManualAdjustmentAnnouncement" xml:space="preserve">
    <value>Handmatige aanpassing</value>
    <comment>Announcement used in Graphing Calculator when graph view button is clicked and manual adjustment is set</comment>
  </data>
  <data name="GridResetAnnouncement" xml:space="preserve">
    <value>Grafiekweergave is opnieuw ingesteld</value>
    <comment>Announcement used in Graphing Calculator when graph view button is clicked and automatic best fit is set, resetting the graph</comment>
  </data>
  <data name="zoomInButton.[using:Windows.UI.Xaml.Controls]ToolTipService.ToolTip" xml:space="preserve">
    <value>Inzoomen (Ctrl + plusteken)</value>
    <comment>This is the tool tip automation name for the Calculator zoom in button.</comment>
  </data>
  <data name="zoomInButton.[using:Windows.UI.Xaml.Automation]AutomationProperties.Name" xml:space="preserve">
    <value>Inzoomen</value>
    <comment>Screen reader prompt for the zoom in button.</comment>
  </data>
  <data name="zoomOutButton.[using:Windows.UI.Xaml.Controls]ToolTipService.ToolTip" xml:space="preserve">
    <value>Uitzoomen (Ctrl + minteken)</value>
    <comment>This is the tool tip automation name for the Calculator zoom out button.</comment>
  </data>
  <data name="zoomOutButton.[using:Windows.UI.Xaml.Automation]AutomationProperties.Name" xml:space="preserve">
    <value>Uitzoomen</value>
    <comment>Screen reader prompt for the zoom out button.</comment>
  </data>
  <data name="EquationTextBoxAddPanel.[using:Windows.UI.Xaml.Controls]ToolTipService.ToolTip" xml:space="preserve">
    <value>Vergelijking toevoegen</value>
    <comment>Placeholder text for the equation input button</comment>
  </data>
  <data name="ShareActionErrorMessage" xml:space="preserve">
    <value>Kan op dit moment niet delen.</value>
    <comment>If there is an error in the sharing action will display a dialog with this text.</comment>
  </data>
  <data name="ShareActionErrorOk" xml:space="preserve">
    <value>OK</value>
    <comment>Used on the dismiss button of the share action error dialog.</comment>
  </data>
  <data name="ShareActionTitle" xml:space="preserve">
    <value>Kijk wat voor grafiek ik met Windows Rekenmachine heb gemaakt</value>
    <comment>Sent as part of the shared content. The title for the share.</comment>
  </data>
  <data name="EquationsShareHeader" xml:space="preserve">
    <value>Vergelijkingen</value>
    <comment>Header that appears over the equations section when sharing</comment>
  </data>
  <data name="VariablesShareHeader" xml:space="preserve">
    <value>Variabelen</value>
    <comment>Header that appears over the variables section when sharing</comment>
  </data>
  <data name="GraphImageAltText" xml:space="preserve">
    <value>Afbeelding van een grafiek met vergelijkingen</value>
    <comment>Alt text for the graph image when output via Share</comment>
  </data>
  <data name="VaiablesHeader.Text" xml:space="preserve">
    <value>Variabelen</value>
    <comment>Header text for variables area</comment>
  </data>
  <data name="StepTextBlock.Text" xml:space="preserve">
    <value>Stap</value>
    <comment>Label text for the step text box</comment>
  </data>
  <data name="MinTextBlock.Text" xml:space="preserve">
    <value>Min</value>
    <comment>Label text for the min text box</comment>
  </data>
  <data name="MaxTextBlock.Text" xml:space="preserve">
    <value>Max</value>
    <comment>Label text for the max text box</comment>
  </data>
  <data name="LineColorText.Text" xml:space="preserve">
    <value>Kleur</value>
    <comment>Label for the Line Color section of the style picker</comment>
  </data>
  <data name="StyleChooserBoxHeading.Text" xml:space="preserve">
    <value>Stij</value>
    <comment>Label for the Line Style section of the style picker</comment>
  </data>
  <data name="KeyGraphFeaturesLabel.Text" xml:space="preserve">
    <value>Functieanalyse</value>
    <comment>Title for KeyGraphFeatures Control</comment>
  </data>
  <data name="KGFHorizontalAsymptotesNone" xml:space="preserve">
    <value>De functie heeft geen horizontale asymptoten.</value>
    <comment>Message displayed when the graph does not have any horizontal asymptotes</comment>
  </data>
  <data name="KGFInflectionPointsNone" xml:space="preserve">
    <value>Er zijn geen buigpunten voor de functie.</value>
    <comment>Message displayed when the graph does not have any inflection points</comment>
  </data>
  <data name="KGFMaximaNone" xml:space="preserve">
    <value>De functie heeft geen maximale punten.</value>
    <comment>Message displayed when the graph does not have any maxima</comment>
  </data>
  <data name="KGFMinimaNone" xml:space="preserve">
    <value>De functie heeft geen minimale punten.</value>
    <comment>Message displayed when the graph does not have any minima</comment>
  </data>
  <data name="KGFMonotonicityConstant" xml:space="preserve">
    <value>Constante</value>
    <comment>String describing constant monotonicity of a function</comment>
  </data>
  <data name="KGFMonotonicityDecreasing" xml:space="preserve">
    <value>Aflopend</value>
    <comment>String describing decreasing monotonicity of a function</comment>
  </data>
  <data name="KGFMonotonicityError" xml:space="preserve">
    <value>Kan de monotoniteit van de functie niet bepalen.</value>
    <comment>Error displayed when monotonicity cannot be determined</comment>
  </data>
  <data name="KGFMonotonicityIncreasing" xml:space="preserve">
    <value>Oplopend</value>
    <comment>String describing increasing monotonicity of a function</comment>
  </data>
  <data name="KGFMonotonicityUnknown" xml:space="preserve">
    <value>De monotoniteit van de functie is onbekend.</value>
    <comment>Error displayed when monotonicity is unknown</comment>
  </data>
  <data name="KGFObliqueAsymptotesNone" xml:space="preserve">
    <value>De functie heeft geen schuine asymptoten.</value>
    <comment>Message displayed when the graph does not have any oblique asymptotes</comment>
  </data>
  <data name="KGFParityError" xml:space="preserve">
    <value>Kan de pariteit van de functie niet bepalen.</value>
    <comment>Error displayed when parity is cannot be determined</comment>
  </data>
  <data name="KGFParityEven" xml:space="preserve">
    <value>De functie is even.</value>
    <comment>Message displayed with the function parity is even</comment>
  </data>
  <data name="KGFParityNeither" xml:space="preserve">
    <value>De functie is nog even noch oneven.</value>
    <comment>Message displayed with the function parity is neither even nor odd</comment>
  </data>
  <data name="KGFParityOdd" xml:space="preserve">
    <value>De functie is oneven.</value>
    <comment>Message displayed with the function parity is odd</comment>
  </data>
  <data name="KGFParityUnknown" xml:space="preserve">
    <value>De pariteit van de functie is onbekend.</value>
    <comment>Error displayed when parity is unknown</comment>
  </data>
  <data name="KGFPeriodicityError" xml:space="preserve">
    <value>Periodiciteit wordt niet ondersteund voor deze functie.</value>
    <comment>Error displayed when periodicity is not supported</comment>
  </data>
  <data name="KGFPeriodicityNotPeriodic" xml:space="preserve">
    <value>De functie is niet periodiek.</value>
    <comment>Message displayed with the function periodicity is not periodic</comment>
  </data>
  <data name="KGFPeriodicityUnknown" xml:space="preserve">
    <value>De periodiciteit van de functie is onbekend.</value>
    <comment>Message displayed with the function periodicity is unknown</comment>
  </data>
  <data name="KGFTooComplexFeaturesError" xml:space="preserve">
    <value>Deze functies zijn te complex voor de rekenmachine:</value>
    <comment>Error displayed when analysis features cannot be calculated</comment>
  </data>
  <data name="KGFVerticalAsymptotesNone" xml:space="preserve">
    <value>De functie heeft geen verticale asymptoten.</value>
    <comment>Message displayed when the graph does not have any vertical asymptotes</comment>
  </data>
  <data name="KGFXInterceptNone" xml:space="preserve">
    <value>De functie heeft geen x-snijpunten.</value>
    <comment>Message displayed when the graph does not have any x-intercepts</comment>
  </data>
  <data name="KGFYInterceptNone" xml:space="preserve">
    <value>De functie heeft geen y-snijpunten.</value>
    <comment>Message displayed when the graph does not have any y-intercepts</comment>
  </data>
  <data name="Domain" xml:space="preserve">
    <value>Domein</value>
    <comment>Title for KeyGraphFeatures Domain Property</comment>
  </data>
  <data name="HorizontalAsymptotes" xml:space="preserve">
    <value>Horizontale asymptoten</value>
    <comment>Title for KeyGraphFeatures Horizontal aysmptotes Property</comment>
  </data>
  <data name="InflectionPoints" xml:space="preserve">
    <value>Buigpunten</value>
    <comment>Title for KeyGraphFeatures Inflection points Property</comment>
  </data>
  <data name="KGFAnalysisNotSupported" xml:space="preserve">
    <value>De analyse wordt niet ondersteund voor deze functie.</value>
    <comment>Error displayed when graph analysis is not supported or had an error.</comment>
  </data>
  <data name="KGFVariableIsNotX" xml:space="preserve">
    <value>Analyse wordt alleen ondersteund voor functies in de notatie f(x). Voorbeeld: y=x</value>
    <comment>Error displayed when graph analysis detects the function format is not f(x).</comment>
  </data>
  <data name="Maxima" xml:space="preserve">
    <value>Maxima</value>
    <comment>Title for KeyGraphFeatures Maxima Property</comment>
  </data>
  <data name="Minima" xml:space="preserve">
    <value>Minima</value>
    <comment>Title for KeyGraphFeatures Minima Property</comment>
  </data>
  <data name="Monotonicity" xml:space="preserve">
    <value>Monotoniteit</value>
    <comment>Title for KeyGraphFeatures Monotonicity Property</comment>
  </data>
  <data name="ObliqueAsymptotes" xml:space="preserve">
    <value>Schuine asymptoten</value>
    <comment>Title for KeyGraphFeatures Oblique asymptotes Property</comment>
  </data>
  <data name="Parity" xml:space="preserve">
    <value>Pariteit</value>
    <comment>Title for KeyGraphFeatures Parity Property</comment>
  </data>
  <data name="Periodicity" xml:space="preserve">
    <value>Cyclus</value>
    <comment>Title for KeyGraphFeatures Periodicity Property. The period of a mathematical function is the smallest interval in its input values such that its output values repeat every such interval.</comment>
  </data>
  <data name="Range" xml:space="preserve">
    <value>Bereik</value>
    <comment>Title for KeyGraphFeatures Range Property</comment>
  </data>
  <data name="VerticalAsymptotes" xml:space="preserve">
    <value>Verticale asymptoten</value>
    <comment>Title for KeyGraphFeatures Vertical asymptotes Property</comment>
  </data>
  <data name="XIntercept" xml:space="preserve">
    <value>X-snijpunt</value>
    <comment>Title for KeyGraphFeatures XIntercept Property</comment>
  </data>
  <data name="YIntercept" xml:space="preserve">
    <value>Y-snijpunt</value>
    <comment>Title for KeyGraphFeatures YIntercept Property</comment>
  </data>
  <data name="KGFAnalysisCouldNotBePerformed" xml:space="preserve">
    <value>De analyse kan niet worden uitgevoerd voor de functie.</value>
  </data>
  <data name="KGFDomainNone" xml:space="preserve">
    <value>Kan het domein voor deze functie niet berekenen.</value>
    <comment>Error displayed when Domain is not returned from the analyzer.</comment>
  </data>
  <data name="KGFRangeNone" xml:space="preserve">
    <value>Kan het bereik voor deze functie niet berekenen.</value>
    <comment>Error displayed when Range is not returned from the analyzer.</comment>
  </data>
  <data name="Overflow" xml:space="preserve">
    <value>Overloop (het getal is te groot)</value>
    <comment>Error that occurs during graphing when the number is too large. To see this error, assign a large number to variable a, then keep doing "a:=a*a" until it happens.</comment>
  </data>
  <data name="RequireRadiansMode" xml:space="preserve">
    <value>Radialenmodus is vereist om deze vergelijking weer te geven in een grafiek.</value>
    <comment>Error that occurs during graphing when radians is required.</comment>
  </data>
  <data name="TooComplexToSolve" xml:space="preserve">
    <value>Deze functie is te complex om te worden weergegeven in een grafiek</value>
    <comment>Error that occurs during graphing when the equation is too complex.</comment>
  </data>
  <data name="RequireDegreesMode" xml:space="preserve">
    <value>Gradenmodus is vereist om deze vergelijking weer te geven in een grafiek</value>
    <comment>Error that occurs during graphing when degrees is required</comment>
  </data>
  <data name="FactorialInvalidArgument" xml:space="preserve">
    <value>De faculteitsfunctie heeft een ongeldig argument</value>
    <comment>Error that occurs during graphing when a factorial function has an invalid argument.</comment>
  </data>
  <data name="FactorialCannotPerformOnLargeNumber" xml:space="preserve">
    <value>De faculteitsfunctie heeft een argument dat te groot is voor een grafiek</value>
    <comment>Error that occurs during graphing when a factorial has a large n</comment>
  </data>
  <data name="ModuloCannotPerformOnFloat" xml:space="preserve">
    <value>Modulo kan alleen worden gebruikt voor gehele getallen</value>
    <comment>Error that occurs during graphing when modulo is used with a float.</comment>
  </data>
  <data name="EquationHasNoSolution" xml:space="preserve">
    <value>De vergelijking heeft geen oplossing</value>
    <comment>Error that occurs during graphing when the equation has no solution.</comment>
  </data>
  <data name="DivideByZero" xml:space="preserve">
    <value>Kan niet delen door nul</value>
    <comment>Error that occurs during graphing when a divison by zero occurs.</comment>
  </data>
  <data name="MutuallyExclusiveConditions" xml:space="preserve">
    <value>De vergelijking bevat logische voorwaarden die elkaar wederzijds uitsluiten</value>
    <comment>Error that occurs during graphing when mutually exclusive conditions are used.</comment>
  </data>
  <data name="OutOfDomain" xml:space="preserve">
    <value>Vergelijking valt buiten het domein</value>
    <comment>Error that occurs during graphing when the equation is out of domain.</comment>
  </data>
  <data name="GE_NotSupported" xml:space="preserve">
    <value>In een grafiek weergeven van deze vergelijking wordt niet ondersteund</value>
    <comment>Error that occurs during graphing when the equation is not supported.</comment>
  </data>
  <data name="ParenthesisMismatch" xml:space="preserve">
    <value>Er ontbreekt een haakje openen in de vergelijking</value>
    <comment>Error that occurs during graphing when the equation is missing a (</comment>
  </data>
  <data name="UnmatchedParenthesis" xml:space="preserve">
    <value>Er ontbreekt een haakje sluiten in de vergelijking</value>
    <comment>Error that occurs during graphing when the equation is missing a )</comment>
  </data>
  <data name="TooManyDecimalPoints" xml:space="preserve">
    <value>Een getal bevat teveel cijfers achter de komma</value>
    <comment>Error that occurs during graphing when a number has too many decimals. Ex: 1.2.3</comment>
  </data>
  <data name="DecimalPointWithoutDigits" xml:space="preserve">
    <value>Er ontbreken cijfers achter de komma</value>
    <comment>Error that occurs during graphing with a decimal point without digits</comment>
  </data>
  <data name="UnexpectedEndOfExpression" xml:space="preserve">
    <value>Onverwacht einde van expressie</value>
    <comment>Error that occurs during graphing when the expression ends unexpectedly. Ex: 3-4*</comment>
  </data>
  <data name="UnexpectedToken" xml:space="preserve">
    <value>De expressie bevat onverwachte tekens</value>
    <comment>Error that occurs during graphing when there is an unexpected token.</comment>
  </data>
  <data name="InvalidToken" xml:space="preserve">
    <value>De expressie bevat ongeldige tekens</value>
    <comment>Error that occurs during graphing when there is an invalid token.</comment>
  </data>
  <data name="TooManyEquals" xml:space="preserve">
    <value>Er zijn te veel gelijktekens</value>
    <comment>Error that occurs during graphing when there are too many equals.</comment>
  </data>
  <data name="EqualWithoutGraphVariable" xml:space="preserve">
    <value>De functie moet minstens één x- of y-variabele bevatten</value>
    <comment>Error that occurs during graphing when the equation is missing x or y.</comment>
  </data>
  <data name="InvalidEquationSyntax" xml:space="preserve">
    <value>Ongeldige expressie</value>
    <comment>Error that occurs during graphing when an invalid syntax is used.</comment>
  </data>
  <data name="EmptyExpression" xml:space="preserve">
    <value>De expressie is leeg</value>
    <comment>Error that occurs during graphing when the expression is empty</comment>
  </data>
  <data name="EqualWithoutEquation" xml:space="preserve">
    <value>Gelijkteken is gebruikt zonder een vergelijking</value>
    <comment>Error that occurs during graphing when equal is used without an equation. Ex: sin(x=y)</comment>
  </data>
  <data name="ExpectParenthesisAfterFunctionName" xml:space="preserve">
    <value>Haakjes na functienaam ontbreken</value>
    <comment>Error that occurs during graphing when parenthesis are missing after a function.</comment>
  </data>
  <data name="IncorrectNumParameter" xml:space="preserve">
    <value>Er is een rekenkundige bewerking met een onjuist aantal parameters</value>
    <comment>Error that occurs during graphing when a function has the wrong number of parameters</comment>
  </data>
  <data name="InvalidVariableNameFormat" xml:space="preserve">
    <value>Een variabelenaam is ongeldig</value>
    <comment>Error that occurs during graphing when a variable name is invalid.</comment>
  </data>
  <data name="BracketMismatch" xml:space="preserve">
    <value>Er ontbreekt een haak openen in de vergelijking</value>
    <comment>Error that occurs during graphing when a { is missing</comment>
  </data>
  <data name="UnmatchedBracket" xml:space="preserve">
    <value>Er ontbreekt een haak sluiten in de vergelijking</value>
    <comment>Error that occurs during graphing when a } is missing.</comment>
  </data>
  <data name="CannotUseIInReal" xml:space="preserve">
    <value>‘i’ en ‘I’ kunnen niet worden gebruikt als variabelenaam</value>
    <comment>Error that occurs during graphing when i or I is used.</comment>
  </data>
  <data name="GeneralError" xml:space="preserve">
    <value>De vergelijking wordt niet weergegeven in een grafiek</value>
    <comment>General error that occurs during graphing.</comment>
  </data>
  <data name="InvalidNumberDigit" xml:space="preserve">
    <value>Het cijfer kan niet worden omgezet voor de opgegeven basis</value>
    <comment>Error that occurs during graphing when trying to use bases incorrect. Ex: base(2,1020).</comment>
  </data>
  <data name="InvalidNumberBase" xml:space="preserve">
    <value>De basis moet groter dan 2 zijn en kleiner dan 36</value>
    <comment>Error that occurs during graphing when the base is out of range.</comment>
  </data>
  <data name="InvalidVariableSpecification" xml:space="preserve">
    <value>Voor een wiskundige bewerking moet een van de paramaters een variabele zijn</value>
    <comment>Error that occurs during graphing when a function requires a variable in a particular position. Ex: 2nd argument of deriv.</comment>
  </data>
  <data name="ExpectingLogicalOperands" xml:space="preserve">
    <value>Vergelijking combineert logische en scalaire operanden</value>
    <comment>Error that occurs during graphing when operands are mixed. Such as true and 1.</comment>
  </data>
  <data name="CannotUseIndexVarInOpLimits" xml:space="preserve">
    <value>x of y kan niet worden gebruikt in de boven- of ondergrens</value>
    <comment>Error that occurs during graphing when x or y is used in integral upper limits.</comment>
  </data>
  <data name="CannotUseIndexVarInLimPoint" xml:space="preserve">
    <value>x of y kan niet worden gebruikt in het limietpunt</value>
    <comment>Error that occurs during graphing when x or y is used in the limit point.</comment>
  </data>
  <data name="CannotUseComplexInfinityInReal" xml:space="preserve">
    <value>Kan geen complex oneindigheid gebruiken</value>
    <comment>Error that occurs during graphing when complex infinity is used</comment>
  </data>
  <data name="CannotUseIInInequalitySolving" xml:space="preserve">
    <value>Kan geen complex getallen gebruiken in ongelijkheden</value>
    <comment>Error that occurs during graphing when complex numbers are used in inequalities.</comment>
  </data>
  <data name="equationAnalysisBack.[using:Windows.UI.Xaml.Controls]ToolTipService.ToolTip" xml:space="preserve">
    <value>Teruggaan naar de lijst met functies</value>
    <comment>This is the tooltip for the back button in the equation analysis page in the graphing calculator</comment>
  </data>
  <data name="equationAnalysisBack.[using:Windows.UI.Xaml.Automation]AutomationProperties.Name" xml:space="preserve">
    <value>Teruggaan naar de lijst met functies</value>
    <comment>This is the automation name for the back button in the equation analysis page in the graphing calculator</comment>
  </data>
  <data name="functionAnalysisButton.[using:Windows.UI.Xaml.Controls]ToolTipService.ToolTip" xml:space="preserve">
    <value>Functie analyseren</value>
    <comment>This is the tooltip for the analyze function button</comment>
  </data>
  <data name="functionAnalysisButton.[using:Windows.UI.Xaml.Automation]AutomationProperties.Name" xml:space="preserve">
    <value>Functie analyseren</value>
    <comment>This is the automation name for the analyze function button</comment>
  </data>
  <data name="functionAnalysisMenuItem" xml:space="preserve">
    <value>Functie analyseren</value>
    <comment>This is the text for the for the analyze function context menu command</comment>
  </data>
  <data name="removeButton.[using:Windows.UI.Xaml.Controls]ToolTipService.ToolTip" xml:space="preserve">
    <value>Vergelijking verwijderen</value>
    <comment>This is the tooltip for the graphing calculator remove equation buttons</comment>
  </data>
  <data name="removeButton.[using:Windows.UI.Xaml.Automation]AutomationProperties.Name" xml:space="preserve">
    <value>Vergelijking verwijderen</value>
    <comment>This is the automation name for the graphing calculator remove equation buttons</comment>
  </data>
  <data name="removeMenuItem" xml:space="preserve">
    <value>Vergelijking verwijderen</value>
    <comment>This is the text for the for the remove equation context menu command</comment>
  </data>
  <data name="shareButton.[using:Windows.UI.Xaml.Controls]ToolTipService.ToolTip" xml:space="preserve">
    <value>Delen</value>
    <comment>This is the automation name for the graphing calculator share button.</comment>
  </data>
  <data name="shareButton.[using:Windows.UI.Xaml.Automation]AutomationProperties.Name" xml:space="preserve">
    <value>Delen</value>
    <comment>This is the tooltip for the graphing calculator share button.</comment>
  </data>
  <data name="colorChooserButton.[using:Windows.UI.Xaml.Controls]ToolTipService.ToolTip" xml:space="preserve">
    <value>Vergelijkingsstijl wijzigen</value>
    <comment>This is the tooltip for the graphing calculator equation style button</comment>
  </data>
  <data name="colorChooserButton.[using:Windows.UI.Xaml.Automation]AutomationProperties.Name" xml:space="preserve">
    <value>Vergelijkingsstijl wijzigen</value>
    <comment>This is the automation name for the graphing calculator equation style button</comment>
  </data>
  <data name="colorChooserMenuItem" xml:space="preserve">
    <value>Vergelijkingsstijl wijzigen</value>
    <comment>This is the text for the for the equation style context menu command</comment>
  </data>
  <data name="showEquationButtonToolTip" xml:space="preserve">
    <value>Vergelijking weergeven</value>
    <comment>This is the tooltip/automation name shown when visibility is set to hidden in the graphing calculator.</comment>
  </data>
  <data name="hideEquationButtonToolTip" xml:space="preserve">
    <value>Vergelijking verbergen</value>
    <comment>This is the tooltip/automation name shown when visibility is set to visible in the graphing calculator.</comment>
  </data>
  <data name="showEquationButtonAutomationName" xml:space="preserve">
    <value>Vergelijking %1 weergeven</value>
    <comment>{Locked="%1"}, This is the tooltip/automation name shown when visibility is set to hidden in the graphing calculator. %1 is the equation number.</comment>
  </data>
  <data name="hideEquationButtonAutomationName" xml:space="preserve">
    <value>Vergelijking %1 verbergen</value>
    <comment>{Locked="%1"}, This is the tooltip/automation name shown when visibility is set to visible in the graphing calculator. %1 is the equation number.</comment>
  </data>
  <data name="disableTracingButtonToolTip" xml:space="preserve">
    <value>Tracering stoppen</value>
    <comment>This is the tooltip/automation name for the graphing calculator stop tracing button</comment>
  </data>
  <data name="enableTracingButtonToolTip" xml:space="preserve">
    <value>Tracering starten</value>
    <comment>This is the tooltip/automation name for the graphing calculator start tracing button</comment>
  </data>
  <data name="graphAutomationName" xml:space="preserve">
    <value>Het diagramweergave venster, de x-as die wordt begrensd door %1 en %2, y-as die wordt begrensd door %3 en %4, waarbij %5 vergelijkingen worden weergegeven</value>
    <comment>{Locked="%1","%2", "%3", "%4", "%5"}. </comment>
  </data>
  <data name="sliderOptionsButton.[using:Windows.UI.Xaml.Controls]ToolTipService.ToolTip" xml:space="preserve">
    <value>Schuifregelaar configureren</value>
    <comment>This is the tooltip text for the slider options button in Graphing Calculator</comment>
  </data>
  <data name="sliderOptionsButton.[using:Windows.UI.Xaml.Automation]AutomationProperties.Name" xml:space="preserve">
    <value>Schuifregelaar configureren</value>
    <comment>This is the automation name text for the slider options button in Graphing Calculator</comment>
  </data>
  <data name="GraphSwitchToEquationMode" xml:space="preserve">
    <value>Vergelijkingsmodus activeren</value>
    <comment>Used in Graphing Calculator to switch the view to the equation mode</comment>
  </data>
  <data name="GraphSwitchToGraphMode" xml:space="preserve">
    <value>Grafiekmodus activeren</value>
    <comment>Used in Graphing Calculator to switch the view to the graph mode</comment>
  </data>
  <data name="SwitchModeToggleButton.[using:Windows.UI.Xaml.Automation]AutomationProperties.Name" xml:space="preserve">
    <value>Vergelijkingsmodus activeren</value>
    <comment>Used in Graphing Calculator to switch the view to the equation mode</comment>
  </data>
  <data name="GraphSwitchedToEquationModeAnnouncement" xml:space="preserve">
    <value>De huidige modus is de vergelijkingsmodus</value>
    <comment>Announcement used in Graphing Calculator when switching to the equation mode</comment>
  </data>
  <data name="GraphSwitchedToGraphModeAnnouncement" xml:space="preserve">
    <value>De huidige modus is grafiekmodus</value>
    <comment>Announcement used in Graphing Calculator when switching to the graph mode</comment>
  </data>
  <data name="GridHeading.Text" xml:space="preserve">
    <value>Venster</value>
    <comment>Heading for window extents on the settings </comment>
  </data>
  <data name="TrigModeDegrees.Content" xml:space="preserve">
    <value>Graden</value>
    <comment>Degrees mode on settings page</comment>
  </data>
  <data name="TrigModeGradians.Content" xml:space="preserve">
    <value>Gradiënten</value>
    <comment>Gradian mode on settings page</comment>
  </data>
  <data name="TrigModeRadians.Content" xml:space="preserve">
    <value>Radialen</value>
    <comment>Radians mode on settings page</comment>
  </data>
  <data name="UnitsHeading.Text" xml:space="preserve">
    <value>Eenheden</value>
    <comment>Heading for Unit's on the settings</comment>
  </data>
  <data name="ResetViewButton.Content" xml:space="preserve">
    <value>Weergave opnieuw instellen</value>
    <comment>Hyperlink button to reset the view of the graph</comment>
  </data>
  <data name="GraphSettingsXMax.Header" xml:space="preserve">
    <value>X-Max</value>
    <comment>X maximum value header</comment>
  </data>
  <data name="GraphSettingsXMin.Header" xml:space="preserve">
    <value>X-Min</value>
    <comment>X minimum value header</comment>
  </data>
  <data name="GraphSettingsYMax.Header" xml:space="preserve">
    <value>Y-Max</value>
    <comment>Y Maximum value header</comment>
  </data>
  <data name="GraphSettingsYMin.Header" xml:space="preserve">
    <value>Y-Min</value>
    <comment>Y minimum value header</comment>
  </data>
  <data name="graphSettingsButton.[using:Windows.UI.Xaml.Controls]ToolTipService.ToolTip" xml:space="preserve">
    <value>Grafiekopties</value>
    <comment>This is the tooltip text for the graph options button in Graphing Calculator</comment>
  </data>
  <data name="graphSettingsButton.[using:Windows.UI.Xaml.Automation]AutomationProperties.Name" xml:space="preserve">
    <value>Grafiekopties</value>
    <comment>This is the automation name text for the graph options button in Graphing Calculator</comment>
  </data>
  <data name="GraphOptionsHeading.Text" xml:space="preserve">
    <value>Grafiekopties</value>
    <comment>Heading for the Graph options flyout in Graphing mode.</comment>
  </data>
  <data name="VariableAreaSettings.[using:Windows.UI.Xaml.Automation]AutomationProperties.Name" xml:space="preserve">
    <value>Opties voor variabele</value>
    <comment>Screen reader prompt for the variable settings toggle button</comment>
  </data>
  <data name="VariableAreaSettings.[using:Windows.UI.Xaml.Controls]ToolTipService.ToolTip" xml:space="preserve">
    <value>Variabele opties voor wisselknop</value>
    <comment>Tool tip for the variable settings toggle button</comment>
  </data>
  <data name="LineThicknessBoxHeading.Text" xml:space="preserve">
    <value>Lijndikte</value>
    <comment>Heading for the Graph options flyout in Graphing mode.</comment>
  </data>
  <data name="LineOptionsHeading.Text" xml:space="preserve">
    <value>Lijnopties</value>
    <comment>Heading for the equation style flyout in Graphing mode.</comment>
  </data>
  <data name="SmallLineWidthAutomationName" xml:space="preserve">
    <value>Kleine lijnbreedte</value>
    <comment>Automation name for line width setting</comment>
  </data>
  <data name="MediumLineWidthAutomationName" xml:space="preserve">
    <value>Gemiddelde lijnbreedte</value>
    <comment>Automation name for line width setting</comment>
  </data>
  <data name="LargeLineWidthAutomationName" xml:space="preserve">
    <value>Grote lijnbreedte</value>
    <comment>Automation name for line width setting</comment>
  </data>
  <data name="ExtraLargeLineWidthAutomationName" xml:space="preserve">
    <value>Extra grote lijnbreedte</value>
    <comment>Automation name for line width setting</comment>
  </data>
  <data name="mathRichEditBox.PlaceholderText" xml:space="preserve">
    <value>Voer een expressie in</value>
    <comment>this is the placeholder text used by the textbox to enter an equation</comment>
  </data>
  <data name="GraphCopyMenuItem.Text" xml:space="preserve">
    <value>Kopiëren</value>
    <comment>Copy menu item for the graph context menu</comment>
  </data>
  <data name="cutEquationMenuItem.Text" xml:space="preserve">
    <value>Knippen</value>
    <comment>Cut menu item from the Equation TextBox</comment>
  </data>
  <data name="copyEquationMenuItem.Text" xml:space="preserve">
    <value>Kopiëren</value>
    <comment>Copy menu item from the Equation TextBox</comment>
  </data>
  <data name="pasteEquationMenuItem.Text" xml:space="preserve">
    <value>Plakken</value>
    <comment>Paste menu item from the Equation TextBox</comment>
  </data>
  <data name="undoEquationMenuItem.Text" xml:space="preserve">
    <value>Ongedaan maken</value>
    <comment>Undo menu item from the Equation TextBox</comment>
  </data>
  <data name="selectAllEquationMenuItem.Text" xml:space="preserve">
    <value>Alles selecteren</value>
    <comment>Select all menu item from the Equation TextBox</comment>
  </data>
  <data name="EquationInputButton.[using:Windows.UI.Xaml.Automation]AutomationProperties.Name" xml:space="preserve">
    <value>Functie-invoer</value>
    <comment>The automation name for the Equation Input ListView item that is shown when Calculator is in graphing mode.</comment>
  </data>
  <data name="EquationInputList.[using:Windows.UI.Xaml.Automation]AutomationProperties.Name" xml:space="preserve">
    <value>Functie-invoer</value>
    <comment>The automation name for the Equation Input ListView that is shown when Calculator is in graphing mode.</comment>
  </data>
  <data name="EquationInputPanel.[using:Windows.UI.Xaml.Automation]AutomationProperties.Name" xml:space="preserve">
    <value>Paneel voor functie-invoer</value>
    <comment>The automation name for the Equation Input StackPanel that is shown when Calculator is in graphing mode.</comment>
  </data>
  <data name="VariableStackPanel.[using:Windows.UI.Xaml.Automation]AutomationProperties.Name" xml:space="preserve">
    <value>Paneel voor variabelen</value>
    <comment>The automation name for the Variable StackPanel that is shown when Calculator is in graphing mode.</comment>
  </data>
  <data name="VariableListView.[using:Windows.UI.Xaml.Automation]AutomationProperties.Name" xml:space="preserve">
    <value>Lijst met variabelen</value>
    <comment>The automation name for the Variable ListView that is shown when Calculator is in graphing mode.</comment>
  </data>
  <data name="VariableListViewItem" xml:space="preserve">
    <value>Lijstitem voor variabele %1</value>
    <comment>The automation name for the Variable ListViewItem that is shown when Calculator is in graphing mode.</comment>
  </data>
  <data name="VariableValueTextBox.[using:Windows.UI.Xaml.Automation]AutomationProperties.Name" xml:space="preserve">
    <value>Tekstvak Waarde van variabele</value>
    <comment>The automation name for the Variable Value Textbox that is shown when Calculator is in graphing mode.</comment>
  </data>
  <data name="VariableValueSlider.[using:Windows.UI.Xaml.Automation]AutomationProperties.Name" xml:space="preserve">
    <value>Schuifregelaar voor waarde van variabele</value>
    <comment>The automation name for the Variable Value Slider that is shown when Calculator is in graphing mode.</comment>
  </data>
  <data name="VariableMinTextBox.[using:Windows.UI.Xaml.Automation]AutomationProperties.Name" xml:space="preserve">
    <value>Tekstvak Minimale waarde van variabele</value>
    <comment>The automation name for the Variable Min Value Textbox that is shown when Calculator is in graphing mode.</comment>
  </data>
  <data name="VariableStepTextBox.[using:Windows.UI.Xaml.Automation]AutomationProperties.Name" xml:space="preserve">
    <value>Tekstvak Intervalwaarde van variabele</value>
    <comment>The automation name for the Variable Step Textbox that is shown when Calculator is in graphing mode.</comment>
  </data>
  <data name="VariableMaxTextBox.[using:Windows.UI.Xaml.Automation]AutomationProperties.Name" xml:space="preserve">
    <value>Tekstvak Maximale waarde van variabele</value>
    <comment>The automation name for the Variable Max Value Textbox that is shown when Calculator is in graphing mode.</comment>
  </data>
  <data name="solidLineStyleAutomationName" xml:space="preserve">
    <value>Effen lijnstijl</value>
    <comment>Name of the solid line style for a graphed equation</comment>
  </data>
  <data name="dotLineStyleAutomationName" xml:space="preserve">
    <value>Lijnstijl voor punten</value>
    <comment>Name of the dotted line style for a graphed equation</comment>
  </data>
  <data name="dashLineStyleAutomationName" xml:space="preserve">
    <value>Streepje lijnstijl:</value>
    <comment>Name of the dashed line style for a graphed equation</comment>
  </data>
  <data name="equationColor1AutomationName" xml:space="preserve">
    <value>Marineblauw</value>
    <comment>Name of color in the color picker</comment>
  </data>
  <data name="equationColor2AutomationName" xml:space="preserve">
    <value>Zeeschuim</value>
    <comment>Name of color in the color picker</comment>
  </data>
  <data name="equationColor3AutomationName" xml:space="preserve">
    <value>Violet</value>
    <comment>Name of color in the color picker</comment>
  </data>
  <data name="equationColor4AutomationName" xml:space="preserve">
    <value>Groen</value>
    <comment>Name of color in the color picker</comment>
  </data>
  <data name="equationColor5AutomationName" xml:space="preserve">
    <value>Mintgroen</value>
    <comment>Name of color in the color picker</comment>
  </data>
  <data name="equationColor6AutomationName" xml:space="preserve">
    <value>Donkergroen</value>
    <comment>Name of color in the color picker</comment>
  </data>
  <data name="equationColor7AutomationName" xml:space="preserve">
    <value>Houtskool</value>
    <comment>Name of color in the color picker</comment>
  </data>
  <data name="equationColor8AutomationName" xml:space="preserve">
    <value>Rood</value>
    <comment>Name of color in the color picker</comment>
  </data>
  <data name="equationColor9AutomationName" xml:space="preserve">
    <value>Lichtpaars</value>
    <comment>Name of color in the color picker</comment>
  </data>
  <data name="equationColor10AutomationName" xml:space="preserve">
    <value>Magenta</value>
    <comment>Name of color in the color picker</comment>
  </data>
  <data name="equationColor11AutomationName" xml:space="preserve">
    <value>Geelgoud</value>
    <comment>Name of color in the color picker</comment>
  </data>
  <data name="equationColor12AutomationName" xml:space="preserve">
    <value>Helderoranje</value>
    <comment>Name of color in the color picker</comment>
  </data>
  <data name="equationColor13AutomationName" xml:space="preserve">
    <value>Bruin</value>
    <comment>Name of color in the color picker</comment>
  </data>
  <data name="equationColor14BlackAutomationName" xml:space="preserve">
    <value>Zwart</value>
    <comment>Name of color in the color picker</comment>
  </data>
  <data name="equationColor14WhiteAutomationName" xml:space="preserve">
    <value>Wit</value>
    <comment>Name of color in the color picker</comment>
  </data>
  <data name="equationHighContrastColor1AutomationName" xml:space="preserve">
    <value>Kleur 1</value>
    <comment>Name of color in the color picker</comment>
  </data>
  <data name="equationHighContrastColor2AutomationName" xml:space="preserve">
    <value>Kleur 2</value>
    <comment>Name of color in the color picker</comment>
  </data>
  <data name="equationHighContrastColor3AutomationName" xml:space="preserve">
    <value>Kleur 3</value>
    <comment>Name of color in the color picker</comment>
  </data>
  <data name="equationHighContrastColor4AutomationName" xml:space="preserve">
    <value>Kleur 4</value>
    <comment>Name of color in the color picker</comment>
  </data>
  <data name="GraphThemeHeading.Text" xml:space="preserve">
    <value>Grafiekthema</value>
    <comment>Graph settings heading for the theme options</comment>
  </data>
  <data name="AlwaysLightTheme.Content" xml:space="preserve">
    <value>Altijd licht</value>
    <comment>Graph settings option to set graph to light theme</comment>
  </data>
  <data name="MatchAppTheme.Content" xml:space="preserve">
    <value>Overeenkomen met app-thema</value>
    <comment>Graph settings option to set graph to match the app theme</comment>
  </data>
  <data name="GraphThemeHeading.[using:Windows.UI.Xaml.Automation]AutomationProperties.Name" xml:space="preserve">
    <value>Thema</value>
    <comment>This is the automation name text for the Graph settings heading for the theme options</comment>
  </data>
  <data name="AlwaysLightTheme.[using:Windows.UI.Xaml.Automation]AutomationProperties.Name" xml:space="preserve">
    <value>Altijd licht</value>
    <comment>This is the automation name text for the Graph settings option to set graph to light theme</comment>
  </data>
  <data name="MatchAppTheme.[using:Windows.UI.Xaml.Automation]AutomationProperties.Name" xml:space="preserve">
    <value>Overeenkomen met app-thema</value>
    <comment>This is the automation name text for the Graph settings option to set graph to match the app theme</comment>
  </data>
  <data name="FunctionRemovedAnnouncement" xml:space="preserve">
    <value>Functie verwijderd</value>
    <comment>Announcement used in Graphing Calculator when a function is removed from the function list</comment>
  </data>
  <data name="KGFEquationTextBox.[using:Windows.UI.Xaml.Automation]AutomationProperties.Name" xml:space="preserve">
    <value>Vak Vergelijking van functieanalyse</value>
    <comment>This is the automation name text for the equation box in the function analysis panel</comment>
  </data>
  <data name="graphingEqualButton.[using:Windows.UI.Xaml.Automation]AutomationProperties.Name" xml:space="preserve">
    <value>Is gelijk aan</value>
    <comment>Screen reader prompt for the equal button on the graphing calculator operator keypad</comment>
  </data>
  <data name="lessThanFlyoutButton.[using:Windows.UI.Xaml.Automation]AutomationProperties.Name" xml:space="preserve">
    <value>Kleiner dan</value>
    <comment>Screen reader prompt for the Less than button</comment>
  </data>
  <data name="lessThanOrEqualFlyoutButton.[using:Windows.UI.Xaml.Automation]AutomationProperties.Name" xml:space="preserve">
    <value>Kleiner dan of gelijk aan</value>
    <comment>Screen reader prompt for the Less than or equal button</comment>
  </data>
  <data name="equalsFlyoutButton.[using:Windows.UI.Xaml.Automation]AutomationProperties.Name" xml:space="preserve">
    <value>Gelijk aan</value>
    <comment>Screen reader prompt for the Equal button</comment>
  </data>
  <data name="greaterThanOrEqualFlyoutButton.[using:Windows.UI.Xaml.Automation]AutomationProperties.Name" xml:space="preserve">
    <value>Groter dan of gelijk aan</value>
    <comment>Screen reader prompt for the Greater than or equal button</comment>
  </data>
  <data name="greaterThanFlyoutButton.[using:Windows.UI.Xaml.Automation]AutomationProperties.Name" xml:space="preserve">
    <value>Groter dan</value>
    <comment>Screen reader prompt for the Greater than button</comment>
  </data>
  <data name="xButton.[using:Windows.UI.Xaml.Automation]AutomationProperties.Name" xml:space="preserve">
    <value>X</value>
    <comment>Screen reader prompt for the X button on the graphing calculator operator keypad</comment>
  </data>
  <data name="yButton.[using:Windows.UI.Xaml.Automation]AutomationProperties.Name" xml:space="preserve">
    <value>Y</value>
    <comment>Screen reader prompt for the Y button on the graphing calculator operator keypad</comment>
  </data>
  <data name="submitButton.[using:Windows.UI.Xaml.Automation]AutomationProperties.Name" xml:space="preserve">
    <value>Verzenden</value>
    <comment>Screen reader prompt for the submit button on the graphing calculator operator keypad</comment>
  </data>
  <data name="FunctionAnalysisGrid.[using:Windows.UI.Xaml.Automation]AutomationProperties.Name" xml:space="preserve">
    <value>Functieanalyse</value>
    <comment>Screen reader prompt for the function analysis grid</comment>
  </data>
  <data name="GraphSettingsGrid.[using:Windows.UI.Xaml.Automation]AutomationProperties.Name" xml:space="preserve">
    <value>Grafiekopties</value>
    <comment>Screen reader prompt for the graph options panel</comment>
  </data>
  <data name="DockPanel_HistoryMemoryLists" xml:space="preserve">
    <value>Geschiedenis- en geheugenlijsten</value>
    <comment>Automation name for the group of controls for history and memory lists.</comment>
  </data>
  <data name="DockPanel_MemoryList" xml:space="preserve">
    <value>Geheugenlijst</value>
    <comment>Automation name for the group of controls for memory list.</comment>
  </data>
  <data name="Format_HistorySlotCleared" xml:space="preserve">
    <value>Geschiedenissleuf %1 is gewist</value>
    <comment>{Locked='%1'} Formatting string for a Narrator announcement when the user clears a history slot. The %1 is the index of the history slot. For example, users might hear "History slot 2 cleared".</comment>
  </data>
  <data name="CalcAlwaysOnTop" xml:space="preserve">
    <value>Rekenmachine altijd op voorgrond</value>
    <comment>Announcement to indicate calculator window is always shown on top.</comment>
  </data>
  <data name="CalcBackToFullView" xml:space="preserve">
    <value>Rekenmachine terug in volledige weergave</value>
    <comment>Announcement to indicate calculator window is now back to full view.</comment>
  </data>
  <data name="arithmeticShiftButtonSelected" xml:space="preserve">
    <value>Rekenkundige verschuiving geselecteerd</value>
    <comment>Label for a radio button that toggles arithmetic shift behavior for the shift operations.</comment>
  </data>
  <data name="logicalShiftButtonSelected" xml:space="preserve">
    <value>Logische verschuiving geselecteerd</value>
    <comment>Label for a radio button that toggles logical shift behavior for the shift operations.</comment>
  </data>
  <data name="rotateCircularButtonSelected" xml:space="preserve">
    <value>Circulaire verschuiving draaien geselecteerd</value>
    <comment>Label for a radio button that toggles rotate circular behavior for the shift operations.</comment>
  </data>
  <data name="rotateCarryShiftButtonSelected" xml:space="preserve">
    <value>Draaien via overnemen circulaire verschuiving geselecteerd</value>
    <comment>Label for a radio button that toggles rotate circular with carry behavior for the shift operations.</comment>
  </data>
  <data name="SettingsHeader.Text" xml:space="preserve">
    <value>Instellingen</value>
    <comment>Header text of Settings page</comment>
  </data>
  <data name="SettingsAppearance.Text" xml:space="preserve">
    <value>Uiterlijk</value>
    <comment>Subtitle of appearance setting on Settings page</comment>
  </data>
  <data name="AppThemeExpander.Header" xml:space="preserve">
    <value>App-thema</value>
    <comment>Title of App theme expander</comment>
  </data>
  <data name="AppThemeExpander.Description" xml:space="preserve">
    <value>Selecteer welk app-thema moet worden weergegeven</value>
    <comment>Description of App theme expander</comment>
  </data>
  <data name="LightThemeRadioButton.Content" xml:space="preserve">
    <value>Licht</value>
    <comment>Lable for light theme option</comment>
  </data>
  <data name="DarkThemeRadioButton.Content" xml:space="preserve">
    <value>Donker</value>
    <comment>Lable for dark theme option</comment>
  </data>
  <data name="SystemThemeRadioButton.Content" xml:space="preserve">
    <value>Systeeminstelling gebruiken</value>
    <comment>Lable for the app theme option to use system setting</comment>
  </data>
  <data name="TitleBarBackButton.[using:Windows.UI.Xaml.Automation]AutomationProperties.Name" xml:space="preserve">
    <value>Terug</value>
    <comment>Screen reader prompt for the Back button in title bar to back to main page</comment>
  </data>
  <data name="SettingsPageOpenedAnnouncement" xml:space="preserve">
    <value>Instellingenpagina</value>
    <comment>Announcement used when Settings page is opened</comment>
  </data>
  <data name="MathRichEditBox.[using:Windows.UI.Xaml.Automation]AutomationProperties.Name" xml:space="preserve">
    <value>Het contextmenu openen voor beschikbare acties</value>
    <comment>Screen reader prompt for the context menu of the expression box</comment>
  </data>
  <data name="ErrorButtonOk" xml:space="preserve">
    <value>OK</value>
    <comment>The text of OK button to dismiss an error dialog.</comment>
  </data>
  <data name="SnapshotRestoreError" xml:space="preserve">
    <value>Kan deze momentopname niet herstellen.</value>
    <comment>The error message to notify user that restoring from snapshot has failed.</comment>
  </data>
</root>