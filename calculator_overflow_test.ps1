# Windows Calculator Overflow Testing Script
# This PowerShell script tests for integer overflow vulnerabilities

param(
    [switch]$Verbose,
    [switch]$MemoryTest,
    [switch]$CrashTest
)

Write-Host "Windows Calculator Overflow Vulnerability Test" -ForegroundColor Cyan
Write-Host "=" * 50 -ForegroundColor Cyan

# Function to start calculator and get process info
function Start-CalculatorTest {
    Write-Host "`nStarting Windows Calculator..." -ForegroundColor Yellow
    
    # Kill any existing calculator processes
    Get-Process -Name "*calc*" -ErrorAction SilentlyContinue | Stop-Process -Force
    Start-Sleep -Seconds 1
    
    # Start calculator
    Start-Process "calc.exe"
    Start-Sleep -Seconds 3
    
    # Get calculator process
    $calcProcess = Get-Process -Name "*calc*" -ErrorAction SilentlyContinue | Select-Object -First 1
    
    if ($calcProcess) {
        Write-Host "Calculator started successfully (PID: $($calcProcess.Id))" -ForegroundColor Green
        $initialMemory = $calcProcess.WorkingSet64 / 1MB
        Write-Host "Initial memory usage: $([math]::Round($initialMemory, 2)) MB" -ForegroundColor Green
        return $calcProcess
    } else {
        Write-Host "Failed to start calculator" -ForegroundColor Red
        return $null
    }
}

# Function to send keys to calculator
function Send-CalculatorInput {
    param(
        [string]$Input,
        [int]$DelayMs = 50
    )
    
    Add-Type -AssemblyName System.Windows.Forms
    
    # Focus on calculator window
    $calcWindow = Get-Process -Name "*calc*" -ErrorAction SilentlyContinue | Select-Object -First 1
    if ($calcWindow) {
        # Clear calculator first
        [System.Windows.Forms.SendKeys]::SendWait("{ESC}")
        Start-Sleep -Milliseconds 200
        
        # Send input character by character
        foreach ($char in $Input.ToCharArray()) {
            [System.Windows.Forms.SendKeys]::SendWait($char)
            Start-Sleep -Milliseconds $DelayMs
        }
        return $true
    }
    return $false
}

# Test 1: Large number input
function Test-LargeNumberInput {
    param($Process)
    
    Write-Host "`n=== Test 1: Large Number Input ===" -ForegroundColor Cyan
    
    $testCases = @(
        @{Name="Small"; Digits=50},
        @{Name="Medium"; Digits=100},
        @{Name="Large"; Digits=500},
        @{Name="Very Large"; Digits=1000}
    )
    
    foreach ($test in $testCases) {
        Write-Host "`nTesting $($test.Name) number ($($test.Digits) digits)..." -ForegroundColor Yellow
        
        $initialMemory = $Process.WorkingSet64 / 1MB
        Write-Host "Memory before: $([math]::Round($initialMemory, 2)) MB"
        
        # Create large number string
        $largeNumber = "9" * $test.Digits
        
        if (Send-CalculatorInput -Input $largeNumber) {
            Start-Sleep -Seconds 2
            
            # Refresh process info
            $Process.Refresh()
            $finalMemory = $Process.WorkingSet64 / 1MB
            $memoryIncrease = $finalMemory - $initialMemory
            
            Write-Host "Memory after: $([math]::Round($finalMemory, 2)) MB"
            Write-Host "Memory increase: $([math]::Round($memoryIncrease, 2)) MB"
            
            if ($memoryIncrease -gt 10) {
                Write-Host "🚨 POTENTIAL MEMORY ISSUE: Large memory increase detected!" -ForegroundColor Red
            }
            
            # Check if process is still running
            if ($Process.HasExited) {
                Write-Host "🚨 CRASH DETECTED: Calculator process terminated!" -ForegroundColor Red
                return $false
            }
        } else {
            Write-Host "❌ Failed to send input" -ForegroundColor Red
        }
    }
    return $true
}

# Test 2: Multiplication overflow
function Test-MultiplicationOverflow {
    param($Process)
    
    Write-Host "`n=== Test 2: Multiplication Overflow ===" -ForegroundColor Cyan
    
    $testCases = @(
        @{Name="Large * Large"; Num1="999999999"; Op="*"; Num2="999999999"},
        @{Name="Max 32-bit"; Num1="4294967295"; Op="*"; Num2="4294967295"},
        @{Name="Repeated multiplication"; Num1="99999"; Op="*"; Num2="99999"}
    )
    
    foreach ($test in $testCases) {
        Write-Host "`nTesting $($test.Name): $($test.Num1) $($test.Op) $($test.Num2)" -ForegroundColor Yellow
        
        $initialMemory = $Process.WorkingSet64 / 1MB
        
        # Send first number
        if (Send-CalculatorInput -Input $test.Num1) {
            Start-Sleep -Milliseconds 500
            
            # Send operator
            [System.Windows.Forms.SendKeys]::SendWait($test.Op)
            Start-Sleep -Milliseconds 500
            
            # Send second number
            if (Send-CalculatorInput -Input $test.Num2) {
                Start-Sleep -Milliseconds 500
                
                # Press equals
                [System.Windows.Forms.SendKeys]::SendWait("{ENTER}")
                Start-Sleep -Seconds 2
                
                $Process.Refresh()
                $finalMemory = $Process.WorkingSet64 / 1MB
                $memoryChange = $finalMemory - $initialMemory
                
                Write-Host "Memory change: $([math]::Round($memoryChange, 2)) MB"
                
                if ($Process.HasExited) {
                    Write-Host "🚨 CRASH DETECTED: Calculator crashed during operation!" -ForegroundColor Red
                    return $false
                }
            }
        }
    }
    return $true
}

# Test 3: Copy-paste attack
function Test-CopyPasteAttack {
    param($Process)
    
    Write-Host "`n=== Test 3: Copy-Paste Attack ===" -ForegroundColor Cyan
    
    # Create extremely large number
    $largeNumber = "9" * 5000
    Write-Host "Testing copy-paste of $($largeNumber.Length) digit number..." -ForegroundColor Yellow
    
    try {
        # Copy to clipboard
        Set-Clipboard -Value $largeNumber
        Start-Sleep -Milliseconds 500
        
        $initialMemory = $Process.WorkingSet64 / 1MB
        
        # Paste into calculator
        [System.Windows.Forms.SendKeys]::SendWait("^v")
        Start-Sleep -Seconds 3
        
        $Process.Refresh()
        $finalMemory = $Process.WorkingSet64 / 1MB
        $memoryChange = $finalMemory - $initialMemory
        
        Write-Host "Memory change: $([math]::Round($memoryChange, 2)) MB"
        
        if ($memoryChange -gt 20) {
            Write-Host "🚨 LARGE MEMORY ALLOCATION DETECTED!" -ForegroundColor Red
        }
        
        if ($Process.HasExited) {
            Write-Host "🚨 CRASH DETECTED!" -ForegroundColor Red
            return $false
        }
    } catch {
        Write-Host "Error in copy-paste test: $($_.Exception.Message)" -ForegroundColor Red
    }
    
    return $true
}

# Test 4: Stress test with rapid operations
function Test-StressOperations {
    param($Process)
    
    Write-Host "`n=== Test 4: Stress Test ===" -ForegroundColor Cyan
    
    Write-Host "Performing rapid calculations..." -ForegroundColor Yellow
    
    $initialMemory = $Process.WorkingSet64 / 1MB
    
    # Perform many rapid operations
    for ($i = 1; $i -le 50; $i++) {
        if (Send-CalculatorInput -Input "999999" -DelayMs 10) {
            [System.Windows.Forms.SendKeys]::SendWait("*")
            Start-Sleep -Milliseconds 10
            if (Send-CalculatorInput -Input "999999" -DelayMs 10) {
                [System.Windows.Forms.SendKeys]::SendWait("=")
                Start-Sleep -Milliseconds 10
            }
        }
        
        if ($i % 10 -eq 0) {
            Write-Host "Completed $i operations..."
            if ($Process.HasExited) {
                Write-Host "🚨 CRASH DETECTED after $i operations!" -ForegroundColor Red
                return $false
            }
        }
    }
    
    $Process.Refresh()
    $finalMemory = $Process.WorkingSet64 / 1MB
    $memoryChange = $finalMemory - $initialMemory
    
    Write-Host "Memory change after stress test: $([math]::Round($memoryChange, 2)) MB"
    
    return $true
}

# Main execution
function Main {
    Add-Type -AssemblyName System.Windows.Forms
    
    $calcProcess = Start-CalculatorTest
    if (-not $calcProcess) {
        Write-Host "Cannot proceed without calculator process" -ForegroundColor Red
        return
    }
    
    try {
        $success = $true
        
        if ($success) { $success = Test-LargeNumberInput -Process $calcProcess }
        if ($success) { $success = Test-MultiplicationOverflow -Process $calcProcess }
        if ($success) { $success = Test-CopyPasteAttack -Process $calcProcess }
        if ($success) { $success = Test-StressOperations -Process $calcProcess }
        
        Write-Host "`n=== Test Summary ===" -ForegroundColor Cyan
        if ($success) {
            Write-Host "All tests completed without crashes" -ForegroundColor Green
        } else {
            Write-Host "Some tests caused crashes or failures" -ForegroundColor Red
        }
        
        if (-not $calcProcess.HasExited) {
            $calcProcess.Refresh()
            $finalMemory = $calcProcess.WorkingSet64 / 1MB
            Write-Host "Final memory usage: $([math]::Round($finalMemory, 2)) MB" -ForegroundColor Green
        }
        
    } catch {
        Write-Host "Unexpected error: $($_.Exception.Message)" -ForegroundColor Red
    } finally {
        Write-Host "`nTest completed. Check for any crashes or memory issues." -ForegroundColor Cyan
    }
}

# Run the main function
Main
